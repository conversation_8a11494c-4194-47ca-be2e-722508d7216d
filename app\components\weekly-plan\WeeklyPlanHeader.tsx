'use client';

interface WeeklyPlanHeaderProps {
  startDate: string;
  endDate: string;
}

export function WeeklyPlanHeader({ startDate, endDate }: WeeklyPlanHeaderProps) {
  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <div className="flex justify-between items-center">
      <h2 className="text-xl font-semibold">
        Weekly Plan: {formatDate(startDate)} - {formatDate(endDate)}
      </h2>
    </div>
  );
}
