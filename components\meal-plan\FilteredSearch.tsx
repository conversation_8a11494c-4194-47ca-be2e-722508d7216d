"use client";

import { useState } from 'react';
import { Search, X, Filter, Clock, Flame, DollarSign } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Slider,
  SliderTrack,
  SliderRange,
  SliderThumb,
} from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';

export interface SearchFilters {
  query: string;
  dietaryPreferences: string[];
  cookingTime: [number, number]; // [min, max] in minutes
  calories: [number, number]; // [min, max]
  costRange: [number, number]; // [min, max] in dollars
}

interface FilteredSearchProps {
  onSearch: (filters: SearchFilters) => void;
  initialFilters?: Partial<SearchFilters>;
  showFilterButton?: boolean;
}

const DEFAULT_FILTERS: SearchFilters = {
  query: '',
  dietaryPreferences: [],
  cookingTime: [0, 120],
  calories: [0, 1500],
  costRange: [0, 50],
};

const DIETARY_PREFERENCES = [
  'Vegetarian',
  'Vegan',
  'Gluten-Free',
  'Dairy-Free',
  'Keto',
  'Low-Carb',
  'High-Protein',
  'Paleo',
];

export function FilteredSearch({
  onSearch,
  initialFilters = {},
  showFilterButton = true,
}: FilteredSearchProps) {
  const [filters, setFilters] = useState<SearchFilters>({
    ...DEFAULT_FILTERS,
    ...initialFilters,
  });
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Count active filters (excluding query)
  const activeFilterCount = [
    filters.dietaryPreferences.length > 0,
    filters.cookingTime[0] > DEFAULT_FILTERS.cookingTime[0] || filters.cookingTime[1] < DEFAULT_FILTERS.cookingTime[1],
    filters.calories[0] > DEFAULT_FILTERS.calories[0] || filters.calories[1] < DEFAULT_FILTERS.calories[1],
    filters.costRange[0] > DEFAULT_FILTERS.costRange[0] || filters.costRange[1] < DEFAULT_FILTERS.costRange[1],
  ].filter(Boolean).length;

  // Handle search query change
  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFilters = { ...filters, query: e.target.value };
    setFilters(newFilters);
    onSearch(newFilters);
  };

  // Handle dietary preference toggle
  const handleDietaryPreferenceToggle = (preference: string) => {
    const newPreferences = filters.dietaryPreferences.includes(preference)
      ? filters.dietaryPreferences.filter(p => p !== preference)
      : [...filters.dietaryPreferences, preference];
    
    const newFilters = { ...filters, dietaryPreferences: newPreferences };
    setFilters(newFilters);
    onSearch(newFilters);
  };

  // Handle cooking time change
  const handleCookingTimeChange = (value: [number, number]) => {
    const newFilters = { ...filters, cookingTime: value };
    setFilters(newFilters);
    onSearch(newFilters);
  };

  // Handle calories change
  const handleCaloriesChange = (value: [number, number]) => {
    const newFilters = { ...filters, calories: value };
    setFilters(newFilters);
    onSearch(newFilters);
  };

  // Handle cost range change
  const handleCostRangeChange = (value: [number, number]) => {
    const newFilters = { ...filters, costRange: value };
    setFilters(newFilters);
    onSearch(newFilters);
  };

  // Reset all filters
  const handleResetFilters = () => {
    setFilters(DEFAULT_FILTERS);
    onSearch(DEFAULT_FILTERS);
    setIsFilterOpen(false);
  };

  // Clear search query
  const handleClearQuery = () => {
    const newFilters = { ...filters, query: '' };
    setFilters(newFilters);
    onSearch(newFilters);
  };

  return (
    <div className="space-y-2">
      <div className="relative flex items-center">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search meals or ingredients..."
          className="pl-8 pr-10"
          value={filters.query}
          onChange={handleQueryChange}
        />
        {filters.query && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-1 top-1 h-7 w-7"
            onClick={handleClearQuery}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {showFilterButton && (
        <div className="flex justify-between items-center">
          <div className="flex flex-wrap gap-1">
            {filters.dietaryPreferences.map(pref => (
              <Badge key={pref} variant="secondary" className="text-xs">
                {pref}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 ml-1 p-0"
                  onClick={() => handleDietaryPreferenceToggle(pref)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
            {filters.cookingTime[0] > DEFAULT_FILTERS.cookingTime[0] || 
             filters.cookingTime[1] < DEFAULT_FILTERS.cookingTime[1] ? (
              <Badge variant="secondary" className="text-xs">
                {filters.cookingTime[0]}-{filters.cookingTime[1]} min
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 ml-1 p-0"
                  onClick={() => handleCookingTimeChange(DEFAULT_FILTERS.cookingTime)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ) : null}
            {filters.calories[0] > DEFAULT_FILTERS.calories[0] || 
             filters.calories[1] < DEFAULT_FILTERS.calories[1] ? (
              <Badge variant="secondary" className="text-xs">
                {filters.calories[0]}-{filters.calories[1]} cal
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 ml-1 p-0"
                  onClick={() => handleCaloriesChange(DEFAULT_FILTERS.calories)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ) : null}
            {filters.costRange[0] > DEFAULT_FILTERS.costRange[0] || 
             filters.costRange[1] < DEFAULT_FILTERS.costRange[1] ? (
              <Badge variant="secondary" className="text-xs">
                ${filters.costRange[0]}-${filters.costRange[1]}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 ml-1 p-0"
                  onClick={() => handleCostRangeChange(DEFAULT_FILTERS.costRange)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ) : null}
          </div>

          <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-8">
                <Filter className="h-3.5 w-3.5 mr-2" />
                Filters
                {activeFilterCount > 0 && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 flex items-center justify-center">
                    {activeFilterCount}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-4" align="end">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Filters</h4>
                  <Button variant="ghost" size="sm" onClick={handleResetFilters}>
                    Reset
                  </Button>
                </div>

                <Separator />

                {/* Dietary Preferences */}
                <div className="space-y-2">
                  <h5 className="text-sm font-medium">Dietary Preferences</h5>
                  <div className="grid grid-cols-2 gap-2">
                    {DIETARY_PREFERENCES.map(preference => (
                      <div key={preference} className="flex items-center space-x-2">
                        <Checkbox
                          id={`pref-${preference}`}
                          checked={filters.dietaryPreferences.includes(preference)}
                          onCheckedChange={() => handleDietaryPreferenceToggle(preference)}
                        />
                        <Label htmlFor={`pref-${preference}`} className="text-sm">
                          {preference}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* Cooking Time */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h5 className="text-sm font-medium flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      Cooking Time
                    </h5>
                    <span className="text-xs text-muted-foreground">
                      {filters.cookingTime[0]} - {filters.cookingTime[1]} min
                    </span>
                  </div>
                  <Slider
                    defaultValue={filters.cookingTime}
                    min={0}
                    max={120}
                    step={5}
                    onValueChange={handleCookingTimeChange as any}
                    className="py-2"
                  />
                </div>

                <Separator />

                {/* Calories */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h5 className="text-sm font-medium flex items-center">
                      <Flame className="h-4 w-4 mr-1" />
                      Calories
                    </h5>
                    <span className="text-xs text-muted-foreground">
                      {filters.calories[0]} - {filters.calories[1]} cal
                    </span>
                  </div>
                  <Slider
                    defaultValue={filters.calories}
                    min={0}
                    max={1500}
                    step={50}
                    onValueChange={handleCaloriesChange as any}
                    className="py-2"
                  />
                </div>

                <Separator />

                {/* Cost Range */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h5 className="text-sm font-medium flex items-center">
                      <DollarSign className="h-4 w-4 mr-1" />
                      Cost Range
                    </h5>
                    <span className="text-xs text-muted-foreground">
                      ${filters.costRange[0]} - ${filters.costRange[1]}
                    </span>
                  </div>
                  <Slider
                    defaultValue={filters.costRange}
                    min={0}
                    max={50}
                    step={1}
                    onValueChange={handleCostRangeChange as any}
                    className="py-2"
                  />
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      )}
    </div>
  );
}
