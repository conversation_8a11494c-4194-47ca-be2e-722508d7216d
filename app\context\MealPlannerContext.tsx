'use client';

import { createContext, useContext, useCallback, useState, ReactNode } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

interface Meal {
  id: string;
  name: string;
  time: string;
  calories: number;
}

interface GroceryItem {
  id: string;
  name: string;
  checked: boolean;
}

interface GroceryCategory {
  id: string;
  name: string;
  items: GroceryItem[];
}

interface GroceryList {
  categories: GroceryCategory[];
  isLoading: boolean;
  error: Error | null;
}

interface MealPlannerContextType {
  todaysMeals: Meal[];
  isLoading: boolean;
  error: Error | null;
  groceryList: GroceryList;
  loadTodaysMeals: () => Promise<void>;
  loadGroceryList: () => Promise<void>;
}

const MealPlannerContext = createContext<MealPlannerContextType | undefined>(undefined);

export function MealPlannerProvider({ children }: { children: ReactNode }) {
  const [todaysMeals, setTodaysMeals] = useState<Meal[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [groceryList, setGroceryList] = useState<GroceryList>({
    categories: [],
    isLoading: false,
    error: null,
  });

  const supabase = createClientComponentClient();

  const loadTodaysMeals = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const today = new Date().toISOString().split('T')[0];
      const { data, error: supabaseError } = await supabase
        .from('meals')
        .select('*')
        .eq('date', today)
        .order('time');

      if (supabaseError) throw supabaseError;

      setTodaysMeals(data || []);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to load meals'));
    } finally {
      setIsLoading(false);
    }
  }, [supabase]);

  const loadGroceryList = useCallback(async () => {
    setGroceryList(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      const { data, error: supabaseError } = await supabase
        .from('shopping_lists')
        .select('*')
        .order('category');

      if (supabaseError) throw supabaseError;

      // Transform the data into categories
      const categorizedData = data?.reduce((acc: GroceryCategory[], item) => {
        // Implementation of categorization logic
        return acc;
      }, []) || [];

      setGroceryList(prev => ({
        ...prev,
        categories: categorizedData,
        isLoading: false,
      }));
    } catch (err) {
      setGroceryList(prev => ({
        ...prev,
        error: err instanceof Error ? err : new Error('Failed to load grocery list'),
        isLoading: false,
      }));
    }
  }, [supabase]);

  return (
    <MealPlannerContext.Provider
      value={{
        todaysMeals,
        isLoading,
        error,
        groceryList,
        loadTodaysMeals,
        loadGroceryList,
      }}
    >
      {children}
    </MealPlannerContext.Provider>
  );
}

export function useMealPlanner() {
  const context = useContext(MealPlannerContext);
  if (context === undefined) {
    throw new Error('useMealPlanner must be used within a MealPlannerProvider');
  }
  return context;
}