#!/bin/bash

# This script runs the diagnostic SQL using the Supabase CLI
# It requires the Supabase CLI to be installed and configured

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "Error: Supabase CLI is not installed."
    echo "Please install it by following the instructions at https://supabase.com/docs/guides/cli"
    exit 1
fi

# Create the results directory if it doesn't exist
mkdir -p "$(dirname "$0")/results"

# Get the current timestamp
TIMESTAMP=$(date +"%Y-%m-%d_%H-%M-%S")

# Run the diagnostic SQL and save the results
echo "Running database diagnostic..."
supabase db execute --file "$(dirname "$0")/check_database.sql" > "$(dirname "$0")/results/diagnostic_results_${TIMESTAMP}.txt"

echo "Diagnostic complete. Results saved to $(dirname "$0")/results/diagnostic_results_${TIMESTAMP}.txt"
