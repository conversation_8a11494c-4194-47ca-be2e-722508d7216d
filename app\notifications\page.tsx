"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
// Import the new hook instead of the old one
import { useSupabase } from "@/components/supabase-provider";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { ChevronLeft, Bell, Calendar, ShoppingCart, Settings, Info } from "lucide-react";

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'meal_reminder' | 'system' | 'shopping_list' | 'plan_update';
  read: boolean;
  created_at: string;
}

export default function NotificationsPage() {
  const router = useRouter();
  // Use the new hook instead of the old one
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const [isLoading, setIsLoading] = useState(true);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [activeTab, setActiveTab] = useState("all");

  // Mock notifications data
  const mockNotifications: Notification[] = [
    {
      id: "1",
      title: "Meal Reminder",
      message: "Don't forget to prepare your lunch: Grilled Chicken Salad",
      type: "meal_reminder",
      read: false,
      created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString() // 30 minutes ago
    },
    {
      id: "2",
      title: "Shopping List Updated",
      message: "Your shopping list for this week has been updated with 3 new items",
      type: "shopping_list",
      read: true,
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString() // 2 hours ago
    },
    {
      id: "3",
      title: "New Meal Plan Available",
      message: "Your meal plan for next week is ready to view",
      type: "plan_update",
      read: false,
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString() // 1 day ago
    },
    {
      id: "4",
      title: "System Update",
      message: "We've added new features to help you track your nutrition goals",
      type: "system",
      read: true,
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString() // 3 days ago
    }
  ];

  useEffect(() => {
    // Don't fetch if Supabase is still loading or not available
    if (isSupabaseLoading || !supabase) {
      return;
    }

    const loadNotifications = async () => {
      try {
        setIsLoading(true);

        // In a real app, this would fetch from your database
        // For now, we'll use mock data
        setNotifications(mockNotifications);

      } catch (error) {
        console.error('Error fetching notifications:', error);
        toast.error('Failed to load notifications');
      } finally {
        setIsLoading(false);
      }
    };

    loadNotifications();
  }, [supabase, isSupabaseLoading]);

  const handleBack = () => {
    router.push('/dashboard');
  };

  const markAsRead = (id: string) => {
    setNotifications(notifications.map(notification =>
      notification.id === id ? { ...notification, read: true } : notification
    ));
    toast.success('Notification marked as read');
  };

  const markAllAsRead = () => {
    setNotifications(notifications.map(notification => ({ ...notification, read: true })));
    toast.success('All notifications marked as read');
  };

  const deleteNotification = (id: string) => {
    setNotifications(notifications.filter(notification => notification.id !== id));
    toast.success('Notification deleted');
  };

  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 'all') return true;
    if (activeTab === 'unread') return !notification.read;
    return notification.type === activeTab;
  });

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'meal_reminder':
        return <Calendar className="h-5 w-5 text-blue-500" />;
      case 'shopping_list':
        return <ShoppingCart className="h-5 w-5 text-green-500" />;
      case 'plan_update':
        return <Settings className="h-5 w-5 text-purple-500" />;
      case 'system':
        return <Info className="h-5 w-5 text-orange-500" />;
      default:
        return <Bell className="h-5 w-5 text-gray-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.round(diffMs / (1000 * 60));
    const diffHours = Math.round(diffMs / (1000 * 60 * 60));
    const diffDays = Math.round(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 60) {
      return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    } else {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    }
  };

  // Show loading state if either Supabase is loading or our component is loading
  if (isSupabaseLoading || isLoading) {
    return (
      <div className="container max-w-4xl py-8">
        <div className="flex items-center mb-6">
          <Button variant="ghost" onClick={handleBack} className="mr-2">
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">Notifications</h1>
          {isSupabaseLoading && (
            <p className="ml-4 text-muted-foreground">Initializing database connection...</p>
          )}
        </div>
        <div className="flex justify-center items-center min-h-[300px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          <p className="ml-4 text-muted-foreground">
            {isSupabaseLoading ? 'Initializing database connection...' : 'Loading notifications...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl py-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="ghost" onClick={handleBack} className="mr-2">
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">Notifications</h1>
        </div>
        {notifications.some(n => !n.read) && (
          <Button variant="outline" onClick={markAllAsRead}>
            Mark all as read
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-[1fr_3fr] gap-6">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="email-notifications">Email Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications via email
                  </p>
                </div>
                <Switch id="email-notifications" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="push-notifications">Push Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications in-app
                  </p>
                </div>
                <Switch id="push-notifications" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="meal-reminders">Meal Reminders</Label>
                  <p className="text-sm text-muted-foreground">
                    Get reminded about upcoming meals
                  </p>
                </div>
                <Switch id="meal-reminders" defaultChecked />
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="unread">Unread</TabsTrigger>
              <TabsTrigger value="meal_reminder">Meal Reminders</TabsTrigger>
              <TabsTrigger value="shopping_list">Shopping Lists</TabsTrigger>
              <TabsTrigger value="plan_update">Plan Updates</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab}>
              {filteredNotifications.length === 0 ? (
                <Card>
                  <CardContent className="py-8 text-center">
                    <Bell className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">No notifications to display</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {filteredNotifications.map((notification) => (
                    <Card key={notification.id} className={notification.read ? "opacity-70" : ""}>
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <div className="flex items-center">
                            {getNotificationIcon(notification.type)}
                            <CardTitle className="text-lg ml-2">{notification.title}</CardTitle>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {formatDate(notification.created_at)}
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pb-2">
                        <p>{notification.message}</p>
                      </CardContent>
                      <CardFooter className="flex justify-end space-x-2 pt-0">
                        {!notification.read && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => markAsRead(notification.id)}
                          >
                            Mark as read
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteNotification(notification.id)}
                        >
                          Delete
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
