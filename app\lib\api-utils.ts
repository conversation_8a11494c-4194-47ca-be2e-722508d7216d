import { NextResponse } from 'next/server';
import { ApiResponse } from '@/app/types/api';

export function createApiResponse<T>(
  data: T | null,
  error?: string,
  status: number = 200
): NextResponse<ApiResponse<T>> {
  if (error) {
    return NextResponse.json({ error }, { status });
  }
  return NextResponse.json({ data }, { status });
}

export function handleApiError(error: unknown, message: string): NextResponse {
  console.error(`${message}:`, error);
  return NextResponse.json(
    { error: message },
    { status: 500 }
  );
}