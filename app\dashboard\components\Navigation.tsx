'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import {
  Settings,
  ShoppingCart,
  Calendar,
  Package
} from "lucide-react";

export function Navigation() {
  return (
    <nav className="flex items-center gap-2">
      <Button variant="ghost" size="icon" asChild>
        <Link href="/meal-plan">
          <Calendar className="h-5 w-5" />
        </Link>
      </Button>
      <Button variant="ghost" size="icon" asChild>
        <Link href="/shopping-list">
          <ShoppingCart className="h-5 w-5" />
        </Link>
      </Button>
      <Button variant="ghost" size="icon" asChild>
        <Link href="/pantry">
          <Package className="h-5 w-5" />
        </Link>
      </Button>
      <Button variant="ghost" size="icon" asChild>
        <Link href="/settings">
          <Settings className="h-5 w-5" />
        </Link>
      </Button>
    </nav>
  );
}



