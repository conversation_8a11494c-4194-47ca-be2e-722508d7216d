export interface PantryItem {
  id: string;
  user_id: string;
  name: string;
  category: string;
  quantity: string;
  unit: string;
  expiry_date?: string;
  created_at: string;
  updated_at: string;
}

export interface PantryCategory {
  name: string;
  items: PantryItem[];
}

export interface NewPantryItem {
  name: string;
  category: string;
  quantity: string;
  unit: string;
  expiry_date?: string;
}
