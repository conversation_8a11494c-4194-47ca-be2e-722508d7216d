import { NextResponse } from "next/server";
import { mealPlanService } from '@/app/services/database-server';
import { createServerClient } from '@/app/services/supabase-server';

// We'll use the mealPlanService instead of a direct Supabase client

export async function POST(request: Request) {
  try {
    // Parse the request body
    const body = await request.json();

    // Log the request for debugging
    console.log('Disable RLS meal plan API called');

    // Extract the necessary data
    const {
      user_id,
      start_date,
      end_date,
      total_cost,
      meal_data
    } = body;

    // Validate the required fields
    if (!start_date || !end_date || !total_cost || !meal_data) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Use a fallback user ID if none is provided
    const effectiveUserId = user_id || '00000000-0000-0000-0000-000000000000';

    // First, ensure the user exists in the users table
    try {
      // Create a server-side Supabase client
      const supabase = createServerClient();

      // Check if the user exists
      const { data: existingUser, error: userCheckError } = await supabase
        .from('users')
        .select('id')
        .eq('id', effectiveUserId)
        .single();

      if (userCheckError || !existingUser) {
        console.log('User not found, creating user record first');

        // Create the user record
        const { error: createUserError } = await supabase
          .from('users')
          .insert({
            id: effectiveUserId,
            email: `user-${effectiveUserId.substring(0, 8)}@example.com`,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (createUserError) {
          console.error('Error creating user record:', createUserError);
          // Continue anyway, the meal plan creation might still work
        } else {
          console.log('User record created successfully');
        }
      } else {
        console.log('User record already exists');
      }
    } catch (userError) {
      console.error('Error checking/creating user:', userError);
      // Continue anyway, the meal plan creation might still work
    }

    // Insert the meal plan using the meal plan service
    const { data, error } = await mealPlanService.createMealPlan({
      userId: effectiveUserId,
      startDate: start_date,
      endDate: end_date,
      totalCost: total_cost,
      mealData: meal_data,
      status: 'active'
    });

    if (error) {
      console.error('Error inserting meal plan with RLS disabled:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Meal plan saved successfully with RLS disabled",
      data
    });
  } catch (e) {
    console.error('Unexpected error in disable RLS meal plan API:', e);
    return NextResponse.json(
      {
        error: "Failed to save meal plan",
        details: e instanceof Error ? e.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
