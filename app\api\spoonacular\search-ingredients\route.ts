import { NextRequest, NextResponse } from 'next/server';
import { searchIngredients } from '@/lib/api/spoonacular';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query');
    
    if (!query) {
      return NextResponse.json(
        { error: 'Missing required parameter: query' },
        { status: 400 }
      );
    }
    
    const results = await searchIngredients(query);

    return NextResponse.json(results);
  } catch (error: any) {
    console.error('Error in Spoonacular search ingredients API route:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while searching ingredients' },
      { status: 500 }
    );
  }
}
