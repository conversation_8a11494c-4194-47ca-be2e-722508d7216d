import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { MealPlanAssignment, AssignMealPlanRequest } from '@/types/database-extended';

export function useMealPlanAssignments(startDate?: string, endDate?: string) {
  const queryClient = useQueryClient();

  const {
    data: assignments,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['meal-plan-assignments', startDate, endDate],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (startDate) params.append('start_date', startDate);
      if (endDate) params.append('end_date', endDate);
      
      const url = `/api/meal-plan-assignments${params.toString() ? `?${params.toString()}` : ''}`;
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Failed to fetch meal plan assignments');
      }
      const result = await response.json();
      return result.data as MealPlanAssignment[];
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  const assignMealPlanMutation = useMutation({
    mutationFn: async ({ meal_plan_id, assigned_date }: AssignMealPlanRequest) => {
      const response = await fetch('/api/meal-plan-assignments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          meal_plan_id,
          assigned_date,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to assign meal plan');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['meal-plan-assignments'] });
    },
  });

  const unassignMealPlanMutation = useMutation({
    mutationFn: async (assignmentId: string) => {
      const response = await fetch(`/api/meal-plan-assignments/${assignmentId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to unassign meal plan');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['meal-plan-assignments'] });
    },
  });

  const assignMealPlan = (mealPlanId: string, assignedDate: string) => {
    return assignMealPlanMutation.mutateAsync({ 
      meal_plan_id: mealPlanId, 
      assigned_date: assignedDate 
    });
  };

  const unassignMealPlan = (assignmentId: string) => {
    return unassignMealPlanMutation.mutateAsync(assignmentId);
  };

  const getAssignmentForDate = (date: string) => {
    return assignments?.find(assignment => assignment.assigned_date === date);
  };

  const isDateAssigned = (date: string) => {
    return !!getAssignmentForDate(date);
  };

  const getMealPlanForDate = (date: string) => {
    const assignment = getAssignmentForDate(date);
    return assignment?.meal_plans || null;
  };

  return {
    assignments: assignments || [],
    isLoading,
    error,
    refetch,
    assignMealPlan,
    unassignMealPlan,
    getAssignmentForDate,
    isDateAssigned,
    getMealPlanForDate,
    isAssigning: assignMealPlanMutation.isPending,
    isUnassigning: unassignMealPlanMutation.isPending,
  };
}

export function useMealPlanAssignment(assignmentId: string) {
  const queryClient = useQueryClient();

  const {
    data: assignment,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['meal-plan-assignment', assignmentId],
    queryFn: async () => {
      const response = await fetch(`/api/meal-plan-assignments/${assignmentId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch meal plan assignment');
      }
      const result = await response.json();
      return result.data as MealPlanAssignment;
    },
    enabled: !!assignmentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const unassignMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch(`/api/meal-plan-assignments/${assignmentId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to unassign meal plan');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['meal-plan-assignment', assignmentId] });
      queryClient.invalidateQueries({ queryKey: ['meal-plan-assignments'] });
    },
  });

  const unassign = () => {
    return unassignMutation.mutateAsync();
  };

  return {
    assignment,
    isLoading,
    error,
    refetch,
    unassign,
    isUnassigning: unassignMutation.isPending,
  };
}
