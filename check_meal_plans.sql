-- Check meal_plans table structure
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'meal_plans'
ORDER BY ordinal_position;

-- Check RLS policies for meal_plans
SELECT tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies
WHERE schemaname = 'public'
AND tablename = 'meal_plans';

-- Check if meal_plan_assignments table exists
SELECT EXISTS (
  SELECT FROM information_schema.tables
  WHERE table_schema = 'public'
  AND table_name = 'meal_plan_assignments'
);

-- Check sample data from meal_plans
SELECT id, user_id, start_date, end_date, status
FROM meal_plans
LIMIT 5;

-- Check all columns in meal_plans
SELECT column_name
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'meal_plans'
ORDER BY ordinal_position;
