import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Cache for connection status to avoid repeated failed attempts
let connectionStatus: 'unknown' | 'connected' | 'failed' = 'unknown';
let lastConnectionCheck = 0;
const CONNECTION_CHECK_INTERVAL = 30000; // 30 seconds

export async function middleware(req: NextRequest) {
  const res = NextResponse.next();

  // Skip auth for static files and API routes that don't need auth
  if (
    req.nextUrl.pathname.startsWith('/_next') ||
    req.nextUrl.pathname.startsWith('/api/health') ||
    req.nextUrl.pathname.includes('.') // Static files
  ) {
    return res;
  }

  const now = Date.now();

  // If we recently failed to connect, skip Supabase calls temporarily
  if (connectionStatus === 'failed' && (now - lastConnectionCheck) < CONNECTION_CHECK_INTERVAL) {
    console.warn('Skipping Supabase middleware due to recent connection failure');
    res.headers.set('x-auth-status', 'connection-failed');
    return res;
  }

  try {
    const supabase = createMiddlewareClient({ req, res });

    // Set a shorter timeout for the session check
    const sessionPromise = supabase.auth.getSession();
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Session check timeout')), 5000)
    );

    const { data: { session }, error } = await Promise.race([
      sessionPromise,
      timeoutPromise
    ]) as any;

    if (error) {
      console.warn('Supabase middleware session error:', error.message);

      // Check if it's a connection error
      if (error.message?.includes('fetch failed') || error.message?.includes('timeout')) {
        connectionStatus = 'failed';
        lastConnectionCheck = now;
        res.headers.set('x-auth-status', 'connection-error');
      } else {
        res.headers.set('x-auth-status', 'auth-error');
      }
    } else {
      connectionStatus = 'connected';
      lastConnectionCheck = now;
      res.headers.set('x-auth-status', 'connected');

      // Add session info to response headers for debugging
      if (session) {
        res.headers.set('x-user-id', session.user.id);
        res.headers.set('x-session-status', 'active');
      } else {
        res.headers.set('x-session-status', 'none');
      }
    }
  } catch (error: any) {
    console.warn('Supabase middleware error:', error.message);
    connectionStatus = 'failed';
    lastConnectionCheck = now;
    res.headers.set('x-auth-status', 'middleware-error');

    // Don't block the request, just log the error
  }

  return res;
}

