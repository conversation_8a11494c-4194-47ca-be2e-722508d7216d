-- Enable RLS
ALTER TABLE meal_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE meals ENABLE ROW LEVEL SECURITY;
ALTER TABLE recipes ENABLE ROW LEVEL SECURITY;

-- Policies for meal_plans
CREATE POLICY "Users can view their own meal plans"
ON meal_plans FOR SELECT
TO authenticated
USING (user_id = auth.uid()::text);

CREATE POLICY "Users can insert their own meal plans"
ON meal_plans FOR INSERT
TO authenticated
WITH CHECK (user_id = auth.uid()::text);

CREATE POLICY "Users can update their own meal plans"
ON meal_plans FOR UPDATE
TO authenticated
USING (user_id = auth.uid()::text);

-- Policies for meals
CREATE POLICY "Users can view meals from their meal plans"
ON meals FOR SELECT
TO authenticated
USING (
  meal_plan_id IN (
    SELECT id FROM meal_plans WHERE user_id = auth.uid()::text
  )
);

CREATE POLICY "Users can insert meals to their meal plans"
ON meals FOR INSERT
TO authenticated
WITH CHECK (
  meal_plan_id IN (
    SELECT id FROM meal_plans WHERE user_id = auth.uid()::text
  )
);

-- Policies for recipes
CREATE POLICY "Users can view their own recipes"
ON recipes FOR SELECT
TO authenticated
USING (user_id = auth.uid()::text);

CREATE POLICY "Users can insert their own recipes"
ON recipes FOR INSERT
TO authenticated
WITH CHECK (user_id = auth.uid()::text);
