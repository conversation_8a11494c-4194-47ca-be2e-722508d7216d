import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export async function POST() {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // Update all but the most recent active meal plan to inactive
    const { error } = await supabase.rpc('cleanup_active_meal_plans');

    if (error) {
      throw error;
    }

    return NextResponse.json({ message: 'Cleanup successful' });
  } catch (error) {
    console.error('Cleanup error:', error);
    return NextResponse.json(
      { error: 'Failed to cleanup meal plans' },
      { status: 500 }
    );
  }
}