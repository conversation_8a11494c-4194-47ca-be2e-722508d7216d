import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const changePasswordSchema = z.object({
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Check if user is authenticated
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { 
          error: 'Unauthorized',
          details: 'You must be logged in to change your password' 
        },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { password } = changePasswordSchema.parse(body);

    // Update password
    const { error } = await supabase.auth.updateUser({
      password: password
    });

    if (error) {
      console.error('Password change error:', error);
      return NextResponse.json(
        { 
          error: 'Failed to change password',
          details: error.message 
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      message: 'Password changed successfully',
      success: true
    });

  } catch (error) {
    console.error('Password change request error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid input',
          details: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: 'An unexpected error occurred' 
      },
      { status: 500 }
    );
  }
}
