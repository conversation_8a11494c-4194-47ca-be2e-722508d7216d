'use client';

import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { DashboardStats, WeeklyMealStats, NutritionSummary } from '@/types/database-extended';

// Legacy types for compatibility
export interface UpcomingMeal {
  id: string;
  name: string;
  time: string;
  calories: number;
  image: string;
}

export interface NutritionData {
  name: string;
  value: number;
  color: string;
  recommended: number;
}

export interface MealStatus {
  meal: string;
  status: 'completed' | 'missed' | 'partial';
  time: string;
}

export interface WeeklyChartData {
  calories: {
    day: string;
    breakfast: number;
    lunch: number;
    dinner: number;
    snacks: number;
    target: number;
  }[];
  macros: {
    day: string;
    protein: number;
    carbs: number;
    fats: number;
  }[];
}

// Enhanced Dashboard service using new APIs
export const dashboardService = {
  // Fetch dashboard statistics
  getDashboardStats: async (): Promise<DashboardStats> => {
    try {
      const response = await fetch('/api/dashboard/stats');
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard stats');
      }
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      // Return fallback stats
      return {
        total_meal_plans: 0,
        active_meal_plans: 0,
        completed_meals: 0,
        total_meals: 0,
        completion_rate: 0,
        weekly_budget_used: 0,
        weekly_budget_total: 100,
        favorite_recipes_count: 0,
      };
    }
  },

  // Fetch upcoming meals
  getUpcomingMeals: async (): Promise<UpcomingMeal[]> => {
    try {
      const response = await fetch('/api/dashboard/upcoming-meals');
      if (!response.ok) {
        throw new Error('Failed to fetch upcoming meals');
      }
      const result = await response.json();
      return result.data || [];
    } catch (error) {
      console.error('Error fetching upcoming meals:', error);
      // Return fallback data
      return [
        {
          id: '1',
          name: 'No upcoming meals',
          time: 'Create a meal plan',
          calories: 0,
          image: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c'
        }
      ];
    }
  },

  // Fetch nutrition summary
  getNutritionSummary: async (): Promise<NutritionSummary> => {
    try {
      const response = await fetch('/api/dashboard/nutrition-summary');
      if (!response.ok) {
        throw new Error('Failed to fetch nutrition summary');
      }
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Error fetching nutrition summary:', error);
      // Return fallback data
      return {
        total_calories: 0,
        total_protein: 0,
        total_carbs: 0,
        total_fat: 0,
        avg_calories_per_meal: 0,
        avg_protein_per_meal: 0,
        avg_carbs_per_meal: 0,
        avg_fat_per_meal: 0,
      };
    }
  },

  },

  // Fetch nutrition data (legacy compatibility)
  getNutritionData: async (): Promise<NutritionData[]> => {
    try {
      const nutritionSummary = await dashboardService.getNutritionSummary();

      return [
        {
          name: 'Protein',
          value: Math.round(nutritionSummary.total_protein),
          color: 'hsl(var(--chart-1))',
          recommended: 150
        },
        {
          name: 'Carbs',
          value: Math.round(nutritionSummary.total_carbs),
          color: 'hsl(var(--chart-2))',
          recommended: 300
        },
        {
          name: 'Fats',
          value: Math.round(nutritionSummary.total_fat),
          color: 'hsl(var(--chart-3))',
          recommended: 70
        }
      ];
    } catch (error) {
      console.error('Error fetching nutrition data:', error);
      toast.error('Failed to load nutrition data');

      // Return fallback data
      return [
        { name: 'Protein', value: 120, color: 'hsl(var(--chart-1))', recommended: 150 },
        { name: 'Carbs', value: 240, color: 'hsl(var(--chart-2))', recommended: 300 },
        { name: 'Fats', value: 65, color: 'hsl(var(--chart-3))', recommended: 70 }
      ];
    }
  },

  // Fetch meal status data (legacy compatibility)
  getMealStatus: async (): Promise<MealStatus[]> => {
    try {
      const response = await fetch('/api/dashboard/meal-status');
      if (!response.ok) {
        throw new Error('Failed to fetch meal status');
      }
      const result = await response.json();
      return result.data || [];
    } catch (error) {
      console.error('Error fetching meal status:', error);
      // Return fallback data
      return [
        { meal: 'Breakfast', status: 'completed', time: '8:00 AM' },
        { meal: 'Lunch', status: 'partial', time: '1:00 PM' },
        { meal: 'Dinner', status: 'missed', time: '7:00 PM' }
      ];
    }
  },
  },

  // Fetch weekly chart data (legacy compatibility)
  getWeeklyChartData: async (): Promise<WeeklyChartData> => {
    try {
      const response = await fetch('/api/dashboard/weekly-chart-data');
      if (!response.ok) {
        throw new Error('Failed to fetch weekly chart data');
      }
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Error fetching weekly chart data:', error);
      // Return fallback data
      return {
        calories: [
          { day: 'Mon', breakfast: 400, lunch: 600, dinner: 500, snacks: 300, target: 2000 },
          { day: 'Tue', breakfast: 350, lunch: 650, dinner: 550, snacks: 250, target: 2000 },
          { day: 'Wed', breakfast: 450, lunch: 550, dinner: 500, snacks: 350, target: 2000 },
          { day: 'Thu', breakfast: 400, lunch: 600, dinner: 450, snacks: 300, target: 2000 },
          { day: 'Fri', breakfast: 500, lunch: 500, dinner: 600, snacks: 200, target: 2000 },
          { day: 'Sat', breakfast: 400, lunch: 700, dinner: 500, snacks: 400, target: 2000 },
          { day: 'Sun', breakfast: 350, lunch: 650, dinner: 550, snacks: 300, target: 2000 },
        ],
        macros: [
          { day: 'Mon', protein: 120, carbs: 240, fats: 65 },
          { day: 'Tue', protein: 115, carbs: 220, fats: 70 },
          { day: 'Wed', protein: 125, carbs: 250, fats: 60 },
          { day: 'Thu', protein: 118, carbs: 235, fats: 68 },
          { day: 'Fri', protein: 122, carbs: 245, fats: 63 },
          { day: 'Sat', protein: 130, carbs: 260, fats: 70 },
          { day: 'Sun', protein: 116, carbs: 230, fats: 65 },
        ]
      };
    }
  }
};
};

export default dashboardService;
