-- Migration: Standardize meals table
-- This migration ensures the meals table has a consistent structure
-- and proper RLS policies

-- First, record this migration
INSERT INTO public.schema_migrations (version, description)
VALUES ('20230804000001', 'Standardize meals table')
ON CONFLICT (version) DO NOTHING;

-- Ensure the meals table exists with the correct structure
CREATE TABLE IF NOT EXISTS public.meals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    meal_plan_id UUID NOT NULL,
    recipe_id UUID NOT NULL,
    day TEXT NOT NULL,
    meal_type TEXT NOT NULL,
    servings INTEGER NOT NULL DEFAULT 1,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add foreign key constraints if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'meals_meal_plan_id_fkey' 
        AND conrelid = 'public.meals'::regclass
    ) THEN
        ALTER TABLE public.meals
        ADD CONSTRAINT meals_meal_plan_id_fkey
        FOREIGN KEY (meal_plan_id) REFERENCES public.meal_plans(id) ON DELETE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'meals_recipe_id_fkey' 
        AND conrelid = 'public.meals'::regclass
    ) THEN
        ALTER TABLE public.meals
        ADD CONSTRAINT meals_recipe_id_fkey
        FOREIGN KEY (recipe_id) REFERENCES public.recipes(id) ON DELETE CASCADE;
    END IF;
END
$$;

-- Add comment to the table
COMMENT ON TABLE public.meals IS 'Stores individual meals within meal plans';

-- Enable Row Level Security
ALTER TABLE public.meals ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view meals from their meal plans" ON public.meals;
DROP POLICY IF EXISTS "Users can insert meals to their meal plans" ON public.meals;
DROP POLICY IF EXISTS "Users can view meals in their meal plans" ON public.meals;
DROP POLICY IF EXISTS "Users can create meals in their meal plans" ON public.meals;
DROP POLICY IF EXISTS "Users can update their own meals" ON public.meals;
DROP POLICY IF EXISTS "Users can delete their own meals" ON public.meals;

-- Create standardized policies
CREATE POLICY "Users can view their own meals"
ON public.meals
FOR SELECT
USING (meal_plan_id IN (
  SELECT id FROM public.meal_plans WHERE user_id = auth.uid()
));

CREATE POLICY "Users can insert their own meals"
ON public.meals
FOR INSERT
WITH CHECK (meal_plan_id IN (
  SELECT id FROM public.meal_plans WHERE user_id = auth.uid()
));

CREATE POLICY "Users can update their own meals"
ON public.meals
FOR UPDATE
USING (meal_plan_id IN (
  SELECT id FROM public.meal_plans WHERE user_id = auth.uid()
))
WITH CHECK (meal_plan_id IN (
  SELECT id FROM public.meal_plans WHERE user_id = auth.uid()
));

CREATE POLICY "Users can delete their own meals"
ON public.meals
FOR DELETE
USING (meal_plan_id IN (
  SELECT id FROM public.meal_plans WHERE user_id = auth.uid()
));

-- Grant permissions to authenticated users
GRANT ALL ON public.meals TO authenticated;
