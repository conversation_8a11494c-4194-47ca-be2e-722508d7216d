-- Create the function to insert a meal plan with <PERSON><PERSON> disabled
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION public.insert_meal_plan_bypass_rls(
    p_user_id UUID,
    p_start_date TIMESTAMP WITH TIME ZONE,
    p_end_date TIMESTAMP WITH TIME ZONE,
    p_total_cost DOUBLE PRECISION,
    p_meal_data JSONB,
    p_status TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_id UUID;
BEGIN
    -- Insert the meal plan
    INSERT INTO public.meal_plans (
        user_id,
        start_date,
        end_date,
        total_cost,
        meal_data,
        status,
        created_at,
        updated_at
    )
    VALUES (
        p_user_id,
        p_start_date,
        p_end_date,
        p_total_cost,
        p_meal_data,
        p_status,
        NOW(),
        NOW()
    )
    RETURNING id INTO v_id;
    
    RETURN v_id;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.insert_meal_plan_bypass_rls TO authenticated;
