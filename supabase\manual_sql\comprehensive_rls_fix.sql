-- Comprehensive RLS policy standardization script
-- This script checks column existence and data types before creating policies

-- Function to check if a column exists in a table
CREATE OR REPLACE FUNCTION column_exists(p_table_name TEXT, p_column_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    v_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = p_table_name
        AND column_name = p_column_name
    ) INTO v_exists;

    RETURN v_exists;
END;
$$ LANGUAGE plpgsql;

-- Function to get the data type of a column
CREATE OR REPLACE FUNCTION get_column_type(p_table_name TEXT, p_column_name TEXT)
RETURNS TEXT AS $$
DECLARE
    v_type TEXT;
BEGIN
    SELECT data_type INTO v_type
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = p_table_name
    AND column_name = p_column_name;

    RETURN v_type;
END;
$$ LANGUAGE plpgsql;

-- 1. meal_plans table
DO $$
BEGIN
    RAISE NOTICE 'Processing meal_plans table...';

    -- Enable RLS
    ALTER TABLE public.meal_plans ENABLE ROW LEVEL SECURITY;

    -- Drop existing policies
    DROP POLICY IF EXISTS "Users can view their own meal_plans" ON public.meal_plans;
    DROP POLICY IF EXISTS "Users can insert their own meal_plans" ON public.meal_plans;
    DROP POLICY IF EXISTS "Users can update their own meal_plans" ON public.meal_plans;
    DROP POLICY IF EXISTS "Users can delete their own meal_plans" ON public.meal_plans;

    -- Check if user_id column exists
    IF column_exists('meal_plans', 'user_id') THEN
        DECLARE
            column_type TEXT := get_column_type('meal_plans', 'user_id');
        BEGIN
            RAISE NOTICE 'meal_plans.user_id type: %', column_type;

            IF column_type = 'uuid' THEN
                -- For UUID type
                CREATE POLICY "Users can view their own meal_plans" ON public.meal_plans FOR SELECT USING (user_id = auth.uid());
                CREATE POLICY "Users can insert their own meal_plans" ON public.meal_plans FOR INSERT WITH CHECK (user_id = auth.uid());
                CREATE POLICY "Users can update their own meal_plans" ON public.meal_plans FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
                CREATE POLICY "Users can delete their own meal_plans" ON public.meal_plans FOR DELETE USING (user_id = auth.uid());
            ELSE
                -- For text type
                CREATE POLICY "Users can view their own meal_plans" ON public.meal_plans FOR SELECT USING (user_id = auth.uid()::text);
                CREATE POLICY "Users can insert their own meal_plans" ON public.meal_plans FOR INSERT WITH CHECK (user_id = auth.uid()::text);
                CREATE POLICY "Users can update their own meal_plans" ON public.meal_plans FOR UPDATE USING (user_id = auth.uid()::text) WITH CHECK (user_id = auth.uid()::text);
                CREATE POLICY "Users can delete their own meal_plans" ON public.meal_plans FOR DELETE USING (user_id = auth.uid()::text);
            END IF;

            GRANT ALL ON public.meal_plans TO authenticated;
            RAISE NOTICE 'meal_plans policies created successfully';
        END;
    ELSE
        RAISE EXCEPTION 'user_id column not found in meal_plans table';
    END IF;
END
$$;

-- 2. meals table
DO $$
BEGIN
    RAISE NOTICE 'Processing meals table...';

    -- Enable RLS
    ALTER TABLE public.meals ENABLE ROW LEVEL SECURITY;

    -- Drop existing policies
    DROP POLICY IF EXISTS "Users can view their own meals" ON public.meals;
    DROP POLICY IF EXISTS "Users can insert their own meals" ON public.meals;
    DROP POLICY IF EXISTS "Users can update their own meals" ON public.meals;
    DROP POLICY IF EXISTS "Users can delete their own meals" ON public.meals;
    DROP POLICY IF EXISTS "Users can view meals from their meal plans" ON public.meals;
    DROP POLICY IF EXISTS "Users can insert meals to their meal plans" ON public.meals;
    DROP POLICY IF EXISTS "Users can view meals in their meal plans" ON public.meals;
    DROP POLICY IF EXISTS "Users can create meals in their meal plans" ON public.meals;

    -- Check if meal_plan_id column exists
    IF column_exists('meals', 'meal_plan_id') THEN
        -- Check if meal_plans table has user_id column
        IF column_exists('meal_plans', 'user_id') THEN
            DECLARE
                column_type TEXT := get_column_type('meal_plans', 'user_id');
            BEGIN
                RAISE NOTICE 'meal_plans.user_id type: %', column_type;

                IF column_type = 'uuid' THEN
                    -- For UUID type
                    CREATE POLICY "Users can view their own meals" ON public.meals FOR SELECT
                    USING (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()));

                    CREATE POLICY "Users can insert their own meals" ON public.meals FOR INSERT
                    WITH CHECK (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()));

                    CREATE POLICY "Users can update their own meals" ON public.meals FOR UPDATE
                    USING (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()))
                    WITH CHECK (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()));

                    CREATE POLICY "Users can delete their own meals" ON public.meals FOR DELETE
                    USING (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()));
                ELSE
                    -- For text type
                    CREATE POLICY "Users can view their own meals" ON public.meals FOR SELECT
                    USING (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()::text));

                    CREATE POLICY "Users can insert their own meals" ON public.meals FOR INSERT
                    WITH CHECK (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()::text));

                    CREATE POLICY "Users can update their own meals" ON public.meals FOR UPDATE
                    USING (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()::text))
                    WITH CHECK (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()::text));

                    CREATE POLICY "Users can delete their own meals" ON public.meals FOR DELETE
                    USING (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()::text));
                END IF;

                GRANT ALL ON public.meals TO authenticated;
                RAISE NOTICE 'meals policies created successfully';
            END;
        ELSE
            RAISE EXCEPTION 'user_id column not found in meal_plans table';
        END IF;
    ELSE
        RAISE EXCEPTION 'meal_plan_id column not found in meals table';
    END IF;
END
$$;

-- 3. pantry_items table
DO $$
BEGIN
    RAISE NOTICE 'Processing pantry_items table...';

    -- Enable RLS
    ALTER TABLE public.pantry_items ENABLE ROW LEVEL SECURITY;

    -- Drop existing policies
    DROP POLICY IF EXISTS "Users can view their own pantry_items" ON public.pantry_items;
    DROP POLICY IF EXISTS "Users can insert their own pantry_items" ON public.pantry_items;
    DROP POLICY IF EXISTS "Users can update their own pantry_items" ON public.pantry_items;
    DROP POLICY IF EXISTS "Users can delete their own pantry_items" ON public.pantry_items;
    DROP POLICY IF EXISTS "pantry_items_select_policy" ON public.pantry_items;
    DROP POLICY IF EXISTS "pantry_items_insert_policy" ON public.pantry_items;
    DROP POLICY IF EXISTS "pantry_items_update_policy" ON public.pantry_items;
    DROP POLICY IF EXISTS "pantry_items_delete_policy" ON public.pantry_items;

    -- Check if user_id column exists
    IF column_exists('pantry_items', 'user_id') THEN
        DECLARE
            column_type TEXT := get_column_type('pantry_items', 'user_id');
        BEGIN
            RAISE NOTICE 'pantry_items.user_id type: %', column_type;

            IF column_type = 'uuid' THEN
                -- For UUID type
                CREATE POLICY "Users can view their own pantry_items" ON public.pantry_items FOR SELECT USING (user_id = auth.uid());
                CREATE POLICY "Users can insert their own pantry_items" ON public.pantry_items FOR INSERT WITH CHECK (user_id = auth.uid());
                CREATE POLICY "Users can update their own pantry_items" ON public.pantry_items FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
                CREATE POLICY "Users can delete their own pantry_items" ON public.pantry_items FOR DELETE USING (user_id = auth.uid());
            ELSE
                -- For text type
                CREATE POLICY "Users can view their own pantry_items" ON public.pantry_items FOR SELECT USING (user_id = auth.uid()::text);
                CREATE POLICY "Users can insert their own pantry_items" ON public.pantry_items FOR INSERT WITH CHECK (user_id = auth.uid()::text);
                CREATE POLICY "Users can update their own pantry_items" ON public.pantry_items FOR UPDATE USING (user_id = auth.uid()::text) WITH CHECK (user_id = auth.uid()::text);
                CREATE POLICY "Users can delete their own pantry_items" ON public.pantry_items FOR DELETE USING (user_id = auth.uid()::text);
            END IF;

            GRANT ALL ON public.pantry_items TO authenticated;
            RAISE NOTICE 'pantry_items policies created successfully';
        END;
    ELSE
        RAISE EXCEPTION 'user_id column not found in pantry_items table';
    END IF;
END
$$;

-- 4. recipes table
DO $$
BEGIN
    RAISE NOTICE 'Processing recipes table...';

    -- Enable RLS
    ALTER TABLE public.recipes ENABLE ROW LEVEL SECURITY;

    -- Drop existing policies
    DROP POLICY IF EXISTS "Users can view their own recipes" ON public.recipes;
    DROP POLICY IF EXISTS "Users can insert their own recipes" ON public.recipes;
    DROP POLICY IF EXISTS "Users can update their own recipes" ON public.recipes;
    DROP POLICY IF EXISTS "Users can delete their own recipes" ON public.recipes;
    DROP POLICY IF EXISTS "Allow anonymous access for testing" ON public.recipes;
    DROP POLICY IF EXISTS "Enable read access for all users" ON public.recipes;
    DROP POLICY IF EXISTS "Enable delete for authenticated users on their own recipes" ON public.recipes;
    DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.recipes;
    DROP POLICY IF EXISTS "Enable update for authenticated users on their own recipes" ON public.recipes;
    DROP POLICY IF EXISTS "Users can create their own recipes" ON public.recipes;

    -- Check if user_id column exists
    IF column_exists('recipes', 'user_id') THEN
        DECLARE
            column_type TEXT := get_column_type('recipes', 'user_id');
        BEGIN
            RAISE NOTICE 'recipes.user_id type: %', column_type;

            IF column_type = 'uuid' THEN
                -- For UUID type
                CREATE POLICY "Users can view their own recipes" ON public.recipes FOR SELECT USING (user_id = auth.uid());
                CREATE POLICY "Users can insert their own recipes" ON public.recipes FOR INSERT WITH CHECK (user_id = auth.uid());
                CREATE POLICY "Users can update their own recipes" ON public.recipes FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
                CREATE POLICY "Users can delete their own recipes" ON public.recipes FOR DELETE USING (user_id = auth.uid());
            ELSE
                -- For text type
                CREATE POLICY "Users can view their own recipes" ON public.recipes FOR SELECT USING (user_id = auth.uid()::text);
                CREATE POLICY "Users can insert their own recipes" ON public.recipes FOR INSERT WITH CHECK (user_id = auth.uid()::text);
                CREATE POLICY "Users can update their own recipes" ON public.recipes FOR UPDATE USING (user_id = auth.uid()::text) WITH CHECK (user_id = auth.uid()::text);
                CREATE POLICY "Users can delete their own recipes" ON public.recipes FOR DELETE USING (user_id = auth.uid()::text);
            END IF;

            GRANT ALL ON public.recipes TO authenticated;
            RAISE NOTICE 'recipes policies created successfully';
        END;
    ELSE
        RAISE EXCEPTION 'user_id column not found in recipes table';
    END IF;
END
$$;

-- 5. shopping_lists table
DO $$
BEGIN
    RAISE NOTICE 'Processing shopping_lists table...';

    -- Enable RLS
    ALTER TABLE public.shopping_lists ENABLE ROW LEVEL SECURITY;

    -- Drop existing policies
    DROP POLICY IF EXISTS "Users can view their own shopping_lists" ON public.shopping_lists;
    DROP POLICY IF EXISTS "Users can insert their own shopping_lists" ON public.shopping_lists;
    DROP POLICY IF EXISTS "Users can update their own shopping_lists" ON public.shopping_lists;
    DROP POLICY IF EXISTS "Users can delete their own shopping_lists" ON public.shopping_lists;

    -- Check if user_id column exists
    IF column_exists('shopping_lists', 'user_id') THEN
        DECLARE
            column_type TEXT := get_column_type('shopping_lists', 'user_id');
        BEGIN
            RAISE NOTICE 'shopping_lists.user_id type: %', column_type;

            IF column_type = 'uuid' THEN
                -- For UUID type
                CREATE POLICY "Users can view their own shopping_lists" ON public.shopping_lists FOR SELECT USING (user_id = auth.uid());
                CREATE POLICY "Users can insert their own shopping_lists" ON public.shopping_lists FOR INSERT WITH CHECK (user_id = auth.uid());
                CREATE POLICY "Users can update their own shopping_lists" ON public.shopping_lists FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
                CREATE POLICY "Users can delete their own shopping_lists" ON public.shopping_lists FOR DELETE USING (user_id = auth.uid());
            ELSE
                -- For text type
                CREATE POLICY "Users can view their own shopping_lists" ON public.shopping_lists FOR SELECT USING (user_id = auth.uid()::text);
                CREATE POLICY "Users can insert their own shopping_lists" ON public.shopping_lists FOR INSERT WITH CHECK (user_id = auth.uid()::text);
                CREATE POLICY "Users can update their own shopping_lists" ON public.shopping_lists FOR UPDATE USING (user_id = auth.uid()::text) WITH CHECK (user_id = auth.uid()::text);
                CREATE POLICY "Users can delete their own shopping_lists" ON public.shopping_lists FOR DELETE USING (user_id = auth.uid()::text);
            END IF;

            GRANT ALL ON public.shopping_lists TO authenticated;
            RAISE NOTICE 'shopping_lists policies created successfully';
        END;
    ELSE
        RAISE EXCEPTION 'user_id column not found in shopping_lists table';
    END IF;
END
$$;

-- 6. shopping_items table
DO $$
BEGIN
    RAISE NOTICE 'Processing shopping_items table...';

    -- Enable RLS
    ALTER TABLE public.shopping_items ENABLE ROW LEVEL SECURITY;

    -- Drop existing policies
    DROP POLICY IF EXISTS "Users can view their own shopping_items" ON public.shopping_items;
    DROP POLICY IF EXISTS "Users can insert their own shopping_items" ON public.shopping_items;
    DROP POLICY IF EXISTS "Users can update their own shopping_items" ON public.shopping_items;
    DROP POLICY IF EXISTS "Users can delete their own shopping_items" ON public.shopping_items;

    -- We know from checking that shopping_items has a shopping_list_id column
    RAISE NOTICE 'Using shopping_list_id column for policies';

    -- Check if shopping_lists table has user_id column
    IF column_exists('shopping_lists', 'user_id') THEN
        DECLARE
            column_type TEXT := get_column_type('shopping_lists', 'user_id');
        BEGIN
            RAISE NOTICE 'shopping_lists.user_id type: %', column_type;

            IF column_type = 'uuid' THEN
                -- For UUID type
                CREATE POLICY "Users can view their own shopping_items" ON public.shopping_items FOR SELECT
                USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));

                CREATE POLICY "Users can insert their own shopping_items" ON public.shopping_items FOR INSERT
                WITH CHECK (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));

                CREATE POLICY "Users can update their own shopping_items" ON public.shopping_items FOR UPDATE
                USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()))
                WITH CHECK (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));

                CREATE POLICY "Users can delete their own shopping_items" ON public.shopping_items FOR DELETE
                USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));
            ELSE
                -- For text type
                CREATE POLICY "Users can view their own shopping_items" ON public.shopping_items FOR SELECT
                USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));

                CREATE POLICY "Users can insert their own shopping_items" ON public.shopping_items FOR INSERT
                WITH CHECK (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));

                CREATE POLICY "Users can update their own shopping_items" ON public.shopping_items FOR UPDATE
                USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text))
                WITH CHECK (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));

                CREATE POLICY "Users can delete their own shopping_items" ON public.shopping_items FOR DELETE
                USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));
            END IF;

            GRANT ALL ON public.shopping_items TO authenticated;
            RAISE NOTICE 'shopping_items policies created successfully';
        END;
    ELSE
        RAISE EXCEPTION 'user_id column not found in shopping_lists table';
    END IF;
END
$$;

-- 7. user_preferences table
DO $$
BEGIN
    RAISE NOTICE 'Processing user_preferences table...';

    -- Enable RLS
    ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

    -- Drop existing policies
    DROP POLICY IF EXISTS "Users can view their own user_preferences" ON public.user_preferences;
    DROP POLICY IF EXISTS "Users can insert their own user_preferences" ON public.user_preferences;
    DROP POLICY IF EXISTS "Users can update their own user_preferences" ON public.user_preferences;
    DROP POLICY IF EXISTS "Users can delete their own user_preferences" ON public.user_preferences;

    -- Check if user_id column exists
    IF column_exists('user_preferences', 'user_id') THEN
        DECLARE
            column_type TEXT := get_column_type('user_preferences', 'user_id');
        BEGIN
            RAISE NOTICE 'user_preferences.user_id type: %', column_type;

            IF column_type = 'uuid' THEN
                -- For UUID type
                CREATE POLICY "Users can view their own user_preferences" ON public.user_preferences FOR SELECT USING (user_id = auth.uid());
                CREATE POLICY "Users can insert their own user_preferences" ON public.user_preferences FOR INSERT WITH CHECK (user_id = auth.uid());
                CREATE POLICY "Users can update their own user_preferences" ON public.user_preferences FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
                CREATE POLICY "Users can delete their own user_preferences" ON public.user_preferences FOR DELETE USING (user_id = auth.uid());
            ELSE
                -- For text type
                CREATE POLICY "Users can view their own user_preferences" ON public.user_preferences FOR SELECT USING (user_id = auth.uid()::text);
                CREATE POLICY "Users can insert their own user_preferences" ON public.user_preferences FOR INSERT WITH CHECK (user_id = auth.uid()::text);
                CREATE POLICY "Users can update their own user_preferences" ON public.user_preferences FOR UPDATE USING (user_id = auth.uid()::text) WITH CHECK (user_id = auth.uid()::text);
                CREATE POLICY "Users can delete their own user_preferences" ON public.user_preferences FOR DELETE USING (user_id = auth.uid()::text);
            END IF;

            GRANT ALL ON public.user_preferences TO authenticated;
            RAISE NOTICE 'user_preferences policies created successfully';
        END;
    ELSE
        RAISE EXCEPTION 'user_id column not found in user_preferences table';
    END IF;
END
$$;

-- 8. users table
DO $$
BEGIN
    RAISE NOTICE 'Processing users table...';

    -- Enable RLS
    ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

    -- Drop existing policies
    DROP POLICY IF EXISTS "Users can view their own users" ON public.users;
    DROP POLICY IF EXISTS "Users can insert their own users" ON public.users;
    DROP POLICY IF EXISTS "Users can update their own users" ON public.users;
    DROP POLICY IF EXISTS "Users can delete their own users" ON public.users;
    DROP POLICY IF EXISTS "Users can view own record" ON public.users;
    DROP POLICY IF EXISTS "Users can update own record" ON public.users;
    DROP POLICY IF EXISTS "Allow insert during signup" ON public.users;

    -- Check if id column exists
    IF column_exists('users', 'id') THEN
        DECLARE
            column_type TEXT := get_column_type('users', 'id');
        BEGIN
            RAISE NOTICE 'users.id type: %', column_type;

            IF column_type = 'uuid' THEN
                -- For UUID type
                CREATE POLICY "Users can view their own users" ON public.users FOR SELECT USING (id = auth.uid());
                CREATE POLICY "Users can insert their own users" ON public.users FOR INSERT WITH CHECK (id = auth.uid());
                CREATE POLICY "Users can update their own users" ON public.users FOR UPDATE USING (id = auth.uid()) WITH CHECK (id = auth.uid());
                CREATE POLICY "Users can delete their own users" ON public.users FOR DELETE USING (id = auth.uid());
            ELSE
                -- For text type
                CREATE POLICY "Users can view their own users" ON public.users FOR SELECT USING (id = auth.uid()::text);
                CREATE POLICY "Users can insert their own users" ON public.users FOR INSERT WITH CHECK (id = auth.uid()::text);
                CREATE POLICY "Users can update their own users" ON public.users FOR UPDATE USING (id = auth.uid()::text) WITH CHECK (id = auth.uid()::text);
                CREATE POLICY "Users can delete their own users" ON public.users FOR DELETE USING (id = auth.uid()::text);
            END IF;

            GRANT ALL ON public.users TO authenticated;
            RAISE NOTICE 'users policies created successfully';
        END;
    ELSE
        RAISE EXCEPTION 'id column not found in users table';
    END IF;
END
$$;

-- 9. Clean up legacy tables (optional)
-- DROP TABLE IF EXISTS public.meal_plans_old;

-- 10. Disable RLS for system tables (optional)
ALTER TABLE public._prisma_migrations DISABLE ROW LEVEL SECURITY;

-- Drop the helper functions
DROP FUNCTION IF EXISTS column_exists;
DROP FUNCTION IF EXISTS get_column_type;
