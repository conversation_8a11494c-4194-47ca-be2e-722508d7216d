export interface MealCustomization {
  portionSize: number;
  servingSize: number;
  cookingTime: number;
  ingredients: IngredientSubstitution[];
}

export interface IngredientSubstitution {
  originalId: string;
  substitutionId: string | null;
  quantity: number;
  unit: string;
}

export interface Meal {
  id: string;
  type: string;
  name: string;
  image: string;
  calories: number;
  prepTime: number;
  rating?: number;
  isFavorite?: boolean;
  customization?: MealCustomization;
  originalServings: number;
}

export interface DayPlan {
  date: string;
  meals: Meal[];
}

export interface GroceryItem {
  id: string;
  name: string;
  checked: boolean;
  quantity: number;
  unit: string;
}

export interface GroceryCategory {
  id: string;
  name: string;
  items: GroceryItem[];
}

export interface GroceryListState {
  categories: GroceryCategory[];
  isLoading: boolean;
  error: string | null;
}

export interface MealPlannerState {
  todaysMeals: Meal[];
  weeklyPlan: DayPlan[];
  alternativeMeals: Meal[];
  isLoading: boolean;
  error: string | null;
  groceryList: GroceryListState;
}

export interface MealPlannerContextType extends MealPlannerState {
  loadTodaysMeals: () => Promise<void>;
  loadWeeklyPlan: () => Promise<void>;
  loadAlternativeMeals: (mealType: string, excludeMealId: string) => Promise<void>;
  swapMeal: (oldMealId: string, newMealId: string) => Promise<void>;
  toggleFavorite: (meal: Meal) => Promise<void>;
  loadGroceryList: () => Promise<void>;
  toggleGroceryItem: (categoryId: string, itemId: string, checked: boolean) => Promise<void>;
}