-- Add meal_data column to meal_plans if it doesn't exist
ALTER TABLE "public"."meal_plans" 
ADD COLUMN IF NOT EXISTS "meal_data" JSONB;

-- Add indexes to meal_plans
CREATE INDEX IF NOT EXISTS "meal_plans_status_idx" ON "public"."meal_plans"("status");
CREATE INDEX IF NOT EXISTS "meal_plans_user_id_idx" ON "public"."meal_plans"("user_id");

-- Add hashed_password to users if it doesn't exist
ALTER TABLE "public"."users" 
ADD COLUMN IF NOT EXISTS "hashed_password" TEXT;

-- Drop the ingredients table references since it doesn't exist
DROP TABLE IF EXISTS "public"."ingredients";