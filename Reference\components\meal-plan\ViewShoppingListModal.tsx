"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ShoppingCart, Package, Printer, Download, Search, Check, AlertTriangle } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { generateShoppingList } from "@/lib/shopping-list";
import { ShoppingItem, PantryItem } from "@/types/meal-plan";
import { toast } from "sonner";

interface ViewShoppingListModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ViewShoppingListModal({ isOpen, onClose }: ViewShoppingListModalProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [shoppingList, setShoppingList] = useState<ShoppingItem[]>([]);
  const [pantryItems, setPantryItems] = useState<PantryItem[]>([]);
  const [checkedItems, setCheckedItems] = useState<Record<string, boolean>>({});
  const [activeTab, setActiveTab] = useState("shopping");

  useEffect(() => {
    const fetchShoppingList = async () => {
      setIsLoading(true);
      try {
        const { shoppingList, pantryItems } = await generateShoppingList();
        setShoppingList(shoppingList);
        setPantryItems(pantryItems);
        
        // Initialize checked state
        const initialCheckedState: Record<string, boolean> = {};
        shoppingList.forEach(item => {
          initialCheckedState[item.id] = false;
        });
        setCheckedItems(initialCheckedState);
      } catch (error) {
        console.error("Failed to generate shopping list:", error);
        toast.error("Failed to generate shopping list");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      fetchShoppingList();
    }
  }, [isOpen]);

  const handleToggleItem = (id: string, checked: boolean) => {
    setCheckedItems(prev => ({ ...prev, [id]: checked }));
  };

  const handleAddToPantry = () => {
    const selectedItems = shoppingList.filter(item => checkedItems[item.id]);
    if (selectedItems.length === 0) {
      toast.warning("No items selected");
      return;
    }
    
    toast.success(`Added ${selectedItems.length} items to pantry`);
    // Reset checked items
    const newCheckedState: Record<string, boolean> = {};
    shoppingList.forEach(item => {
      newCheckedState[item.id] = false;
    });
    setCheckedItems(newCheckedState);
  };

  const handlePrintList = () => {
    toast.success("Shopping list sent to printer");
  };

  const handleExportList = () => {
    toast.success("Shopping list exported");
  };

  const filteredShoppingList = shoppingList.filter(item => 
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const categories = [...new Set(filteredShoppingList.map(item => item.category))];

  const totalCost = filteredShoppingList.reduce((sum, item) => sum + item.cost, 0);
  
  const isAllChecked = filteredShoppingList.length > 0 && 
    filteredShoppingList.every(item => checkedItems[item.id]);
  
  const isAnyChecked = filteredShoppingList.some(item => checkedItems[item.id]);

  const handleToggleAll = () => {
    const newCheckedState = { ...checkedItems };
    const newValue = !isAllChecked;
    
    filteredShoppingList.forEach(item => {
      newCheckedState[item.id] = newValue;
    });
    
    setCheckedItems(newCheckedState);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5 text-emerald-500" />
            Shopping List
          </DialogTitle>
          <DialogDescription>
            All the ingredients you need for your meal plan this week.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="shopping" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="shopping">Shopping List</TabsTrigger>
            <TabsTrigger value="pantry">Pantry</TabsTrigger>
          </TabsList>

          <TabsContent value="shopping" className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="relative w-full max-w-sm">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search items..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="flex gap-1">
                <Button variant="outline" size="icon" onClick={handlePrintList}>
                  <Printer className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon" onClick={handleExportList}>
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {isLoading ? (
              <div className="py-8 space-y-4">
                <div className="h-5 w-32 bg-muted rounded animate-pulse"></div>
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex gap-2">
                      <div className="h-5 w-5 bg-muted rounded animate-pulse"></div>
                      <div className="h-5 w-full bg-muted rounded animate-pulse"></div>
                    </div>
                  ))}
                </div>
              </div>
            ) : filteredShoppingList.length === 0 ? (
              <div className="py-8 text-center">
                <ShoppingCart className="h-12 w-12 text-muted-foreground/40 mx-auto mb-4" />
                <h3 className="text-lg font-medium">
                  {searchTerm ? "No matching items" : "Your shopping list is empty"}
                </h3>
                <p className="text-muted-foreground mt-1">
                  {searchTerm ? "Try a different search term" : "Generate a meal plan to populate your shopping list"}
                </p>
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="select-all" 
                      checked={isAllChecked}
                      onCheckedChange={handleToggleAll}
                    />
                    <label
                      htmlFor="select-all"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Select All
                    </label>
                  </div>
                  <p className="text-sm font-medium">
                    Total: <span className="text-emerald-600">${totalCost.toFixed(2)}</span>
                  </p>
                </div>

                <ScrollArea className="h-[300px] pr-4">
                  <div className="space-y-6">
                    {categories.map(category => {
                      const categoryItems = filteredShoppingList.filter(
                        item => item.category === category
                      );
                      
                      return (
                        <div key={category}>
                          <h3 className="font-medium mb-2 flex items-center">
                            <span className="w-3 h-3 rounded-full bg-primary mr-2"></span>
                            {category}
                          </h3>
                          <div className="space-y-2">
                            {categoryItems.map(item => (
                              <div key={item.id} className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <Checkbox 
                                    id={item.id} 
                                    checked={checkedItems[item.id] || false}
                                    onCheckedChange={(checked) => 
                                      handleToggleItem(item.id, !!checked)
                                    }
                                  />
                                  <label
                                    htmlFor={item.id}
                                    className={cn(
                                      "text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                                      checkedItems[item.id] ? "line-through text-muted-foreground" : ""
                                    )}
                                  >
                                    {item.quantity} {item.unit} {item.name}
                                  </label>
                                </div>
                                <div className="flex items-center">
                                  {item.inPantry && (
                                    <Badge variant="outline" className="mr-2 text-xs bg-green-500/10 text-green-700 border-green-300">
                                      In Pantry
                                    </Badge>
                                  )}
                                  <span className="text-sm font-medium">${item.cost.toFixed(2)}</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </ScrollArea>

                <div className="pt-2">
                  <Button 
                    onClick={handleAddToPantry} 
                    disabled={!isAnyChecked}
                    className="w-full"
                  >
                    <Package className="mr-2 h-4 w-4" />
                    Add Checked Items to Pantry
                  </Button>
                </div>
              </>
            )}
          </TabsContent>

          <TabsContent value="pantry" className="space-y-4">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search pantry items..."
                className="pl-8"
              />
            </div>

            {isLoading ? (
              <div className="py-8 space-y-4">
                <div className="h-5 w-32 bg-muted rounded animate-pulse"></div>
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="h-10 bg-muted rounded animate-pulse"></div>
                  ))}
                </div>
              </div>
            ) : pantryItems.length === 0 ? (
              <div className="py-8 text-center">
                <Package className="h-12 w-12 text-muted-foreground/40 mx-auto mb-4" />
                <h3 className="text-lg font-medium">Your pantry is empty</h3>
                <p className="text-muted-foreground mt-1">
                  Add items to your pantry to optimize your shopping list
                </p>
              </div>
            ) : (
              <ScrollArea className="h-[350px] pr-4">
                <div className="space-y-6">
                  {[...new Set(pantryItems.map(item => item.category))].map(category => (
                    <div key={category}>
                      <h3 className="font-medium mb-2 flex items-center">
                        <span className="w-3 h-3 rounded-full bg-primary mr-2"></span>
                        {category}
                      </h3>
                      <div className="space-y-2">
                        {pantryItems
                          .filter(item => item.category === category)
                          .map(item => (
                            <div key={item.id} className="flex items-center justify-between p-2 bg-muted/50 rounded-lg">
                              <div>
                                <p className="text-sm font-medium">{item.name}</p>
                                <p className="text-xs text-muted-foreground">
                                  {item.quantity} {item.unit}
                                </p>
                              </div>
                              {item.lowStock && (
                                <Badge variant="outline" className="bg-yellow-500/10 text-yellow-700 border-yellow-300">
                                  <AlertTriangle className="h-3 w-3 mr-1" />
                                  Low
                                </Badge>
                              )}
                            </div>
                          ))
                        }
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}