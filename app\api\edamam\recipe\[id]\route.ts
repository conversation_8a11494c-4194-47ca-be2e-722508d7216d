import { NextRequest, NextResponse } from 'next/server';
import { getRecipe } from '@/lib/api/edamam';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const recipeId = params.id;
    const recipe = await getRecipe(recipeId);

    return NextResponse.json(recipe);
  } catch (error: any) {
    console.error(`Error in Edamam recipe API route for ID ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while fetching the recipe' },
      { status: 500 }
    );
  }
}
