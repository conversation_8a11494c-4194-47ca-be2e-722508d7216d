// This script applies the disable RLS function to your Supabase project
// Run this script with: node scripts/apply_disable_rls_function.js

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Supabase connection details
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service role key not found in environment variables.');
  console.error('Make sure you have a .env file with NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY.');
  process.exit(1);
}

// Create Supabase client with service role key (has admin privileges)
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applyDisableRlsFunction() {
  try {
    console.log('Applying disable RLS function to Supabase...');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../supabase/migrations/20230802000000_create_disable_rls_function.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL directly
    const { data, error } = await supabase.rpc('_', {}, {
      headers: {
        'Content-Type': 'application/json',
        'Prefer': 'params=single-object',
        'X-Raw-SQL': sqlContent
      }
    });

    if (error) {
      console.error('Error applying disable RLS function:', error);
      return;
    }

    console.log('Disable RLS function applied successfully!');
    
    // Test the function
    console.log('Testing the create_disable_rls_function...');
    const { error: testError } = await supabase.rpc('create_disable_rls_function');
    
    if (testError) {
      console.error('Error testing create_disable_rls_function:', testError);
    } else {
      console.log('create_disable_rls_function executed successfully!');
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

applyDisableRlsFunction();
