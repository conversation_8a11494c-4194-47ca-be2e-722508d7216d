'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Filter, X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export interface FilterOptions {
  cuisine: string;
  difficulty: string;
  dietaryRestrictions: string[];
  prepTimeMax: number;
  cookTimeMax: number;
  costMax: number;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface AdvancedFilterProps {
  initialFilters: FilterOptions;
  onFilterChange: (filters: FilterOptions) => void;
  onClose?: () => void;
  cuisineOptions: { label: string; value: string }[];
  difficultyOptions: { label: string; value: string }[];
  dietaryOptions: { label: string; value: string }[];
}

export function AdvancedFilter({
  initialFilters,
  onFilterChange,
  onClose,
  cuisineOptions,
  difficultyOptions,
  dietaryOptions
}: AdvancedFilterProps) {
  const [filters, setFilters] = useState<FilterOptions>(initialFilters);
  const [activeFiltersCount, setActiveFiltersCount] = useState<number>(
    countActiveFilters(initialFilters)
  );

  // Count how many filters are active
  function countActiveFilters(filters: FilterOptions): number {
    let count = 0;
    if (filters.cuisine) count++;
    if (filters.difficulty) count++;
    if (filters.dietaryRestrictions.length > 0) count += filters.dietaryRestrictions.length;
    if (filters.prepTimeMax < 120) count++;
    if (filters.cookTimeMax < 120) count++;
    if (filters.costMax < 20) count++;
    if (filters.sortBy !== 'created_at') count++;
    return count;
  }

  // Update filters and notify parent
  const updateFilters = (newFilters: Partial<FilterOptions>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    setActiveFiltersCount(countActiveFilters(updatedFilters));
    onFilterChange(updatedFilters);
  };

  // Toggle a dietary restriction
  const toggleDietaryRestriction = (value: string) => {
    const current = [...filters.dietaryRestrictions];
    const index = current.indexOf(value);
    
    if (index === -1) {
      current.push(value);
    } else {
      current.splice(index, 1);
    }
    
    updateFilters({ dietaryRestrictions: current });
  };

  // Reset all filters
  const resetFilters = () => {
    const defaultFilters: FilterOptions = {
      cuisine: '',
      difficulty: '',
      dietaryRestrictions: [],
      prepTimeMax: 120,
      cookTimeMax: 120,
      costMax: 20,
      sortBy: 'created_at',
      sortOrder: 'desc'
    };
    
    setFilters(defaultFilters);
    setActiveFiltersCount(0);
    onFilterChange(defaultFilters);
  };

  return (
    <Card className="w-full mb-6">
      <CardContent className="pt-6">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            <h3 className="font-medium">Advanced Filters</h3>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount} active
              </Badge>
            )}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={resetFilters}>
              Reset
            </Button>
            {onClose && (
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label className="block mb-2">Cuisine</Label>
            <Select
              value={filters.cuisine}
              onValueChange={(value) => updateFilters({ cuisine: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="All cuisines" />
              </SelectTrigger>
              <SelectContent>
                {cuisineOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="block mb-2">Difficulty</Label>
            <Select
              value={filters.difficulty}
              onValueChange={(value) => updateFilters({ difficulty: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Any difficulty" />
              </SelectTrigger>
              <SelectContent>
                {difficultyOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="block mb-2">Prep Time (max {filters.prepTimeMax} min)</Label>
            <Slider
              value={[filters.prepTimeMax]}
              min={5}
              max={120}
              step={5}
              onValueChange={(value) => updateFilters({ prepTimeMax: value[0] })}
              className="my-4"
            />
          </div>

          <div>
            <Label className="block mb-2">Cook Time (max {filters.cookTimeMax} min)</Label>
            <Slider
              value={[filters.cookTimeMax]}
              min={5}
              max={120}
              step={5}
              onValueChange={(value) => updateFilters({ cookTimeMax: value[0] })}
              className="my-4"
            />
          </div>

          <div>
            <Label className="block mb-2">Cost per Serving (max ${filters.costMax.toFixed(2)})</Label>
            <Slider
              value={[filters.costMax]}
              min={1}
              max={20}
              step={0.5}
              onValueChange={(value) => updateFilters({ costMax: value[0] })}
              className="my-4"
            />
          </div>

          <div>
            <Label className="block mb-2">Sort By</Label>
            <div className="flex gap-2">
              <Select
                value={filters.sortBy}
                onValueChange={(value) => updateFilters({ sortBy: value })}
              >
                <SelectTrigger className="flex-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at">Date Added</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="prep_time">Prep Time</SelectItem>
                  <SelectItem value="cook_time">Cook Time</SelectItem>
                  <SelectItem value="cost_per_serving">Cost</SelectItem>
                </SelectContent>
              </Select>
              
              <Select
                value={filters.sortOrder}
                onValueChange={(value) => updateFilters({ sortOrder: value as 'asc' | 'desc' })}
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="asc">Ascending</SelectItem>
                  <SelectItem value="desc">Descending</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <Label className="block mb-2">Dietary Restrictions</Label>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
            {dietaryOptions.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`diet-${option.value}`}
                  checked={filters.dietaryRestrictions.includes(option.value)}
                  onCheckedChange={() => toggleDietaryRestriction(option.value)}
                />
                <label
                  htmlFor={`diet-${option.value}`}
                  className="text-sm cursor-pointer"
                >
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
