// Script to check if recipes exist in Supabase
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with hardcoded values
const supabaseUrl = 'https://nhboafhjkkmuwwoolktr.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5oYm9hZmhqa2ttdXd3b29sa3RyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIxMDI4ODAsImV4cCI6MjA1NzY3ODg4MH0.F6v2quJgeoQubU1PIRPVjGauX6fsqaW0PKvfLebbABw';

console.log('Using Supabase URL:', supabaseUrl);

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkRecipes() {
  console.log('Checking recipes in Supabase...');
  
  try {
    // Query recipes from Supabase
    const { data, error } = await supabase
      .from('recipes')
      .select('*');
    
    if (error) {
      console.error('Error querying recipes:', error);
      process.exit(1);
    }
    
    console.log(`Found ${data.length} recipes:`);
    data.forEach(recipe => {
      console.log(`- ID: ${recipe.id}, Name: ${recipe.name}`);
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Run the check script
checkRecipes();
