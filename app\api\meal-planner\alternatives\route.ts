import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { AlternativeMealsResponse } from '@/app/types/api';

export async function GET(
  request: Request
): Promise<NextResponse<AlternativeMealsResponse>> {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const currentMealId = searchParams.get('currentMealId');

    if (!type || !currentMealId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // TODO: Replace with actual database query
    const mockAlternatives = [
      {
        id: "alt1",
        name: "Quinoa Bowl",
        image: "https://images.unsplash.com/photo-1546069901-ba9599a7e63c",
        calories: 400,
        prepTime: 20,
        type: type
      },
      {
        id: "alt2",
        name: "Chicken Caesar Salad",
        image: "https://images.unsplash.com/photo-1550304943-4f24f54ddde9",
        calories: 380,
        prepTime: 15,
        type: type
      }
    ];

    return NextResponse.json({ data: { alternatives: mockAlternatives } });
  } catch (error) {
    console.error('Error fetching alternative meals:', error);
    return NextResponse.json(
      { error: 'Failed to fetch alternative meals' },
      { status: 500 }
    );
  }
}

