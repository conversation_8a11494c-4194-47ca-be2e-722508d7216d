"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Utensils, ShoppingCart, Package, Settings, LogOut, Menu, Home, Calendar } from "lucide-react";
import { getUserInfo } from "@/lib/user";
import { cn } from "@/lib/utils";

interface MealPlanHeaderProps {
  onViewShoppingList: () => void;
}

export default function MealPlanHeader({ onViewShoppingList }: MealPlanHeaderProps) {
  const [username, setUsername] = useState("User");
  const router = useRouter();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        const user = await getUserInfo();
        if (user?.name) {
          setUsername(user.name);
        }
      } catch (error) {
        console.error("Failed to fetch user info:", error);
      }
    };

    fetchUserInfo();

    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);

    return () => {
      window.removeEventListener("resize", checkScreenSize);
    };
  }, []);

  const navigation = [
    { name: "Dashboard", href: "/dashboard", icon: Home },
    { name: "Meal Plan", href: "/meal-plan", icon: Calendar, current: true },
    { name: "Shopping List", href: "#", icon: ShoppingCart, action: onViewShoppingList },
    { name: "Pantry", href: "/pantry", icon: Package },
    { name: "Preferences", href: "/preferences", icon: Settings },
  ];

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between px-4">
        <div className="flex items-center gap-2">
          <Utensils className="h-6 w-6 text-primary" />
          <span className="text-xl font-semibold">LeanEats</span>
        </div>

        <div className="hidden md:block">
          <p className="text-lg font-medium">Hi, {username}</p>
        </div>

        {isMobile ? (
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <Menu className="h-6 w-6" />
              </Button>
            </SheetTrigger>
            <SheetContent>
              <div className="mt-6 flex flex-col gap-4">
                {navigation.map((item) => (
                  <Button
                    key={item.name}
                    variant={item.current ? "default" : "ghost"}
                    className={cn(
                      "justify-start gap-2",
                      item.current ? "bg-primary text-primary-foreground" : ""
                    )}
                    onClick={() => {
                      if (item.action) {
                        item.action();
                      } else if (item.href) {
                        router.push(item.href);
                      }
                    }}
                  >
                    <item.icon className="h-5 w-5" />
                    {item.name}
                  </Button>
                ))}
                <Button variant="ghost" className="justify-start gap-2 text-destructive">
                  <LogOut className="h-5 w-5" />
                  Logout
                </Button>
              </div>
            </SheetContent>
          </Sheet>
        ) : (
          <nav className="flex items-center space-x-1">
            {navigation.map((item) => (
              <Button
                key={item.name}
                variant={item.current ? "default" : "ghost"}
                className={cn(
                  "px-3",
                  item.current ? "bg-primary text-primary-foreground" : ""
                )}
                onClick={() => {
                  if (item.action) {
                    item.action();
                  } else if (item.href) {
                    router.push(item.href);
                  }
                }}
              >
                {item.name}
              </Button>
            ))}
            <Button variant="ghost" size="icon" className="text-destructive">
              <LogOut className="h-5 w-5" />
            </Button>
          </nav>
        )}
      </div>
    </header>
  );
}