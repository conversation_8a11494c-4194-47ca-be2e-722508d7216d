'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, ChefHat, Utensils, ShoppingCart, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';

interface GeneratingAnimationProps {
  title?: string;
  description?: string;
}

export function GeneratingAnimation({
  title = "Generating Your Meal Plan",
  description = "This may take a minute. We're crafting a personalized meal plan just for you."
}: GeneratingAnimationProps) {
  const [step, setStep] = useState(0);
  const steps = [
    { icon: ChefHat, text: "Finding recipes that match your preferences..." },
    { icon: Utensils, text: "Calculating nutritional information..." },
    { icon: ShoppingCart, text: "Creating your shopping list..." },
    { icon: Calendar, text: "Organizing your weekly meal plan..." },
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setStep((prevStep) => (prevStep + 1) % steps.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [steps.length]);

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
        </div>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {steps.map((s, i) => {
            const Icon = s.icon;
            const isActive = i === step;
            const isPast = i < step;

            return (
              <div
                key={i}
                className={cn(
                  "flex items-center space-x-4 transition-opacity duration-500",
                  isActive ? "opacity-100" : isPast ? "opacity-50" : "opacity-30"
                )}
              >
                <div className={cn(
                  "p-2 rounded-full",
                  isActive ? "bg-primary/10 text-primary" : "bg-muted text-muted-foreground"
                )}>
                  <Icon className={cn(
                    "h-5 w-5",
                    isActive ? "animate-pulse" : ""
                  )} />
                </div>
                <div className="flex-1">
                  <p className={cn(
                    "text-sm",
                    isActive ? "font-medium text-foreground" : "text-muted-foreground"
                  )}>
                    {s.text}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
