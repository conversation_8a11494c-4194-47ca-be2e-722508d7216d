"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import MealPlanHeader from "@/components/meal-plan/MealPlanHeader";
import MealPlanCalendar from "@/components/meal-plan/MealPlanCalendar";
import MealPlanActions from "@/components/meal-plan/MealPlanActions";
import GeneratePlanModal from "@/components/meal-plan/GeneratePlanModal";
import AddMealModal from "@/components/meal-plan/AddMealModal";
import ViewShoppingListModal from "@/components/meal-plan/ViewShoppingListModal";
import MealPlanSidebar from "@/components/meal-plan/MealPlanSidebar";
import { DndProvider } from "@/components/meal-plan/DragDropProvider";
import { getMealPlan } from "@/lib/meal-plan";
import { useMealPlanStore } from "@/lib/stores/meal-plan-store";
import { Toaster } from "@/components/ui/sonner";
import { cn } from "@/lib/utils";

export default function MealPlanPage() {
  const router = useRouter();
  const [isGenerateModalOpen, setIsGenerateModalOpen] = useState(false);
  const [isAddMealModalOpen, setIsAddMealModalOpen] = useState(false);
  const [isShoppingListModalOpen, setIsShoppingListModalOpen] = useState(false);
  const [selectedDay, setSelectedDay] = useState<string | null>(null);
  const [selectedMealType, setSelectedMealType] = useState<string | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const { setMealPlan, isLoading, setIsLoading } = useMealPlanStore();

  useEffect(() => {
    const fetchMealPlan = async () => {
      setIsLoading(true);
      try {
        const mealPlanData = await getMealPlan();
        setMealPlan(mealPlanData);
      } catch (error) {
        console.error("Failed to fetch meal plan:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMealPlan();
  }, [setMealPlan, setIsLoading]);

  const handleAddMeal = (day: string, mealType: string) => {
    setSelectedDay(day);
    setSelectedMealType(mealType);
    setIsAddMealModalOpen(true);
  };

  const handleGeneratePlan = () => {
    setIsGenerateModalOpen(true);
  };

  const handleViewShoppingList = () => {
    setIsShoppingListModalOpen(true);
  };

  return (
    <div className="min-h-screen bg-background">
      <MealPlanHeader onViewShoppingList={handleViewShoppingList} />
      
      <main className="container mx-auto px-4 py-6 flex">
        <MealPlanSidebar 
          isOpen={isSidebarOpen}
          onToggle={() => setIsSidebarOpen(!isSidebarOpen)}
        />
        
        <div className={cn(
          "transition-all duration-200",
          isSidebarOpen ? "ml-[300px]" : "ml-0"
        )}>
          <DndProvider>
            <MealPlanCalendar onAddMeal={handleAddMeal} />
            <MealPlanActions 
              onGeneratePlan={handleGeneratePlan}
              onViewShoppingList={handleViewShoppingList}
            />
          </DndProvider>
        </div>
      </main>

      {isGenerateModalOpen && (
        <GeneratePlanModal 
          isOpen={isGenerateModalOpen} 
          onClose={() => setIsGenerateModalOpen(false)} 
        />
      )}

      {isAddMealModalOpen && selectedDay && selectedMealType && (
        <AddMealModal 
          isOpen={isAddMealModalOpen}
          onClose={() => setIsAddMealModalOpen(false)}
          day={selectedDay}
          mealType={selectedMealType}
        />
      )}

      {isShoppingListModalOpen && (
        <ViewShoppingListModal
          isOpen={isShoppingListModalOpen}
          onClose={() => setIsShoppingListModalOpen(false)}
        />
      )}

      <Toaster position="bottom-right" />
    </div>
  );
}