"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSupabase } from "@/components/supabase-provider";
import { toast } from "sonner";
import Image from "next/image";
import { recipeService } from "@/app/services/database-client";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ChevronLeft, Clock, Utensils, DollarSign, ShoppingCart } from "lucide-react";

// Import our new components
import { ServingsAdjuster } from "@/components/recipe/servings-adjuster";
import { CookingMode } from "@/components/recipe/cooking-mode";
import { NutritionFacts } from "@/components/recipe/nutrition-facts";
import { IngredientList } from "@/components/recipe/ingredient-list";
import { InstructionSteps } from "@/components/recipe/instruction-steps";
import { RecipeActions } from "@/components/recipe/recipe-actions";

interface Ingredient {
  name: string;
  amount: string;
  unit: string;
}

interface Nutrition {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
}

interface MealDetail {
  id: number;
  name: string;
  description: string;
  image: string;
  prepTime: number;
  cookTime: number;
  servings: number;
  difficulty: string;
  ingredients: Ingredient[];
  instructions: string[];
  nutrition: Nutrition;
  tags: string[];
  isFavorite: boolean;
  mealType: string;
  scheduledFor: string;
}

export default function MealDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const [isLoading, setIsLoading] = useState(true);
  const [meal, setMeal] = useState<MealDetail | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [completed, setCompleted] = useState(false);
  const mealId = params.id;

  // New state for enhanced features
  const [servingsScaleFactor, setServingsScaleFactor] = useState(1);
  const [showCookingMode, setShowCookingMode] = useState(false);

  useEffect(() => {
    // Don't fetch if Supabase is still loading or not available
    if (isSupabaseLoading || !supabase) {
      return;
    }

    const fetchMealDetails = async () => {
      try {
        setIsLoading(true);
        console.log('Fetching meal details for ID:', mealId);
        // Log to the UI for debugging
        toast(`Fetching meal details for ID: ${mealId}`);

        // First try to fetch from Supabase using the recipe service
        try {
          const { data, error } = await recipeService.getRecipeById(mealId);

          if (error) {
            console.error('Error fetching recipe:', error);
            toast.error(`Error fetching recipe: ${error}`);
            throw error;
          }

          if (data) {
            console.log('Successfully fetched recipe:', data);

            // Transform the data to match our MealDetail interface
            const mealData: MealDetail = {
              id: parseInt(data.id) || 0,
              name: data.name || 'Unknown Recipe',
              description: data.description || '',
              image: data.image_url || 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c',
              prepTime: data.prep_time || 0,
              cookTime: data.cook_time || 0,
              servings: data.servings || 1,
              difficulty: 'Medium',
              ingredients: data.ingredients ? JSON.parse(data.ingredients) : [
                { name: "Ingredient 1", amount: "1", unit: "cup" },
                { name: "Ingredient 2", amount: "2", unit: "tbsp" }
              ],
              instructions: data.instructions ? JSON.parse(data.instructions) : ['No instructions provided'],
              nutrition: {
                calories: data.calories_per_serving || 350,
                protein: data.protein_per_serving || 20,
                carbs: data.carbs_per_serving || 30,
                fat: data.fat_per_serving || 15,
                fiber: 5
              },
              tags: data.tags ? JSON.parse(data.tags) : [],
              isFavorite: data.is_favorite || false,
              mealType: 'Dinner',
              scheduledFor: 'Today'
            };

            setMeal(mealData);
            return;
          }
        } catch (serviceError) {
          console.error('Error using recipe service:', serviceError);
          toast.error('Error using recipe service, trying direct Supabase query');
          // Continue to direct Supabase query as fallback
        }

        // Define predefined meals for fallback
        const predefinedMeals: Record<string, MealDetail> = {
          '1': {
            id: 1,
            name: "Grilled Chicken Salad",
            description: "A healthy and delicious salad with grilled chicken breast, mixed greens, and a light vinaigrette dressing.",
            image: "https://images.unsplash.com/photo-1527477396000-e27163b481c2",
            prepTime: 15,
            cookTime: 20,
            servings: 2,
            difficulty: "Easy",
            ingredients: [
              { name: "Chicken breast", amount: "2", unit: "pieces" },
              { name: "Mixed greens", amount: "4", unit: "cups" },
              { name: "Cherry tomatoes", amount: "1", unit: "cup" },
              { name: "Cucumber", amount: "1", unit: "medium" },
              { name: "Red onion", amount: "1/4", unit: "cup" },
              { name: "Olive oil", amount: "2", unit: "tbsp" },
              { name: "Lemon juice", amount: "1", unit: "tbsp" },
              { name: "Salt", amount: "1/2", unit: "tsp" },
              { name: "Black pepper", amount: "1/4", unit: "tsp" }
            ],
            instructions: [
              "Season chicken breasts with salt and pepper.",
              "Grill chicken for 6-8 minutes per side until fully cooked.",
              "Let chicken rest for 5 minutes, then slice into strips.",
              "In a large bowl, combine mixed greens, tomatoes, cucumber, and red onion.",
              "In a small bowl, whisk together olive oil, lemon juice, salt, and pepper.",
              "Drizzle dressing over salad and toss to combine.",
              "Top with sliced grilled chicken and serve immediately."
            ],
            nutrition: {
              calories: 350,
              protein: 35,
              carbs: 15,
              fat: 18,
              fiber: 5
            },
            tags: ["high-protein", "low-carb", "gluten-free"],
            isFavorite: true,
            mealType: "Lunch",
            scheduledFor: "Today, 1:00 PM"
          },
          '2': {
            id: 2,
            name: "Quinoa Buddha Bowl",
            description: "A nutritious and colorful bowl packed with quinoa, roasted vegetables, and a tahini dressing.",
            image: "https://images.unsplash.com/photo-1546069901-ba9599a7e63c",
            prepTime: 20,
            cookTime: 25,
            servings: 2,
            difficulty: "Medium",
            ingredients: [
              { name: "Quinoa", amount: "1", unit: "cup" },
              { name: "Sweet potato", amount: "1", unit: "medium" },
              { name: "Broccoli", amount: "1", unit: "head" },
              { name: "Chickpeas", amount: "1", unit: "can" },
              { name: "Avocado", amount: "1", unit: "" },
              { name: "Tahini", amount: "2", unit: "tbsp" },
              { name: "Lemon juice", amount: "1", unit: "tbsp" },
              { name: "Maple syrup", amount: "1", unit: "tsp" },
              { name: "Olive oil", amount: "2", unit: "tbsp" },
              { name: "Salt", amount: "1/2", unit: "tsp" },
              { name: "Cumin", amount: "1/2", unit: "tsp" }
            ],
            instructions: [
              "Preheat oven to 400°F (200°C).",
              "Cook quinoa according to package instructions.",
              "Dice sweet potato, toss with olive oil, salt, and cumin, and roast for 25 minutes.",
              "Cut broccoli into florets, toss with olive oil and salt, and roast for 15 minutes.",
              "Drain and rinse chickpeas, toss with olive oil and spices, and roast for 15 minutes.",
              "Make dressing by whisking together tahini, lemon juice, maple syrup, and water.",
              "Assemble bowls with quinoa, roasted vegetables, chickpeas, and sliced avocado.",
              "Drizzle with tahini dressing and serve."
            ],
            nutrition: {
              calories: 450,
              protein: 15,
              carbs: 65,
              fat: 20,
              fiber: 12
            },
            tags: ["vegetarian", "high-fiber", "plant-based"],
            isFavorite: false,
            mealType: "Dinner",
            scheduledFor: "Today, 7:00 PM"
          },
          '3': {
            id: 3,
            name: "Smoothie Bowl",
            description: "A refreshing and nutritious smoothie bowl topped with fresh fruits, granola, and nuts.",
            image: "https://images.unsplash.com/photo-1553530666-ba11a90a0868",
            prepTime: 10,
            cookTime: 0,
            servings: 1,
            difficulty: "Easy",
            ingredients: [
              { name: "Frozen banana", amount: "1", unit: "large" },
              { name: "Frozen berries", amount: "1", unit: "cup" },
              { name: "Greek yogurt", amount: "1/2", unit: "cup" },
              { name: "Almond milk", amount: "1/4", unit: "cup" },
              { name: "Honey", amount: "1", unit: "tbsp" },
              { name: "Granola", amount: "1/4", unit: "cup" },
              { name: "Fresh berries", amount: "1/4", unit: "cup" },
              { name: "Sliced banana", amount: "1/2", unit: "" },
              { name: "Chia seeds", amount: "1", unit: "tsp" },
              { name: "Almond butter", amount: "1", unit: "tbsp" }
            ],
            instructions: [
              "In a blender, combine frozen banana, frozen berries, Greek yogurt, almond milk, and honey.",
              "Blend until smooth and thick. Add more almond milk if needed.",
              "Pour into a bowl.",
              "Top with granola, fresh berries, sliced banana, chia seeds, and a drizzle of almond butter.",
              "Serve immediately."
            ],
            nutrition: {
              calories: 320,
              protein: 15,
              carbs: 55,
              fat: 8,
              fiber: 10
            },
            tags: ["breakfast", "vegetarian", "quick"],
            isFavorite: true,
            mealType: "Breakfast",
            scheduledFor: "Tomorrow, 8:00 AM"
          },
          '4': {
            id: 4,
            name: "Grilled Salmon with Asparagus",
            description: "Perfectly grilled salmon served with roasted asparagus and a lemon butter sauce.",
            image: "https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2",
            prepTime: 15,
            cookTime: 15,
            servings: 2,
            difficulty: "Medium",
            ingredients: [
              { name: "Salmon fillets", amount: "2", unit: "6 oz each" },
              { name: "Asparagus", amount: "1", unit: "bunch" },
              { name: "Butter", amount: "2", unit: "tbsp" },
              { name: "Lemon", amount: "1", unit: "" },
              { name: "Garlic", amount: "2", unit: "cloves" },
              { name: "Olive oil", amount: "2", unit: "tbsp" },
              { name: "Salt", amount: "1/2", unit: "tsp" },
              { name: "Black pepper", amount: "1/4", unit: "tsp" },
              { name: "Fresh dill", amount: "2", unit: "tbsp" }
            ],
            instructions: [
              "Preheat grill to medium-high heat.",
              "Season salmon fillets with salt, pepper, and a drizzle of olive oil.",
              "Trim asparagus ends and toss with olive oil, salt, and pepper.",
              "Grill salmon for 4-5 minutes per side until it flakes easily with a fork.",
              "Grill asparagus for 3-4 minutes, turning occasionally.",
              "In a small saucepan, melt butter with minced garlic and the juice of half a lemon.",
              "Plate salmon and asparagus, drizzle with lemon butter sauce.",
              "Garnish with fresh dill and lemon wedges."
            ],
            nutrition: {
              calories: 420,
              protein: 40,
              carbs: 10,
              fat: 25,
              fiber: 4
            },
            tags: ["high-protein", "low-carb", "omega-3"],
            isFavorite: true,
            mealType: "Dinner",
            scheduledFor: "Tomorrow, 6:30 PM"
          }
        };

        // First try to fetch from Supabase
        try {
          console.log('Attempting to fetch from Supabase for ID:', mealId);
          toast(`Attempting to fetch from Supabase for ID: ${mealId}`);
          const { data, error } = await supabase
            .from('recipes')
            .select('*')
            .eq('id', mealId)
            .single();

          if (error) {
            console.error('Error fetching recipe from Supabase:', error);
            toast.error(`Error fetching recipe from Supabase: ${error.message}`);
            throw error;
          }

          if (data) {
            console.log('Successfully fetched recipe from Supabase:', data);

            // Transform the Supabase data to match our MealDetail interface
            const mealData: MealDetail = {
              id: parseInt(data.id) || 0,
              name: data.name || 'Unknown Recipe',
              description: data.description || '',
              image: data.image || 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c',
              prepTime: data.prep_time || 0,
              cookTime: data.cook_time || 0,
              servings: data.servings || 1,
              difficulty: data.difficulty || 'Medium',
              ingredients: data.ingredients || [
                { name: "Ingredient 1", amount: "1", unit: "cup" },
                { name: "Ingredient 2", amount: "2", unit: "tbsp" }
              ],
              instructions: data.instructions || ['No instructions provided'],
              nutrition: data.nutrition || {
                calories: 350,
                protein: 20,
                carbs: 30,
                fat: 15,
                fiber: 5
              },
              tags: data.dietary_restrictions || [],
              isFavorite: data.is_favorite || false,
              mealType: data.meal_type || 'Dinner',
              scheduledFor: 'Today'
            };

            setMeal(mealData);
            return;
          }
        } catch (supabaseError) {
          console.error('Failed to fetch from Supabase:', supabaseError);
          toast.error('Failed to fetch from Supabase, using fallback data');
          // Continue to fallback data
        }

        // If Supabase fetch failed, use predefined meals
        console.log('Using predefined meal data as fallback');
        if (predefinedMeals[mealId]) {
          console.log('Using predefined meal for ID:', mealId);
          setMeal(predefinedMeals[mealId]);
        } else {
          console.log('Using default predefined meal');
          setMeal(predefinedMeals['1']); // Default to the first meal
        }
      } catch (error) {
        console.error('Error in fetchMealDetails:', error);
        toast.error('Failed to load meal details. Using fallback data.');

        // Fallback to a default meal if there's an error
        setMeal({
          id: 1,
          name: "Grilled Chicken Salad",
          description: "A healthy and delicious salad with grilled chicken breast, mixed greens, and a light vinaigrette dressing.",
          image: "https://images.unsplash.com/photo-1527477396000-e27163b481c2",
          prepTime: 15,
          cookTime: 20,
          servings: 2,
          difficulty: "Easy",
          ingredients: [
            { name: "Chicken breast", amount: "2", unit: "pieces" },
            { name: "Mixed greens", amount: "4", unit: "cups" },
            { name: "Cherry tomatoes", amount: "1", unit: "cup" },
            { name: "Cucumber", amount: "1", unit: "medium" },
            { name: "Red onion", amount: "1/4", unit: "cup" },
            { name: "Olive oil", amount: "2", unit: "tbsp" },
            { name: "Lemon juice", amount: "1", unit: "tbsp" },
            { name: "Salt", amount: "1/2", unit: "tsp" },
            { name: "Black pepper", amount: "1/4", unit: "tsp" }
          ],
          instructions: [
            "Season chicken breasts with salt and pepper.",
            "Grill chicken for 6-8 minutes per side until fully cooked.",
            "Let chicken rest for 5 minutes, then slice into strips.",
            "In a large bowl, combine mixed greens, tomatoes, cucumber, and red onion.",
            "In a small bowl, whisk together olive oil, lemon juice, salt, and pepper.",
            "Drizzle dressing over salad and toss to combine.",
            "Top with sliced grilled chicken and serve immediately."
          ],
          nutrition: {
            calories: 350,
            protein: 35,
            carbs: 15,
            fat: 18,
            fiber: 5
          },
          tags: ["high-protein", "low-carb", "gluten-free"],
          isFavorite: true,
          mealType: "Lunch",
          scheduledFor: "Today, 1:00 PM"
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchMealDetails();
  }, [params.id, supabase, isSupabaseLoading]);

  const handleBack = () => {
    router.push('/meal-plan/view');
  };

  const toggleFavorite = async () => {
    if (!meal) return;

    try {
      // Optimistically update UI
      setMeal({ ...meal, isFavorite: !meal.isFavorite });

      // Update in database if we have a Supabase connection
      if (supabase) {
        const { error } = await supabase
          .from('recipes')
          .update({ is_favorite: !meal.isFavorite })
          .eq('id', mealId);

        if (error) {
          console.error('Error updating favorite status:', error);
          // Revert UI change on error
          setMeal({ ...meal, isFavorite: meal.isFavorite });
          toast.error('Failed to update favorite status');
        } else {
          toast.success(meal.isFavorite ? 'Removed from favorites' : 'Added to favorites');
        }
      } else {
        toast.success(meal.isFavorite ? 'Removed from favorites' : 'Added to favorites');
      }
    } catch (error) {
      console.error('Error in toggleFavorite:', error);
      // Revert UI change on error
      setMeal({ ...meal, isFavorite: meal.isFavorite });
      toast.error('An error occurred');
    }
  };

  const markAsCompleted = () => {
    setCompleted(!completed);
    toast.success(completed ? 'Meal marked as not completed' : 'Meal marked as completed');
  };

  // Function to handle servings adjustment
  const handleServingsChange = (newServings: number, scaleFactor: number) => {
    setServingsScaleFactor(scaleFactor);
  };

  // Function to start cooking mode
  const startCookingMode = () => {
    setShowCookingMode(true);
  };

  // Function to close cooking mode
  const closeCookingMode = () => {
    setShowCookingMode(false);
  };

  // Function to edit the recipe
  const handleEditRecipe = () => {
    router.push(`/recipes/edit/${mealId}`);
  };

  // Function to add ingredients to shopping list
  const addToShoppingList = async (ingredients: Ingredient[]) => {
    try {
      if (!meal) return;

      // Get the current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        toast.error('You must be logged in to add items to your shopping list');
        router.push('/login');
        return;
      }

      // Format ingredients for shopping list
      const shoppingItems = ingredients.map(ingredient => ({
        name: ingredient.name,
        quantity: ingredient.amount,
        unit: ingredient.unit,
        category: ingredient.name.toLowerCase().includes('chicken') ? 'Meat & Seafood' :
                 ingredient.name.toLowerCase().includes('milk') ? 'Dairy' :
                 ingredient.name.toLowerCase().includes('apple') ? 'Fruits' :
                 ingredient.name.toLowerCase().includes('lettuce') ? 'Vegetables' :
                 ingredient.name.toLowerCase().includes('bread') ? 'Grains & Bread' :
                 ingredient.name.toLowerCase().includes('oil') ? 'Condiments & Spices' :
                 'Other',
        checked: false
      }));

      // Create a new shopping list with these ingredients
      const { error } = await supabase
        .from('shopping_lists')
        .insert({
          user_id: user.id,
          meal_plan_id: null, // Not associated with a specific meal plan
          items: shoppingItems,
          created_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error inserting shopping list:', error);
        throw error;
      }

      toast.success('Ingredients added to shopping list');

      // Ask if the user wants to view the shopping list
      const shouldNavigate = window.confirm('Ingredients added to shopping list. Would you like to view your shopping list now?');
      if (shouldNavigate) {
        router.push('/shopping-list');
      }
    } catch (error) {
      console.error('Error adding ingredients to shopping list:', error);
      toast.error('Failed to add ingredients to shopping list');
    }
  };

  // Show loading state if either Supabase is loading or our component is loading
  if (isSupabaseLoading || isLoading || !meal) {
    return (
      <div className="container max-w-4xl py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          <p className="ml-4 text-muted-foreground">
            {isSupabaseLoading ? 'Initializing database connection...' : 'Loading meal details...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl py-8">
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={handleBack} className="mr-2">
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-2xl font-bold">{meal.name}</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="md:col-span-2">
          <div className="relative w-full h-[300px] rounded-lg overflow-hidden mb-4">
            <Image
              src={meal.image}
              alt={meal.name}
              fill
              style={{ objectFit: "cover" }}
              priority
            />
          </div>

          <div className="flex flex-wrap gap-2 mb-4">
            {meal.tags.map((tag, index) => (
              <Badge key={index} variant="secondary">
                {tag}
              </Badge>
            ))}
          </div>

          <p className="text-muted-foreground mb-4">{meal.description}</p>

          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6">
            <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg">
              <Clock className="h-5 w-5 mb-1 text-muted-foreground" />
              <span className="text-sm font-medium">{meal.prepTime + meal.cookTime} min</span>
              <span className="text-xs text-muted-foreground">Total Time</span>
            </div>
            <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg">
              <Utensils className="h-5 w-5 mb-1 text-muted-foreground" />
              <span className="text-sm font-medium">{meal.difficulty}</span>
              <span className="text-xs text-muted-foreground">Difficulty</span>
            </div>
            <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg">
              <DollarSign className="h-5 w-5 mb-1 text-muted-foreground" />
              <span className="text-sm font-medium">$8.50</span>
              <span className="text-xs text-muted-foreground">Per Serving</span>
            </div>
            <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg">
              <Star className="h-5 w-5 mb-1 text-muted-foreground" />
              <span className="text-sm font-medium">{meal.nutrition.calories}</span>
              <span className="text-xs text-muted-foreground">Calories</span>
            </div>
          </div>

          <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
            <div className="flex items-center">
              <ServingsAdjuster
                initialServings={meal.servings}
                onChange={handleServingsChange}
              />
            </div>

            <RecipeActions
              recipeId={mealId}
              isFavorite={meal.isFavorite}
              isCompleted={completed}
              onToggleFavorite={toggleFavorite}
              onMarkCompleted={markAsCompleted}
              onEdit={handleEditRecipe}
            />
          </div>
        </div>

        <div className="md:col-span-1">
          <NutritionFacts
            nutrition={meal.nutrition}
            servingSize={`${Math.round(meal.servings * servingsScaleFactor)} serving${Math.round(meal.servings * servingsScaleFactor) !== 1 ? 's' : ''}`}
            scaleFactor={servingsScaleFactor}
          />
        </div>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="ingredients">Ingredients</TabsTrigger>
          <TabsTrigger value="instructions">Instructions</TabsTrigger>
        </TabsList>
        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>Meal Overview</CardTitle>
              <CardDescription>Quick summary of this meal</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Description</h3>
                  <p className="text-muted-foreground">{meal.description}</p>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Preparation Time</h3>
                  <p className="text-muted-foreground">
                    Prep: {meal.prepTime} minutes | Cook: {meal.cookTime} minutes | Total: {meal.prepTime + meal.cookTime} minutes
                  </p>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Servings</h3>
                  <p className="text-muted-foreground">This recipe makes {meal.servings} servings.</p>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Scheduled For</h3>
                  <p className="text-muted-foreground">{meal.scheduledFor}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="ingredients">
          <IngredientList
            ingredients={meal.ingredients}
            servings={meal.servings}
            scaleFactor={servingsScaleFactor}
            onAddToShoppingList={addToShoppingList}
          />
        </TabsContent>
        <TabsContent value="instructions">
          <InstructionSteps
            instructions={meal.instructions}
            onStartCookingMode={startCookingMode}
          />
        </TabsContent>
      </Tabs>

      {/* Cooking Mode */}
      {showCookingMode && (
        <CookingMode
          instructions={meal.instructions}
          prepTime={meal.prepTime}
          cookTime={meal.cookTime}
          onClose={closeCookingMode}
        />
      )}
    </div>
  );
}