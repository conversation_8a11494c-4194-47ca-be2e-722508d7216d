-- Comprehensive diagnostic script to check the current state of the database
-- This script will identify existing tables, columns, and RLS policies

-- Check which tables exist in the public schema
SELECT 
    table_name 
FROM 
    information_schema.tables 
WHERE 
    table_schema = 'public'
    AND table_type = 'BASE TABLE'
ORDER BY 
    table_name;

-- Check which tables have RLS enabled
SELECT 
    tablename, 
    rowsecurity
FROM 
    pg_tables
WHERE 
    schemaname = 'public'
ORDER BY 
    tablename;

-- Check all columns for each table in the public schema
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
ORDER BY 
    table_name, 
    ordinal_position;

-- Check all RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM 
    pg_policies
WHERE 
    schemaname = 'public'
ORDER BY 
    tablename, 
    policyname;

-- Check all foreign key constraints
SELECT
    tc.table_schema, 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_schema AS foreign_table_schema,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
JOIN 
    information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN 
    information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE 
    tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_schema = 'public'
ORDER BY 
    tc.table_name, 
    kcu.column_name;

-- Check all indexes
SELECT
    tablename,
    indexname,
    indexdef
FROM
    pg_indexes
WHERE
    schemaname = 'public'
ORDER BY
    tablename,
    indexname;

-- Check all triggers
SELECT
    trigger_schema,
    trigger_name,
    event_manipulation,
    event_object_schema,
    event_object_table,
    action_statement,
    action_timing
FROM
    information_schema.triggers
WHERE
    trigger_schema = 'public'
ORDER BY
    event_object_table,
    trigger_name;

-- Check all functions
SELECT
    routine_schema,
    routine_name,
    routine_type,
    data_type AS return_type,
    external_language
FROM
    information_schema.routines
WHERE
    routine_schema = 'public'
ORDER BY
    routine_name;

-- Check all sequences
SELECT
    sequence_schema,
    sequence_name
FROM
    information_schema.sequences
WHERE
    sequence_schema = 'public'
ORDER BY
    sequence_name;

-- Check all views
SELECT
    table_schema,
    table_name,
    view_definition
FROM
    information_schema.views
WHERE
    table_schema = 'public'
ORDER BY
    table_name;

-- Check all extensions
SELECT
    extname,
    extversion
FROM
    pg_extension
ORDER BY
    extname;

-- Check database size
SELECT
    pg_size_pretty(pg_database_size(current_database())) AS database_size;

-- Check table sizes
SELECT
    relname AS table_name,
    pg_size_pretty(pg_total_relation_size(relid)) AS total_size,
    pg_size_pretty(pg_relation_size(relid)) AS data_size,
    pg_size_pretty(pg_total_relation_size(relid) - pg_relation_size(relid)) AS external_size
FROM
    pg_catalog.pg_statio_user_tables
ORDER BY
    pg_total_relation_size(relid) DESC;

-- Check user roles
SELECT
    rolname,
    rolsuper,
    rolinherit,
    rolcreaterole,
    rolcreatedb,
    rolcanlogin,
    rolreplication,
    rolconnlimit,
    rolvaliduntil
FROM
    pg_roles
ORDER BY
    rolname;

-- Check role memberships
SELECT
    pg_get_userbyid(roleid) AS rolname,
    pg_get_userbyid(member) AS member,
    grantor,
    admin_option
FROM
    pg_auth_members
ORDER BY
    rolname,
    member;

-- Check schema permissions
SELECT
    n.nspname AS schema,
    r.rolname AS grantee,
    p.perm AS privilege_type
FROM
    pg_namespace n
CROSS JOIN
    pg_roles r
CROSS JOIN
    (VALUES ('USAGE'), ('CREATE')) AS p(perm)
WHERE
    n.nspname = 'public'
    AND pg_has_role(r.oid, n.nspowner, 'USAGE')
    AND r.rolname != 'postgres'
ORDER BY
    schema,
    grantee,
    privilege_type;

-- Check table permissions
SELECT
    table_schema,
    table_name,
    grantee,
    privilege_type
FROM
    information_schema.table_privileges
WHERE
    table_schema = 'public'
ORDER BY
    table_name,
    grantee,
    privilege_type;

-- Check column permissions
SELECT
    table_schema,
    table_name,
    column_name,
    grantee,
    privilege_type
FROM
    information_schema.column_privileges
WHERE
    table_schema = 'public'
ORDER BY
    table_name,
    column_name,
    grantee,
    privilege_type;

-- Check for tables with user_id column but missing RLS policies
SELECT 
    t.table_name
FROM 
    information_schema.tables t
JOIN 
    information_schema.columns c ON t.table_name = c.table_name AND t.table_schema = c.table_schema
LEFT JOIN 
    pg_policies p ON t.table_name = p.tablename AND p.schemaname = 'public'
WHERE 
    t.table_schema = 'public'
    AND t.table_type = 'BASE TABLE'
    AND c.column_name = 'user_id'
    AND p.policyname IS NULL
GROUP BY 
    t.table_name;

-- Check for tables with RLS enabled but no policies
SELECT 
    t.tablename
FROM 
    pg_tables t
LEFT JOIN 
    pg_policies p ON t.tablename = p.tablename AND t.schemaname = p.schemaname
WHERE 
    t.schemaname = 'public'
    AND t.rowsecurity = true
    AND p.policyname IS NULL;

-- Check for tables with policies assigned to public role instead of authenticated
SELECT 
    tablename,
    policyname,
    roles
FROM 
    pg_policies
WHERE 
    schemaname = 'public'
    AND 'public' = ANY(roles)
ORDER BY 
    tablename, policyname;

-- Check for type mismatches in RLS policies
SELECT 
    p.schemaname,
    p.tablename,
    p.policyname,
    p.cmd,
    c.data_type AS user_id_data_type,
    p.qual AS policy_using_expression,
    p.with_check AS policy_with_check_expression,
    CASE
        WHEN c.data_type = 'uuid' AND (
            p.qual::text LIKE '%auth.uid()%' AND p.qual::text NOT LIKE '%::text%'
        ) THEN 'MISMATCH: UUID column without text casting'
        WHEN c.data_type = 'text' AND (
            p.qual::text LIKE '%auth.uid()::uuid%'
        ) THEN 'MISMATCH: text column with UUID casting'
        ELSE 'OK'
    END AS type_check
FROM 
    pg_policies p
JOIN 
    information_schema.columns c ON p.tablename = c.table_name
WHERE 
    p.schemaname = 'public'
    AND c.column_name = 'user_id'
    AND c.table_schema = 'public'
    AND (
        p.qual::text LIKE '%auth.uid()%' OR
        p.with_check::text LIKE '%auth.uid()%'
    );

-- Check for missing indexes on foreign keys
SELECT
    tc.table_schema, 
    tc.table_name, 
    kcu.column_name,
    CASE WHEN i.indexrelid IS NULL THEN 'Missing' ELSE 'Present' END AS index_status
FROM 
    information_schema.table_constraints AS tc
JOIN 
    information_schema.key_column_usage AS kcu ON tc.constraint_name = kcu.constraint_name
LEFT JOIN 
    pg_catalog.pg_constraint pc ON tc.constraint_name = pc.conname
LEFT JOIN 
    pg_catalog.pg_index i ON pc.conindid = i.indexrelid
WHERE 
    tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public';

-- Check for missing updated_at triggers
SELECT 
    t.table_name
FROM 
    information_schema.tables t
JOIN 
    information_schema.columns c ON t.table_name = c.table_name AND t.table_schema = c.table_schema
LEFT JOIN 
    pg_trigger tr ON tr.tgname = 'update_' || t.table_name || '_updated_at'
LEFT JOIN 
    pg_class cl ON cl.oid = tr.tgrelid AND cl.relname = t.table_name
WHERE 
    t.table_schema = 'public'
    AND t.table_type = 'BASE TABLE'
    AND c.column_name = 'updated_at'
    AND tr.tgname IS NULL;

-- Check for missing permissions for authenticated users
SELECT 
    table_schema,
    table_name,
    privilege_type,
    grantee
FROM 
    information_schema.table_privileges
WHERE 
    table_schema = 'public'
    AND grantee = 'authenticated'
ORDER BY 
    table_name, privilege_type;
