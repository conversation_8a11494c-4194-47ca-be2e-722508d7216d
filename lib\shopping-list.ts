// Shopping List Utilities and Types
export interface ShoppingItem {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  category: string;
  checked: boolean;
  notes?: string;
}

export interface ShoppingList {
  id: string;
  name: string;
  items: ShoppingItem[];
  createdAt: Date;
  updatedAt: Date;
}

// Shopping list categories
export const SHOPPING_CATEGORIES = [
  'Produce',
  'Meat & Seafood',
  'Dairy & Eggs',
  'Pantry',
  'Frozen',
  'Bakery',
  'Beverages',
  'Snacks',
  'Other'
] as const;

export type ShoppingCategory = typeof SHOPPING_CATEGORIES[number];

// Utility functions
export function groupItemsByCategory(items: ShoppingItem[]): Record<string, ShoppingItem[]> {
  return items.reduce((groups, item) => {
    const category = item.category || 'Other';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(item);
    return groups;
  }, {} as Record<string, ShoppingItem[]>);
}

export function calculateTotalItems(items: ShoppingItem[]): number {
  return items.length;
}

export function calculateCheckedItems(items: ShoppingItem[]): number {
  return items.filter(item => item.checked).length;
}

export function calculateProgress(items: ShoppingItem[]): number {
  const total = calculateTotalItems(items);
  const checked = calculateCheckedItems(items);
  return total > 0 ? (checked / total) * 100 : 0;
}

export function sortItemsByCategory(items: ShoppingItem[]): ShoppingItem[] {
  return items.sort((a, b) => {
    const categoryA = SHOPPING_CATEGORIES.indexOf(a.category as ShoppingCategory);
    const categoryB = SHOPPING_CATEGORIES.indexOf(b.category as ShoppingCategory);
    
    if (categoryA !== categoryB) {
      return categoryA - categoryB;
    }
    
    return a.name.localeCompare(b.name);
  });
}

export function createShoppingItem(
  name: string,
  quantity: number = 1,
  unit: string = 'item',
  category: string = 'Other'
): Omit<ShoppingItem, 'id'> {
  return {
    name: name.trim(),
    quantity,
    unit,
    category,
    checked: false,
  };
}

export function duplicateShoppingList(list: ShoppingList, newName: string): Omit<ShoppingList, 'id'> {
  return {
    name: newName,
    items: list.items.map(item => ({
      ...item,
      id: crypto.randomUUID(),
      checked: false,
    })),
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

export function mergeShoppingLists(lists: ShoppingList[]): ShoppingItem[] {
  const itemMap = new Map<string, ShoppingItem>();
  
  lists.forEach(list => {
    list.items.forEach(item => {
      const key = `${item.name.toLowerCase()}-${item.unit}`;
      const existing = itemMap.get(key);
      
      if (existing) {
        existing.quantity += item.quantity;
      } else {
        itemMap.set(key, { ...item, id: crypto.randomUUID() });
      }
    });
  });
  
  return Array.from(itemMap.values());
}

export function exportShoppingListToText(list: ShoppingList): string {
  const grouped = groupItemsByCategory(list.items);
  let text = `${list.name}\n${'='.repeat(list.name.length)}\n\n`;
  
  Object.entries(grouped).forEach(([category, items]) => {
    text += `${category}:\n`;
    items.forEach(item => {
      const status = item.checked ? '✓' : '☐';
      text += `  ${status} ${item.quantity} ${item.unit} ${item.name}\n`;
    });
    text += '\n';
  });
  
  return text;
}
