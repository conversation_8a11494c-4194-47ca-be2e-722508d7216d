"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ShoppingCart } from "lucide-react";

interface WeeklySummaryProps {
  totalCost: number;
  averageCalories: number;
  macros: {
    protein: number;
    carbs: number;
    fats: number;
  };
  onGenerateShoppingList: () => void;
}

export function WeeklySummary({
  totalCost,
  averageCalories,
  macros,
  onGenerateShoppingList
}: WeeklySummaryProps) {
  return (
    <div className="flex flex-col md:flex-row gap-6">
      <div className="flex-1">
        <h2 className="text-lg font-semibold mb-4">Weekly Summary</h2>
        
        <div className="space-y-4">
          <div>
            <p className="text-sm text-muted-foreground">Total Cost</p>
            <p className="text-2xl font-bold">${totalCost.toFixed(2)}</p>
          </div>
          
          <div>
            <p className="text-sm text-muted-foreground">Average Calories/Meal</p>
            <p className="text-2xl font-bold">{averageCalories}</p>
          </div>
          
          <Button 
            className="w-full mt-4" 
            variant="default" 
            onClick={onGenerateShoppingList}
          >
            <ShoppingCart className="mr-2 h-4 w-4" />
            Generate Shopping List
          </Button>
        </div>
      </div>
      
      <div className="flex-1 flex flex-col items-center justify-center">
        <div className="relative w-40 h-40">
          {/* Donut chart */}
          <svg viewBox="0 0 100 100" className="w-full h-full">
            <circle
              cx="50"
              cy="50"
              r="40"
              fill="none"
              stroke="#f0f0f0"
              strokeWidth="20"
            />
            
            {/* Protein segment */}
            <circle
              cx="50"
              cy="50"
              r="40"
              fill="none"
              stroke="#f97316"
              strokeWidth="20"
              strokeDasharray={`${macros.protein * 2.51} 251`}
              strokeDashoffset="0"
              transform="rotate(-90 50 50)"
            />
            
            {/* Carbs segment */}
            <circle
              cx="50"
              cy="50"
              r="40"
              fill="none"
              stroke="#10b981"
              strokeWidth="20"
              strokeDasharray={`${macros.carbs * 2.51} 251`}
              strokeDashoffset={`${-macros.protein * 2.51}`}
              transform="rotate(-90 50 50)"
            />
            
            {/* Fats segment */}
            <circle
              cx="50"
              cy="50"
              r="40"
              fill="none"
              stroke="#1e40af"
              strokeWidth="20"
              strokeDasharray={`${macros.fats * 2.51} 251`}
              strokeDashoffset={`${-(macros.protein + macros.carbs) * 2.51}`}
              transform="rotate(-90 50 50)"
            />
          </svg>
        </div>
        
        <div className="flex justify-center gap-6 mt-4">
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full bg-orange-500 mr-2"></div>
            <span className="text-xs">Protein</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full bg-emerald-500 mr-2"></div>
            <span className="text-xs">Carbs</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full bg-blue-800 mr-2"></div>
            <span className="text-xs">Fats</span>
          </div>
        </div>
      </div>
    </div>
  );
}
