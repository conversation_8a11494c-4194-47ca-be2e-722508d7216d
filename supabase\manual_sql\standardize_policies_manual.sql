-- Standardize RLS policies for all tables manually

-- 1. meal_plans table
ALTER TABLE public.meal_plans ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view their own meal_plans" ON public.meal_plans;
DROP POLICY IF EXISTS "Users can insert their own meal_plans" ON public.meal_plans;
DROP POLICY IF EXISTS "Users can update their own meal_plans" ON public.meal_plans;
DROP POLICY IF EXISTS "Users can delete their own meal_plans" ON public.meal_plans;

-- Check the data type of user_id in meal_plans
DO $$
DECLARE
    column_type TEXT;
BEGIN
    SELECT data_type INTO column_type
    FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'meal_plans'
    AND column_name = 'user_id';
    
    RAISE NOTICE 'meal_plans.user_id type: %', column_type;
    
    IF column_type = 'uuid' THEN
        -- For UUID type
        CREATE POLICY "Users can view their own meal_plans" ON public.meal_plans FOR SELECT USING (user_id = auth.uid());
        CREATE POLICY "Users can insert their own meal_plans" ON public.meal_plans FOR INSERT WITH CHECK (user_id = auth.uid());
        CREATE POLICY "Users can update their own meal_plans" ON public.meal_plans FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
        CREATE POLICY "Users can delete their own meal_plans" ON public.meal_plans FOR DELETE USING (user_id = auth.uid());
    ELSE
        -- For text type
        CREATE POLICY "Users can view their own meal_plans" ON public.meal_plans FOR SELECT USING (user_id = auth.uid()::text);
        CREATE POLICY "Users can insert their own meal_plans" ON public.meal_plans FOR INSERT WITH CHECK (user_id = auth.uid()::text);
        CREATE POLICY "Users can update their own meal_plans" ON public.meal_plans FOR UPDATE USING (user_id = auth.uid()::text) WITH CHECK (user_id = auth.uid()::text);
        CREATE POLICY "Users can delete their own meal_plans" ON public.meal_plans FOR DELETE USING (user_id = auth.uid()::text);
    END IF;
END
$$;

GRANT ALL ON public.meal_plans TO authenticated;

-- 2. meals table
ALTER TABLE public.meals ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view their own meals" ON public.meals;
DROP POLICY IF EXISTS "Users can insert their own meals" ON public.meals;
DROP POLICY IF EXISTS "Users can update their own meals" ON public.meals;
DROP POLICY IF EXISTS "Users can delete their own meals" ON public.meals;
DROP POLICY IF EXISTS "Users can view meals from their meal plans" ON public.meals;
DROP POLICY IF EXISTS "Users can insert meals to their meal plans" ON public.meals;
DROP POLICY IF EXISTS "Users can view meals in their meal plans" ON public.meals;
DROP POLICY IF EXISTS "Users can create meals in their meal plans" ON public.meals;

-- Check the data type of user_id in meal_plans
DO $$
DECLARE
    column_type TEXT;
BEGIN
    SELECT data_type INTO column_type
    FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'meal_plans'
    AND column_name = 'user_id';
    
    RAISE NOTICE 'meal_plans.user_id type: %', column_type;
    
    IF column_type = 'uuid' THEN
        -- For UUID type
        CREATE POLICY "Users can view their own meals" ON public.meals FOR SELECT 
        USING (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()));
        
        CREATE POLICY "Users can insert their own meals" ON public.meals FOR INSERT 
        WITH CHECK (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()));
        
        CREATE POLICY "Users can update their own meals" ON public.meals FOR UPDATE 
        USING (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()))
        WITH CHECK (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()));
        
        CREATE POLICY "Users can delete their own meals" ON public.meals FOR DELETE 
        USING (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()));
    ELSE
        -- For text type
        CREATE POLICY "Users can view their own meals" ON public.meals FOR SELECT 
        USING (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()::text));
        
        CREATE POLICY "Users can insert their own meals" ON public.meals FOR INSERT 
        WITH CHECK (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()::text));
        
        CREATE POLICY "Users can update their own meals" ON public.meals FOR UPDATE 
        USING (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()::text))
        WITH CHECK (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()::text));
        
        CREATE POLICY "Users can delete their own meals" ON public.meals FOR DELETE 
        USING (meal_plan_id IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid()::text));
    END IF;
END
$$;

GRANT ALL ON public.meals TO authenticated;

-- 3. pantry_items table
ALTER TABLE public.pantry_items ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view their own pantry_items" ON public.pantry_items;
DROP POLICY IF EXISTS "Users can insert their own pantry_items" ON public.pantry_items;
DROP POLICY IF EXISTS "Users can update their own pantry_items" ON public.pantry_items;
DROP POLICY IF EXISTS "Users can delete their own pantry_items" ON public.pantry_items;
DROP POLICY IF EXISTS "pantry_items_select_policy" ON public.pantry_items;
DROP POLICY IF EXISTS "pantry_items_insert_policy" ON public.pantry_items;
DROP POLICY IF EXISTS "pantry_items_update_policy" ON public.pantry_items;
DROP POLICY IF EXISTS "pantry_items_delete_policy" ON public.pantry_items;

-- Check the data type of user_id in pantry_items
DO $$
DECLARE
    column_type TEXT;
BEGIN
    SELECT data_type INTO column_type
    FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'pantry_items'
    AND column_name = 'user_id';
    
    RAISE NOTICE 'pantry_items.user_id type: %', column_type;
    
    IF column_type = 'uuid' THEN
        -- For UUID type
        CREATE POLICY "Users can view their own pantry_items" ON public.pantry_items FOR SELECT USING (user_id = auth.uid());
        CREATE POLICY "Users can insert their own pantry_items" ON public.pantry_items FOR INSERT WITH CHECK (user_id = auth.uid());
        CREATE POLICY "Users can update their own pantry_items" ON public.pantry_items FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
        CREATE POLICY "Users can delete their own pantry_items" ON public.pantry_items FOR DELETE USING (user_id = auth.uid());
    ELSE
        -- For text type
        CREATE POLICY "Users can view their own pantry_items" ON public.pantry_items FOR SELECT USING (user_id = auth.uid()::text);
        CREATE POLICY "Users can insert their own pantry_items" ON public.pantry_items FOR INSERT WITH CHECK (user_id = auth.uid()::text);
        CREATE POLICY "Users can update their own pantry_items" ON public.pantry_items FOR UPDATE USING (user_id = auth.uid()::text) WITH CHECK (user_id = auth.uid()::text);
        CREATE POLICY "Users can delete their own pantry_items" ON public.pantry_items FOR DELETE USING (user_id = auth.uid()::text);
    END IF;
END
$$;

GRANT ALL ON public.pantry_items TO authenticated;

-- 4. recipes table
ALTER TABLE public.recipes ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view their own recipes" ON public.recipes;
DROP POLICY IF EXISTS "Users can insert their own recipes" ON public.recipes;
DROP POLICY IF EXISTS "Users can update their own recipes" ON public.recipes;
DROP POLICY IF EXISTS "Users can delete their own recipes" ON public.recipes;
DROP POLICY IF EXISTS "Allow anonymous access for testing" ON public.recipes;
DROP POLICY IF EXISTS "Enable read access for all users" ON public.recipes;
DROP POLICY IF EXISTS "Enable delete for authenticated users on their own recipes" ON public.recipes;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.recipes;
DROP POLICY IF EXISTS "Enable update for authenticated users on their own recipes" ON public.recipes;
DROP POLICY IF EXISTS "Users can create their own recipes" ON public.recipes;

-- Check the data type of user_id in recipes
DO $$
DECLARE
    column_type TEXT;
BEGIN
    SELECT data_type INTO column_type
    FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'recipes'
    AND column_name = 'user_id';
    
    RAISE NOTICE 'recipes.user_id type: %', column_type;
    
    IF column_type = 'uuid' THEN
        -- For UUID type
        CREATE POLICY "Users can view their own recipes" ON public.recipes FOR SELECT USING (user_id = auth.uid());
        CREATE POLICY "Users can insert their own recipes" ON public.recipes FOR INSERT WITH CHECK (user_id = auth.uid());
        CREATE POLICY "Users can update their own recipes" ON public.recipes FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
        CREATE POLICY "Users can delete their own recipes" ON public.recipes FOR DELETE USING (user_id = auth.uid());
    ELSE
        -- For text type
        CREATE POLICY "Users can view their own recipes" ON public.recipes FOR SELECT USING (user_id = auth.uid()::text);
        CREATE POLICY "Users can insert their own recipes" ON public.recipes FOR INSERT WITH CHECK (user_id = auth.uid()::text);
        CREATE POLICY "Users can update their own recipes" ON public.recipes FOR UPDATE USING (user_id = auth.uid()::text) WITH CHECK (user_id = auth.uid()::text);
        CREATE POLICY "Users can delete their own recipes" ON public.recipes FOR DELETE USING (user_id = auth.uid()::text);
    END IF;
END
$$;

GRANT ALL ON public.recipes TO authenticated;

-- 5. shopping_lists table
ALTER TABLE public.shopping_lists ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view their own shopping_lists" ON public.shopping_lists;
DROP POLICY IF EXISTS "Users can insert their own shopping_lists" ON public.shopping_lists;
DROP POLICY IF EXISTS "Users can update their own shopping_lists" ON public.shopping_lists;
DROP POLICY IF EXISTS "Users can delete their own shopping_lists" ON public.shopping_lists;

-- Check the data type of user_id in shopping_lists
DO $$
DECLARE
    column_type TEXT;
BEGIN
    SELECT data_type INTO column_type
    FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'shopping_lists'
    AND column_name = 'user_id';
    
    RAISE NOTICE 'shopping_lists.user_id type: %', column_type;
    
    IF column_type = 'uuid' THEN
        -- For UUID type
        CREATE POLICY "Users can view their own shopping_lists" ON public.shopping_lists FOR SELECT USING (user_id = auth.uid());
        CREATE POLICY "Users can insert their own shopping_lists" ON public.shopping_lists FOR INSERT WITH CHECK (user_id = auth.uid());
        CREATE POLICY "Users can update their own shopping_lists" ON public.shopping_lists FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
        CREATE POLICY "Users can delete their own shopping_lists" ON public.shopping_lists FOR DELETE USING (user_id = auth.uid());
    ELSE
        -- For text type
        CREATE POLICY "Users can view their own shopping_lists" ON public.shopping_lists FOR SELECT USING (user_id = auth.uid()::text);
        CREATE POLICY "Users can insert their own shopping_lists" ON public.shopping_lists FOR INSERT WITH CHECK (user_id = auth.uid()::text);
        CREATE POLICY "Users can update their own shopping_lists" ON public.shopping_lists FOR UPDATE USING (user_id = auth.uid()::text) WITH CHECK (user_id = auth.uid()::text);
        CREATE POLICY "Users can delete their own shopping_lists" ON public.shopping_lists FOR DELETE USING (user_id = auth.uid()::text);
    END IF;
END
$$;

GRANT ALL ON public.shopping_lists TO authenticated;

-- 6. shopping_items table
ALTER TABLE public.shopping_items ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view their own shopping_items" ON public.shopping_items;
DROP POLICY IF EXISTS "Users can insert their own shopping_items" ON public.shopping_items;
DROP POLICY IF EXISTS "Users can update their own shopping_items" ON public.shopping_items;
DROP POLICY IF EXISTS "Users can delete their own shopping_items" ON public.shopping_items;

-- Check the data type of user_id in shopping_lists
DO $$
DECLARE
    column_type TEXT;
BEGIN
    SELECT data_type INTO column_type
    FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'shopping_lists'
    AND column_name = 'user_id';
    
    RAISE NOTICE 'shopping_lists.user_id type: %', column_type;
    
    IF column_type = 'uuid' THEN
        -- For UUID type
        CREATE POLICY "Users can view their own shopping_items" ON public.shopping_items FOR SELECT 
        USING (list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));
        
        CREATE POLICY "Users can insert their own shopping_items" ON public.shopping_items FOR INSERT 
        WITH CHECK (list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));
        
        CREATE POLICY "Users can update their own shopping_items" ON public.shopping_items FOR UPDATE 
        USING (list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()))
        WITH CHECK (list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));
        
        CREATE POLICY "Users can delete their own shopping_items" ON public.shopping_items FOR DELETE 
        USING (list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));
    ELSE
        -- For text type
        CREATE POLICY "Users can view their own shopping_items" ON public.shopping_items FOR SELECT 
        USING (list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));
        
        CREATE POLICY "Users can insert their own shopping_items" ON public.shopping_items FOR INSERT 
        WITH CHECK (list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));
        
        CREATE POLICY "Users can update their own shopping_items" ON public.shopping_items FOR UPDATE 
        USING (list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text))
        WITH CHECK (list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));
        
        CREATE POLICY "Users can delete their own shopping_items" ON public.shopping_items FOR DELETE 
        USING (list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));
    END IF;
END
$$;

GRANT ALL ON public.shopping_items TO authenticated;

-- 7. user_preferences table
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view their own user_preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can insert their own user_preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can update their own user_preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can delete their own user_preferences" ON public.user_preferences;

-- Check the data type of user_id in user_preferences
DO $$
DECLARE
    column_type TEXT;
BEGIN
    SELECT data_type INTO column_type
    FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'user_preferences'
    AND column_name = 'user_id';
    
    RAISE NOTICE 'user_preferences.user_id type: %', column_type;
    
    IF column_type = 'uuid' THEN
        -- For UUID type
        CREATE POLICY "Users can view their own user_preferences" ON public.user_preferences FOR SELECT USING (user_id = auth.uid());
        CREATE POLICY "Users can insert their own user_preferences" ON public.user_preferences FOR INSERT WITH CHECK (user_id = auth.uid());
        CREATE POLICY "Users can update their own user_preferences" ON public.user_preferences FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
        CREATE POLICY "Users can delete their own user_preferences" ON public.user_preferences FOR DELETE USING (user_id = auth.uid());
    ELSE
        -- For text type
        CREATE POLICY "Users can view their own user_preferences" ON public.user_preferences FOR SELECT USING (user_id = auth.uid()::text);
        CREATE POLICY "Users can insert their own user_preferences" ON public.user_preferences FOR INSERT WITH CHECK (user_id = auth.uid()::text);
        CREATE POLICY "Users can update their own user_preferences" ON public.user_preferences FOR UPDATE USING (user_id = auth.uid()::text) WITH CHECK (user_id = auth.uid()::text);
        CREATE POLICY "Users can delete their own user_preferences" ON public.user_preferences FOR DELETE USING (user_id = auth.uid()::text);
    END IF;
END
$$;

GRANT ALL ON public.user_preferences TO authenticated;

-- 8. users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view their own users" ON public.users;
DROP POLICY IF EXISTS "Users can insert their own users" ON public.users;
DROP POLICY IF EXISTS "Users can update their own users" ON public.users;
DROP POLICY IF EXISTS "Users can delete their own users" ON public.users;
DROP POLICY IF EXISTS "Users can view own record" ON public.users;
DROP POLICY IF EXISTS "Users can update own record" ON public.users;
DROP POLICY IF EXISTS "Allow insert during signup" ON public.users;

-- Check the data type of id in users
DO $$
DECLARE
    column_type TEXT;
BEGIN
    SELECT data_type INTO column_type
    FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'users'
    AND column_name = 'id';
    
    RAISE NOTICE 'users.id type: %', column_type;
    
    IF column_type = 'uuid' THEN
        -- For UUID type
        CREATE POLICY "Users can view their own users" ON public.users FOR SELECT USING (id = auth.uid());
        CREATE POLICY "Users can insert their own users" ON public.users FOR INSERT WITH CHECK (id = auth.uid());
        CREATE POLICY "Users can update their own users" ON public.users FOR UPDATE USING (id = auth.uid()) WITH CHECK (id = auth.uid());
        CREATE POLICY "Users can delete their own users" ON public.users FOR DELETE USING (id = auth.uid());
    ELSE
        -- For text type
        CREATE POLICY "Users can view their own users" ON public.users FOR SELECT USING (id = auth.uid()::text);
        CREATE POLICY "Users can insert their own users" ON public.users FOR INSERT WITH CHECK (id = auth.uid()::text);
        CREATE POLICY "Users can update their own users" ON public.users FOR UPDATE USING (id = auth.uid()::text) WITH CHECK (id = auth.uid()::text);
        CREATE POLICY "Users can delete their own users" ON public.users FOR DELETE USING (id = auth.uid()::text);
    END IF;
END
$$;

GRANT ALL ON public.users TO authenticated;

-- 9. Clean up legacy tables (optional)
-- DROP TABLE IF EXISTS public.meal_plans_old;

-- 10. Disable RLS for system tables (optional)
ALTER TABLE public._prisma_migrations DISABLE ROW LEVEL SECURITY;
