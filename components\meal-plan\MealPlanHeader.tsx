"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { SimpleTabs as Tabs, SimpleTabsList as TabsList, SimpleTabsTrigger as TabsTrigger } from '@/components/ui/simple-tabs';
import {
  Calendar,
  LayoutGrid,
  List,
  Plus,
  ShoppingCart,
  RefreshCw
} from 'lucide-react';

interface MealPlanHeaderProps {
  onViewChange: (view: 'calendar' | 'list') => void;
  onLayoutChange: (layout: 'grid' | 'list') => void;
  onGeneratePlan: () => void;
  onAddMeal: () => void;
  onViewShoppingList: () => void;
  currentView: 'calendar' | 'list';
  currentLayout: 'grid' | 'list';
}

export function MealPlanHeader({
  onViewChange,
  onLayoutChange,
  onGeneratePlan,
  onAddMeal,
  onViewShoppingList,
  currentView,
  currentLayout
}: MealPlanHeaderProps) {
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
      <div>
        <h1 className="text-2xl font-bold">Meal Plan</h1>
        <p className="text-muted-foreground">Plan and organize your meals</p>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
        <div className="flex items-center gap-2">
          <Tabs
            defaultValue={currentView}
            className="w-full"
            onValueChange={(value) => onViewChange(value as 'calendar' | 'list')}
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="calendar" className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span className="hidden sm:inline">Calendar</span>
              </TabsTrigger>
              <TabsTrigger value="list" className="flex items-center gap-1">
                <List className="h-4 w-4" />
                <span className="hidden sm:inline">List</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>

          {currentView === 'calendar' && (
            <Tabs
              defaultValue={currentLayout}
              className="w-full"
              onValueChange={(value) => onLayoutChange(value as 'grid' | 'list')}
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="grid" className="px-2">
                  <LayoutGrid className="h-4 w-4" />
                </TabsTrigger>
                <TabsTrigger value="list" className="px-2">
                  <List className="h-4 w-4" />
                </TabsTrigger>
              </TabsList>
            </Tabs>
          )}
        </div>

        <div className="flex gap-2 ml-auto">
          <Button
            variant="outline"
            size="sm"
            onClick={onViewShoppingList}
            className="flex items-center gap-1"
          >
            <ShoppingCart className="h-4 w-4" />
            <span className="hidden sm:inline">Shopping List</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onAddMeal}
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            <span className="hidden sm:inline">Add Meal</span>
          </Button>

          <Button
            variant="default"
            size="sm"
            onClick={onGeneratePlan}
            className="flex items-center gap-1"
          >
            <RefreshCw className="h-4 w-4" />
            <span className="hidden sm:inline">Generate Plan</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
