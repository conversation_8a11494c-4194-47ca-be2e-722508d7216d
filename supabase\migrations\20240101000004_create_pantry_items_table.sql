-- Create pantry_items table
CREATE TABLE IF NOT EXISTS pantry_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  category TEXT NOT NULL,
  quantity TEXT NOT NULL,
  unit TEXT NOT NULL,
  expiry_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE pantry_items ENABLE ROW LEVEL SECURITY;

-- Policy for selecting pantry items (users can only see their own)
CREATE POLICY "Users can view their own pantry_items"
  ON pantry_items
  FOR SELECT
  USING (user_id::text = auth.uid()::text);

-- Policy for inserting pantry items
CREATE POLICY "Users can insert their own pantry_items"
  ON pantry_items
  FOR INSERT
  WITH CHECK (user_id::text = auth.uid()::text);

-- Policy for updating pantry items
CREATE POLICY "Users can update their own pantry_items"
  ON pantry_items
  FOR UPDATE
  USING (user_id::text = auth.uid()::text);

-- Policy for deleting pantry items
CREATE POLICY "Users can delete their own pantry_items"
  ON pantry_items
  FOR DELETE
  USING (user_id::text = auth.uid()::text);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS pantry_items_user_id_idx ON pantry_items(user_id);
CREATE INDEX IF NOT EXISTS pantry_items_category_idx ON pantry_items(category);

-- Create trigger for updating the updated_at timestamp
CREATE OR REPLACE FUNCTION update_pantry_items_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_pantry_items_updated_at
BEFORE UPDATE ON pantry_items
FOR EACH ROW
EXECUTE FUNCTION update_pantry_items_updated_at();

-- Grant permissions to authenticated users
GRANT ALL ON pantry_items TO authenticated;
