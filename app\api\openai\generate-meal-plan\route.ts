import { NextRequest, NextResponse } from 'next/server';
import { generateMealPlanWithAI } from '@/lib/api/openai';
import { MealGenerationOptions } from '@/types/new-meal-plan';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const options: MealGenerationOptions = body.options;
    
    if (!options) {
      return NextResponse.json(
        { error: 'Missing required parameter: options' },
        { status: 400 }
      );
    }
    
    const mealPlan = await generateMealPlanWithAI(options);
    
    return NextResponse.json(mealPlan);
  } catch (error: any) {
    console.error('Error in OpenAI generate meal plan API route:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while generating the meal plan' },
      { status: 500 }
    );
  }
}
