'use client';

import { useEffect, useState } from 'react';
import { UserNav } from './UserNav';
import { Navigation } from './Navigation';
import { formatDate } from '@/lib/utils';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';

export function DashboardHeader() {
  const [greeting, setGreeting] = useState('');
  const [userName, setUserName] = useState('');
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    const getTimeBasedGreeting = () => {
      const hour = new Date().getHours();
      if (hour < 12) return 'Good morning';
      if (hour < 18) return 'Good afternoon';
      return 'Good evening';
    };
    setGreeting(getTimeBasedGreeting());

    const fetchUserProfile = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data: profile } = await supabase
          .from('users')
          .select('name')
          .eq('id', user.id)
          .single();
        
        if (profile?.name) {
          setUserName(profile.name);
        }
      }
    };

    fetchUserProfile();
  }, [supabase]);

  return (
    <header className="border-b">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold">{greeting}{userName && `, ${userName}`}</h1>
            <p className="text-muted-foreground">{formatDate(new Date())}</p>
          </div>
          <div className="flex items-center space-x-4">
            <Navigation />
            <UserNav />
          </div>
        </div>
      </div>
    </header>
  );
}

