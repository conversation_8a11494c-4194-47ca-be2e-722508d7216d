'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Calendar } from "lucide-react";
import { useSupabase } from "@/components/supabase-provider";
import { useState, useEffect } from "react";

interface PlanSummaryProps {
  isLoading?: boolean;
}

interface PlanSummaryData {
  totalCost: number;
  averageCalories: number;
  daysRemaining: number;
  startDate: string;
  endDate: string;
}

export function PlanSummary({ isLoading = false }: PlanSummaryProps) {
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const [planData, setPlanData] = useState<PlanSummaryData | null>(null);
  const [loadingData, setLoadingData] = useState(true);

  useEffect(() => {
    // Don't fetch if Supabase is still loading or not available
    if (isSupabaseLoading || !supabase) {
      return;
    }

    const fetchPlanSummary = async () => {
      try {
        setLoadingData(true);

        // Get current user
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError || !user) {
          console.log('User not authenticated, using mock data');
          setPlanData(mockPlanData);
          return;
        }

        // Get current date
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // Format date for query
        const todayStr = today.toISOString().split('T')[0];

        // Fetch active meal plan
        const { data, error } = await supabase
          .from('meal_plans')
          .select('*')
          .eq('user_id', user.id)
          .eq('status', 'active')
          .lte('start_date', todayStr)
          .gte('end_date', todayStr)
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        if (error) {
          console.log('No active meal plan found, using mock data');
          setPlanData(mockPlanData);
          return;
        }

        if (data) {
          // Calculate days remaining
          const endDate = new Date(data.end_date);
          const diffTime = Math.abs(endDate.getTime() - today.getTime());
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          // Extract data from meal plan
          const mealPlanData = data.meal_data;

          setPlanData({
            totalCost: mealPlanData?.mealPlan?.summary?.totalCost || 0,
            averageCalories: mealPlanData?.mealPlan?.summary?.averageCalories || 0,
            daysRemaining: diffDays,
            startDate: data.start_date,
            endDate: data.end_date
          });
        } else {
          setPlanData(mockPlanData);
        }
      } catch (error) {
        console.error('Error fetching plan summary:', error);
        setPlanData(mockPlanData);
      } finally {
        setLoadingData(false);
      }
    };

    fetchPlanSummary();
  }, [supabase, isSupabaseLoading]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  if (isLoading || isSupabaseLoading || loadingData || !planData) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5 text-primary" />
            <CardTitle>Plan Summary</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-4 w-3/4" />
            <div className="grid grid-cols-2 gap-2">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
            </div>
            <Skeleton className="h-4 w-1/2" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center space-x-2">
          <Calendar className="h-5 w-5 text-primary" />
          <CardTitle>Plan Summary</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            {formatDate(planData.startDate)} - {formatDate(planData.endDate)}
          </p>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Daily Average</p>
              <p className="text-xl font-bold">{Math.round(planData.averageCalories)} kcal</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Weekly Cost</p>
              <p className="text-xl font-bold">${planData.totalCost.toFixed(2)}</p>
            </div>
          </div>

          <p className="text-sm font-medium">
            {planData.daysRemaining} days remaining
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

// Mock data for when no plan is found
const mockPlanData: PlanSummaryData = {
  totalCost: 89.50,
  averageCalories: 2100,
  daysRemaining: 5,
  startDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
  endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString()
};
