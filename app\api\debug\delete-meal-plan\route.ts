import { NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

export async function DELETE(request: Request) {
  try {
    // Only allow this endpoint in development mode
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: "This endpoint is only available in development mode" },
        { status: 403 }
      );
    }

    // Get the meal plan ID from the URL
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: "Meal plan ID is required" },
        { status: 400 }
      );
    }

    console.log('Debug delete meal plan API called with ID:', id);

    // Create a Supabase admin client
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Delete the meal plan
    const { error } = await supabaseAdmin
      .from('meal_plans')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting meal plan:', error);
      return NextResponse.json(
        {
          error: "Failed to delete meal plan",
          details: error.message,
          code: error.code
        },
        { status: 500 }
      );
    }

    // Add cache control headers to prevent caching
    const response = NextResponse.json({
      success: true,
      message: "Meal plan deleted successfully"
    });
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    return response;
  } catch (error) {
    console.error("Unexpected error in debug/delete-meal-plan API:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
