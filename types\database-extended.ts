// Extended database types for new tables
export interface FavoriteMeal {
  id: string;
  user_id: string;
  recipe_id: string;
  created_at: string;
}

export interface MealNote {
  id: string;
  user_id: string;
  meal_plan_id: string;
  meal_id: string;
  note: string;
  created_at: string;
  updated_at: string;
}

export interface UserPreferences {
  id: string;
  user_id: string;
  cooking_skill_level: 'beginner' | 'intermediate' | 'advanced';
  max_cooking_time: number; // minutes
  preferred_cuisines: string[];
  excluded_ingredients: string[];
  notification_preferences: {
    email: boolean;
    push: boolean;
    meal_reminders: boolean;
  };
  privacy_settings: {
    data_sharing: boolean;
    analytics: boolean;
  };
  units_preference: 'metric' | 'imperial';
  currency_preference: string;
  created_at: string;
  updated_at: string;
}

export interface SearchHistory {
  id: string;
  user_id: string;
  search_query: string;
  search_type: 'recipe' | 'ingredient' | 'cuisine';
  results_count: number;
  created_at: string;
}

export interface MealPlanAssignment {
  id: string;
  user_id: string;
  meal_plan_id: string;
  assigned_date: string;
  created_at: string;
  updated_at: string;
}

export interface MealCompletion {
  id: string;
  user_id: string;
  meal_plan_id: string;
  meal_id: string;
  status: 'completed' | 'skipped' | 'pending';
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

// Extended user type with preferences
export interface ExtendedUser {
  id: string;
  email: string;
  weekly_budget?: number;
  household_size?: number;
  dietary_restrictions?: string[];
  created_at: string;
  updated_at: string;
  preferences?: UserPreferences;
}

// API response types
export interface CreateFavoriteMealRequest {
  recipe_id: string;
}

export interface CreateMealNoteRequest {
  meal_plan_id: string;
  meal_id: string;
  note: string;
}

export interface UpdateUserPreferencesRequest {
  cooking_skill_level?: 'beginner' | 'intermediate' | 'advanced';
  max_cooking_time?: number;
  preferred_cuisines?: string[];
  excluded_ingredients?: string[];
  notification_preferences?: {
    email?: boolean;
    push?: boolean;
    meal_reminders?: boolean;
  };
  privacy_settings?: {
    data_sharing?: boolean;
    analytics?: boolean;
  };
  units_preference?: 'metric' | 'imperial';
  currency_preference?: string;
}

export interface CreateSearchHistoryRequest {
  search_query: string;
  search_type: 'recipe' | 'ingredient' | 'cuisine';
  results_count: number;
}

export interface AssignMealPlanRequest {
  meal_plan_id: string;
  assigned_date: string;
}

export interface UpdateMealCompletionRequest {
  meal_plan_id: string;
  meal_id: string;
  status: 'completed' | 'skipped' | 'pending';
}
