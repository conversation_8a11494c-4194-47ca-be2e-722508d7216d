// This script applies SQL directly to your Supabase project
// Run this script with: node scripts/apply_sql_direct.js

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Supabase connection details
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service role key not found in environment variables.');
  console.error('Make sure you have a .env file with NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY.');
  process.exit(1);
}

// Create Supabase client with service role key (has admin privileges)
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applySqlDirect() {
  try {
    console.log('Applying SQL directly to Supabase...');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../supabase/migrations/20230802000000_create_disable_rls_function.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // Split the SQL content into individual statements
    const statements = sqlContent
      .replace(/--.*\\n/g, '') // Remove comments
      .split(';')
      .filter(stmt => stmt.trim() !== '');

    console.log(`Found ${statements.length} SQL statements to execute`);

    // Use the REST API directly to execute the SQL
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseServiceKey,
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'Prefer': 'params=single-object',
        'X-Raw-SQL': sqlContent
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error applying SQL:', errorText);
      return;
    }

    console.log('SQL applied successfully!');

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

applySqlDirect();
