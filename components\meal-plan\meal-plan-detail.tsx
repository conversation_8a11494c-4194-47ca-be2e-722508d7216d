'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { format, parseISO, addDays, isSameDay } from 'date-fns';
import {
  Calendar,
  ChevronLeft,
  Clock,
  DollarSign,
  Edit,
  Heart,
  ShoppingCart,
  Utensils,
  PieChart,
  BarChart3,
  Printer,
  Share2,
  Download,
  Info,
  AlertCircle,
  Check,
  Bookmark,
  Star,
  Flame,
  Trophy,
  TrendingUp
} from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Tooltip, TooltipContent, Toolt<PERSON>Provider, TooltipTrigger } from '@/components/ui/tooltip';
import { toast } from 'sonner';
import { AddToCalendarButton } from './add-to-calendar-button';
import { PieChart as PieChartComponent } from '@/components/charts/pie-chart';
import { BarChart as BarChartComponent } from '@/components/charts/bar-chart';
import { MealCompletionTracker } from './meal-completion-tracker';

interface MealPlan {
  id: string;
  user_id: string;
  start_date: string;
  end_date: string;
  total_cost: number;
  meal_data: any;
  status: string;
  created_at: string;
  updated_at: string;
  name?: string;
  description?: string;
}

interface MealPlanDetailProps {
  plan: MealPlan;
  onBack: () => void;
  onEdit?: (plan: MealPlan) => void;
  onAddToCalendar?: (plan: MealPlan) => void;
  onGenerateShoppingList?: (plan: MealPlan) => void;
  onShare?: (plan: MealPlan) => void;
  onPrint?: (plan: MealPlan) => void;
  onDownload?: (plan: MealPlan) => void;
  onSwapMeal?: (mealId: string, date: string, mealType: string) => void;
}

export function MealPlanDetail({
  plan,
  onBack,
  onEdit,
  onAddToCalendar,
  onGenerateShoppingList,
  onShare,
  onPrint,
  onDownload,
  onSwapMeal
}: MealPlanDetailProps) {
  const [isFavorite, setIsFavorite] = useState(false);
  const [activeDay, setActiveDay] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('meals');
  const [expandedMeal, setExpandedMeal] = useState<string | null>(null);
  const [nutritionView, setNutritionView] = useState<'chart' | 'table'>('chart');
  const [showNutritionDetails, setShowNutritionDetails] = useState(false);
  const [showProgressTab, setShowProgressTab] = useState(false);

  const startDate = parseISO(plan.start_date);
  const endDate = parseISO(plan.end_date);

  const handleFavoriteToggle = () => {
    setIsFavorite(!isFavorite);
    toast.success(isFavorite ? 'Removed from favorites' : 'Added to favorites');
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(plan);
    } else {
      toast('Edit functionality coming soon');
    }
  };

  const handleAddToCalendar = () => {
    if (onAddToCalendar) {
      onAddToCalendar(plan);
    } else {
      toast('Calendar integration coming soon');
    }
  };

  const handleGenerateShoppingList = () => {
    if (onGenerateShoppingList) {
      onGenerateShoppingList(plan);
    } else {
      // Show loading toast
      toast('Generating shopping list...');

      // Import dynamically to reduce initial load time
      import('@/app/services/meal-plan-service').then(({ mealPlanService }) => {
        mealPlanService.generateShoppingList(plan.id)
          .then(({ data, error }) => {
            if (error) {
              toast(`Failed to generate shopping list: ${error}`);
              return;
            }

            if (data) {
              toast.success('Shopping list generated successfully');
              // Redirect to the shopping list page using the dynamic route
              window.location.href = `/shopping-list/${data.id}`;
            }
          })
          .catch(err => {
            console.error('Error generating shopping list:', err);
            toast(`Error: ${err.message || 'Failed to generate shopping list'}`);
          });
      });
    }
  };

  const handleShare = () => {
    if (onShare) {
      onShare(plan);
    } else {
      // Fallback to Web Share API if available
      if (navigator.share) {
        navigator.share({
          title: plan.name || `Meal Plan (${format(startDate, 'MMM d')})`,
          text: `Check out my meal plan: ${plan.name || `Meal Plan (${format(startDate, 'MMM d')})`}`,
          url: window.location.href,
        })
        .then(() => toast.success('Shared successfully'))
        .catch((error) => {
          console.error('Error sharing:', error);
          toast.error('Failed to share');
        });
      } else {
        // Copy link to clipboard as fallback
        navigator.clipboard.writeText(window.location.href)
          .then(() => toast.success('Link copied to clipboard'))
          .catch(() => toast.error('Failed to copy link'));
      }
    }
  };

  const handlePrint = () => {
    if (onPrint) {
      onPrint(plan);
    } else {
      window.print();
    }
  };

  const handleDownload = () => {
    if (onDownload) {
      onDownload(plan);
    } else {
      // Create a simple JSON file for download
      const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(plan, null, 2));
      const downloadAnchorNode = document.createElement('a');
      downloadAnchorNode.setAttribute("href", dataStr);
      downloadAnchorNode.setAttribute("download", `${plan.name || 'meal-plan'}.json`);
      document.body.appendChild(downloadAnchorNode);
      downloadAnchorNode.click();
      downloadAnchorNode.remove();
      toast.success('Meal plan downloaded');
    }
  };

  const handleSwapMeal = (mealId: string, date: string, mealType: string) => {
    if (onSwapMeal) {
      onSwapMeal(mealId, date, mealType);
    } else {
      toast('Meal swapping coming soon');
    }
  };

  const toggleMealExpansion = (mealId: string) => {
    if (expandedMeal === mealId) {
      setExpandedMeal(null);
    } else {
      setExpandedMeal(mealId);
    }
  };

  // Extract meal plan data
  const mealPlanData = plan.meal_data?.mealPlan || {};
  const weekData = mealPlanData.week || [];
  const nutritionSummary = mealPlanData.nutritionSummary || {
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0,
    sugar: 0,
    sodium: 0
  };

  // Set active day to first day if not set
  useEffect(() => {
    if (!activeDay && weekData.length > 0) {
      setActiveDay(weekData[0].date);
    }
  }, [activeDay, weekData]);

  // Get meals for active day
  const activeDayMeals = weekData.find(day => day.date === activeDay)?.meals || [];

  // Calculate daily nutrition averages
  const daysCount = weekData.length || 7;
  const dailyAverage = {
    calories: Math.round(nutritionSummary.calories / daysCount),
    protein: Math.round(nutritionSummary.protein / daysCount),
    carbs: Math.round(nutritionSummary.carbs / daysCount),
    fat: Math.round(nutritionSummary.fat / daysCount),
    fiber: Math.round((nutritionSummary.fiber || 0) / daysCount),
    sugar: Math.round((nutritionSummary.sugar || 0) / daysCount),
    sodium: Math.round((nutritionSummary.sodium || 0) / daysCount)
  };

  // Calculate macronutrient percentages
  const totalCaloriesFromMacros =
    (dailyAverage.protein * 4) +
    (dailyAverage.carbs * 4) +
    (dailyAverage.fat * 9);

  const macroPercentages = {
    protein: Math.round((dailyAverage.protein * 4 / totalCaloriesFromMacros) * 100) || 0,
    carbs: Math.round((dailyAverage.carbs * 4 / totalCaloriesFromMacros) * 100) || 0,
    fat: Math.round((dailyAverage.fat * 9 / totalCaloriesFromMacros) * 100) || 0
  };

  // Prepare data for pie chart
  const macroChartData = [
    { name: 'Protein', value: macroPercentages.protein, color: 'hsl(var(--blue-500))' },
    { name: 'Carbs', value: macroPercentages.carbs, color: 'hsl(var(--green-500))' },
    { name: 'Fat', value: macroPercentages.fat, color: 'hsl(var(--yellow-500))' }
  ];

  // Prepare data for bar chart (daily calories)
  const caloriesByDay = weekData.map(day => ({
    name: format(parseISO(day.date), 'EEE'),
    value: day.meals.reduce((sum, meal) => sum + (meal.calories || 0), 0),
    target: 2000 // Target daily calories (could be customized per user)
  }));

  // Get meal types for the plan
  const mealTypes = Array.from(
    new Set(weekData.flatMap(day => day.meals.map(meal => meal.type)))
  );

  // Calculate nutrition by meal type
  const nutritionByMealType = mealTypes.map(type => {
    const meals = weekData.flatMap(day =>
      day.meals.filter(meal => meal.type === type)
    );

    const totalMeals = meals.length;
    const avgCalories = Math.round(meals.reduce((sum, meal) => sum + (meal.calories || 0), 0) / totalMeals) || 0;
    const avgProtein = Math.round(meals.reduce((sum, meal) => sum + (meal.protein || 0), 0) / totalMeals) || 0;
    const avgCarbs = Math.round(meals.reduce((sum, meal) => sum + (meal.carbs || 0), 0) / totalMeals) || 0;
    const avgFat = Math.round(meals.reduce((sum, meal) => sum + (meal.fat || 0), 0) / totalMeals) || 0;

    return {
      type,
      avgCalories,
      avgProtein,
      avgCarbs,
      avgFat
    };
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="flex items-center">
          <Button variant="ghost" onClick={onBack} className="mr-2">
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              {plan.name || `Meal Plan (${format(startDate, 'MMM d')})`}
            </h1>
            <p className="text-muted-foreground">
              {format(startDate, 'MMMM d')} - {format(endDate, 'MMMM d, yyyy')}
            </p>
          </div>
        </div>

        <div className="flex flex-wrap items-center gap-2 justify-end">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={isFavorite ? "default" : "outline"}
                  size="icon"
                  onClick={handleFavoriteToggle}
                >
                  <Heart className={`h-4 w-4 ${isFavorite ? "fill-primary" : ""}`} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isFavorite ? "Remove from favorites" : "Add to favorites"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleShare}
                >
                  <Share2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Share meal plan</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handlePrint}
                >
                  <Printer className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Print meal plan</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleDownload}
                >
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Download meal plan</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Button
            variant="outline"
            size="sm"
            onClick={handleEdit}
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-6">
        <div className="md:w-2/3 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Meal Plan Overview</CardTitle>
              <CardDescription>
                {weekData.length} days of planned meals
              </CardDescription>
            </CardHeader>
            <CardContent>
              {plan.description && (
                <div className="bg-muted/30 p-4 rounded-lg mb-6">
                  <p className="text-muted-foreground">{plan.description}</p>
                </div>
              )}

              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6">
                <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg hover:bg-muted/70 transition-colors">
                  <DollarSign className="h-5 w-5 mb-1 text-primary" />
                  <span className="text-lg font-medium">${plan.total_cost.toFixed(2)}</span>
                  <span className="text-xs text-muted-foreground">Total Cost</span>
                </div>
                <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg hover:bg-muted/70 transition-colors">
                  <Clock className="h-5 w-5 mb-1 text-primary" />
                  <span className="text-lg font-medium">{mealPlanData.totalPrepTime || '--'} min</span>
                  <span className="text-xs text-muted-foreground">Total Prep Time</span>
                </div>
                <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg hover:bg-muted/70 transition-colors">
                  <Utensils className="h-5 w-5 mb-1 text-primary" />
                  <span className="text-lg font-medium">{mealPlanData.meals?.length || weekData.reduce((sum, day) => sum + day.meals.length, 0)}</span>
                  <span className="text-xs text-muted-foreground">Total Meals</span>
                </div>
                <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg hover:bg-muted/70 transition-colors">
                  <Flame className="h-5 w-5 mb-1 text-primary" />
                  <span className="text-lg font-medium">{dailyAverage.calories}</span>
                  <span className="text-xs text-muted-foreground">Avg Cal/Day</span>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-sm font-medium mb-2">Dietary Preferences</h3>
                <div className="flex flex-wrap gap-2">
                  {plan.meal_data?.dietaryPreferences?.length > 0 ? (
                    plan.meal_data.dietaryPreferences.map((pref: string, index: number) => (
                      <Badge key={index} variant="secondary" className="bg-primary/10">
                        {pref}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-sm text-muted-foreground">No specific dietary preferences</span>
                  )}
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-sm font-medium mb-2">Macronutrient Breakdown</h3>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex gap-4">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                      <span className="text-sm">Protein {macroPercentages.protein}%</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                      <span className="text-sm">Carbs {macroPercentages.carbs}%</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                      <span className="text-sm">Fat {macroPercentages.fat}%</span>
                    </div>
                  </div>
                </div>
                <div className="flex h-2 w-full rounded-full overflow-hidden">
                  <div
                    className="bg-blue-500 h-full"
                    style={{ width: `${macroPercentages.protein}%` }}
                  />
                  <div
                    className="bg-green-500 h-full"
                    style={{ width: `${macroPercentages.carbs}%` }}
                  />
                  <div
                    className="bg-yellow-500 h-full"
                    style={{ width: `${macroPercentages.fat}%` }}
                  />
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                <AddToCalendarButton
                  mealPlan={plan}
                  onClick={handleAddToCalendar}
                  className="flex-1 sm:flex-none"
                />

                <Button
                  variant="outline"
                  className="flex-1 sm:flex-none"
                  onClick={handleGenerateShoppingList}
                >
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Generate Shopping List
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Weekly Meal Schedule</CardTitle>
                  <CardDescription>
                    View your meals for each day of the week
                  </CardDescription>
                </div>
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-auto">
                  <TabsList className="grid w-[270px] grid-cols-3">
                    <TabsTrigger value="meals">Meals</TabsTrigger>
                    <TabsTrigger value="nutrition">Nutrition</TabsTrigger>
                    <TabsTrigger value="progress">Progress</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </CardHeader>
            <CardContent>
              <TabsContent value="meals" className="mt-0">
                <div className="mb-6">
                  <Tabs defaultValue={activeDay || ''} onValueChange={setActiveDay}>
                    <ScrollArea className="w-full">
                      <TabsList className="mb-4 w-full justify-start">
                        {weekData.map((day) => {
                          const dayDate = parseISO(day.date);
                          const isToday = isSameDay(dayDate, new Date());
                          return (
                            <TabsTrigger
                              key={day.date}
                              value={day.date}
                              className={isToday ? 'bg-primary/10 text-primary' : ''}
                            >
                              <div className="flex flex-col items-center">
                                <span className="text-xs">{format(dayDate, 'EEE')}</span>
                                <span className={`text-sm font-medium ${isToday ? 'text-primary' : ''}`}>
                                  {format(dayDate, 'MMM d')}
                                </span>
                              </div>
                            </TabsTrigger>
                          );
                        })}
                      </TabsList>
                    </ScrollArea>

                    {weekData.map((day) => (
                      <TabsContent key={day.date} value={day.date} className="mt-0">
                        <div className="space-y-4">
                        {day.meals.length > 0 ? day.meals.map((meal, index) => (
                          <Card key={`${meal.id || index}`} className={expandedMeal === (meal.id || `meal-${index}`) ? 'border-primary' : ''}>
                            <CardContent className="p-4">
                              <div className="flex flex-col sm:flex-row gap-4">
                                <div className="sm:w-1/4">
                                  <div className="aspect-video rounded-md overflow-hidden bg-muted">
                                    {meal.image ? (
                                      <img
                                        src={meal.image}
                                        alt={meal.name}
                                        className="w-full h-full object-cover"
                                      />
                                    ) : (
                                      <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                                        No image
                                      </div>
                                    )}
                                  </div>
                                </div>

                                <div className="sm:w-3/4">
                                  <div className="flex justify-between items-start">
                                    <div>
                                      <h3 className="font-medium">{meal.name}</h3>
                                      <div className="flex items-center gap-2">
                                        <Badge variant="outline" className="text-xs">{meal.type}</Badge>
                                        {meal.tags?.map((tag: string, i: number) => (
                                          <Badge key={i} variant="secondary" className="text-xs">{tag}</Badge>
                                        ))}
                                      </div>
                                    </div>
                                    <Badge className="bg-primary/10 text-primary hover:bg-primary/20 transition-colors">
                                      {meal.calories} cal
                                    </Badge>
                                  </div>

                                  <Separator className="my-3" />

                                  <div className="grid grid-cols-4 gap-2 text-sm">
                                    <div>
                                      <span className="text-muted-foreground">Protein:</span>{' '}
                                      <span className="font-medium">{meal.protein}g</span>
                                    </div>
                                    <div>
                                      <span className="text-muted-foreground">Carbs:</span>{' '}
                                      <span className="font-medium">{meal.carbs}g</span>
                                    </div>
                                    <div>
                                      <span className="text-muted-foreground">Fat:</span>{' '}
                                      <span className="font-medium">{meal.fat}g</span>
                                    </div>
                                    <div>
                                      <span className="text-muted-foreground">Prep:</span>{' '}
                                      <span className="font-medium">{meal.prepTime} min</span>
                                    </div>
                                  </div>

                                  {expandedMeal === (meal.id || `meal-${index}`) && (
                                    <div className="mt-3 pt-3 border-t">
                                      <h4 className="text-sm font-medium mb-2">Ingredients</h4>
                                      <ul className="text-sm space-y-1 mb-3">
                                        {meal.ingredients ? (
                                          meal.ingredients.map((ingredient: any, i: number) => (
                                            <li key={i} className="flex items-start">
                                              <span className="mr-2">•</span>
                                              <span>
                                                {ingredient.quantity} {ingredient.unit} {ingredient.name}
                                              </span>
                                            </li>
                                          ))
                                        ) : (
                                          <li className="text-muted-foreground">No ingredients listed</li>
                                        )}
                                      </ul>

                                      {meal.instructions && (
                                        <>
                                          <h4 className="text-sm font-medium mb-2">Instructions</h4>
                                          <p className="text-sm text-muted-foreground">{meal.instructions}</p>
                                        </>
                                      )}
                                    </div>
                                  )}

                                  <div className="flex justify-between items-center mt-3">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleSwapMeal(meal.id || `meal-${index}`, day.date, meal.type)}
                                    >
                                      Swap Meal
                                    </Button>

                                    <div className="flex gap-2">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => toggleMealExpansion(meal.id || `meal-${index}`)}
                                      >
                                        {expandedMeal === (meal.id || `meal-${index}`) ? 'Hide Details' : 'View Details'}
                                      </Button>

                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <Button
                                              variant="ghost"
                                              size="icon"
                                              onClick={() => toast.success('Added to favorites')}
                                            >
                                              <Star className="h-4 w-4" />
                                            </Button>
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>Add to favorites</p>
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        )) : (
                          <div className="text-center py-8 border rounded-lg">
                            <AlertCircle className="h-12 w-12 mx-auto text-muted-foreground" />
                            <h3 className="mt-4 text-lg font-medium">No meals planned</h3>
                            <p className="mt-2 text-sm text-muted-foreground">
                              There are no meals planned for this day
                            </p>
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  ))}
                  </Tabs>
                </div>
              </TabsContent>

              <TabsContent value="nutrition" className="mt-0">
                <div className="space-y-6">
                  <div className="flex flex-col sm:flex-row gap-4 justify-between items-start">
                    <div>
                      <h3 className="text-lg font-medium">Daily Calories</h3>
                      <p className="text-sm text-muted-foreground">Calorie intake for each day of the week</p>
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowNutritionDetails(!showNutritionDetails)}
                    >
                      {showNutritionDetails ? 'Hide Details' : 'Show Details'}
                    </Button>
                  </div>

                  <div className="h-[200px] w-full">
                    <BarChartComponent
                      data={caloriesByDay}
                      showYAxis={true}
                      showTarget={true}
                    />
                  </div>

                  {showNutritionDetails && (
                    <div className="space-y-4">
                      <Separator />

                      <div>
                        <h3 className="text-lg font-medium mb-4">Nutrition by Meal Type</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {nutritionByMealType.map((mealType) => (
                            <Card key={mealType.type}>
                              <CardHeader className="py-3">
                                <CardTitle className="text-base">{mealType.type}</CardTitle>
                              </CardHeader>
                              <CardContent className="py-0">
                                <div className="space-y-2">
                                  <div>
                                    <div className="flex justify-between mb-1">
                                      <span className="text-sm">Calories</span>
                                      <span className="text-sm font-medium">{mealType.avgCalories} kcal</span>
                                    </div>
                                    <Progress value={(mealType.avgCalories / 800) * 100} className="h-2" />
                                  </div>
                                  <div>
                                    <div className="flex justify-between mb-1">
                                      <span className="text-sm">Protein</span>
                                      <span className="text-sm font-medium">{mealType.avgProtein}g</span>
                                    </div>
                                    <Progress value={(mealType.avgProtein / 30) * 100} className="h-2 bg-blue-100 dark:bg-blue-900">
                                      <div className="h-full bg-blue-500" style={{ width: `${(mealType.avgProtein / 30) * 100}%` }} />
                                    </Progress>
                                  </div>
                                  <div>
                                    <div className="flex justify-between mb-1">
                                      <span className="text-sm">Carbs</span>
                                      <span className="text-sm font-medium">{mealType.avgCarbs}g</span>
                                    </div>
                                    <Progress value={(mealType.avgCarbs / 100) * 100} className="h-2 bg-green-100 dark:bg-green-900">
                                      <div className="h-full bg-green-500" style={{ width: `${(mealType.avgCarbs / 100) * 100}%` }} />
                                    </Progress>
                                  </div>
                                  <div>
                                    <div className="flex justify-between mb-1">
                                      <span className="text-sm">Fat</span>
                                      <span className="text-sm font-medium">{mealType.avgFat}g</span>
                                    </div>
                                    <Progress value={(mealType.avgFat / 30) * 100} className="h-2 bg-yellow-100 dark:bg-yellow-900">
                                      <div className="h-full bg-yellow-500" style={{ width: `${(mealType.avgFat / 30) * 100}%` }} />
                                    </Progress>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="progress" className="mt-0">
                <MealCompletionTracker
                  mealPlan={plan}
                  onUpdate={() => {
                    toast.success("Progress updated");
                  }}
                />
              </TabsContent>
            </CardContent>
          </Card>
        </div>

        <div className="md:w-1/3 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Nutritional Information</CardTitle>
              <CardDescription>
                Average daily nutritional values
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center mb-6">
                <div className="w-40 h-40">
                  <PieChartComponent data={macroChartData} />
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-primary mr-2"></div>
                      Calories
                    </span>
                    <span className="font-medium">{dailyAverage.calories} kcal</span>
                  </div>
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full bg-primary rounded-full"
                      style={{ width: `${Math.min(100, dailyAverage.calories / 2500 * 100)}%` }}
                    />
                  </div>
                  <div className="flex justify-between mt-1 text-xs text-muted-foreground">
                    <span>0</span>
                    <span>Target: 2000 kcal</span>
                    <span>3000+</span>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between mb-1">
                    <span className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                      Protein
                    </span>
                    <span className="font-medium">{dailyAverage.protein}g</span>
                  </div>
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full bg-blue-500 rounded-full"
                      style={{ width: `${Math.min(100, dailyAverage.protein / 100 * 100)}%` }}
                    />
                  </div>
                  <div className="flex justify-between mt-1 text-xs text-muted-foreground">
                    <span>0</span>
                    <span>Target: 50g</span>
                    <span>100g+</span>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between mb-1">
                    <span className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                      Carbohydrates
                    </span>
                    <span className="font-medium">{dailyAverage.carbs}g</span>
                  </div>
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full bg-green-500 rounded-full"
                      style={{ width: `${Math.min(100, dailyAverage.carbs / 300 * 100)}%` }}
                    />
                  </div>
                  <div className="flex justify-between mt-1 text-xs text-muted-foreground">
                    <span>0</span>
                    <span>Target: 250g</span>
                    <span>400g+</span>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between mb-1">
                    <span className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                      Fat
                    </span>
                    <span className="font-medium">{dailyAverage.fat}g</span>
                  </div>
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full bg-yellow-500 rounded-full"
                      style={{ width: `${Math.min(100, dailyAverage.fat / 70 * 100)}%` }}
                    />
                  </div>
                  <div className="flex justify-between mt-1 text-xs text-muted-foreground">
                    <span>0</span>
                    <span>Target: 55g</span>
                    <span>100g+</span>
                  </div>
                </div>

                {(dailyAverage.fiber > 0 || dailyAverage.sugar > 0 || dailyAverage.sodium > 0) && (
                  <div className="pt-2 mt-2 border-t">
                    <h4 className="text-sm font-medium mb-3">Additional Nutrients</h4>

                    {dailyAverage.fiber > 0 && (
                      <div className="flex justify-between mb-2">
                        <span className="text-sm">Fiber</span>
                        <span className="text-sm font-medium">{dailyAverage.fiber}g</span>
                      </div>
                    )}

                    {dailyAverage.sugar > 0 && (
                      <div className="flex justify-between mb-2">
                        <span className="text-sm">Sugar</span>
                        <span className="text-sm font-medium">{dailyAverage.sugar}g</span>
                      </div>
                    )}

                    {dailyAverage.sodium > 0 && (
                      <div className="flex justify-between mb-2">
                        <span className="text-sm">Sodium</span>
                        <span className="text-sm font-medium">{dailyAverage.sodium}mg</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Meal Types</CardTitle>
              <CardDescription>
                Breakdown by meal type
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {nutritionByMealType.map((mealType) => (
                  <div key={mealType.type} className="pb-3 last:pb-0 last:border-0 border-b">
                    <h3 className="font-medium mb-1">{mealType.type}</h3>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-muted-foreground">Avg. Calories:</span>
                      <span>{mealType.avgCalories} kcal</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Protein / Carbs / Fat:</span>
                      <span>{mealType.avgProtein}g / {mealType.avgCarbs}g / {mealType.avgFat}g</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Calendar</CardTitle>
              <CardDescription>
                View when this meal plan is scheduled
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center">
                <Calendar
                  mode="range"
                  selected={{
                    from: startDate,
                    to: endDate
                  }}
                  className="rounded-md border"
                  disabled
                />
              </div>

              <Button
                variant="outline"
                className="w-full mt-4"
                onClick={handleAddToCalendar}
              >
                <Calendar className="h-4 w-4 mr-2" />
                Add to Calendar
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
