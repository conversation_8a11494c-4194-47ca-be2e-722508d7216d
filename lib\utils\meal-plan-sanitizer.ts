"use client";

import { Me<PERSON>, <PERSON>al<PERSON><PERSON> } from '@/types/new-meal-plan';

/**
 * Sanitizes a meal plan to ensure it can be safely stored in the database
 * and retrieved later.
 * 
 * This function:
 * 1. Ensures all required fields are present
 * 2. Normalizes field names
 * 3. Removes any circular references
 * 4. Limits the size of large fields
 * 5. Ensures all data is serializable
 */
export function sanitizeMealPlan(mealPlan: MealPlan): MealPlan {
  if (!mealPlan) return {};
  
  const sanitizedMealPlan: MealPlan = {};
  
  // Process each day in the meal plan
  Object.keys(mealPlan).forEach(day => {
    sanitizedMealPlan[day] = {};
    
    // Process each meal type in the day
    Object.keys(mealPlan[day]).forEach(mealType => {
      if (mealPlan[day][mealType]) {
        sanitizedMealPlan[day][mealType] = sanitizeMeal(mealPlan[day][mealType]);
      }
    });
    
    // Remove the day if it has no meals
    if (Object.keys(sanitizedMealPlan[day]).length === 0) {
      delete sanitizedMealPlan[day];
    }
  });
  
  return sanitizedMealPlan;
}

/**
 * Sanitizes a meal to ensure it can be safely stored in the database
 * and retrieved later.
 */
export function sanitizeMeal(meal: Meal): Meal {
  if (!meal) return null as any;
  
  // Create a new object with only the fields we need
  const sanitizedMeal: Meal = {
    id: meal.id || `meal-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    name: meal.name || 'Unnamed Meal',
    description: meal.description || '',
    image: meal.image || '',
    ingredients: sanitizeIngredients(meal.ingredients || []),
    instructions: Array.isArray(meal.instructions) 
      ? meal.instructions.map(i => String(i).substring(0, 500)) 
      : [],
    nutrition: {
      protein: typeof meal.nutrition?.protein === 'number' ? meal.nutrition.protein : 0,
      carbs: typeof meal.nutrition?.carbs === 'number' ? meal.nutrition.carbs : 0,
      fat: typeof meal.nutrition?.fat === 'number' ? meal.nutrition.fat : 0
    },
    calories: typeof meal.calories === 'number' ? meal.calories : 0,
    cost: typeof meal.cost === 'number' ? meal.cost : 0,
    servings: typeof meal.servings === 'number' ? meal.servings : 1,
    prepTime: typeof meal.prepTime === 'number' ? meal.prepTime : 0,
    cookTime: typeof meal.cookTime === 'number' ? meal.cookTime : 0,
    status: meal.status || null,
    favorite: !!meal.favorite,
    notes: meal.notes || ''
  };
  
  return sanitizedMeal;
}

/**
 * Sanitizes an array of ingredients to ensure they can be safely stored
 * in the database and retrieved later.
 */
function sanitizeIngredients(ingredients: any[]): any[] {
  if (!Array.isArray(ingredients)) return [];
  
  return ingredients.map(ingredient => {
    // Handle different property naming conventions
    const name = ingredient.name || ingredient.food || '';
    const quantity = ingredient.quantity || ingredient.amount || '1';
    const unit = ingredient.unit || ingredient.measure || '';
    
    return {
      name: String(name).substring(0, 100),
      quantity: String(quantity),
      unit: String(unit).substring(0, 20)
    };
  });
}

/**
 * Checks if a meal plan is valid and can be safely stored in the database.
 */
export function isMealPlanValid(mealPlan: MealPlan): boolean {
  if (!mealPlan || typeof mealPlan !== 'object') return false;
  
  // Check if the meal plan has at least one day
  if (Object.keys(mealPlan).length === 0) return false;
  
  // Check if each day has at least one meal
  for (const day in mealPlan) {
    if (Object.keys(mealPlan[day]).length === 0) return false;
    
    // Check if each meal has the required fields
    for (const mealType in mealPlan[day]) {
      const meal = mealPlan[day][mealType];
      if (!meal || !meal.id || !meal.name) return false;
    }
  }
  
  return true;
}

/**
 * Estimates the size of a meal plan in bytes.
 */
export function estimateMealPlanSize(mealPlan: MealPlan): number {
  return new TextEncoder().encode(JSON.stringify(mealPlan)).length;
}
