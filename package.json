{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start", "lint": "next lint", "setup": "node scripts/setup.js", "clean": "rm -rf .next node_modules", "prisma:generate": "prisma generate", "prisma:push": "prisma db push", "verify-imports": "ts-node --project scripts/tsconfig.json scripts/verify-imports.ts", "prebuild": "npm run verify-imports", "populate-recipes": "node scripts/populate-recipes.js", "populate-recipes-updated": "node scripts/populate-recipes-updated.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.3.0", "@next/swc-wasm-nodejs": "13.5.1", "@prisma/client": "latest", "@radix-ui/primitive": "^1.1.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.2.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.10", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.5", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.7", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/auth-helpers-nextjs": "latest", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.71.5", "@tanstack/react-query-devtools": "^5.71.5", "@types/pg": "^8.11.11", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "buffer": "^6.0.3", "bufferutil": "^4.0.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "cmdk": "^0.2.0", "crypto-browserify": "^3.12.0", "d3": "^7.9.0", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "embla-carousel-react": "^8.0.0", "encoding": "^0.1.13", "framer-motion": "^10.18.0", "logger": "^0.0.1", "lucide-react": "^0.263.0", "next": "latest", "next-themes": "^0.2.1", "node-fetch": "^3.3.2", "openai": "^4.87.3", "pg": "^8.14.1", "react": "latest", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "latest", "react-hook-form": "^7.45.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.0.0", "recharts": "^2.15.1", "schema": "^0.2.1", "shadcn": "^2.4.0-canary.17", "sonner": "^0.6.0", "stream-browserify": "^3.0.0", "supabase": "^2.19.7", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.3.3", "tailwindcss-animate": "^1.0.7", "utf-8-validate": "^6.0.3", "zod": "^3.21.4", "zustand": "^4.5.6"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/bcryptjs": "^2.4.6", "@types/d3": "^7.4.3", "@types/glob": "^8.1.0", "@types/node": "latest", "@types/react": "latest", "@types/react-dom": "latest", "@typescript-eslint/eslint-plugin": "latest", "@typescript-eslint/parser": "latest", "autoprefixer": "^10.4.14", "eslint": "latest", "eslint-config-next": "latest", "glob": "^11.0.1", "jest": "^29.7.0", "node-mocks-http": "^1.16.2", "postcss": "^8.4.27", "prettier": "^3.2.0", "prisma": "latest", "ts-node": "^10.9.2", "typescript": "latest", "winston": "^3.10.0"}, "peerDependencies": {"@opentelemetry/api": "^1.7.0"}}