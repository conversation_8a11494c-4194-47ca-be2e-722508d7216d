import axios, { AxiosError } from 'axios';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, GroceryList, MealPreferences } from '../types/mealPlanner';

const api = axios.create({
  baseURL: '/api/meal-planner',
  headers: {
    'Content-Type': 'application/json',
  },
});

export const mealPlannerService = {
  getTodaysMeals: async (): Promise<Meal[]> => {
    try {
      const response = await api.get<{ data: { meals: Meal[] } }>('/today');
      return response.data.data.meals;
    } catch (error) {
      console.error('[Service Error] getTodaysMeals:', error);
      if (axios.isAxiosError(error)) {
        throw new Error(error.response?.data?.error || 'Failed to fetch today\'s meals');
      }
      throw new Error('An unexpected error occurred');
    }
  },

  // Fetch weekly meal plan
  getWeeklyPlan: async (): Promise<DayPlan[]> => {
    try {
      const { data } = await api.get<{ data: { weeklyPlan: DayPlan[] } }>('/weekly');
      return data.data.weeklyPlan;
    } catch (error) {
      handleApiError(error as AxiosError, 'Failed to fetch weekly meal plan');
    }
  },

  // Generate new meal plan
  generateMealPlan: async (preferences: MealPreferences): Promise<DayPlan[]> => {
    try {
      const { data } = await api.post<{ data: { weeklyPlan: DayPlan[] } }>('/generate', preferences);
      return data.data.weeklyPlan;
    } catch (error) {
      handleApiError(error as AxiosError, 'Failed to generate meal plan');
    }
  },

  // Fetch alternative meals
  getAlternativeMeals: async (mealType: string, excludeMealId: string): Promise<Meal[]> => {
    try {
      const { data } = await api.get<{ data: { alternatives: Meal[] } }>('/alternatives', {
        params: { 
          type: mealType, 
          currentMealId: excludeMealId 
        }
      });
      return data.data.alternatives;
    } catch (error) {
      handleApiError(error as AxiosError, 'Failed to fetch alternative meals');
    }
  },

  // Swap meal
  swapMeal: async (oldMealId: string, newMealId: string, date?: string): Promise<void> => {
    try {
      await api.post('/swap', {
        oldMealId,
        newMealId,
        date
      });
    } catch (error) {
      handleApiError(error as AxiosError, 'Failed to swap meal');
    }
  },

  // Get grocery list
  getGroceryList: async (): Promise<GroceryList> => {
    try {
      const { data } = await api.get<{ data: { groceryList: GroceryList } }>('/grocery-list');
      return data.data.groceryList;
    } catch (error) {
      handleApiError(error as AxiosError, 'Failed to fetch grocery list');
    }
  },

  // Toggle grocery item
  toggleGroceryItem: async (
    categoryId: string, 
    itemId: string, 
    checked: boolean
  ): Promise<void> => {
    try {
      await api.put('/grocery-list/toggle', {
        categoryId,
        itemId,
        checked
      });
    } catch (error) {
      handleApiError(error as AxiosError, 'Failed to update grocery item');
    }
  },

  // Update meal preferences
  updatePreferences: async (preferences: MealPreferences): Promise<void> => {
    try {
      await api.put('/preferences', preferences);
    } catch (error) {
      handleApiError(error as AxiosError, 'Failed to update meal preferences');
    }
  },

  // Toggle meal favorite status
  toggleFavorite: async (mealId: string): Promise<void> => {
    try {
      await api.post('/favorites/toggle', { mealId });
    } catch (error) {
      handleApiError(error as AxiosError, 'Failed to toggle favorite status');
    }
  },

  // Get favorite meals
  getFavorites: async (): Promise<Meal[]> => {
    try {
      const { data } = await api.get<{ data: { meals: Meal[] } }>('/favorites');
      return data.data.meals;
    } catch (error) {
      handleApiError(error as AxiosError, 'Failed to fetch favorite meals');
    }
  },

  // Add custom meal
  addCustomMeal: async (meal: Omit<Meal, 'id'>): Promise<Meal> => {
    try {
      const { data } = await api.post<{ data: { meal: Meal } }>('/meals/custom', meal);
      return data.data.meal;
    } catch (error) {
      handleApiError(error as AxiosError, 'Failed to add custom meal');
    }
  }
};

export default mealPlannerService;



