import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    // Create a Supabase client
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Read the diagnostic SQL file
    const sqlFilePath = path.join(process.cwd(), 'supabase', 'diagnostic', 'check_database.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Split the SQL into individual queries (each query ends with a semicolon)
    const queries = sql.split(';').filter(query => query.trim() !== '');

    const results = [];

    // Run each query and collect results
    for (let i = 0; i < queries.length; i++) {
      const query = queries[i].trim();
      if (!query) continue;

      // Extract the comment above the query to use as a title
      const commentMatch = query.match(/--\s*(.*?)(?:\r?\n|\r)/);
      const title = commentMatch ? commentMatch[1].trim() : `Query ${i + 1}`;

      try {
        // Run the query
        const { data, error } = await supabase.rpc('pgcall', { query });

        if (error) {
          results.push({
            title,
            query,
            error: error.message,
            data: null
          });
        } else {
          results.push({
            title,
            query,
            data,
            error: null
          });
        }
      } catch (error) {
        results.push({
          title,
          query,
          error: error.message,
          data: null
        });
      }
    }

    // Create output directory if it doesn't exist
    const outputDir = path.join(process.cwd(), 'supabase', 'diagnostic', 'results');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Save results to a JSON file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputFilePath = path.join(outputDir, `diagnostic_results_${timestamp}.json`);
    fs.writeFileSync(outputFilePath, JSON.stringify(results, null, 2));

    return NextResponse.json({
      message: 'Diagnostic complete',
      timestamp,
      resultsPath: outputFilePath,
      results
    });
  } catch (error) {
    console.error('Error running diagnostic:', error);
    return NextResponse.json(
      { error: 'Failed to run diagnostic' },
      { status: 500 }
    );
  }
}
