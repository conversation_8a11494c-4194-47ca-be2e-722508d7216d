'use client';

import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

interface PieChartProps {
  data: {
    name: string;
    value: number;
    color: string;
  }[];
  width?: number;
  height?: number;
  innerRadius?: number;
  outerRadius?: number;
}

export function PieChart({
  data,
  width = 200,
  height = 200,
  innerRadius = 0,
  outerRadius = 80
}: PieChartProps) {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    // Clear any existing chart
    d3.select(svgRef.current).selectAll('*').remove();

    const svg = d3.select(svgRef.current)
      .attr('width', width)
      .attr('height', height)
      .append('g')
      .attr('transform', `translate(${width / 2}, ${height / 2})`);

    // Filter out zero values
    const filteredData = data.filter(d => d.value > 0);
    
    // If no valid data, show empty circle
    if (filteredData.length === 0) {
      svg.append('circle')
        .attr('r', outerRadius)
        .attr('fill', 'none')
        .attr('stroke', '#e2e8f0')
        .attr('stroke-width', 2);
      
      svg.append('text')
        .attr('text-anchor', 'middle')
        .attr('dy', '0.35em')
        .attr('class', 'text-muted-foreground text-sm')
        .text('No data');
      
      return;
    }

    // Create pie generator
    const pie = d3.pie<any>()
      .value(d => d.value)
      .sort(null);

    // Create arc generator
    const arc = d3.arc<any>()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius);

    // Create arcs
    const arcs = svg.selectAll('.arc')
      .data(pie(filteredData))
      .enter()
      .append('g')
      .attr('class', 'arc');

    // Add path (slice)
    arcs.append('path')
      .attr('d', arc)
      .attr('fill', d => d.data.color)
      .attr('stroke', 'white')
      .attr('stroke-width', 1)
      .style('opacity', 0.8)
      .on('mouseover', function() {
        d3.select(this).style('opacity', 1);
      })
      .on('mouseout', function() {
        d3.select(this).style('opacity', 0.8);
      });

    // Add labels for slices with enough space
    arcs.append('text')
      .attr('transform', d => {
        // Only add labels for slices that are big enough
        if (d.endAngle - d.startAngle < 0.3) return '';
        return `translate(${arc.centroid(d)})`;
      })
      .attr('text-anchor', 'middle')
      .attr('dy', '0.35em')
      .attr('fill', 'white')
      .attr('font-size', '12px')
      .attr('font-weight', 'bold')
      .text(d => d.data.value > 5 ? `${d.data.value}%` : '');

  }, [data, width, height, innerRadius, outerRadius]);

  return (
    <div className="flex justify-center items-center w-full h-full">
      <svg ref={svgRef} className="max-w-full max-h-full"></svg>
    </div>
  );
}
