-- Update Shopping List Schema

-- Check if shopping_lists table exists and create it if it doesn't
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'shopping_lists') THEN
        CREATE TABLE public.shopping_lists (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES auth.users(id) NOT NULL,
            name TEXT NOT NULL DEFAULT 'Shopping List',
            meal_plan_id UUID REFERENCES public.meal_plans(id),
            status TEXT NOT NULL DEFAULT 'active',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
        );

        -- Add RLS policies
        ALTER TABLE public.shopping_lists ENABLE ROW LEVEL SECURITY;
        
        -- Create policy to allow users to see only their own shopping lists
        CREATE POLICY "Users can view their own shopping lists" 
            ON public.shopping_lists 
            FOR SELECT 
            USING (auth.uid() = user_id);
            
        -- Create policy to allow users to insert their own shopping lists
        CREATE POLICY "Users can insert their own shopping lists" 
            ON public.shopping_lists 
            FOR INSERT 
            WITH CHECK (auth.uid() = user_id);
            
        -- Create policy to allow users to update their own shopping lists
        CREATE POLICY "Users can update their own shopping lists" 
            ON public.shopping_lists 
            FOR UPDATE 
            USING (auth.uid() = user_id);
            
        -- Create policy to allow users to delete their own shopping lists
        CREATE POLICY "Users can delete their own shopping lists" 
            ON public.shopping_lists 
            FOR DELETE 
            USING (auth.uid() = user_id);
    END IF;
END
$$;

-- Check if shopping_items table exists and create it if it doesn't
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'shopping_items') THEN
        CREATE TABLE public.shopping_items (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            shopping_list_id UUID REFERENCES public.shopping_lists(id) ON DELETE CASCADE NOT NULL,
            name TEXT NOT NULL,
            quantity TEXT NOT NULL DEFAULT '1',
            unit TEXT NOT NULL DEFAULT 'item',
            category TEXT NOT NULL DEFAULT 'Other',
            checked BOOLEAN NOT NULL DEFAULT false,
            in_pantry BOOLEAN NOT NULL DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
        );

        -- Add RLS policies
        ALTER TABLE public.shopping_items ENABLE ROW LEVEL SECURITY;
        
        -- Create policy to allow users to see only items from their own shopping lists
        CREATE POLICY "Users can view their own shopping items" 
            ON public.shopping_items 
            FOR SELECT 
            USING (
                EXISTS (
                    SELECT 1 FROM public.shopping_lists 
                    WHERE id = shopping_list_id AND user_id = auth.uid()
                )
            );
            
        -- Create policy to allow users to insert items to their own shopping lists
        CREATE POLICY "Users can insert their own shopping items" 
            ON public.shopping_items 
            FOR INSERT 
            WITH CHECK (
                EXISTS (
                    SELECT 1 FROM public.shopping_lists 
                    WHERE id = shopping_list_id AND user_id = auth.uid()
                )
            );
            
        -- Create policy to allow users to update items in their own shopping lists
        CREATE POLICY "Users can update their own shopping items" 
            ON public.shopping_items 
            FOR UPDATE 
            USING (
                EXISTS (
                    SELECT 1 FROM public.shopping_lists 
                    WHERE id = shopping_list_id AND user_id = auth.uid()
                )
            );
            
        -- Create policy to allow users to delete items from their own shopping lists
        CREATE POLICY "Users can delete their own shopping items" 
            ON public.shopping_items 
            FOR DELETE 
            USING (
                EXISTS (
                    SELECT 1 FROM public.shopping_lists 
                    WHERE id = shopping_list_id AND user_id = auth.uid()
                )
            );
    END IF;
END
$$;

-- Add indexes for better performance
DO $$
BEGIN
    -- Add index on user_id for shopping_lists
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_shopping_lists_user_id') THEN
        CREATE INDEX idx_shopping_lists_user_id ON public.shopping_lists(user_id);
    END IF;
    
    -- Add index on shopping_list_id for shopping_items
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_shopping_items_shopping_list_id') THEN
        CREATE INDEX idx_shopping_items_shopping_list_id ON public.shopping_items(shopping_list_id);
    END IF;
    
    -- Add index on category for shopping_items
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_shopping_items_category') THEN
        CREATE INDEX idx_shopping_items_category ON public.shopping_items(category);
    END IF;
END
$$;
