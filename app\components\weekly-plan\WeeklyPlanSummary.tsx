'use client';

interface WeeklyPlanSummaryProps {
  totalCost: number;
  averageCalories: number;
  nutritionSummary?: {
    protein: number;
    carbs: number;
    fat: number;
  };
  macros?: {
    protein: string;
    carbs: string;
    fats: string;
  };
}

export function WeeklyPlanSummary({ totalCost, averageCalories, nutritionSummary, macros }: WeeklyPlanSummaryProps) {
  // Ensure macros is properly structured
  const safeProtein = macros?.protein || (nutritionSummary ? Math.round(nutritionSummary.protein) + 'g' : '0g');
  const safeCarbs = macros?.carbs || (nutritionSummary ? Math.round(nutritionSummary.carbs) + 'g' : '0g');
  const safeFats = macros?.fats || (nutritionSummary ? Math.round(nutritionSummary.fat) + 'g' : '0g');
  return (
    <div className="bg-muted p-4 rounded-lg">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <p className="text-sm text-muted-foreground">Total Cost</p>
          <p className="text-2xl font-bold">${totalCost.toFixed(2)}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Average Daily Calories</p>
          <p className="text-2xl font-bold">{Math.round(averageCalories)} kcal</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Macros (Daily Average)</p>
          <div className="flex gap-3 mt-1">
            <div>
              <span className="font-semibold">{safeProtein}</span>
              <span className="text-xs text-muted-foreground ml-1">Protein</span>
            </div>
            <div>
              <span className="font-semibold">{safeCarbs}</span>
              <span className="text-xs text-muted-foreground ml-1">Carbs</span>
            </div>
            <div>
              <span className="font-semibold">{safeFats}</span>
              <span className="text-xs text-muted-foreground ml-1">Fat</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
