"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend, Label } from 'recharts';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useNutritionData } from "@/app/hooks/useDashboardData";

// Fallback data in case the API fails
const NUTRITION_DATA = [
  { name: 'Protein', value: 120, color: 'hsl(var(--chart-1))', recommended: 150 },
  { name: '<PERSON><PERSON>', value: 240, color: 'hsl(var(--chart-2))', recommended: 300 },
  { name: 'Fats', value: 65, color: 'hsl(var(--chart-3))', recommended: 70 }
];

interface NutritionData {
  name: string;
  value: number;
  color: string;
  recommended: number;
}

export function NutritionChart() {
  const { data: nutritionData, isLoading, isError } = useNutritionData();

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload as NutritionData;
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm">Current: {data.value}g</p>
          <p className="text-sm">Recommended: {data.recommended}g</p>
          <p className="text-sm font-medium">
            {Math.round((data.value / data.recommended) * 100)}% of daily goal
          </p>
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Macronutrient Breakdown</CardTitle>
          <CardDescription>Daily nutrition distribution</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            <Skeleton className="h-[200px] w-[200px] rounded-full" />
          </div>
          <div className="grid grid-cols-3 gap-4 mt-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="text-center">
                <Skeleton className="h-3 w-3 rounded-full mx-auto mb-1" />
                <Skeleton className="h-4 w-20 mx-auto mb-2" />
                <Skeleton className="h-3 w-16 mx-auto" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isError || !nutritionData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Macronutrient Breakdown</CardTitle>
          <CardDescription>Daily nutrition distribution</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            <p className="text-muted-foreground">Failed to load nutrition data</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Macronutrient Breakdown</CardTitle>
        <CardDescription>Daily nutrition distribution</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={nutritionData}
                cx="50%"
                cy="50%"
                innerRadius={50}
                outerRadius={80}
                paddingAngle={5}
                dataKey="value"
              >
                {nutritionData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
                <Label
                  position="center"
                  content={({ viewBox }) => {
                    const { cx, cy } = viewBox as { cx: number; cy: number };
                    const totalValue = nutritionData.reduce((sum, entry) => sum + entry.value, 0);
                    return (
                      <text
                        x={cx}
                        y={cy}
                        textAnchor="middle"
                        dominantBaseline="middle"
                        className="text-sm font-medium"
                      >
                        <tspan x={cx} dy="-0.5em" className="text-xs text-muted-foreground">
                          Total
                        </tspan>
                        <tspan x={cx} dy="1.5em" className="text-lg font-bold">
                          {totalValue}g
                        </tspan>
                      </text>
                    );
                  }}
                />
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="grid grid-cols-3 gap-4 mt-4">
          {nutritionData.map((item) => (
            <div key={item.name} className="text-center">
              <div
                className="w-3 h-3 rounded-full mx-auto mb-1"
                style={{ backgroundColor: item.color }}
              />
              <p className="text-sm font-medium">{item.name}</p>
              <p className="text-xs text-muted-foreground">{item.value}g</p>
              <p className="text-xs text-muted-foreground">
                {Math.round((item.value / item.recommended) * 100)}% of goal
              </p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
