export const clearAuthCookies = () => {
  try {
    // Clear all Supabase-related cookies
    const cookies = document.cookie.split(';');
    
    for (let cookie of cookies) {
      const cookieName = cookie.split('=')[0].trim();
      if (cookieName.startsWith('sb-')) {
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; secure; samesite=lax`;
      }
    }

    // Clear local storage
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('sb-')) {
        localStorage.removeItem(key);
      }
    });

    // Clear session storage
    Object.keys(sessionStorage).forEach(key => {
      if (key.startsWith('sb-')) {
        sessionStorage.removeItem(key);
      }
    });
  } catch (error) {
    console.error('Error clearing auth data:', error);
  }
};

export const isValidSession = (session: any): boolean => {
  return !!(
    session &&
    session.user &&
    session.user.id &&
    session.access_token &&
    new Date(session.expires_at * 1000) > new Date()
  );
};
