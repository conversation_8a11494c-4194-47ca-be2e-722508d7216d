import { NextResponse } from "next/server";
import { AuthService } from "@/services/auth-service";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { email, password, weeklyBudget, householdSize, dietaryRestrictions } = body;

    // Validate required fields
    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 }
      );
    }

    const { user } = await AuthService.createUser({
      email,
      password,
      weeklyBudget: parseFloat(weeklyBudget) || 0,
      householdSize: parseInt(householdSize) || 1,
      dietaryRestrictions,
    });

    return NextResponse.json({ user });
  } catch (error) {
    console.error("Signup error:", error);
    return NextResponse.json(
      { error: "An error occurred during signup" },
      { status: 500 }
    );
  }
}

