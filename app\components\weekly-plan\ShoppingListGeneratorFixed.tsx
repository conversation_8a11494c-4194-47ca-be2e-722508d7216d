'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { mealPlanService } from '@/app/services/meal-plan-service';
import { toast } from 'sonner';
import { Loader2, ShoppingCart } from 'lucide-react';

interface Ingredient {
  name: string;
  amount: number;
  unit: string;
  category: string;
  checked: boolean;
}

interface MealPlan {
  id: string;
  user_id: string;
  start_date: string;
  end_date: string;
  total_cost: number;
  meal_data: any;
  status: string;
  created_at: string;
  updated_at: string;
  name?: string;
  description?: string;
}

interface ShoppingListGeneratorProps {
  mealPlan: MealPlan | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ShoppingListGeneratorFixed({
  mealPlan,
  open,
  onOpenChange,
}: ShoppingListGeneratorProps) {
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (open && mealPlan) {
      generateShoppingList();
    } else {
      setIngredients([]);
      setIsLoading(false);
      setIsGenerating(false);
    }
  }, [open, mealPlan]);

  // Generate shopping list from meal plan
  const generateShoppingList = async () => {
    if (!mealPlan) return;

    try {
      setIsGenerating(true);
      
      const { data, error } = await mealPlanService.generateShoppingList(mealPlan.id);
      
      if (error) {
        console.error('Error generating shopping list:', error);
        toast.error('Failed to generate shopping list');
        return;
      }
      
      if (data && data.ingredients) {
        // Process ingredients and add checked property
        const processedIngredients = data.ingredients.map((ingredient: any) => ({
          ...ingredient,
          checked: false,
        }));
        
        setIngredients(processedIngredients);
      }
    } catch (err) {
      console.error('Error generating shopping list:', err);
      toast.error('An unexpected error occurred');
    } finally {
      setIsGenerating(false);
    }
  };

  // Toggle ingredient checked state
  const toggleIngredient = (index: number) => {
    setIngredients(prev => 
      prev.map((ingredient, i) => 
        i === index ? { ...ingredient, checked: !ingredient.checked } : ingredient
      )
    );
  };

  // Group ingredients by category
  const groupedIngredients = ingredients.reduce((acc, ingredient) => {
    const category = ingredient.category || 'Other';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(ingredient);
    return acc;
  }, {} as Record<string, Ingredient[]>);

  // Handle print shopping list
  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const uncheckedIngredients = ingredients.filter(ingredient => !ingredient.checked);
    
    const groupedUnchecked = uncheckedIngredients.reduce((acc, ingredient) => {
      const category = ingredient.category || 'Other';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(ingredient);
      return acc;
    }, {} as Record<string, Ingredient[]>);

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Shopping List - ${mealPlan?.name || 'Meal Plan'}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { text-align: center; }
            h2 { margin-top: 20px; border-bottom: 1px solid #ccc; }
            ul { list-style-type: none; padding-left: 0; }
            li { margin-bottom: 8px; }
          </style>
        </head>
        <body>
          <h1>Shopping List - ${mealPlan?.name || 'Meal Plan'}</h1>
          ${Object.entries(groupedUnchecked).map(([category, items]) => `
            <h2>${category}</h2>
            <ul>
              ${items.map(item => `<li>${item.amount} ${item.unit} ${item.name}</li>`).join('')}
            </ul>
          `).join('')}
        </body>
      </html>
    `;

    printWindow.document.write(html);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Shopping List</DialogTitle>
          <DialogDescription>
            Ingredients needed for your meal plan. Check off items you already have.
          </DialogDescription>
        </DialogHeader>
        
        {isGenerating ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p>Generating your shopping list...</p>
          </div>
        ) : ingredients.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8">
            <ShoppingCart className="h-8 w-8 text-muted-foreground mb-4" />
            <p>No ingredients found for this meal plan.</p>
          </div>
        ) : (
          <ScrollArea className="h-[400px] pr-4">
            {Object.entries(groupedIngredients).map(([category, items]) => (
              <div key={category} className="mb-6">
                <h3 className="font-medium mb-2">{category}</h3>
                <div className="space-y-2">
                  {items.map((ingredient, index) => {
                    const globalIndex = ingredients.findIndex(i => 
                      i.name === ingredient.name && 
                      i.amount === ingredient.amount && 
                      i.unit === ingredient.unit
                    );
                    
                    return (
                      <div key={index} className="flex items-center space-x-2">
                        <Checkbox 
                          id={`ingredient-${globalIndex}`}
                          checked={ingredient.checked}
                          onCheckedChange={() => toggleIngredient(globalIndex)}
                        />
                        <label 
                          htmlFor={`ingredient-${globalIndex}`}
                          className={`text-sm ${ingredient.checked ? 'line-through text-muted-foreground' : ''}`}
                        >
                          {ingredient.amount} {ingredient.unit} {ingredient.name}
                        </label>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </ScrollArea>
        )}
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isGenerating}>
            Close
          </Button>
          <Button onClick={handlePrint} disabled={isGenerating || ingredients.length === 0}>
            Print List
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 