import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const updateMealCompletionSchema = z.object({
  meal_plan_id: z.string().uuid('Invalid meal plan ID'),
  meal_id: z.string().min(1, 'Meal ID is required'),
  status: z.enum(['completed', 'skipped', 'pending'], {
    errorMap: () => ({ message: 'Status must be completed, skipped, or pending' })
  }),
});

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get current user
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const mealPlanId = searchParams.get('meal_plan_id');
    const status = searchParams.get('status');

    let query = supabase
      .from('meal_completions')
      .select('*')
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false });

    if (mealPlanId) {
      query = query.eq('meal_plan_id', mealPlanId);
    }
    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching meal completions:', error);
      return NextResponse.json(
        { error: 'Failed to fetch meal completions' },
        { status: 500 }
      );
    }

    return NextResponse.json({ data: data || [] });

  } catch (error) {
    console.error('Meal completions GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get current user
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { meal_plan_id, meal_id, status } = updateMealCompletionSchema.parse(body);

    // Check if meal plan exists and belongs to user
    const { data: mealPlan, error: mealPlanError } = await supabase
      .from('meal_plans')
      .select('id')
      .eq('id', meal_plan_id)
      .eq('user_id', session.user.id)
      .single();

    if (mealPlanError || !mealPlan) {
      return NextResponse.json(
        { error: 'Meal plan not found' },
        { status: 404 }
      );
    }

    // Check if completion record already exists
    const { data: existingCompletion, error: existingError } = await supabase
      .from('meal_completions')
      .select('id')
      .eq('user_id', session.user.id)
      .eq('meal_plan_id', meal_plan_id)
      .eq('meal_id', meal_id)
      .single();

    const completedAt = status === 'completed' ? new Date().toISOString() : null;

    if (existingCompletion) {
      // Update existing completion
      const { data, error } = await supabase
        .from('meal_completions')
        .update({
          status,
          completed_at: completedAt,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingCompletion.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating meal completion:', error);
        return NextResponse.json(
          { error: 'Failed to update meal completion' },
          { status: 500 }
        );
      }

      return NextResponse.json({ 
        data, 
        message: 'Meal completion updated successfully',
        action: 'updated'
      });
    } else {
      // Create new completion record
      const { data, error } = await supabase
        .from('meal_completions')
        .insert({
          user_id: session.user.id,
          meal_plan_id,
          meal_id,
          status,
          completed_at: completedAt,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating meal completion:', error);
        return NextResponse.json(
          { error: 'Failed to create meal completion' },
          { status: 500 }
        );
      }

      return NextResponse.json({ 
        data, 
        message: 'Meal completion recorded successfully',
        action: 'created'
      });
    }

  } catch (error) {
    console.error('Meal completions POST error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
