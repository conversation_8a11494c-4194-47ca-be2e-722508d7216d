import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { checked } = await request.json();
    const updatedItem = await prisma.shopping_items.update({
      where: { id: params.id },
      data: { completed: checked },
    });

    return NextResponse.json(updatedItem);
  } catch (error) {
    console.error('Failed to update shopping item:', error);
    return NextResponse.json(
      { error: 'Failed to update shopping item' },
      { status: 500 }
    );
  }
}

