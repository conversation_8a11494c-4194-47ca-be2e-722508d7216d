-- Diagnostic script to check user_id column types across all tables

-- Check user_id column types
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
    AND column_name = 'user_id'
ORDER BY 
    table_name;

-- Check RLS policies that use auth.uid()
SELECT 
    tablename AS table_name,
    policyname AS policy_name,
    cmd AS operation,
    qual AS using_expression
FROM 
    pg_policies
WHERE 
    schemaname = 'public'
    AND (qual::text LIKE '%auth.uid()%' OR with_check::text LIKE '%auth.uid()%')
ORDER BY 
    tablename, policyname;

-- Check foreign key constraints involving user_id
SELECT 
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM 
    information_schema.table_constraints tc
JOIN 
    information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN 
    information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
WHERE 
    tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND kcu.column_name = 'user_id'
ORDER BY 
    tc.table_name;
