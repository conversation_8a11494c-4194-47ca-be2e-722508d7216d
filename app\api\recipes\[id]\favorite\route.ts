import { NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import type { Database } from "@/types/supabase";
import { recipeService, ClientType, getSupabaseClient } from "@/app/services/database-server";

// PATCH handler to toggle favorite status
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();

    if (authError || !session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if the recipe exists and belongs to the user
    const { data: recipe, error: fetchError } = await recipeService.getRecipeById(params.id);

    if (fetchError) {
      console.error("Error fetching recipe:", fetchError);
      return NextResponse.json(
        { error: "Recipe not found" },
        { status: 404 }
      );
    }

    if (recipe.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "You don't have permission to update this recipe" },
        { status: 403 }
      );
    }

    // Toggle the favorite status
    const newFavoriteStatus = !recipe.is_favorite;

    // Use the recipe service to toggle favorite status
    const { data: updatedRecipe, error } = await recipeService.toggleFavorite(params.id, newFavoriteStatus);

    if (error) {
      console.error("Error updating recipe favorite status:", error);
      return NextResponse.json(
        { error: "Failed to update favorite status" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      id: params.id,
      is_favorite: newFavoriteStatus
    });
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
