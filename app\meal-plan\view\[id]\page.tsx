'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSupabase } from '@/components/supabase-provider';
import { WeeklyPlanView } from '@/components/weekly-plan/WeeklyPlanView';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import { toast } from 'sonner';
import { WeeklyPlan } from '@/types/mealPlan';

export default function MealPlanDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const [mealPlan, setMealPlan] = useState<WeeklyPlan | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!supabase || isSupabaseLoading) return;

    const fetchMealPlan = async () => {
      try {
        // Get the user's ID
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          toast.error('You must be logged in to view meal plans');
          router.push('/login');
          return;
        }

        // Fetch the meal plan using the direct API
        const response = await fetch(`/api/direct-meal-plans/${params.id}?userId=${user.id}`);
        const result = await response.json();

        if (result.success && result.data) {
          // Transform the data into WeeklyPlan format
          const weeklyPlan: WeeklyPlan = {
            id: result.data.id,
            startDate: result.data.start_date,
            endDate: result.data.end_date,
            days: result.data.meal_data.mealPlan.days,
            totalCost: result.data.meal_data.mealPlan.summary.totalCost,
            averageCalories: result.data.meal_data.mealPlan.summary.averageCalories,
            nutritionSummary: result.data.meal_data.mealPlan.summary.nutrition
          };
          setMealPlan(weeklyPlan);
        } else {
          toast.error('Failed to load meal plan');
          router.push('/meal-plan/view');
        }
      } catch (error) {
        console.error('Error fetching meal plan:', error);
        toast.error('Failed to load meal plan');
        router.push('/meal-plan/view');
      } finally {
        setIsLoading(false);
      }
    };

    fetchMealPlan();
  }, [supabase, isSupabaseLoading, params.id, router]);

  const handleBack = () => {
    router.push('/meal-plan/view');
  };

  return (
    <div className="container max-w-6xl py-8">
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={handleBack} className="mr-2">
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-2xl font-bold">Meal Plan Details</h1>
      </div>

      <WeeklyPlanView initialPlan={mealPlan} />
    </div>
  );
} 