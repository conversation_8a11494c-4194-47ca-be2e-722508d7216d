import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { UserPreferences, UpdateUserPreferencesRequest } from '@/types/database-extended';

export function useUserPreferences() {
  const queryClient = useQueryClient();

  const {
    data: preferences,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['user-preferences'],
    queryFn: async () => {
      const response = await fetch('/api/user-preferences');
      if (!response.ok) {
        throw new Error('Failed to fetch user preferences');
      }
      const result = await response.json();
      return result.data as UserPreferences;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const updatePreferencesMutation = useMutation({
    mutationFn: async (updates: UpdateUserPreferencesRequest) => {
      const response = await fetch('/api/user-preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update preferences');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-preferences'] });
    },
  });

  const updatePreferences = (updates: UpdateUserPreferencesRequest) => {
    return updatePreferencesMutation.mutateAsync(updates);
  };

  const updateCookingSkillLevel = (level: 'beginner' | 'intermediate' | 'advanced') => {
    return updatePreferences({ cooking_skill_level: level });
  };

  const updateMaxCookingTime = (time: number) => {
    return updatePreferences({ max_cooking_time: time });
  };

  const updatePreferredCuisines = (cuisines: string[]) => {
    return updatePreferences({ preferred_cuisines: cuisines });
  };

  const updateExcludedIngredients = (ingredients: string[]) => {
    return updatePreferences({ excluded_ingredients: ingredients });
  };

  const updateNotificationPreferences = (notifications: {
    email?: boolean;
    push?: boolean;
    meal_reminders?: boolean;
  }) => {
    return updatePreferences({ notification_preferences: notifications });
  };

  const updatePrivacySettings = (privacy: {
    data_sharing?: boolean;
    analytics?: boolean;
  }) => {
    return updatePreferences({ privacy_settings: privacy });
  };

  const updateUnitsPreference = (units: 'metric' | 'imperial') => {
    return updatePreferences({ units_preference: units });
  };

  const updateCurrencyPreference = (currency: string) => {
    return updatePreferences({ currency_preference: currency });
  };

  // Helper functions to get specific preference values
  const getCookingSkillLevel = () => {
    return preferences?.cooking_skill_level || 'intermediate';
  };

  const getMaxCookingTime = () => {
    return preferences?.max_cooking_time || 30;
  };

  const getPreferredCuisines = () => {
    return preferences?.preferred_cuisines || [];
  };

  const getExcludedIngredients = () => {
    return preferences?.excluded_ingredients || [];
  };

  const getNotificationPreferences = () => {
    return preferences?.notification_preferences || {
      email: true,
      push: false,
      meal_reminders: true
    };
  };

  const getPrivacySettings = () => {
    return preferences?.privacy_settings || {
      data_sharing: false,
      analytics: true
    };
  };

  const getUnitsPreference = () => {
    return preferences?.units_preference || 'metric';
  };

  const getCurrencyPreference = () => {
    return preferences?.currency_preference || 'USD';
  };

  return {
    preferences,
    isLoading,
    error,
    refetch,
    updatePreferences,
    updateCookingSkillLevel,
    updateMaxCookingTime,
    updatePreferredCuisines,
    updateExcludedIngredients,
    updateNotificationPreferences,
    updatePrivacySettings,
    updateUnitsPreference,
    updateCurrencyPreference,
    getCookingSkillLevel,
    getMaxCookingTime,
    getPreferredCuisines,
    getExcludedIngredients,
    getNotificationPreferences,
    getPrivacySettings,
    getUnitsPreference,
    getCurrencyPreference,
    isUpdating: updatePreferencesMutation.isPending,
  };
}
