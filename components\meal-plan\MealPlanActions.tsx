'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar, 
  ShoppingCart, 
  Download, 
  Share2, 
  Edit, 
  Trash2, 
  Play, 
  Pause, 
  Archive,
  Copy
} from 'lucide-react';
import { MealPlan } from '@/lib/meal-plan';

interface MealPlanActionsProps {
  mealPlan: MealPlan;
  onEdit?: () => void;
  onDelete?: () => void;
  onDuplicate?: () => void;
  onActivate?: () => void;
  onPause?: () => void;
  onArchive?: () => void;
  onGenerateShoppingList?: () => void;
  onAddToCalendar?: () => void;
  onExport?: () => void;
  onShare?: () => void;
}

export function MealPlanActions({
  mealPlan,
  onEdit,
  onDelete,
  onDuplicate,
  onActivate,
  onPause,
  onArchive,
  onGenerateShoppingList,
  onAddToCalendar,
  onExport,
  onShare
}: MealPlanActionsProps) {
  const [isLoading, setIsLoading] = useState<string | null>(null);

  const handleAction = async (action: string, callback?: () => void) => {
    if (!callback) return;
    
    setIsLoading(action);
    try {
      await callback();
    } finally {
      setIsLoading(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'archived':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Meal Plan Actions</CardTitle>
            <CardDescription>
              Manage your meal plan settings and options
            </CardDescription>
          </div>
          <Badge className={getStatusColor(mealPlan.status)}>
            {mealPlan.status.charAt(0).toUpperCase() + mealPlan.status.slice(1)}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Primary Actions */}
        <div className="grid grid-cols-2 gap-3">
          {mealPlan.status === 'draft' && onActivate && (
            <Button
              onClick={() => handleAction('activate', onActivate)}
              disabled={isLoading === 'activate'}
              className="w-full"
            >
              <Play className="h-4 w-4 mr-2" />
              {isLoading === 'activate' ? 'Activating...' : 'Activate Plan'}
            </Button>
          )}
          
          {mealPlan.status === 'active' && onPause && (
            <Button
              onClick={() => handleAction('pause', onPause)}
              disabled={isLoading === 'pause'}
              variant="outline"
              className="w-full"
            >
              <Pause className="h-4 w-4 mr-2" />
              {isLoading === 'pause' ? 'Pausing...' : 'Pause Plan'}
            </Button>
          )}
          
          {onEdit && (
            <Button
              onClick={() => handleAction('edit', onEdit)}
              disabled={isLoading === 'edit'}
              variant="outline"
              className="w-full"
            >
              <Edit className="h-4 w-4 mr-2" />
              {isLoading === 'edit' ? 'Loading...' : 'Edit Plan'}
            </Button>
          )}
        </div>

        {/* Secondary Actions */}
        <div className="grid grid-cols-2 gap-3">
          {onAddToCalendar && (
            <Button
              onClick={() => handleAction('calendar', onAddToCalendar)}
              disabled={isLoading === 'calendar'}
              variant="outline"
              className="w-full"
            >
              <Calendar className="h-4 w-4 mr-2" />
              {isLoading === 'calendar' ? 'Adding...' : 'Add to Calendar'}
            </Button>
          )}
          
          {onGenerateShoppingList && (
            <Button
              onClick={() => handleAction('shopping', onGenerateShoppingList)}
              disabled={isLoading === 'shopping'}
              variant="outline"
              className="w-full"
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              {isLoading === 'shopping' ? 'Generating...' : 'Shopping List'}
            </Button>
          )}
        </div>

        {/* Utility Actions */}
        <div className="grid grid-cols-3 gap-2">
          {onDuplicate && (
            <Button
              onClick={() => handleAction('duplicate', onDuplicate)}
              disabled={isLoading === 'duplicate'}
              variant="ghost"
              size="sm"
              className="w-full"
            >
              <Copy className="h-4 w-4 mr-1" />
              {isLoading === 'duplicate' ? 'Copying...' : 'Duplicate'}
            </Button>
          )}
          
          {onExport && (
            <Button
              onClick={() => handleAction('export', onExport)}
              disabled={isLoading === 'export'}
              variant="ghost"
              size="sm"
              className="w-full"
            >
              <Download className="h-4 w-4 mr-1" />
              {isLoading === 'export' ? 'Exporting...' : 'Export'}
            </Button>
          )}
          
          {onShare && (
            <Button
              onClick={() => handleAction('share', onShare)}
              disabled={isLoading === 'share'}
              variant="ghost"
              size="sm"
              className="w-full"
            >
              <Share2 className="h-4 w-4 mr-1" />
              {isLoading === 'share' ? 'Sharing...' : 'Share'}
            </Button>
          )}
        </div>

        {/* Destructive Actions */}
        <div className="border-t pt-4 space-y-2">
          {onArchive && mealPlan.status !== 'archived' && (
            <Button
              onClick={() => handleAction('archive', onArchive)}
              disabled={isLoading === 'archive'}
              variant="outline"
              size="sm"
              className="w-full text-yellow-600 hover:text-yellow-700"
            >
              <Archive className="h-4 w-4 mr-2" />
              {isLoading === 'archive' ? 'Archiving...' : 'Archive Plan'}
            </Button>
          )}
          
          {onDelete && (
            <Button
              onClick={() => handleAction('delete', onDelete)}
              disabled={isLoading === 'delete'}
              variant="destructive"
              size="sm"
              className="w-full"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {isLoading === 'delete' ? 'Deleting...' : 'Delete Plan'}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default MealPlanActions;
