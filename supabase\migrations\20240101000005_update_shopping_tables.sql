-- Update shopping_lists table
ALTER TABLE shopping_lists
ADD COLUMN IF NOT EXISTS meal_plan_id UUID REFERENCES meal_plans(id) ON DELETE SET NULL;

-- Create index for the new column
CREATE INDEX IF NOT EXISTS shopping_lists_meal_plan_id_idx ON shopping_lists(meal_plan_id);

-- Update shopping_items table
-- First, add the new columns
ALTER TABLE shopping_items
ADD COLUMN IF NOT EXISTS category TEXT DEFAULT 'Other',
ADD COLUMN IF NOT EXISTS in_pantry BOOLEAN DEFAULT false;

-- Rename 'completed' to 'checked' if it exists
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'shopping_items' 
        AND column_name = 'completed'
    ) THEN
        ALTER TABLE shopping_items RENAME COLUMN completed TO checked;
    END IF;
END $$;

-- Change quantity from integer to text
ALTER TABLE shopping_items 
ALTER COLUMN quantity TYPE TEXT USING quantity::TEXT;

-- Create index for the new column
CREATE INDEX IF NOT EXISTS shopping_items_category_idx ON shopping_items(category);

-- Check and update RLS policies for shopping_lists
DROP POLICY IF EXISTS "Users can view their own shopping lists" ON shopping_lists;
DROP POLICY IF EXISTS "Users can insert their own shopping lists" ON shopping_lists;
DROP POLICY IF EXISTS "Users can update their own shopping lists" ON shopping_lists;
DROP POLICY IF EXISTS "Users can delete their own shopping lists" ON shopping_lists;

-- Enable RLS on shopping_lists table
ALTER TABLE shopping_lists ENABLE ROW LEVEL SECURITY;

-- Create policies for shopping_lists table with proper type casting
CREATE POLICY "Users can view their own shopping lists"
  ON shopping_lists
  FOR SELECT
  USING (user_id::text = auth.uid()::text);

CREATE POLICY "Users can insert their own shopping lists"
  ON shopping_lists
  FOR INSERT
  WITH CHECK (user_id::text = auth.uid()::text);

CREATE POLICY "Users can update their own shopping lists"
  ON shopping_lists
  FOR UPDATE
  USING (user_id::text = auth.uid()::text);

CREATE POLICY "Users can delete their own shopping lists"
  ON shopping_lists
  FOR DELETE
  USING (user_id::text = auth.uid()::text);

-- Check and update RLS policies for shopping_items
DROP POLICY IF EXISTS "Users can view their own shopping items" ON shopping_items;
DROP POLICY IF EXISTS "Users can insert their own shopping items" ON shopping_items;
DROP POLICY IF EXISTS "Users can update their own shopping items" ON shopping_items;
DROP POLICY IF EXISTS "Users can delete their own shopping items" ON shopping_items;

-- Enable RLS on shopping_items table
ALTER TABLE shopping_items ENABLE ROW LEVEL SECURITY;

-- Create policies for shopping_items table with proper type casting
CREATE POLICY "Users can view their own shopping items"
  ON shopping_items
  FOR SELECT
  USING (shopping_list_id IN (SELECT id FROM shopping_lists WHERE user_id::text = auth.uid()::text));

CREATE POLICY "Users can insert their own shopping items"
  ON shopping_items
  FOR INSERT
  WITH CHECK (shopping_list_id IN (SELECT id FROM shopping_lists WHERE user_id::text = auth.uid()::text));

CREATE POLICY "Users can update their own shopping items"
  ON shopping_items
  FOR UPDATE
  USING (shopping_list_id IN (SELECT id FROM shopping_lists WHERE user_id::text = auth.uid()::text));

CREATE POLICY "Users can delete their own shopping items"
  ON shopping_items
  FOR DELETE
  USING (shopping_list_id IN (SELECT id FROM shopping_lists WHERE user_id::text = auth.uid()::text));

-- Grant permissions to authenticated users
GRANT ALL ON shopping_lists TO authenticated;
GRANT ALL ON shopping_items TO authenticated;
