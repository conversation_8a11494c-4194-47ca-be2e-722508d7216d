"use client";

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Button } from '@/components/ui/button';
import { useMealPlanStore } from '@/lib/stores/meal-plan-store-supabase';
import { mealPlanService } from '@/lib/supabase/meal-plan-service';

export default function MealPlanDebugPage() {
  const [user, setUser] = useState<any>(null);
  const [mealPlans, setMealPlans] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  
  // Get the meal plan store state
  const storeState = useMealPlanStore(state => ({
    userId: state.userId,
    mealPlan: state.mealPlan,
    isLoading: state.isLoading
  }));
  
  // Store actions
  const { 
    setUserId, 
    loadMealPlanFromDatabase, 
    saveMealPlanToDatabase 
  } = useMealPlanStore();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const supabase = createClientComponentClient();
        const { data } = await supabase.auth.getUser();
        setUser(data.user);
        
        if (data.user) {
          // Set the user ID in the store
          setUserId(data.user.id);
        }
      } catch (err) {
        console.error('Error checking auth:', err);
        setError('Failed to check authentication status');
      }
    };
    
    checkAuth();
  }, [setUserId]);

  const fetchMealPlans = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (!user) {
        setError('You must be logged in to fetch meal plans');
        return;
      }
      
      const supabase = createClientComponentClient();
      
      // Direct database query
      const { data, error } = await supabase
        .from('meal_plans')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching meal plans:', error);
        setError(`Failed to fetch meal plans: ${error.message}`);
        return;
      }
      
      setMealPlans(data || []);
      setResult({
        message: `Found ${data?.length || 0} meal plans in the database`,
        data: data
      });
    } catch (err: any) {
      console.error('Error in fetchMealPlans:', err);
      setError(`An unexpected error occurred: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const createTestMealPlan = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (!user) {
        setError('You must be logged in to create a meal plan');
        return;
      }
      
      // Create a simple test meal plan
      const testMealPlan = {
        [`${new Date().toISOString().split('T')[0]}`]: {
          breakfast: {
            id: `test-meal-${Date.now()}`,
            name: "Test Breakfast",
            description: "Test breakfast created for debugging",
            ingredients: [{ name: "Test Ingredient", quantity: "1", unit: "cup" }],
            instructions: ["Step 1: Test"],
            nutrition: { protein: 10, carbs: 20, fat: 5 },
            calories: 200,
            cost: 5.99,
            servings: 1
          },
          lunch: {
            id: `test-meal-${Date.now() + 1}`,
            name: "Test Lunch",
            description: "Test lunch created for debugging",
            ingredients: [{ name: "Test Ingredient", quantity: "2", unit: "tbsp" }],
            instructions: ["Step 1: Test"],
            nutrition: { protein: 15, carbs: 30, fat: 10 },
            calories: 300,
            cost: 7.99,
            servings: 1
          }
        }
      };
      
      // Save using the service directly
      const success = await mealPlanService.saveMealPlan(testMealPlan, user.id);
      
      if (success) {
        setResult({
          message: 'Test meal plan created successfully',
          mealPlan: testMealPlan
        });
        
        // Refresh the meal plans list
        fetchMealPlans();
      } else {
        setError('Failed to create test meal plan');
      }
    } catch (err: any) {
      console.error('Error in createTestMealPlan:', err);
      setError(`An unexpected error occurred: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const loadMealPlanFromStore = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (!user) {
        setError('You must be logged in to load a meal plan');
        return;
      }
      
      // Load meal plan from database using the store
      const success = await loadMealPlanFromDatabase();
      
      if (success) {
        setResult({
          message: 'Meal plan loaded successfully from database',
          mealPlan: useMealPlanStore.getState().mealPlan
        });
      } else {
        setError('Failed to load meal plan from database');
      }
    } catch (err: any) {
      console.error('Error in loadMealPlanFromStore:', err);
      setError(`An unexpected error occurred: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const saveMealPlanToStore = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (!user) {
        setError('You must be logged in to save a meal plan');
        return;
      }
      
      // Check if there's a meal plan in the store
      const currentMealPlan = useMealPlanStore.getState().mealPlan;
      
      if (!currentMealPlan) {
        setError('No meal plan in the store to save');
        return;
      }
      
      // Save meal plan to database using the store
      const success = await saveMealPlanToDatabase();
      
      if (success) {
        setResult({
          message: 'Meal plan saved successfully to database',
          mealPlan: currentMealPlan
        });
        
        // Refresh the meal plans list
        fetchMealPlans();
      } else {
        setError('Failed to save meal plan to database');
      }
    } catch (err: any) {
      console.error('Error in saveMealPlanToStore:', err);
      setError(`An unexpected error occurred: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Meal Plan Debug Page</h1>
      
      <div className="mb-6 p-4 border rounded-md">
        <h2 className="text-lg font-bold mb-2">Authentication Status</h2>
        {user ? (
          <div className="text-green-600">
            Authenticated as: {user.email} (ID: {user.id})
          </div>
        ) : (
          <div className="text-red-600">Not authenticated</div>
        )}
      </div>
      
      <div className="mb-6 p-4 border rounded-md">
        <h2 className="text-lg font-bold mb-2">Meal Plan Store State</h2>
        <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-40">
          {JSON.stringify({
            userId: storeState.userId,
            hasMealPlan: !!storeState.mealPlan,
            isLoading: storeState.isLoading,
            mealPlanDays: storeState.mealPlan ? Object.keys(storeState.mealPlan) : []
          }, null, 2)}
        </pre>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="p-4 border rounded-md">
          <h2 className="text-lg font-bold mb-2">Database Operations</h2>
          <div className="flex flex-col gap-2">
            <Button onClick={fetchMealPlans} disabled={loading}>
              Fetch Meal Plans from Database
            </Button>
            <Button onClick={createTestMealPlan} disabled={loading}>
              Create Test Meal Plan
            </Button>
          </div>
        </div>
        
        <div className="p-4 border rounded-md">
          <h2 className="text-lg font-bold mb-2">Store Operations</h2>
          <div className="flex flex-col gap-2">
            <Button onClick={loadMealPlanFromStore} disabled={loading}>
              Load Meal Plan from Database to Store
            </Button>
            <Button onClick={saveMealPlanToStore} disabled={loading}>
              Save Meal Plan from Store to Database
            </Button>
          </div>
        </div>
      </div>
      
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <h2 className="text-lg font-bold text-red-600 mb-2">Error</h2>
          <p className="text-red-600">{error}</p>
        </div>
      )}
      
      {result && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
          <h2 className="text-lg font-bold text-green-600 mb-2">Result</h2>
          <p className="text-green-600 mb-2">{result.message}</p>
          <pre className="bg-white p-2 rounded overflow-auto max-h-96">
            {JSON.stringify(result.data || result.mealPlan, null, 2)}
          </pre>
        </div>
      )}
      
      {mealPlans.length > 0 && (
        <div className="mb-6 p-4 border rounded-md">
          <h2 className="text-lg font-bold mb-2">Meal Plans in Database ({mealPlans.length})</h2>
          <div className="overflow-auto max-h-96">
            {mealPlans.map((plan, index) => (
              <div key={plan.id} className="mb-4 p-3 bg-gray-50 rounded-md">
                <h3 className="font-semibold">
                  Meal Plan {index + 1} (ID: {plan.id.substring(0, 8)}...)
                </h3>
                <p className="text-sm text-gray-600">
                  Created: {new Date(plan.created_at).toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">
                  Status: {plan.status}
                </p>
                <p className="text-sm text-gray-600">
                  Days: {Object.keys(plan.meal_data || {}).length}
                </p>
                <details className="mt-2">
                  <summary className="cursor-pointer text-blue-600">View Details</summary>
                  <pre className="mt-2 bg-white p-2 rounded overflow-auto max-h-40 text-xs">
                    {JSON.stringify(plan, null, 2)}
                  </pre>
                </details>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
