// Test script for Edamam API
const axios = require('axios');
require('dotenv').config(); // Load environment variables from .env file

// Recipe API configuration
const EDAMAM_RECIPE_APP_ID = process.env.NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES;
const EDAMAM_RECIPE_APP_KEY = process.env.NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES;
const EDAMAM_RECIPE_BASE_URL = 'https://api.edamam.com/api/recipes/v2';

// Food Database API configuration
const EDAMAM_FOOD_DB_APP_ID = process.env.NEXT_PUBLIC_EDAMAM_APP_ID_FOOD_DATABASE;
const EDAMAM_FOOD_DB_APP_KEY = process.env.NEXT_PUBLIC_EDAMAM_APP_KEY_FOOD_DATABASE;
const EDAMAM_FOOD_DB_BASE_URL = 'https://api.edamam.com/api/food-database/v2';

// Log the API keys for debugging
console.log('Edamam API Keys:');
console.log('Recipe API:', {
  EDAMAM_RECIPE_APP_ID,
  EDAMAM_RECIPE_APP_KEY
});
console.log('Food Database API:', {
  EDAMAM_FOOD_DB_APP_ID,
  EDAMAM_FOOD_DB_APP_KEY
});

// Test Recipe Search API
async function testRecipeSearchAPI() {
  try {
    console.log('\n--- Testing Recipe Search API ---');

    // Prepare the query parameters
    const queryParams = new URLSearchParams({
      type: 'public',
      app_id: EDAMAM_RECIPE_APP_ID,
      app_key: EDAMAM_RECIPE_APP_KEY,
      q: 'chicken'
    });

    console.log(`Making request to: ${EDAMAM_RECIPE_BASE_URL}?${queryParams.toString()}`);

    // Make the API request
    const response = await axios.get(`${EDAMAM_RECIPE_BASE_URL}?${queryParams.toString()}`, {
      headers: {
        'Edamam-Account-User': 'user123' // Add a default user ID
      }
    });

    // Check if the response is successful
    if (response.status === 200) {
      console.log('Recipe Search API test: SUCCESS');
      console.log(`Found ${response.data.hits.length} recipes`);
      console.log('First recipe:', response.data.hits[0].recipe.label);
    } else {
      console.log('Recipe Search API test: FAILED');
      console.log('Response:', response.status, response.statusText);
    }

    return response.data;
  } catch (error) {
    console.error('Recipe Search API test: ERROR');
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
      console.error('Response headers:', error.response.headers);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error message:', error.message);
    }
    console.error('Error config:', error.config);
    return null;
  }
}

// Test Food Database API
async function testFoodDatabaseAPI() {
  try {
    console.log('\n--- Testing Food Database API ---');

    // Prepare the query parameters
    const queryParams = new URLSearchParams({
      app_id: EDAMAM_FOOD_DB_APP_ID,
      app_key: EDAMAM_FOOD_DB_APP_KEY,
      ingr: 'apple'
    });

    console.log(`Making request to: ${EDAMAM_FOOD_DB_BASE_URL}/parser?${queryParams.toString()}`);

    // Make the API request
    const response = await axios.get(`${EDAMAM_FOOD_DB_BASE_URL}/parser?${queryParams.toString()}`);

    // Check if the response is successful
    if (response.status === 200) {
      console.log('Food Database API test: SUCCESS');
      console.log(`Found ${response.data.hints.length} food items`);
      if (response.data.hints.length > 0) {
        console.log('First food item:', response.data.hints[0].food.label);

        // Test nutrients endpoint with the first food item
        await testFoodNutrientsAPI(response.data.hints[0].food.foodId);
      }
    } else {
      console.log('Food Database API test: FAILED');
      console.log('Response:', response.status, response.statusText);
    }

    return response.data;
  } catch (error) {
    console.error('Food Database API test: ERROR');
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
      console.error('Response headers:', error.response.headers);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error message:', error.message);
    }
    console.error('Error config:', error.config);
    return null;
  }
}

// Test Food Nutrients API
async function testFoodNutrientsAPI(foodId) {
  try {
    console.log('\n--- Testing Food Nutrients API ---');

    // Prepare the request body
    const requestBody = {
      ingredients: [
        {
          quantity: 1,
          measureURI: 'http://www.edamam.com/ontologies/edamam.owl#Measure_unit',
          foodId: foodId
        }
      ]
    };

    console.log(`Making request to: ${EDAMAM_FOOD_DB_BASE_URL}/nutrients`);
    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    // Make the API request
    const response = await axios.post(
      `${EDAMAM_FOOD_DB_BASE_URL}/nutrients`,
      requestBody,
      {
        params: {
          app_id: EDAMAM_FOOD_DB_APP_ID,
          app_key: EDAMAM_FOOD_DB_APP_KEY
        }
      }
    );

    // Check if the response is successful
    if (response.status === 200) {
      console.log('Food Nutrients API test: SUCCESS');
      console.log('Calories:', response.data.calories);
      console.log('Protein:', response.data.totalNutrients.PROCNT);
      console.log('Carbs:', response.data.totalNutrients.CHOCDF);
      console.log('Fat:', response.data.totalNutrients.FAT);
    } else {
      console.log('Food Nutrients API test: FAILED');
      console.log('Response:', response.status, response.statusText);
    }

    return response.data;
  } catch (error) {
    console.error('Food Nutrients API test: ERROR');
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
      console.error('Response headers:', error.response.headers);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error message:', error.message);
    }
    console.error('Error config:', error.config);
    return null;
  }
}

// Run the tests
async function runTests() {
  try {
    // Test Recipe Search API
    await testRecipeSearchAPI();

    // Test Food Database API
    await testFoodDatabaseAPI();

    console.log('\nAll tests completed.');
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

// Run the tests
runTests();
