"use client";

import { MealGenerationOptions, MealPlan } from '@/types/new-meal-plan';
import { generateMealPlanWithAI, generateRecipeWithAI } from '@/lib/api/openai';
import { DEFAULT_RECIPES } from '@/lib/mock-data/default-recipes';
import { toast } from 'sonner';

/**
 * Generate a meal plan using the AI approach (OpenAI)
 */
export async function generateMealPlanWithAIOnly(options: MealGenerationOptions): Promise<MealPlan> {
  try {
    // Generate a meal plan using OpenAI
    console.log('Generating meal plan with OpenAI...');
    const mealPlan = await generateMealPlanWithAI(options);

    return mealPlan;
  } catch (error) {
    console.error('Error generating meal plan with AI approach:', error);

    // Fallback to default recipes
    console.log('Falling back to default recipes...');
    toast.error('API error: Using default recipes as fallback');

    return generateDefaultMealPlan(options);
  }
}

/**
 * Generate a default meal plan using the default recipes
 */
function generateDefaultMealPlan(options: MealGenerationOptions): MealPlan {
  const mealPlan: MealPlan = {};
  const startDate = new Date();

  // Generate a meal plan for each day
  for (let i = 0; i < options.days; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];

    mealPlan[dateStr] = {};

    // Add breakfast
    const breakfastIndex = i % DEFAULT_RECIPES.breakfast.length;
    mealPlan[dateStr]['breakfast'] = DEFAULT_RECIPES.breakfast[breakfastIndex];

    // Add lunch
    const lunchIndex = i % DEFAULT_RECIPES.lunch.length;
    mealPlan[dateStr]['lunch'] = DEFAULT_RECIPES.lunch[lunchIndex];

    // Add dinner
    const dinnerIndex = i % DEFAULT_RECIPES.dinner.length;
    mealPlan[dateStr]['dinner'] = DEFAULT_RECIPES.dinner[dinnerIndex];
  }

  return mealPlan;
}

/**
 * Generate a single meal using the AI approach
 */
export async function generateMealWithAIOnly(options: {
  mealType: string;
  calories: number;
  dietaryPreferences: string[];
  excludeIngredients: string[];
  cookingTime: string;
}): Promise<any> {
  try {
    // Generate a recipe using OpenAI
    const recipeResponse = await generateRecipeWithAI(options);

    // Transform the recipe to our internal format
    const recipe = recipeResponse.recipe;

    return {
      id: `ai-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      name: recipe.name,
      image: '', // AI doesn't provide images
      ingredients: recipe.ingredients,
      instructions: recipe.instructions,
      cost: recipe.totalCost,
      calories: recipe.nutrition.calories,
      prepTime: recipe.prepTime || 0,
      cookTime: recipe.cookTime || 0,
      macros: {
        protein: recipe.nutrition.protein,
        carbs: recipe.nutrition.carbs,
        fats: recipe.nutrition.fat
      },
      nutrition: {
        protein: recipe.nutrition.protein,
        carbs: recipe.nutrition.carbs,
        fat: recipe.nutrition.fat
      },
      status: null
    };
  } catch (error) {
    console.error('Error generating meal with AI approach:', error);

    // Fallback to default recipes
    console.log('Falling back to default recipes...');
    toast.error('API error: Using default recipe as fallback');

    // Get a default recipe based on the meal type
    if (options.mealType === 'breakfast') {
      return DEFAULT_RECIPES.breakfast[0];
    } else if (options.mealType === 'lunch') {
      return DEFAULT_RECIPES.lunch[0];
    } else if (options.mealType === 'dinner') {
      return DEFAULT_RECIPES.dinner[0];
    } else {
      // If meal type is not recognized, return a breakfast recipe as default
      return DEFAULT_RECIPES.breakfast[0];
    }
  }
}

/**
 * Regenerate a meal in an existing meal plan using the AI approach
 */
export async function regenerateMealWithAIOnly(
  mealPlan: MealPlan,
  date: string,
  mealType: string,
  options: MealGenerationOptions
): Promise<MealPlan> {
  try {
    // Calculate target calories for this meal
    let targetCalories = options.calories;
    if (mealType === 'breakfast') {
      targetCalories = Math.round(options.calories * 0.25);
    } else if (mealType === 'lunch') {
      targetCalories = Math.round(options.calories * 0.35);
    } else if (mealType === 'dinner') {
      targetCalories = Math.round(options.calories * 0.4);
    }

    // Generate a new meal
    const newMeal = await generateMealWithAIOnly({
      mealType,
      calories: targetCalories,
      dietaryPreferences: options.dietaryPreferences,
      excludeIngredients: options.excludeIngredients,
      cookingTime: options.cookingTime
    });

    // Create a copy of the meal plan
    const updatedMealPlan = { ...mealPlan };

    // Update the meal in the meal plan
    if (!updatedMealPlan[date]) {
      updatedMealPlan[date] = {};
    }

    updatedMealPlan[date][mealType] = newMeal;

    return updatedMealPlan;
  } catch (error) {
    console.error('Error regenerating meal with AI approach:', error);
    throw error;
  }
}
