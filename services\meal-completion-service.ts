"use client";

import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";

export interface MealCompletion {
  id: string;
  user_id: string;
  meal_plan_id: string;
  date: string;
  meal_type: string;
  completed_at: string;
  created_at: string;
}

export interface MealCompletionStats {
  totalMeals: number;
  completedMeals: number;
  completionRate: number;
  streak: number;
  todayCompleted: number;
  todayTotal: number;
}

class MealCompletionService {
  private supabase = createClientComponentClient();

  /**
   * Get all meal completions for a specific meal plan
   */
  async getMealCompletions(mealPlanId: string): Promise<MealCompletion[]> {
    try {
      const { data, error } = await this.supabase
        .from('meal_completions')
        .select('*')
        .eq('meal_plan_id', mealPlanId);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching meal completions:', error);
      throw error;
    }
  }

  /**
   * Mark a meal as completed
   */
  async completeMeal(mealPlanId: string, date: string, mealType: string): Promise<MealCompletion> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await this.supabase
        .from('meal_completions')
        .insert({
          user_id: user.id,
          meal_plan_id: mealPlanId,
          date,
          meal_type: mealType,
          completed_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error completing meal:', error);
      throw error;
    }
  }

  /**
   * Mark a meal as incomplete (remove completion)
   */
  async uncompleteMeal(mealPlanId: string, date: string, mealType: string): Promise<void> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { error } = await this.supabase
        .from('meal_completions')
        .delete()
        .eq('user_id', user.id)
        .eq('meal_plan_id', mealPlanId)
        .eq('date', date)
        .eq('meal_type', mealType);

      if (error) throw error;
    } catch (error) {
      console.error('Error uncompleting meal:', error);
      throw error;
    }
  }

  /**
   * Get meal completion statistics for a meal plan
   */
  async getMealCompletionStats(mealPlanId: string, mealPlanData: any): Promise<MealCompletionStats> {
    try {
      // Get all completions for this meal plan
      const completions = await this.getMealCompletions(mealPlanId);
      
      // Default stats
      const stats: MealCompletionStats = {
        totalMeals: 0,
        completedMeals: 0,
        completionRate: 0,
        streak: 0,
        todayCompleted: 0,
        todayTotal: 0
      };
      
      if (!mealPlanData?.mealPlan?.week) {
        return stats;
      }
      
      const week = mealPlanData.mealPlan.week;
      
      // Calculate total meals
      stats.totalMeals = week.reduce((total: number, day: any) => total + day.meals.length, 0);
      
      // Calculate completed meals
      stats.completedMeals = completions.length;
      
      // Calculate completion rate
      stats.completionRate = stats.totalMeals > 0 
        ? Math.round((stats.completedMeals / stats.totalMeals) * 100) 
        : 0;
      
      // Get today's date in the format "YYYY-MM-DD"
      const today = new Date().toISOString().split('T')[0];
      
      // Calculate today's stats
      const todayData = week.find((day: any) => day.date === today);
      if (todayData) {
        stats.todayTotal = todayData.meals.length;
        stats.todayCompleted = completions.filter(c => c.date === today).length;
      }
      
      // Calculate streak
      stats.streak = this.calculateStreak(completions, week);
      
      return stats;
    } catch (error) {
      console.error('Error getting meal completion stats:', error);
      throw error;
    }
  }

  /**
   * Calculate the current streak of completed days
   */
  private calculateStreak(completions: MealCompletion[], week: any[]): number {
    // Get all dates from the meal plan
    const dates = week.map((day: any) => day.date);
    
    if (dates.length === 0) {
      return 0;
    }
    
    // Sort dates in ascending order
    dates.sort();
    
    let currentStreak = 0;
    const today = new Date().toISOString().split('T')[0];
    
    // Group completions by date
    const completionsByDate: Record<string, MealCompletion[]> = {};
    completions.forEach(completion => {
      if (!completionsByDate[completion.date]) {
        completionsByDate[completion.date] = [];
      }
      completionsByDate[completion.date].push(completion);
    });
    
    // Loop through dates up to today
    for (const date of dates) {
      // Skip future dates
      if (date > today) break;
      
      // Get meals for this day
      const dayMeals = week.find((day: any) => day.date === date)?.meals || [];
      
      // Get completed meals for this day
      const completedForDay = completionsByDate[date] || [];
      
      // Check if all meals for the day are completed
      const allCompleted = dayMeals.length > 0 && completedForDay.length === dayMeals.length;
      
      if (allCompleted) {
        currentStreak++;
      } else {
        // Reset streak if a day was missed (but only for past days)
        if (date < today) {
          currentStreak = 0;
        }
      }
    }
    
    return currentStreak;
  }
}

export const mealCompletionService = new MealCompletionService();
