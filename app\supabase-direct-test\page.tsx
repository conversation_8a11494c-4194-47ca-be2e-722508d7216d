'use client';

import React, { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { SimplifiedSupabaseWrapper } from '@/components/supabase-wrapper-simple';

function DirectTestContent({ supabaseClient }: { supabaseClient?: any }) {
  const [testResult, setTestResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [supabase, setSupabase] = useState<any>(null);
  
  // Create our own Supabase client if one wasn't passed as a prop
  useEffect(() => {
    if (supabaseClient) {
      console.log('Using provided Supabase client');
      setSupabase(supabaseClient);
    } else {
      console.log('Creating new Supabase client');
      try {
        const client = createClientComponentClient();
        setSupabase(client);
      } catch (err: any) {
        console.error('Error creating Supabase client:', err);
        setError(err.message || 'Failed to create Supabase client');
      }
    }
  }, [supabaseClient]);
  
  const runTest = async () => {
    try {
      setTestResult(null);
      setError(null);
      
      if (!supabase) {
        throw new Error('Supabase client is not available');
      }
      
      // Test: Try to get the current user
      const { data, error: authError } = await supabase.auth.getUser();
      
      if (authError) {
        throw new Error(`Auth error: ${authError.message}`);
      }
      
      setTestResult(
        `Success! User: ${data.user ? data.user.email || data.user.id : 'Not logged in'}`
      );
    } catch (err: any) {
      console.error('Test error:', err);
      setError(err.message || 'Unknown error');
    }
  };
  
  return (
    <div className="container max-w-4xl py-8">
      <h1 className="text-2xl font-bold mb-6">Direct Supabase Test</h1>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Direct Client Test</CardTitle>
          <CardDescription>
            This test creates a Supabase client directly without using the provider
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div>
              <span className="font-semibold">Supabase client exists:</span> {supabase ? 'Yes ✅' : 'No ❌'}
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={runTest} disabled={!supabase}>Run Test</Button>
        </CardFooter>
      </Card>
      
      {testResult && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-md text-green-700 mb-6">
          {testResult}
        </div>
      )}
      
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md text-red-700 mb-6">
          Error: {error}
        </div>
      )}
    </div>
  );
}

export default function DirectTestPage() {
  return (
    <SimplifiedSupabaseWrapper>
      <DirectTestContent />
    </SimplifiedSupabaseWrapper>
  );
}
