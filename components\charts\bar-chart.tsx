'use client';

import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

interface BarChartProps {
  data: {
    name: string;
    value: number;
    target?: number;
  }[];
  width?: number;
  height?: number;
  showYAxis?: boolean;
  showTarget?: boolean;
  barColor?: string;
  targetColor?: string;
}

export function BarChart({
  data,
  width = 600,
  height = 200,
  showYAxis = false,
  showTarget = false,
  barColor = 'hsl(var(--primary))',
  targetColor = 'hsl(var(--destructive))'
}: BarChartProps) {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    // Clear any existing chart
    d3.select(svgRef.current).selectAll('*').remove();

    // Set margins
    const margin = { top: 20, right: 20, bottom: 30, left: showYAxis ? 50 : 10 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    // Create SVG
    const svg = d3.select(svgRef.current)
      .attr('width', width)
      .attr('height', height)
      .append('g')
      .attr('transform', `translate(${margin.left}, ${margin.top})`);

    // Create scales
    const x = d3.scaleBand()
      .domain(data.map(d => d.name))
      .range([0, innerWidth])
      .padding(0.3);

    // Find max value for y scale, considering both values and targets
    const maxValue = d3.max(data, d => Math.max(d.value, d.target || 0)) || 0;
    const yMax = maxValue * 1.2; // Add 20% padding at the top

    const y = d3.scaleLinear()
      .domain([0, yMax])
      .range([innerHeight, 0]);

    // Add X axis
    svg.append('g')
      .attr('transform', `translate(0, ${innerHeight})`)
      .call(d3.axisBottom(x))
      .selectAll('text')
      .attr('font-size', '12px')
      .attr('text-anchor', 'middle');

    // Add Y axis if requested
    if (showYAxis) {
      svg.append('g')
        .call(d3.axisLeft(y).ticks(5))
        .selectAll('text')
        .attr('font-size', '12px');
    }

    // Add horizontal grid lines
    svg.append('g')
      .attr('class', 'grid')
      .selectAll('line')
      .data(y.ticks(5))
      .enter()
      .append('line')
      .attr('x1', 0)
      .attr('x2', innerWidth)
      .attr('y1', d => y(d))
      .attr('y2', d => y(d))
      .attr('stroke', 'hsl(var(--muted))')
      .attr('stroke-dasharray', '2,2')
      .attr('stroke-width', 0.5);

    // Create tooltip
    const tooltip = d3.select('body')
      .append('div')
      .attr('class', 'tooltip')
      .style('position', 'absolute')
      .style('background-color', 'hsl(var(--background))')
      .style('color', 'hsl(var(--foreground))')
      .style('padding', '8px')
      .style('border-radius', '4px')
      .style('box-shadow', '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)')
      .style('font-size', '12px')
      .style('pointer-events', 'none')
      .style('opacity', 0)
      .style('z-index', 1000);

    // Add bars
    svg.selectAll('.bar')
      .data(data)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', d => x(d.name) || 0)
      .attr('width', x.bandwidth())
      .attr('y', d => y(d.value))
      .attr('height', d => innerHeight - y(d.value))
      .attr('fill', barColor)
      .attr('rx', 2) // Rounded corners
      .on('mouseover', function(event, d) {
        d3.select(this).attr('opacity', 0.8);
        tooltip.transition().duration(200).style('opacity', 0.9);
        tooltip.html(`
          <div>
            <div><strong>${d.name}</strong></div>
            <div>${d.value} calories</div>
            ${d.target ? `<div>Target: ${d.target} calories</div>` : ''}
          </div>
        `)
          .style('left', `${event.pageX + 10}px`)
          .style('top', `${event.pageY - 28}px`);
      })
      .on('mouseout', function() {
        d3.select(this).attr('opacity', 1);
        tooltip.transition().duration(500).style('opacity', 0);
      });

    // Add target lines if requested
    if (showTarget) {
      svg.selectAll('.target-line')
        .data(data.filter(d => d.target))
        .enter()
        .append('line')
        .attr('class', 'target-line')
        .attr('x1', d => (x(d.name) || 0))
        .attr('x2', d => (x(d.name) || 0) + x.bandwidth())
        .attr('y1', d => y(d.target || 0))
        .attr('y2', d => y(d.target || 0))
        .attr('stroke', targetColor)
        .attr('stroke-width', 2)
        .attr('stroke-dasharray', '4,4');

      // Add target markers
      svg.selectAll('.target-marker')
        .data(data.filter(d => d.target))
        .enter()
        .append('circle')
        .attr('class', 'target-marker')
        .attr('cx', d => (x(d.name) || 0) + x.bandwidth() / 2)
        .attr('cy', d => y(d.target || 0))
        .attr('r', 3)
        .attr('fill', targetColor);
    }

    // Clean up tooltip when component unmounts
    return () => {
      d3.select('body').selectAll('.tooltip').remove();
    };
  }, [data, width, height, showYAxis, showTarget, barColor, targetColor]);

  return (
    <div className="w-full h-full">
      <svg ref={svgRef} className="w-full h-full"></svg>
    </div>
  );
}
