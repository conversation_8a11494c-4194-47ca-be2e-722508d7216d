# LeanEats AI Agent Prompt

## Project Overview

You are working on LeanEats, a meal planning application built with Next.js, Tailwind CSS, and Supabase. The application helps users plan meals, manage recipes, track nutrition, and generate shopping lists. Your task is to understand the codebase and make effective contributions while following established patterns and best practices.

## Reference Documentation

Please refer to the following documentation files for detailed information:

1. **Primary Knowledge Base**:
   - `/docs/knowledge_transfer.md` - Primary knowledge transfer document with detailed information about the project architecture, features, component patterns, best practices, and development workflow
   - `/docs/knowledge_transfer_additional.md` - Additional context and information about recent improvements, specific implementations, and technical considerations
   - `/docs/memories.md` - Comprehensive collection of important memories and learnings from the development process

2. **Supabase Integration**:
   - `/docs/supabase-usage.md` - Guide for using Supabase in the application, including implementation details, usage examples, best practices, and troubleshooting
   - `/docs/supabase-migration-guide.md` - Guide for migrating components to use the new Supabase approach, including step-by-step instructions and examples
   - `/docs/supabase-migration-progress.md` - Progress tracking for Supabase migration, including completed migrations, implementation details, and benefits

3. **Architecture** (referenced in knowledge_transfer.md):
   - Application architecture overview
   - Component hierarchy and organization
   - Data flow and state management

4. **Features** (referenced in knowledge_transfer.md):
   - Meal planning functionality
   - Recipe management functionality
   - Shopping list functionality
   - Dashboard functionality

5. **Technical Implementation** (referenced in knowledge_transfer_additional.md):
   - Supabase integration details
   - Next.js patterns and best practices
   - Form handling with React Hook Form and Zod
   - Authentication implementation
   - Performance considerations
   - Accessibility guidelines

## Key Files to Reference

### Core Structure
- `/app/layout.tsx` - Root layout with providers
- `/app/page.tsx` - Home page
- `/middleware.ts` - Next.js middleware for authentication

### Supabase Integration
- `/components/supabase-provider.tsx` - Supabase context provider
- `/types/supabase.ts` - Database type definitions
- `/lib/supabase.ts` - Supabase utility functions

### State Management
- `/app/context/MealPlannerContext.tsx` - Meal planning state management
- `/app/context/ThemeContext.tsx` - Theme state management

### Feature Implementations
- `/app/dashboard/page.tsx` - Dashboard implementation
- `/app/meal-plan/generate/page.tsx` - Meal plan generation
- `/app/meal-plan/view/page.tsx` - Meal plan viewing
- `/app/recipes/page.tsx` - Recipe listing
- `/app/recipes/create/page.tsx` - Recipe creation
- `/app/recipes/edit/[id]/page.tsx` - Recipe editing
- `/app/meal-detail/[id]/page.tsx` - Meal detail view
- `/app/settings/page.tsx` - User settings

### Components
- `/components/ui/` - UI components (shadcn/ui)
- `/app/components/` - App-specific components

## Supabase Integration

We've implemented a lightweight, robust Supabase integration that handles initialization timing, loading states, and error handling properly.

### Using Supabase in Components

When working with Supabase in components:

```tsx
// 1. Import the hook
import { useSupabase } from '@/components/supabase-provider';

// 2. Use the hook with loading state
function MyComponent() {
  const { supabase, isLoading } = useSupabase();
  const [data, setData] = useState(null);

  // 3. Handle loading and null states
  useEffect(() => {
    if (isLoading || !supabase) return;

    const fetchData = async () => {
      try {
        const { data, error } = await supabase.from('table').select('*');
        if (error) throw error;
        setData(data);
      } catch (error) {
        console.error('Error:', error);
      }
    };

    fetchData();
  }, [supabase, isLoading]); // 4. Include both in dependency arrays

  // Handle loading state in rendering
  if (isLoading) {
    return <LoadingComponent />;
  }

  return <div>{/* Render your data */}</div>;
}
```

## Best Practices

1. **Client Components**: Use 'use client' directive for components that:
   - Use hooks
   - Have interactivity
   - Need browser APIs

2. **Data Fetching**:
   - Handle loading and error states
   - Check for null Supabase client
   - Include isLoading in dependency arrays

3. **UI Components**:
   - Use shadcn/ui components
   - Follow the established design system
   - Ensure responsive design
   - Maintain accessibility

4. **Form Handling**:
   - Use React Hook Form with Zod
   - Implement proper validation
   - Provide clear user feedback

5. **Error Handling**:
   - Use try/catch blocks
   - Provide user-friendly error messages
   - Log detailed errors for debugging

## Development Workflow

1. **Understanding Requirements**:
   - Review the feature requirements thoroughly
   - Ask clarifying questions if needed

2. **Planning**:
   - Create a step-by-step plan before implementation
   - Identify potential challenges and solutions

3. **Implementation**:
   - Follow established patterns and best practices
   - Create checkpoints to avoid breaking existing code
   - Write production-ready code

4. **Testing**:
   - Test with real data when possible
   - Verify functionality across different scenarios
   - Check for edge cases and error handling

## Design Guidelines

- Create beautiful production-worthy designs
- Use 'use client' directive with React hooks
- Avoid server/client attribute mismatches
- Stick to the template's supported technologies (JSX, Tailwind, shadcn/ui, React hooks, Lucide icons)
- Use Unsplash for stock photos
- For data visualizations:
  - Include Y-axis labels showing values
  - Add target/limit lines where applicable
  - Use detailed tooltips on hover
  - Reduce whitespace
  - Ensure tooltip text colors match legend colors
- Increase color contrast throughout the application

## Memory References

All important memories and learnings from previous interactions are documented in `/docs/memories.md`. This file contains comprehensive information about:

1. **Design Guidelines**:
   - Production-worthy design principles
   - UI/UX best practices
   - Data visualization guidelines
   - Accessibility and contrast requirements

2. **Development Process**:
   - Step-by-step approach methodology
   - Checkpoint creation and testing
   - Problem analysis techniques
   - Code maintenance strategies

3. **Layout Structure**:
   - Dashboard three-section layout details
   - Component placement guidelines
   - Meal plan page implementation specifics
   - Calendar and UI interaction patterns

4. **Data Management**:
   - Create Plan functionality workflow
   - Pantry Management feature specifications
   - Supabase schema design and updates
   - Shopping list and recipe management

5. **Supabase Integration**:
   - Context initialization and timing solutions
   - Loading state handling patterns
   - Error handling improvements
   - Client/server component boundary management
   - Database security and RLS policies

6. **Next.js Specific Learnings**:
   - Parameter handling in Next.js App Router
   - Server vs. Client component considerations
   - Font system requirements
   - Data fetching patterns

7. **Authentication & Performance**:
   - Authentication best practices
   - Performance optimization techniques
   - Accessibility considerations
   - Testing strategies

## Final Notes

### Development Approach
- Focus on writing production-ready code with proper error handling and user feedback
- Take a step-by-step approach: analyze, plan, implement, test
- Create checkpoints to avoid breaking existing functionality
- Think through problems thoroughly before implementing solutions

### Code Quality
- Maintain the established design patterns and component structure
- Follow the lightweight Supabase integration approach
- Use proper typing for all data structures and API responses
- Implement comprehensive error handling

### Testing and Documentation
- Test thoroughly before submitting changes
- Test with real data when possible
- Document any new approaches or learnings
- Update the knowledge base with new insights

### Collaboration
- Ask for help if you encounter challenges
- Provide clear explanations of your approach
- Reference specific documentation when discussing solutions
- Suggest improvements to the documentation when appropriate

This prompt, along with the referenced documentation, should provide you with a comprehensive understanding of the LeanEats application and the best practices for working with it. Refer to the specific documentation files for more detailed information on particular aspects of the application, and always consult the codebase for the most up-to-date implementation details.
