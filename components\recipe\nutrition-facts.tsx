'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { InfoIcon } from 'lucide-react';

interface NutritionInfo {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
  // Optional additional nutrients
  sugar?: number;
  sodium?: number;
  cholesterol?: number;
  saturatedFat?: number;
}

interface NutritionFactsProps {
  nutrition: NutritionInfo;
  servingSize?: string;
  scaleFactor?: number;
}

export function NutritionFacts({
  nutrition,
  servingSize = "1 serving",
  scaleFactor = 1
}: NutritionFactsProps) {
  // Scale nutrition values based on the scale factor
  const scaledNutrition = {
    calories: Math.round(nutrition.calories * scaleFactor),
    protein: Math.round(nutrition.protein * scaleFactor * 10) / 10,
    carbs: Math.round(nutrition.carbs * scaleFactor * 10) / 10,
    fat: Math.round(nutrition.fat * scaleFactor * 10) / 10,
    fiber: Math.round(nutrition.fiber * scaleFactor * 10) / 10,
    sugar: nutrition.sugar !== undefined ? Math.round(nutrition.sugar * scaleFactor * 10) / 10 : undefined,
    sodium: nutrition.sodium !== undefined ? Math.round(nutrition.sodium * scaleFactor) : undefined,
    cholesterol: nutrition.cholesterol !== undefined ? Math.round(nutrition.cholesterol * scaleFactor) : undefined,
    saturatedFat: nutrition.saturatedFat !== undefined ? Math.round(nutrition.saturatedFat * scaleFactor * 10) / 10 : undefined,
  };
  
  // Daily value percentages (based on a 2000 calorie diet)
  const dailyValues = {
    calories: (scaledNutrition.calories / 2000) * 100,
    protein: (scaledNutrition.protein / 50) * 100,
    carbs: (scaledNutrition.carbs / 300) * 100,
    fat: (scaledNutrition.fat / 70) * 100,
    fiber: (scaledNutrition.fiber / 25) * 100,
    sugar: scaledNutrition.sugar !== undefined ? (scaledNutrition.sugar / 50) * 100 : 0,
    sodium: scaledNutrition.sodium !== undefined ? (scaledNutrition.sodium / 2300) * 100 : 0,
    cholesterol: scaledNutrition.cholesterol !== undefined ? (scaledNutrition.cholesterol / 300) * 100 : 0,
    saturatedFat: scaledNutrition.saturatedFat !== undefined ? (scaledNutrition.saturatedFat / 20) * 100 : 0,
  };
  
  // Helper function to get color based on percentage of daily value
  const getBarColor = (percentage: number, nutrient: string): string => {
    // For calories, protein, and fiber, higher is generally better
    if (nutrient === 'protein' || nutrient === 'fiber') {
      return percentage < 10 ? 'bg-red-500' : 
             percentage < 25 ? 'bg-yellow-500' : 
             'bg-green-500';
    }
    
    // For fat, carbs, sugar, sodium, cholesterol, and saturated fat, lower is generally better
    return percentage > 50 ? 'bg-red-500' : 
           percentage > 25 ? 'bg-yellow-500' : 
           'bg-green-500';
  };
  
  return (
    <TooltipProvider>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            Nutrition Facts
            <Tooltip>
              <TooltipTrigger asChild>
                <InfoIcon className="h-4 w-4 ml-2 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Based on a 2000 calorie diet</p>
              </TooltipContent>
            </Tooltip>
          </CardTitle>
          <CardDescription>Per {servingSize}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex justify-between mb-1">
              <span className="font-medium">Calories</span>
              <span>{scaledNutrition.calories} kcal</span>
            </div>
            <div className="h-2 w-full bg-primary/20 rounded-full overflow-hidden">
              <div 
                className="h-full bg-primary rounded-full" 
                style={{ width: `${Math.min(dailyValues.calories, 100)}%` }} 
              />
            </div>
          </div>
          
          <div>
            <div className="flex justify-between mb-1">
              <span className="font-medium">Protein</span>
              <span>{scaledNutrition.protein}g</span>
            </div>
            <div className="h-2 w-full bg-blue-100 dark:bg-blue-900 rounded-full overflow-hidden">
              <div 
                className={`h-full ${getBarColor(dailyValues.protein, 'protein')} rounded-full`}
                style={{ width: `${Math.min(dailyValues.protein, 100)}%` }} 
              />
            </div>
          </div>
          
          <div>
            <div className="flex justify-between mb-1">
              <span className="font-medium">Carbs</span>
              <span>{scaledNutrition.carbs}g</span>
            </div>
            <div className="h-2 w-full bg-green-100 dark:bg-green-900 rounded-full overflow-hidden">
              <div 
                className={`h-full ${getBarColor(dailyValues.carbs, 'carbs')} rounded-full`}
                style={{ width: `${Math.min(dailyValues.carbs, 100)}%` }} 
              />
            </div>
          </div>
          
          <div>
            <div className="flex justify-between mb-1">
              <span className="font-medium">Fat</span>
              <span>{scaledNutrition.fat}g</span>
            </div>
            <div className="h-2 w-full bg-yellow-100 dark:bg-yellow-900 rounded-full overflow-hidden">
              <div 
                className={`h-full ${getBarColor(dailyValues.fat, 'fat')} rounded-full`}
                style={{ width: `${Math.min(dailyValues.fat, 100)}%` }} 
              />
            </div>
          </div>
          
          <div>
            <div className="flex justify-between mb-1">
              <span className="font-medium">Fiber</span>
              <span>{scaledNutrition.fiber}g</span>
            </div>
            <div className="h-2 w-full bg-purple-100 dark:bg-purple-900 rounded-full overflow-hidden">
              <div 
                className={`h-full ${getBarColor(dailyValues.fiber, 'fiber')} rounded-full`}
                style={{ width: `${Math.min(dailyValues.fiber, 100)}%` }} 
              />
            </div>
          </div>
          
          {scaledNutrition.sugar !== undefined && (
            <div>
              <div className="flex justify-between mb-1">
                <span className="font-medium">Sugar</span>
                <span>{scaledNutrition.sugar}g</span>
              </div>
              <div className="h-2 w-full bg-red-100 dark:bg-red-900 rounded-full overflow-hidden">
                <div 
                  className={`h-full ${getBarColor(dailyValues.sugar, 'sugar')} rounded-full`}
                  style={{ width: `${Math.min(dailyValues.sugar, 100)}%` }} 
                />
              </div>
            </div>
          )}
          
          {scaledNutrition.sodium !== undefined && (
            <div>
              <div className="flex justify-between mb-1">
                <span className="font-medium">Sodium</span>
                <span>{scaledNutrition.sodium}mg</span>
              </div>
              <div className="h-2 w-full bg-orange-100 dark:bg-orange-900 rounded-full overflow-hidden">
                <div 
                  className={`h-full ${getBarColor(dailyValues.sodium, 'sodium')} rounded-full`}
                  style={{ width: `${Math.min(dailyValues.sodium, 100)}%` }} 
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}
