import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient, ClientType } from '@/app/services/supabase-client';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Use admin client to bypass RLS
    const supabase = getSupabaseClient(ClientType.ADMIN);

    // Get the user ID from the query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Fetch the meal plan from the database
    const { data, error } = await supabase
      .from('meal_plans')
      .select('*')
      .eq('id', params.id)
      .single();

    // Check if the meal plan belongs to the user
    if (data && data.user_id !== userId) {
      console.log(`User ${userId} attempted to access meal plan ${params.id} belonging to user ${data.user_id}`);

      // For development purposes, we'll allow access but log a warning
      console.warn('Access granted for development purposes only');
    }

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch meal plan' },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { success: false, error: 'Meal plan not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error('Server error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}