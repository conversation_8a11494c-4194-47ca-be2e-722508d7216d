-- Create a function to execute arbitrary SQL queries
-- This function is used by the diagnostic tool to run queries
-- It should only be accessible to authenticated users with appropriate permissions

-- Drop the function if it already exists
DROP FUNCTION IF EXISTS pgcall(text);

-- Create the function
CREATE OR REPLACE FUNCTION pgcall(query text)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSONB;
BEGIN
    -- Check if the user is authenticated
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Not authenticated';
    END IF;
    
    -- Execute the query and return the results as JSON
    EXECUTE 'SELECT jsonb_agg(row_to_json(t)) FROM (' || query || ') t' INTO result;
    
    -- Return an empty array if no results
    IF result IS NULL THEN
        result := '[]'::JSONB;
    END IF;
    
    RETURN result;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error executing query: %', SQLERRM;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION pgcall(text) TO authenticated;
