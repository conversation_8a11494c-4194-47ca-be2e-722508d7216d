'use client';

import { useEffect, useState, useContext } from 'react';
import { Context } from './supabase-provider';
import { LoadingSpinner } from '@/components/ui/loading';

/**
 * SupabaseWrapper component
 *
 * This component ensures that the wrapped component has access to the Supabase client.
 * It provides a loading state while checking if the Supabase provider is initialized.
 *
 * @example
 * // In your page component
 * export default function MyPage() {
 *   return (
 *     <SupabaseWrapper>
 *       <MyComponent />
 *     </SupabaseWrapper>
 *   );
 * }
 */
export function SupabaseWrapper({
  children,
  loadingComponent
}: {
  children: React.ReactNode;
  loadingComponent?: React.ReactNode;
}) {
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Use useContext directly to avoid the error checking in useSupabase
  const context = useContext(Context);

  // Check if the context exists and is initialized
  useEffect(() => {
    console.log('%c[SupabaseWrapper] Effect running', 'color: blue; font-weight: bold');

    // If context doesn't exist, we're not inside a SupabaseProvider
    if (!context) {
      console.error('%c[SupabaseWrapper] Context is undefined! Make sure SupabaseProvider is in the component tree.', 'color: red; font-weight: bold');
      setError(new Error('SupabaseProvider not found in component tree'));
      return;
    }

    console.log('%c[SupabaseWrapper] Context exists:', 'color: blue', context);
    console.log('%c[SupabaseWrapper] isInitialized:', 'color: blue', context.isInitialized ? 'Yes ✅' : 'No ❌');
    console.log('%c[SupabaseWrapper] supabase client:', 'color: blue', context.supabase ? 'Exists ✅' : 'Missing ❌');

    // If context exists but isn't initialized yet, wait for it
    if (!context.isInitialized) {
      console.log('%c[SupabaseWrapper] Context exists but not initialized yet. Waiting...', 'color: orange');
      return;
    }

    // Context exists and is initialized
    console.log('%c[SupabaseWrapper] Context is initialized and ready!', 'color: green; font-weight: bold');
    setIsReady(true);
  }, [context]);

  // Add another effect to log when the component mounts and unmounts
  useEffect(() => {
    console.log('%c[SupabaseWrapper] Component mounted', 'color: blue; font-weight: bold');

    return () => {
      console.log('%c[SupabaseWrapper] Component unmounting', 'color: orange; font-weight: bold');
    };
  }, []);

  // If there was an error, show an error message
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[200px] p-4 text-red-500 bg-red-50 rounded-md">
        <h3 className="text-lg font-semibold mb-2">Supabase Provider Error</h3>
        <p className="text-sm text-center">{error.message}</p>
        <p className="text-xs mt-4 text-center">Make sure your component is wrapped in SupabaseProvider</p>
      </div>
    );
  }

  // Show loading component while waiting for the provider to initialize
  if (!isReady) {
    return loadingComponent || (
      <div className="flex items-center justify-center min-h-[200px]">
        <LoadingSpinner size="lg" />
        <p className="ml-2 text-muted-foreground">Loading Supabase...</p>
      </div>
    );
  }

  // Provider is initialized, render the children
  return <>{children}</>;
}
