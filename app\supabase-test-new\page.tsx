'use client';

import React, { useEffect, useState } from 'react';
import { useSupabase } from '@/components/supabase-provider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

export default function SupabaseTestNewPage() {
  const [testResult, setTestResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { supabase, isLoading } = useSupabase();

  const runTest = async () => {
    try {
      setTestResult(null);
      setError(null);

      // Test 1: Check if supabase client exists
      if (!supabase) {
        throw new Error('Supabase client is not available');
      }

      // Test 2: Try to get the current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError) {
        throw new Error(`Auth error: ${userError.message}`);
      }

      // Test 3: Try to query a table
      const { data, error: queryError } = await supabase
        .from('recipes')
        .select('id, name')
        .limit(1);

      if (queryError) {
        throw new Error(`Query error: ${queryError.message}`);
      }

      // All tests passed
      setTestResult(`
        Tests passed successfully!
        - Supabase client is available
        - Auth is working ${user ? `(logged in as ${user.email})` : '(not logged in)'}
        - Query executed successfully ${data?.length ? `(found ${data.length} recipes)` : '(no recipes found)'}
      `);
    } catch (err: any) {
      console.error('Test failed:', err);
      setError(err.message || 'An unknown error occurred');
    }
  };

  // Show loading state if Supabase is still initializing
  if (isLoading) {
    return (
      <div className="container max-w-4xl mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle>Supabase Test (New Provider)</CardTitle>
            <CardDescription>Testing the new Supabase provider</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
              <p className="ml-4 text-muted-foreground">Initializing Supabase client...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle>Supabase Test (New Provider)</CardTitle>
          <CardDescription>Testing the new Supabase provider</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${supabase ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <p>Supabase Client: {supabase ? 'Available' : 'Not Available'}</p>
            </div>

            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${!isLoading ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
              <p>Loading State: {isLoading ? 'Loading' : 'Ready'}</p>
            </div>

            <Button onClick={runTest} className="mt-4">
              Run Tests
            </Button>
          </div>

          {testResult && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-md text-green-700 mt-6 whitespace-pre-line">
              {testResult}
            </div>
          )}

          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md text-red-700 mt-6">
              Error: {error}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
