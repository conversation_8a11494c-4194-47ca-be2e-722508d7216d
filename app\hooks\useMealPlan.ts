import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { WeeklyPlan } from '@/types/mealPlan';
import { toast } from 'sonner';

const CACHE_TIME = 1000 * 60 * 5; // 5 minutes
const STALE_TIME = 1000 * 60 * 1; // 1 minute

export function useMealPlan(planId: string) {
  const queryClient = useQueryClient();

  const { data: mealPlan, isLoading, error } = useQuery({
    queryKey: ['mealPlan', planId],
    queryFn: async () => {
      const response = await fetch(`/api/meal-plans/${planId}`);
      if (!response.ok) throw new Error('Failed to fetch meal plan');
      return response.json();
    },
    cacheTime: CACHE_TIME,
    staleTime: STALE_TIME,
  });

  const customizeMeal = useMutation({
    mutationFn: async ({ mealId, modifications }) => {
      const response = await fetch('/api/meal-plans/customize', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          mealPlanId: planId,
          mealId,
          modifications,
        }),
      });
      if (!response.ok) throw new Error('Failed to customize meal');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['mealPlan', planId]);
      toast.success('Meal customized successfully');
    },
    onError: () => {
      toast.error('Failed to customize meal');
    },
  });

  return {
    mealPlan,
    isLoading,
    error,
    customizeMeal,
  };
}