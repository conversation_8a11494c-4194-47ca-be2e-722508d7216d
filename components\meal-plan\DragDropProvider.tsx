"use client";

import { ReactNode, createContext, useContext, useState, useCallback } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { MealDragItem, MealPlan } from '@/types/new-meal-plan';
import { toast } from 'sonner';

interface DragDropContextType {
  onDrop: (item: MealDragItem, targetDay: string, targetMealType: string) => void;
  isDragging: boolean;
  draggedItem: MealDragItem | null;
}

const DragDropContext = createContext<DragDropContextType>({
  onDrop: () => {},
  isDragging: false,
  draggedItem: null,
});

export const useDragDrop = () => useContext(DragDropContext);

interface DragDropProviderProps {
  children: ReactNode;
  onDrop: (item: MealDragItem, targetDay: string, targetMealType: string) => void;
}

export function DragDropProvider({ children, onDrop }: DragDropProviderProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [draggedItem, setDraggedItem] = useState<MealDragItem | null>(null);

  // Enhanced onDrop function that updates the dragging state
  const handleDrop = useCallback((item: MealDragItem, targetDay: string, targetMealType: string) => {
    // Don't do anything if dropping on the same spot
    if (item.day === targetDay && item.mealType === targetMealType) {
      return;
    }

    // Call the original onDrop function
    onDrop(item, targetDay, targetMealType);

    // Show a success toast
    toast.success(`Moved ${item.meal.name} to ${targetMealType} on ${new Date(targetDay).toLocaleDateString()}`);

    // Reset the dragging state
    setIsDragging(false);
    setDraggedItem(null);
  }, [onDrop]);

  // Create a monitor to track drag state
  const dragMonitor = useCallback((monitor: any) => {
    setIsDragging(monitor.isDragging());
    if (monitor.isDragging()) {
      setDraggedItem(monitor.getItem());
    } else {
      setDraggedItem(null);
    }
  }, []);

  return (
    <DndProvider backend={HTML5Backend}>
      <DragDropContext.Provider value={{
        onDrop: handleDrop,
        isDragging,
        draggedItem
      }}>
        {children}
      </DragDropContext.Provider>
    </DndProvider>
  );
}
