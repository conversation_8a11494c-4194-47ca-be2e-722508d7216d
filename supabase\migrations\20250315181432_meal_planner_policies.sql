-- Enable RLS for the meals table
ALTER TABLE public.meals ENABLE ROW LEVEL SECURITY;

-- Create policies for the meals table
CREATE POLICY "Users can view their own meals"
ON public.meals FOR SELECT
TO authenticated
USING (user_id = auth.uid()::text);

CREATE POLICY "Users can create their own meals"
ON public.meals FOR INSERT
TO authenticated
WITH CHECK (user_id = auth.uid()::text);

CREATE POLICY "Users can update their own meals"
ON public.meals FOR UPDATE
TO authenticated
USING (user_id = auth.uid()::text);

CREATE POLICY "Users can delete their own meals"
ON public.meals FOR DELETE
TO authenticated
USING (user_id = auth.uid()::text);