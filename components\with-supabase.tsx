'use client';

import { ComponentType } from 'react';
import { SupabaseWrapper } from './supabase-wrapper';
import { LoadingSpinner } from '@/components/ui/loading';

/**
 * withSupabase Higher-Order Component
 * 
 * This HOC wraps a component with the SupabaseWrapper to ensure it has access to the Supabase client.
 * It provides a loading state while checking if the Supabase provider is initialized.
 * 
 * @example
 * // In your page component
 * function MyPage() {
 *   const { supabase } = useSupabase();
 *   // ...
 * }
 * 
 * export default withSupabase(MyPage);
 */
export function withSupabase<P extends object>(
  Component: ComponentType<P>,
  loadingComponent?: React.ReactNode
) {
  // Return a new component that wraps the original component with SupabaseWrapper
  return function WithSupabaseComponent(props: P) {
    return (
      <SupabaseWrapper
        loadingComponent={
          loadingComponent || (
            <div className="flex items-center justify-center min-h-[200px]">
              <LoadingSpinner size="lg" />
              <p className="ml-2 text-muted-foreground">Loading Supabase...</p>
            </div>
          )
        }
      >
        <Component {...props} />
      </SupabaseWrapper>
    );
  };
}
