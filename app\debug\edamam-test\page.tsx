"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

export default function EdamamTestPage() {
  const [query, setQuery] = useState('');
  const [mealType, setMealType] = useState('breakfast');
  const [calories, setCalories] = useState('400-600');
  const [diet, setDiet] = useState<string[]>([]);
  const [excluded, setExcluded] = useState('');
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const dietOptions = [
    { id: 'balanced', label: 'Balanced' },
    { id: 'high-protein', label: 'High Protein' },
    { id: 'low-carb', label: 'Low Carb' },
    { id: 'low-fat', label: 'Low Fat' }
  ];

  const handleDietChange = (id: string, checked: boolean) => {
    if (checked) {
      setDiet([...diet, id]);
    } else {
      setDiet(diet.filter(item => item !== id));
    }
  };

  const testDirectApi = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      // Get the API keys from environment variables
      const appId = process.env.NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES;
      const appKey = process.env.NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES;

      console.log('API Keys:', { appId, appKey: appKey ? 'Present (not shown)' : 'Missing' });

      if (!appId || !appKey) {
        setError('Missing API keys');
        return;
      }

      // Instead of calling the Edamam API directly (which would cause CORS issues),
      // we'll create a new API route specifically for this test

      // Construct the URL for our API route
      const queryParams = new URLSearchParams({
        query: query || '',
        mealType: mealType,
        calories: calories,
        diet: diet.join(','),
        excluded: excluded,
        random: 'false',
        direct: 'true' // Flag to indicate this is a direct test
      });

      const url = `/api/edamam/test?${queryParams.toString()}`;
      console.log('Test API Route URL:', url);

      // Make the request
      const response = await fetch(url);
      const data = await response.json();

      if (!response.ok) {
        setError(`API Error (${response.status}): ${data.error || 'Unknown error'}`);
        return;
      }

      setResult(data);
    } catch (err: any) {
      console.error('Error testing Edamam API:', err);
      setError(`Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testApiRoute = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      // Construct the URL
      const queryParams = new URLSearchParams({
        query: query || '',
        mealType: mealType,
        calories: calories,
        diet: diet.join(','),
        excluded: excluded,
        random: 'true'
      });

      const url = `/api/edamam/search?${queryParams.toString()}`;
      console.log('API Route URL:', url);

      // Make the request
      const response = await fetch(url);
      const data = await response.json();

      if (!response.ok) {
        setError(`API Error (${response.status}): ${data.error || 'Unknown error'}`);
        return;
      }

      setResult(data);
    } catch (err: any) {
      console.error('Error testing API route:', err);
      setError(`Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Edamam API Test</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="p-4 border rounded-md">
          <h2 className="text-lg font-bold mb-4">API Parameters</h2>

          <div className="space-y-4">
            <div>
              <Label htmlFor="query">Search Query (optional)</Label>
              <Input
                id="query"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Leave empty to use meal type as query"
              />
            </div>

            <div>
              <Label htmlFor="mealType">Meal Type</Label>
              <Select value={mealType} onValueChange={setMealType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select meal type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="breakfast">Breakfast</SelectItem>
                  <SelectItem value="lunch">Lunch</SelectItem>
                  <SelectItem value="dinner">Dinner</SelectItem>
                  <SelectItem value="snack">Snack</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="calories">Calories Range</Label>
              <Input
                id="calories"
                value={calories}
                onChange={(e) => setCalories(e.target.value)}
                placeholder="e.g., 400-600"
              />
            </div>

            <div>
              <Label>Diet</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {dietOptions.map((option) => (
                  <div key={option.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`diet-${option.id}`}
                      checked={diet.includes(option.id)}
                      onCheckedChange={(checked) => handleDietChange(option.id, checked === true)}
                    />
                    <Label htmlFor={`diet-${option.id}`}>{option.label}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="excluded">Excluded Ingredients (comma-separated)</Label>
              <Input
                id="excluded"
                value={excluded}
                onChange={(e) => setExcluded(e.target.value)}
                placeholder="e.g., peanuts, shellfish"
              />
            </div>
          </div>
        </div>

        <div className="p-4 border rounded-md">
          <h2 className="text-lg font-bold mb-4">Test Options</h2>

          <div className="space-y-4">
            <Button
              onClick={testDirectApi}
              disabled={loading}
              className="w-full"
            >
              Test Direct API Call
            </Button>

            <Button
              onClick={testApiRoute}
              disabled={loading}
              className="w-full"
            >
              Test API Route
            </Button>

            <div className="p-2 bg-gray-100 rounded-md">
              <h3 className="font-semibold">API Keys</h3>
              <p className="text-sm">
                App ID: {process.env.NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES || 'Missing'}
              </p>
              <p className="text-sm">
                App Key: {process.env.NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES ? 'Present (not shown)' : 'Missing'}
              </p>
              <p className="text-sm mt-2">
                <strong>Note:</strong> These keys are used in both client-side and server-side code.
                Make sure they are correctly set in your .env file.
              </p>
            </div>
          </div>
        </div>
      </div>

      {loading && (
        <div className="p-4 border rounded-md mb-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            <span className="ml-2">Loading...</span>
          </div>
        </div>
      )}

      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md mb-6">
          <h2 className="text-lg font-bold text-red-600 mb-2">Error</h2>
          <p className="text-red-600 whitespace-pre-wrap">{error}</p>
        </div>
      )}

      {result && (
        <div className="p-4 border rounded-md">
          <h2 className="text-lg font-bold mb-2">Result</h2>

          <div className="mb-4">
            <h3 className="font-semibold">Found {result.hits?.length || 0} recipes</h3>
          </div>

          {result.hits && result.hits.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {result.hits.map((hit: any, index: number) => (
                <div key={index} className="p-3 border rounded-md">
                  <h3 className="font-semibold">{hit.recipe.label}</h3>
                  {hit.recipe.image && (
                    <img
                      src={hit.recipe.image}
                      alt={hit.recipe.label}
                      className="w-full h-40 object-cover rounded-md my-2"
                    />
                  )}
                  <p className="text-sm">Calories: {Math.round(hit.recipe.calories / hit.recipe.yield)} per serving</p>
                  <p className="text-sm">Servings: {hit.recipe.yield}</p>

                  <details className="mt-2">
                    <summary className="cursor-pointer text-blue-600">View Details</summary>
                    <pre className="mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-40 text-xs">
                      {JSON.stringify(hit.recipe, null, 2)}
                    </pre>
                  </details>
                </div>
              ))}
            </div>
          ) : (
            <p>No recipes found</p>
          )}

          <details className="mt-4">
            <summary className="cursor-pointer text-blue-600">View Raw Response</summary>
            <pre className="mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-96 text-xs">
              {JSON.stringify(result, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  );
}
