// This script runs the diagnostic SQL and saves the results to a file
// It requires the @supabase/supabase-js package

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase URL or service role key not found in environment variables.');
  console.error('Please make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.');
  process.exit(1);
}

// Create Supabase client with service role key (needed for full database access)
const supabase = createClient(supabaseUrl, supabaseKey);

// Read the diagnostic SQL file
const sqlFilePath = path.join(__dirname, 'check_database.sql');
const sql = fs.readFileSync(sqlFilePath, 'utf8');

// Split the SQL into individual queries (each query ends with a semicolon)
const queries = sql.split(';').filter(query => query.trim() !== '');

// Function to run a query and return the results
async function runQuery(query) {
  try {
    const { data, error } = await supabase.rpc('pgcall', { query });
    
    if (error) {
      console.error('Error running query:', error);
      return { query, error: error.message, data: null };
    }
    
    return { query, data, error: null };
  } catch (error) {
    console.error('Exception running query:', error);
    return { query, error: error.message, data: null };
  }
}

// Main function to run all queries and save results
async function runDiagnostic() {
  console.log('Running database diagnostic...');
  
  const results = [];
  
  // Run each query and collect results
  for (let i = 0; i < queries.length; i++) {
    const query = queries[i].trim();
    if (!query) continue;
    
    // Extract the comment above the query to use as a title
    const commentMatch = query.match(/--\s*(.*?)(?:\r?\n|\r)/);
    const title = commentMatch ? commentMatch[1].trim() : `Query ${i + 1}`;
    
    console.log(`Running query: ${title}`);
    const result = await runQuery(query);
    results.push({ title, ...result });
  }
  
  // Create output directory if it doesn't exist
  const outputDir = path.join(__dirname, 'results');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir);
  }
  
  // Save results to a JSON file
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const outputFilePath = path.join(outputDir, `diagnostic_results_${timestamp}.json`);
  fs.writeFileSync(outputFilePath, JSON.stringify(results, null, 2));
  
  // Generate a markdown report
  const markdownPath = path.join(outputDir, `diagnostic_report_${timestamp}.md`);
  generateMarkdownReport(results, markdownPath);
  
  console.log(`Diagnostic complete. Results saved to ${outputFilePath}`);
  console.log(`Markdown report saved to ${markdownPath}`);
}

// Function to generate a markdown report from the results
function generateMarkdownReport(results, outputPath) {
  let markdown = '# Database Diagnostic Report\n\n';
  markdown += `Generated on: ${new Date().toLocaleString()}\n\n`;
  
  for (const result of results) {
    markdown += `## ${result.title}\n\n`;
    
    if (result.error) {
      markdown += `**Error:** ${result.error}\n\n`;
    } else if (!result.data || result.data.length === 0) {
      markdown += 'No results found.\n\n';
    } else {
      // Create a table header
      const headers = Object.keys(result.data[0]);
      markdown += '| ' + headers.join(' | ') + ' |\n';
      markdown += '| ' + headers.map(() => '---').join(' | ') + ' |\n';
      
      // Add table rows
      for (const row of result.data) {
        markdown += '| ' + headers.map(header => {
          const value = row[header];
          if (value === null) return 'NULL';
          if (typeof value === 'object') return JSON.stringify(value);
          return String(value).replace(/\|/g, '\\|');
        }).join(' | ') + ' |\n';
      }
      
      markdown += '\n';
    }
  }
  
  fs.writeFileSync(outputPath, markdown);
}

// Run the diagnostic
runDiagnostic().catch(error => {
  console.error('Error running diagnostic:', error);
  process.exit(1);
});
