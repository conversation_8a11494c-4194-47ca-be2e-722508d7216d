export interface Nutrition {
  calories: number;
  protein: number;
  carbs: number;
  fats: number;
}

export interface Meal {
  id: string;
  type: string;
  name: string;
  image?: string;
  prepTime: number;
  cost: number;
  servings: number;
  nutrition: Nutrition;
}

export interface DayPlan {
  date: string;
  day: string;
  meals: Meal[];
  totalCalories: number;
  totalCost: number;
  macros: {
    protein: number;
    carbs: number;
    fats: number;
  };
}

export interface WeeklyPlan {
  id: string;
  startDate: string;
  endDate: string;
  user_id: string;
  status: 'active' | 'inactive' | 'draft';
  totalCost: number;
  averageCalories: number;
  days: DayPlan[];
}