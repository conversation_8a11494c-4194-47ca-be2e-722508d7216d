'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { useRouter, usePathname } from "next/navigation";
import {
  Menu,
  Home,
  Calendar,
  ShoppingCart,
  Settings,
  LogOut,
  User,
  Package,
  Star
} from "lucide-react";
import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";

const menuItems = [
  {
    name: 'Dashboard',
    path: '/dashboard',
    icon: Home
  },
  {
    name: 'Meal Plans',
    path: '/meal-plan/view',
    icon: Calendar
  },
  {
    name: 'New Meal Plans',
    path: '/meal-plan/new',
    icon: Calendar
  },
  {
    name: 'Favorite Meals',
    path: '/favorites',
    icon: Star
  },
  {
    name: 'Shopping List',
    path: '/dashboard/shopping-list',
    icon: ShoppingCart
  },
  {
    name: 'Pantry',
    path: '/pantry',
    icon: Package
  },
  {
    name: 'Settings',
    path: '/settings',
    icon: Set<PERSON><PERSON>
  }
];

export function Burger<PERSON><PERSON><PERSON>() {
  const router = useRouter();
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const version = "v1.0.0";

  const handleNavigation = (path: string) => {
    router.push(path);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') setIsOpen(false);
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, []);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="hover:bg-accent transition-colors"
        >
          <Menu className="h-6 w-6" />
          <span className="sr-only">Open menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent
        side="left"
        className="w-[300px] p-0 border-r"
      >
        <SheetHeader className="sr-only">
          <SheetTitle>Navigation Menu</SheetTitle>
        </SheetHeader>

        <div className="flex flex-col h-full">
          {/* User Profile Section */}
          <div className="p-6 border-b bg-accent/10">
            <div className="flex items-center gap-4">
              <div className="h-10 w-10 rounded-full bg-accent flex items-center justify-center">
                <User className="h-6 w-6" />
              </div>
              <div>
                <h2 className="font-semibold">User Name</h2>
                <p className="text-sm text-muted-foreground"><EMAIL></p>
              </div>
            </div>
          </div>

          {/* Navigation Items */}
          <nav className="flex-1 p-4">
            <div className="space-y-2">
              {menuItems.map((item) => {
                const Icon = item.icon;
                const isActive = pathname === item.path;
                return (
                  <Button
                    key={item.path}
                    variant={isActive ? "secondary" : "ghost"}
                    className={cn(
                      "w-full justify-start gap-2 transition-colors",
                      isActive && "bg-accent"
                    )}
                    onClick={() => handleNavigation(item.path)}
                  >
                    <Icon className={cn(
                      "h-5 w-5 transition-colors",
                      isActive ? "text-primary" : "text-muted-foreground"
                    )} />
                    {item.name}
                  </Button>
                );
              })}
            </div>
          </nav>

          {/* Footer Section */}
          <div className="p-4 border-t">
            <div className="flex flex-col gap-4">
              <Button
                variant="ghost"
                className="w-full justify-start gap-2 text-destructive hover:text-destructive"
                onClick={() => handleNavigation('/logout')}
              >
                <LogOut className="h-5 w-5" />
                Logout
              </Button>
              <div className="text-xs text-muted-foreground text-center">
                {version}
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}