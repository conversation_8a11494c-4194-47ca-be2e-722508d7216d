import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { GroceryListResponse } from '@/app/types/api';

export async function GET(): Promise<NextResponse<GroceryListResponse>> {
  try {
    // TODO: Replace with actual database query
    const mockGroceryList = {
      categories: [
        {
          id: "1",
          name: "Produce",
          items: [
            { id: "p1", name: "Avocado", quantity: "2", checked: false },
            { id: "p2", name: "Spinach", quantity: "200g", checked: false },
          ]
        },
        {
          id: "2",
          name: "Protein",
          items: [
            { id: "pr1", name: "Chicken Breast", quantity: "500g", checked: false },
            { id: "pr2", name: "<PERSON>", quantity: "400g", checked: false },
          ]
        }
      ]
    };

    return NextResponse.json({ data: { groceryList: mockGroceryList } });
  } catch (error) {
    console.error('Error fetching grocery list:', error);
    return NextResponse.json(
      { error: 'Failed to fetch grocery list' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    const { categoryId, itemId, checked } = await request.json();

    if (!categoryId || !itemId || checked === undefined) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // TODO: Update item status in database
    return NextResponse.json({ data: { success: true } });
  } catch (error) {
    console.error('Error updating grocery item:', error);
    return NextResponse.json(
      { error: 'Failed to update grocery item' },
      { status: 500 }
    );
  }
}