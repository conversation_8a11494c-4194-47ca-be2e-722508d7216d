"use client";

import { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { format, addDays } from 'date-fns';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import { ChevronLeft, Save } from 'lucide-react';
import { useMealPlanStore } from '@/lib/stores/meal-plan-store-supabase';
import { DragDropProvider } from '@/components/meal-plan/DragDropProvider';
import { MealPlanHeader } from '@/components/meal-plan/MealPlanHeader';
import { MealPlanSidebar } from '@/components/meal-plan/MealPlanSidebar';
import { MealPlan as LegacyMealPlanType } from '@/types/meal-plan';
import { MealPlanCalendar } from '@/components/meal-plan/MealPlanCalendar';
import { MealPlanList } from '@/components/meal-plan/MealPlanList';
import { MealDetailsModal } from '@/components/meal-plan/MealDetailsModal';
import { GeneratePlanModal } from '@/components/meal-plan/GeneratePlanModal';
import { AddMealModal } from '@/components/meal-plan/AddMealModal';
import { ViewShoppingListModal } from '@/components/meal-plan/ViewShoppingListModal';
import { CalendarAssignmentModal } from '@/components/meal-plan/CalendarAssignmentModal';
import {
  Meal,
  MealPlan,
  MealDragItem,
  MealGenerationOptions,
  LegacyMealPlan,
  ShoppingItem,
  PantryItem
} from '@/types/new-meal-plan';
import {
  getMealPlan,
  generateMealPlan,
  generateMeal,
  generateShoppingList,
  convertLegacyMealPlan
} from '@/lib/meal-plan-utils';
import { mealPlanService } from '@/app/services/meal-plan-service';
import { useSupabase } from '@/components/supabase-provider';

export default function MealPlanPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { supabase } = useSupabase();
  const { mealPlan, isLoading, setMealPlan, setIsLoading, addMeal, removeMeal, setMealStatus, moveMeal, setUserId, saveMealPlanToDatabase } = useMealPlanStore();

  // Legacy meal plans state
  const [legacyMealPlans, setLegacyMealPlans] = useState<LegacyMealPlanType[]>([]);
  const [loadingLegacyPlans, setLoadingLegacyPlans] = useState(false);
  const [activeLegacyPlanId, setActiveLegacyPlanId] = useState<string | null>(null);

  // UI state
  const [currentView, setCurrentView] = useState<'calendar' | 'list'>('calendar');
  const [currentLayout, setCurrentLayout] = useState<'grid' | 'list'>('grid');

  // Modal state
  const [selectedMeal, setSelectedMeal] = useState<Meal | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isGenerateModalOpen, setIsGenerateModalOpen] = useState(false);
  const [isAddMealModalOpen, setIsAddMealModalOpen] = useState(false);
  const [isShoppingListModalOpen, setIsShoppingListModalOpen] = useState(false);
  const [isCalendarModalOpen, setIsCalendarModalOpen] = useState(false);
  const [selectedLegacyPlan, setSelectedLegacyPlan] = useState<any>(null);

  // Shopping list state
  const [shoppingList, setShoppingList] = useState<any[]>([]);
  const [pantryItems, setPantryItems] = useState<any[]>([]);

  // Loading state
  const [isGenerating, setIsGenerating] = useState(false);
  const [isAddingMeal, setIsAddingMeal] = useState(false);
  const [isAssigningToCalendar, setIsAssigningToCalendar] = useState(false);

  // Get refresh parameter from URL
  const refreshParam = searchParams.get('refresh');

  // Fetch legacy meal plans
  const fetchLegacyMealPlans = useCallback(async () => {
    if (!supabase) return;

    setLoadingLegacyPlans(true);
    try {
      console.log('Fetching meal plans...');
      const { data, error } = await mealPlanService.getMealPlans();

      if (error) {
        console.error('Error fetching meal plans:', error);
        toast.error('Failed to fetch meal plans');
        return;
      }

      if (data && data.length > 0) {
        console.log(`Found ${data.length} meal plans`);
        setLegacyMealPlans(data);

        // If no active plan is set, select the first one
        if (!activeLegacyPlanId) {
          setActiveLegacyPlanId(data[0].id);
        }
      } else {
        console.log('No meal plans found');
      }
    } catch (error) {
      console.error('Error fetching legacy meal plans:', error);
      toast.error('Failed to fetch meal plans');
    } finally {
      setLoadingLegacyPlans(false);
    }
  }, [supabase, activeLegacyPlanId]);

  // Set user ID in the store when the component mounts
  useEffect(() => {
    const getUserId = async () => {
      if (!supabase) return;

      const { data } = await supabase.auth.getUser();
      if (data?.user) {
        setUserId(data.user.id);
        console.log('Set user ID in store:', data.user.id);
      }
    };

    getUserId();
  }, [supabase, setUserId]);

  // Refresh meal plans when the refresh parameter changes
  useEffect(() => {
    if (refreshParam) {
      console.log('Refresh parameter detected, refreshing meal plans...');
      fetchLegacyMealPlans();
    }
  }, [refreshParam, fetchLegacyMealPlans]);

  // Fetch meal plan data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch legacy meal plans
        await fetchLegacyMealPlans();

        // If legacy meal plans are available, convert the first one to the new format
        if (legacyMealPlans.length > 0) {
          // If refreshing, use the most recently created meal plan
          const planToUse = refreshParam
            ? legacyMealPlans.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0]
            : legacyMealPlans[0];

          console.log('Converting meal plan to new format:', planToUse.id);
          const convertedPlan = convertLegacyMealPlan(planToUse);
          setMealPlan(convertedPlan);
          setActiveLegacyPlanId(planToUse.id);
        } else {
          // If no legacy meal plans, fetch a new meal plan
          console.log('No meal plans available, creating a default one');
          const newMealPlan = await getMealPlan();
          setMealPlan(newMealPlan);
        }
      } catch (error) {
        console.error('Error in fetchData:', error);
        toast.error('An error occurred while fetching data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [setMealPlan, setIsLoading, fetchLegacyMealPlans, legacyMealPlans.length, refreshParam, setActiveLegacyPlanId]);

  // Handle drag and drop
  const handleDrop = (item: MealDragItem, targetDay: string, targetMealType: string) => {
    if (item.day === targetDay && item.mealType === targetMealType) return;

    moveMeal(
      { day: item.day, mealType: item.mealType },
      { day: targetDay, mealType: targetMealType }
    );

    toast.success(`Moved ${item.meal.name} to ${format(new Date(targetDay), 'EEE, MMM d')} - ${targetMealType}`);
  };

  // Handle view meal details
  const handleViewMealDetails = (meal: Meal) => {
    setSelectedMeal(meal);
    setIsDetailsModalOpen(true);
  };

  // Handle generate plan
  const handleGeneratePlan = async (options: MealGenerationOptions) => {
    setIsGenerating(true);
    try {
      const newMealPlan = await generateMealPlan(options);
      setMealPlan(newMealPlan);
      setIsGenerateModalOpen(false);
      toast.success('Meal plan generated successfully');
    } catch (error) {
      console.error('Error generating meal plan:', error);
      toast.error('Failed to generate meal plan');
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle add meal
  const handleAddMeal = async (date: Date, mealType: string) => {
    setIsAddingMeal(true);
    try {
      const newMeal = await generateMeal({ mealType });
      const dateStr = format(date, 'yyyy-MM-dd');
      addMeal(dateStr, mealType, newMeal);
      setIsAddMealModalOpen(false);
      toast.success(`Added ${newMeal.name} to ${format(date, 'EEE, MMM d')}`);
    } catch (error) {
      console.error('Error adding meal:', error);
      toast.error('Failed to add meal');
    } finally {
      setIsAddingMeal(false);
    }
  };

  // Handle view shopping list
  const handleViewShoppingList = async (specificPlan?: LegacyMealPlanType) => {
    try {
      // Generate shopping list based on the current meal plan
      const { shoppingList: newShoppingList, pantryItems: newPantryItems } = await generateShoppingList(mealPlan || undefined);
      setShoppingList(newShoppingList);
      setPantryItems(newPantryItems);

      // If a specific plan was provided, use it
      if (specificPlan) {
        setSelectedLegacyPlan(specificPlan);
      }

      setIsShoppingListModalOpen(true);
    } catch (error) {
      console.error('Error generating shopping list:', error);
      toast.error('Failed to generate shopping list');
    }
  };

  // Handle adding items to pantry
  const handleAddToPantry = async (items: ShoppingItem[]) => {
    try {
      // In a real implementation, this would call an API to add items to the pantry
      console.log('Adding items to pantry:', items);

      // Update the pantry items
      const newPantryItems = [...pantryItems];

      items.forEach(item => {
        // Check if the item already exists in the pantry
        const existingIndex = newPantryItems.findIndex(p => p.name.toLowerCase() === item.name.toLowerCase());

        if (existingIndex >= 0) {
          // Update existing item
          const existingItem = newPantryItems[existingIndex];
          const existingQuantity = parseFloat(existingItem.quantity) || 0;
          const newQuantity = parseFloat(item.quantity) || 0;

          newPantryItems[existingIndex] = {
            ...existingItem,
            quantity: (existingQuantity + newQuantity).toString(),
            lowStock: false // Reset low stock flag
          };
        } else {
          // Add new item
          newPantryItems.push({
            id: `p${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            name: item.name,
            quantity: item.quantity,
            unit: item.unit,
            category: item.category,
            lowStock: false
          });
        }

        // Mark the item as in pantry in the shopping list
        setShoppingList(prev =>
          prev.map(i =>
            i.name.toLowerCase() === item.name.toLowerCase()
              ? { ...i, inPantry: true }
              : i
          )
        );
      });

      setPantryItems(newPantryItems);
      return true;
    } catch (error) {
      console.error('Error adding items to pantry:', error);
      throw error;
    }
  };

  // Handle updating shopping list
  const handleUpdateShoppingList = (items: ShoppingItem[]) => {
    try {
      // Add new items to the shopping list
      const newShoppingList = [...shoppingList];

      items.forEach(item => {
        // Check if the item already exists in the shopping list
        const existingIndex = newShoppingList.findIndex(i => i.name.toLowerCase() === item.name.toLowerCase());

        if (existingIndex >= 0) {
          // Update existing item
          const existingItem = newShoppingList[existingIndex];
          const existingQuantity = parseFloat(existingItem.quantity) || 0;
          const newQuantity = parseFloat(item.quantity) || 0;

          newShoppingList[existingIndex] = {
            ...existingItem,
            quantity: (existingQuantity + newQuantity).toString()
          };
        } else {
          // Add new item
          newShoppingList.push({
            ...item,
            id: `s${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
          });
        }
      });

      setShoppingList(newShoppingList);
      return true;
    } catch (error) {
      console.error('Error updating shopping list:', error);
      throw error;
    }
  };

  // Handle regenerating shopping list
  const handleRegenerateShoppingList = async (mealPlan: MealPlan) => {
    try {
      const { shoppingList: newShoppingList, pantryItems: newPantryItems } = await generateShoppingList(mealPlan);
      setShoppingList(newShoppingList);
      setPantryItems(newPantryItems);
      return { shoppingList: newShoppingList, pantryItems: newPantryItems };
    } catch (error) {
      console.error('Error regenerating shopping list:', error);
      throw error;
    }
  };

  // Handle view legacy plan
  const handleViewLegacyPlan = (plan: any) => {
    const convertedPlan = convertLegacyMealPlan(plan);
    setMealPlan(convertedPlan);
    setCurrentView('calendar');
    setActiveLegacyPlanId(plan.id);
    toast.success(`Viewing ${plan.name || 'Meal Plan'}`);
  };

  // Handle select legacy plan from sidebar
  const handleSelectLegacyPlan = (plan: LegacyMealPlanType) => {
    handleViewLegacyPlan(plan);
  };

  // Handle delete legacy plan
  const handleDeleteLegacyPlan = async (planId: string) => {
    try {
      const { success, error } = await mealPlanService.deleteMealPlan(planId);

      if (error) {
        console.error('Error deleting meal plan:', error);
        toast.error(typeof error === 'string' ? error : 'Failed to delete meal plan');
        return;
      }

      if (success) {
        setLegacyMealPlans(prev => prev.filter(plan => plan.id !== planId));
        toast.success('Meal plan deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting meal plan:', error);
      toast.error('An unexpected error occurred');
    }
  };

  // Handle add to calendar
  const handleAddToCalendar = (plan: any) => {
    setSelectedLegacyPlan(plan);
    setIsCalendarModalOpen(true);
  };

  // Check for calendar conflicts
  const checkCalendarConflicts = async (weekStartDate: Date) => {
    try {
      // In a real implementation, this would check the database for existing meal plans
      console.log('Checking for conflicts starting', format(weekStartDate, 'MMM d, yyyy'));

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 800));

      // For demo purposes, we'll randomly generate conflicts
      const hasConflicts = Math.random() > 0.5;

      // Generate random conflict days
      const conflictDays: string[] = [];
      if (hasConflicts) {
        const days = 7;
        const numConflicts = Math.floor(Math.random() * 4) + 1; // 1-4 conflicts

        for (let i = 0; i < numConflicts; i++) {
          const dayOffset = Math.floor(Math.random() * days);
          const conflictDate = addDays(weekStartDate, dayOffset);
          conflictDays.push(format(conflictDate, 'yyyy-MM-dd'));
        }
      }

      return { hasConflicts, conflictDays };
    } catch (error) {
      console.error('Error checking calendar conflicts:', error);
      return { hasConflicts: false, conflictDays: [] };
    }
  };

  // Handle assign to calendar
  const handleAssignToCalendar = async (weekStartDate: Date) => {
    if (!selectedLegacyPlan) return;

    setIsAssigningToCalendar(true);
    try {
      // In a real implementation, this would call an API to assign the meal plan to the calendar
      const convertedPlan = convertLegacyMealPlan(selectedLegacyPlan);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      setMealPlan(convertedPlan);
      setIsCalendarModalOpen(false);
      setCurrentView('calendar');
      toast.success(`Added ${selectedLegacyPlan.name || 'Meal Plan'} to calendar starting ${format(weekStartDate, 'MMM d, yyyy')}`);
    } catch (error) {
      console.error('Error assigning to calendar:', error);
      toast.error('Failed to assign meal plan to calendar');
    } finally {
      setIsAssigningToCalendar(false);
    }
  };

  return (
    <div className="container py-6 space-y-6">
      <div className="flex items-center justify-between">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/dashboard')}
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Back
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => saveMealPlanToDatabase()}
        >
          <Save className="h-4 w-4 mr-1" />
          Save Plan
        </Button>
      </div>

      <MealPlanHeader
        onViewChange={setCurrentView}
        onLayoutChange={setCurrentLayout}
        onGeneratePlan={() => setIsGenerateModalOpen(true)}
        onAddMeal={() => setIsAddMealModalOpen(true)}
        onViewShoppingList={handleViewShoppingList}
        currentView={currentView}
        currentLayout={currentLayout}
      />

      <div className="flex flex-col md:flex-row gap-6">
        {/* Sidebar */}
        <div className="w-full md:w-1/4 lg:w-1/5 border rounded-lg overflow-hidden">
          <MealPlanSidebar
            mealPlans={legacyMealPlans}
            activePlanId={activeLegacyPlanId}
            onSelectPlan={handleSelectLegacyPlan}
            onDeletePlan={handleDeleteLegacyPlan}
            onAddToCalendar={handleAddToCalendar}
            onGenerateShoppingList={(plan) => handleViewShoppingList(plan)}
            onCreatePlan={() => router.push('/meal-plan/generate')}
          />
        </div>

        {/* Main Content */}
        <div className="w-full md:w-3/4 lg:w-4/5">
          <DragDropProvider onDrop={handleDrop}>
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Loading meal plan...</p>
                </div>
              </div>
            ) : (
              <>
                {currentView === 'calendar' && mealPlan && (
                  <MealPlanCalendar
                    mealPlan={mealPlan}
                    onViewMealDetails={handleViewMealDetails}
                    onRemoveMeal={removeMeal}
                    onStatusChange={setMealStatus}
                    onRegenerateMeal={(day, mealType) => {
                      // In a real implementation, this would regenerate just this meal
                      toast(`Regenerating meal for ${format(new Date(day), 'EEE, MMM d')} - ${mealType}`);
                    }}
                    onAddMeal={(day, mealType) => {
                      // For now, just open the add meal modal
                      // In a real implementation, this would pre-fill the date and meal type
                      setIsAddMealModalOpen(true);
                    }}
                    layout={currentLayout}
                  />
                )}

                {currentView === 'list' && (
                  <MealPlanList
                    mealPlans={legacyMealPlans}
                    onViewPlan={handleViewLegacyPlan}
                    onDeletePlan={handleDeleteLegacyPlan}
                    onAddToCalendar={handleAddToCalendar}
                    onViewShoppingList={(plan) => {
                      // Generate a shopping list for this specific plan
                      handleViewShoppingList(plan);
                    }}
                  />
                )}
              </>
            )}
          </DragDropProvider>
        </div>
      </div>

      {/* Modals */}
      <MealDetailsModal
        meal={selectedMeal}
        isOpen={isDetailsModalOpen}
        onClose={() => setIsDetailsModalOpen(false)}
      />

      <GeneratePlanModal
        isOpen={isGenerateModalOpen}
        onClose={() => setIsGenerateModalOpen(false)}
        onGenerate={handleGeneratePlan}
        isLoading={isGenerating}
      />

      <AddMealModal
        isOpen={isAddMealModalOpen}
        onClose={() => setIsAddMealModalOpen(false)}
        onAddMeal={handleAddMeal}
        isLoading={isAddingMeal}
      />

      <ViewShoppingListModal
        isOpen={isShoppingListModalOpen}
        onClose={() => setIsShoppingListModalOpen(false)}
        shoppingList={shoppingList}
        pantryItems={pantryItems}
        mealPlan={selectedLegacyPlan}
        newMealPlan={mealPlan}
        onAddToPantry={handleAddToPantry}
        onUpdateShoppingList={handleUpdateShoppingList}
        onGenerateShoppingList={handleRegenerateShoppingList}
      />

      <CalendarAssignmentModal
        isOpen={isCalendarModalOpen}
        onClose={() => setIsCalendarModalOpen(false)}
        mealPlan={selectedLegacyPlan}
        currentMealPlan={mealPlan}
        onAssign={handleAssignToCalendar}
        isLoading={isAssigningToCalendar}
        onCheckConflicts={checkCalendarConflicts}
      />
    </div>
  );
}
