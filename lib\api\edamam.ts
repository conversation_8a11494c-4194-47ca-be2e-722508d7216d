"use client";

import axios from 'axios';
import { MealGenerationOptions } from '@/types/new-meal-plan';

// Edamam Recipe API configuration
// For testing, we'll use environment variables if available, otherwise use empty strings
// In production, these should be set in the .env file
const EDAMAM_RECIPE_APP_ID = process.env.NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES || '';
const EDAMAM_RECIPE_APP_KEY = process.env.NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES || '';
const EDAMAM_RECIPE_BASE_URL = 'https://api.edamam.com/api/recipes/v2';

// Edamam Food Database API configuration
const EDAMAM_FOOD_DB_APP_ID = process.env.NEXT_PUBLIC_EDAMAM_APP_ID_FOOD_DATABASE || '';
const EDAMAM_FOOD_DB_APP_KEY = process.env.NEXT_PUBLIC_EDAMAM_APP_KEY_FOOD_DATABASE || '';
const EDAMAM_FOOD_DB_BASE_URL = 'https://api.edamam.com/api/food-database/v2';

// Log the API keys for debugging (remove in production)
console.log('Edamam API Keys:', {
  EDAMAM_RECIPE_APP_ID,
  EDAMAM_RECIPE_APP_KEY,
  EDAMAM_FOOD_DB_APP_ID,
  EDAMAM_FOOD_DB_APP_KEY
});

// Cache for API responses to reduce API calls
const responseCache = new Map<string, any>();

/**
 * Search for recipes using the Edamam API
 */
export async function searchRecipes(params: {
  query: string;
  diet?: string[];
  health?: string[];
  calories?: string;
  mealType?: string[];
  cuisineType?: string[];
  excluded?: string[];
  random?: boolean;
}): Promise<any> {
  try {
    // Create a cache key based on the params
    const cacheKey = JSON.stringify(params);

    // Check if we have a cached response
    if (responseCache.has(cacheKey)) {
      console.log('Using cached recipe search results');
      return responseCache.get(cacheKey);
    }

    console.log('Searching recipes with Edamam API...', params);
    console.log('Edamam API Keys:', {
      APP_ID: EDAMAM_RECIPE_APP_ID,
      APP_KEY: EDAMAM_RECIPE_APP_KEY ? 'Present (not shown)' : 'Missing'
    });

    // Validate API keys
    if (!EDAMAM_RECIPE_APP_ID || !EDAMAM_RECIPE_APP_KEY) {
      throw new Error('Missing Edamam API keys');
    }

    // According to Edamam API docs, we need to use the correct endpoint
    // The base URL should be https://api.edamam.com/api/recipes/v2
    // And we need to append 'type=public' as a query parameter

    // Prepare the query parameters
    const queryParams = new URLSearchParams({
      type: 'public',
      app_id: EDAMAM_RECIPE_APP_ID,
      app_key: EDAMAM_RECIPE_APP_KEY,
      q: params.query || 'meal' // Use 'meal' as a default query if none provided
    });

    // Add optional parameters
    if (params.diet && params.diet.length > 0) {
      params.diet.forEach(diet => queryParams.append('diet', diet));
    }

    if (params.health && params.health.length > 0) {
      params.health.forEach(health => queryParams.append('health', health));
    }

    if (params.calories) {
      queryParams.append('calories', params.calories);
    }

    if (params.mealType && params.mealType.length > 0) {
      params.mealType.forEach(mealType => queryParams.append('mealType', mealType));
    }

    if (params.cuisineType && params.cuisineType.length > 0) {
      params.cuisineType.forEach(cuisineType => queryParams.append('cuisineType', cuisineType));
    }

    if (params.excluded && params.excluded.length > 0) {
      params.excluded.forEach(excluded => queryParams.append('excluded', excluded));
    }

    if (params.random) {
      queryParams.append('random', 'true');
    }

    // Log the full URL for debugging
    const requestUrl = `${EDAMAM_RECIPE_BASE_URL}?${queryParams.toString()}`;
    console.log('Edamam API request URL:', requestUrl);

    // Make the API request
    try {
      const response = await axios.get(requestUrl, {
        headers: {
          'Edamam-Account-User': 'user123' // Add a default user ID
        }
      });

      // Cache the response
      responseCache.set(cacheKey, response.data);

      return response.data;
    } catch (apiError: any) {
      console.error('Edamam API request failed:', apiError.message);

      if (apiError.response) {
        console.error('Response status:', apiError.response.status);
        console.error('Response data:', apiError.response.data);
      }

      throw apiError;
    }
  } catch (error) {
    console.error('Error searching recipes with Edamam API:', error);
    throw error;
  }
}

/**
 * Get a recipe by ID from the Edamam API
 */
export async function getRecipe(recipeId: string): Promise<any> {
  try {
    // Create a cache key based on the recipe ID
    const cacheKey = `recipe-${recipeId}`;

    // Check if we have a cached response
    if (responseCache.has(cacheKey)) {
      console.log('Using cached recipe');
      return responseCache.get(cacheKey);
    }

    console.log(`Getting recipe ${recipeId} from Edamam API...`);

    // Make the API request
    const response = await axios.get(`${EDAMAM_RECIPE_BASE_URL}/${recipeId}`, {
      params: {
        type: 'public',
        app_id: EDAMAM_RECIPE_APP_ID,
        app_key: EDAMAM_RECIPE_APP_KEY
      },
      headers: {
        'Edamam-Account-User': 'user123' // Add a default user ID
      }
    });

    // Cache the response
    responseCache.set(cacheKey, response.data);

    return response.data;
  } catch (error) {
    console.error(`Error getting recipe ${recipeId} from Edamam API:`, error);
    throw error;
  }
}

/**
 * Get nutritional information for a recipe using the Edamam API
 */
export async function getNutrition(ingredients: string[]): Promise<any> {
  try {
    // Create a cache key based on the ingredients
    const cacheKey = `nutrition-${JSON.stringify(ingredients)}`;

    // Check if we have a cached response
    if (responseCache.has(cacheKey)) {
      console.log('Using cached nutrition data');
      return responseCache.get(cacheKey);
    }

    console.log('Getting nutrition data from Edamam API...');

    // Make the API request
    const response = await axios.post(
      'https://api.edamam.com/api/nutrition-details',
      {
        title: 'Recipe',
        ingr: ingredients
      },
      {
        params: {
          app_id: EDAMAM_RECIPE_APP_ID,
          app_key: EDAMAM_RECIPE_APP_KEY
        },
        headers: {
          'Edamam-Account-User': 'user123' // Add a default user ID
        }
      }
    );

    // Cache the response
    responseCache.set(cacheKey, response.data);

    return response.data;
  } catch (error) {
    console.error('Error getting nutrition data from Edamam API:', error);
    throw error;
  }
}

/**
 * Generate a meal plan using the Edamam API
 */
export async function generateMealPlanWithEdamam(options: MealGenerationOptions): Promise<any> {
  try {
    console.log('Generating meal plan with Edamam API...', options);

    // Validate API keys
    if (!EDAMAM_RECIPE_APP_ID || !EDAMAM_RECIPE_APP_KEY) {
      throw new Error('Missing Edamam API keys');
    }

    const mealPlan: any = {};
    const startDate = new Date();

    // Generate a meal plan for each day
    for (let i = 0; i < options.days; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      const dateStr = date.toISOString().split('T')[0];

      mealPlan[dateStr] = {};

      // Generate meals for each meal type
      const mealTypes = ['breakfast', 'lunch', 'dinner'];
      for (let j = 0; j < options.mealsPerDay; j++) {
        const mealType = mealTypes[j];

        // Calculate target calories for this meal
        let targetCalories = options.calories;
        if (mealType === 'breakfast') {
          targetCalories = Math.round(options.calories * 0.25);
        } else if (mealType === 'lunch') {
          targetCalories = Math.round(options.calories * 0.35);
        } else if (mealType === 'dinner') {
          targetCalories = Math.round(options.calories * 0.4);
        }

        try {
          console.log(`Searching for ${mealType} recipes with calories ${targetCalories - 100}-${targetCalories + 100}`);

          // Search for recipes
          const searchResults = await searchRecipes({
            query: mealType, // Use the meal type as the query
            diet: options.dietaryPreferences,
            health: [],
            calories: `${targetCalories - 100}-${targetCalories + 100}`,
            mealType: [mealType],
            excluded: options.excludeIngredients,
            random: true
          });

          // Get a random recipe from the results
          const hits = searchResults.hits || [];
          console.log(`Found ${hits.length} ${mealType} recipes`);

          if (hits.length > 0) {
            const randomIndex = Math.floor(Math.random() * hits.length);
            const recipe = hits[randomIndex].recipe;
            console.log(`Selected ${mealType} recipe:`, recipe.label);

            // Transform the recipe to our internal format
            mealPlan[dateStr][mealType] = transformEdamamRecipeToInternal(recipe, mealType);
          } else {
            console.log(`No ${mealType} recipes found, using default`);
            // Use a default recipe if no results found
            if (mealType === 'breakfast') {
              mealPlan[dateStr][mealType] = DEFAULT_BREAKFAST_RECIPE;
            } else if (mealType === 'lunch') {
              mealPlan[dateStr][mealType] = DEFAULT_LUNCH_RECIPE;
            } else {
              mealPlan[dateStr][mealType] = DEFAULT_DINNER_RECIPE;
            }
          }
        } catch (mealError) {
          console.error(`Error generating ${mealType} for day ${i + 1}:`, mealError);

          // Use a default recipe if there's an error
          if (mealType === 'breakfast') {
            mealPlan[dateStr][mealType] = DEFAULT_BREAKFAST_RECIPE;
          } else if (mealType === 'lunch') {
            mealPlan[dateStr][mealType] = DEFAULT_LUNCH_RECIPE;
          } else {
            mealPlan[dateStr][mealType] = DEFAULT_DINNER_RECIPE;
          }
        }
      }
    }

    return mealPlan;
  } catch (error) {
    console.error('Error generating meal plan with Edamam API:', error);
    throw error;
  }
}

// Default recipes to use as fallbacks
const DEFAULT_BREAKFAST_RECIPE = {
  id: 'default-breakfast',
  name: 'Classic Oatmeal with Fruit',
  image: 'https://images.unsplash.com/photo-1517673132405-a56a62b18caf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8b2F0bWVhbHxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60',
  ingredients: [
    { name: 'Rolled oats', amount: '1/2', unit: 'cup' },
    { name: 'Milk', amount: '1', unit: 'cup' },
    { name: 'Banana', amount: '1', unit: 'medium' },
    { name: 'Honey', amount: '1', unit: 'tbsp' },
    { name: 'Cinnamon', amount: '1/4', unit: 'tsp' }
  ],
  instructions: [
    'Combine oats and milk in a saucepan and bring to a boil.',
    'Reduce heat and simmer for 5 minutes, stirring occasionally.',
    'Slice the banana and add to the oatmeal.',
    'Drizzle with honey and sprinkle with cinnamon.',
    'Serve hot.'
  ],
  cost: 2.50,
  calories: 350,
  prepTime: 5,
  cookTime: 10,
  macros: { protein: 12, carbs: 60, fat: 8 },
  nutrition: { protein: 12, carbs: 60, fat: 8 },
  status: null
};

const DEFAULT_LUNCH_RECIPE = {
  id: 'default-lunch',
  name: 'Mediterranean Chickpea Salad',
  image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8c2FsYWR8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60',
  ingredients: [
    { name: 'Chickpeas', amount: '1', unit: 'can' },
    { name: 'Cucumber', amount: '1', unit: 'medium' },
    { name: 'Cherry tomatoes', amount: '1', unit: 'cup' },
    { name: 'Red onion', amount: '1/4', unit: 'cup' },
    { name: 'Feta cheese', amount: '1/4', unit: 'cup' },
    { name: 'Olive oil', amount: '2', unit: 'tbsp' },
    { name: 'Lemon juice', amount: '1', unit: 'tbsp' },
    { name: 'Salt and pepper', amount: '1', unit: 'pinch' }
  ],
  instructions: [
    'Drain and rinse the chickpeas.',
    'Dice the cucumber and quarter the cherry tomatoes.',
    'Finely dice the red onion.',
    'Combine all ingredients in a bowl.',
    'Drizzle with olive oil and lemon juice.',
    'Season with salt and pepper to taste.',
    'Toss well and serve.'
  ],
  cost: 4.75,
  calories: 450,
  prepTime: 15,
  cookTime: 0,
  macros: { protein: 15, carbs: 45, fat: 22 },
  nutrition: { protein: 15, carbs: 45, fat: 22 },
  status: null
};

const DEFAULT_DINNER_RECIPE = {
  id: 'default-dinner',
  name: 'Baked Lemon Herb Salmon',
  image: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8c2FsbW9ufGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
  ingredients: [
    { name: 'Salmon fillet', amount: '6', unit: 'oz' },
    { name: 'Olive oil', amount: '1', unit: 'tbsp' },
    { name: 'Lemon', amount: '1', unit: 'medium' },
    { name: 'Garlic', amount: '2', unit: 'cloves' },
    { name: 'Fresh dill', amount: '1', unit: 'tbsp' },
    { name: 'Fresh parsley', amount: '1', unit: 'tbsp' },
    { name: 'Salt and pepper', amount: '1', unit: 'pinch' }
  ],
  instructions: [
    'Preheat oven to 375°F (190°C).',
    'Place salmon on a baking sheet lined with parchment paper.',
    'Drizzle with olive oil and squeeze lemon juice over the top.',
    'Mince garlic and sprinkle over salmon along with herbs.',
    'Season with salt and pepper.',
    'Bake for 15-20 minutes until salmon flakes easily with a fork.',
    'Serve with lemon wedges.'
  ],
  cost: 8.99,
  calories: 350,
  prepTime: 10,
  cookTime: 20,
  macros: { protein: 34, carbs: 2, fat: 22 },
  nutrition: { protein: 34, carbs: 2, fat: 22 },
  status: null
};

/**
 * Transform an Edamam recipe to our internal format
 * @param edamamRecipe The recipe from Edamam API
 * @param mealType The type of meal (breakfast, lunch, dinner)
 */
function transformEdamamRecipeToInternal(edamamRecipe: any, mealType: string): any {
  // We can use mealType for additional customization if needed
  // Extract the recipe ID from the URI
  const recipeId = edamamRecipe.uri.split('#recipe_')[1];

  // Calculate macros
  const calories = Math.round(edamamRecipe.calories / edamamRecipe.yield);
  const protein = Math.round(edamamRecipe.totalNutrients.PROCNT?.quantity / edamamRecipe.yield) || 0;
  const carbs = Math.round(edamamRecipe.totalNutrients.CHOCDF?.quantity / edamamRecipe.yield) || 0;
  const fat = Math.round(edamamRecipe.totalNutrients.FAT?.quantity / edamamRecipe.yield) || 0;

  // Transform the recipe
  return {
    id: recipeId,
    name: edamamRecipe.label,
    image: edamamRecipe.image,
    ingredients: edamamRecipe.ingredients.map((ingredient: any) => ({
      name: ingredient.food,
      amount: ingredient.quantity,
      unit: ingredient.measure || 'unit'
    })),
    instructions: edamamRecipe.ingredientLines || [],
    cost: 0, // To be filled by Spoonacular
    calories,
    prepTime: 0, // Edamam doesn't provide prep time
    cookTime: edamamRecipe.totalTime || 0,
    macros: {
      protein,
      carbs,
      fat
    },
    nutrition: {
      protein,
      carbs,
      fat
    },
    mealType, // Include the meal type in the transformed recipe
    status: null
  };
}

/**
 * Search for food items using the Edamam Food Database API
 */
export async function searchFoodItems(query: string): Promise<any> {
  try {
    // Create a cache key based on the query
    const cacheKey = `food-search-${query}`;

    // Check if we have a cached response
    if (responseCache.has(cacheKey)) {
      console.log('Using cached food search results');
      return responseCache.get(cacheKey);
    }

    console.log(`Searching food items with Edamam Food Database API: ${query}`);

    // Make the API request
    const response = await axios.get(`${EDAMAM_FOOD_DB_BASE_URL}/parser`, {
      params: {
        app_id: EDAMAM_FOOD_DB_APP_ID,
        app_key: EDAMAM_FOOD_DB_APP_KEY,
        ingr: query
      }
    });

    // Cache the response
    responseCache.set(cacheKey, response.data);

    return response.data;
  } catch (error) {
    console.error('Error searching food items with Edamam Food Database API:', error);
    throw error;
  }
}

/**
 * Get food item details using the Edamam Food Database API
 */
export async function getFoodItemDetails(foodId: string, quantity: number = 1, measureURI: string = 'http://www.edamam.com/ontologies/edamam.owl#Measure_unit'): Promise<any> {
  try {
    // Create a cache key based on the food ID, quantity, and measure
    const cacheKey = `food-${foodId}-${quantity}-${measureURI}`;

    // Check if we have a cached response
    if (responseCache.has(cacheKey)) {
      console.log('Using cached food item details');
      return responseCache.get(cacheKey);
    }

    console.log(`Getting food item details from Edamam Food Database API: ${foodId}`);

    // Prepare the request body according to the documentation
    const requestBody = {
      ingredients: [
        {
          quantity: quantity,
          measureURI: measureURI,
          foodId: foodId
        }
      ]
    };

    // Make the API request
    const response = await axios.post(
      `${EDAMAM_FOOD_DB_BASE_URL}/nutrients`,
      requestBody,
      {
        params: {
          app_id: EDAMAM_FOOD_DB_APP_ID,
          app_key: EDAMAM_FOOD_DB_APP_KEY
        }
      }
    );

    // Cache the response
    responseCache.set(cacheKey, response.data);

    return response.data;
  } catch (error) {
    console.error('Error getting food item details from Edamam Food Database API:', error);
    throw error;
  }
}
