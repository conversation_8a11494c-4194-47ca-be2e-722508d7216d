import { NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

export async function GET(request: Request) {
  try {
    // Create a Supabase admin client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Get the query parameters
    const url = new URL(request.url);
    const table = url.searchParams.get('table');

    if (!table) {
      // Get all tables
      const { data, error } = await supabase
        .from('recipes')
        .select('id')
        .limit(1);

      return NextResponse.json({
        message: "Database connection test",
        data,
        error: error ? error.message : null
      });
    }

    // Get a sample row from the specified table
    const { data, error } = await supabase
      .from(table)
      .select('*')
      .limit(1);

    return NextResponse.json({
      table,
      data,
      error: error ? error.message : null
    });
  } catch (error) {
    console.error("Unexpected error in db-schema API:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: process.env.NODE_ENV === 'development'
          ? (error instanceof Error ? error.message : 'Unknown error occurred')
          : undefined
      },
      { status: 500 }
    );
  }
}
