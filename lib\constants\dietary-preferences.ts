// Edamam API supported diet labels
export const EDAMAM_DIET_LABELS = [
  { value: 'balanced', label: 'Balanced' },
  { value: 'high-fiber', label: 'High Fiber' },
  { value: 'high-protein', label: 'High Protein' },
  { value: 'low-carb', label: 'Low Carb' },
  { value: 'low-fat', label: 'Low Fat' },
  { value: 'low-sodium', label: 'Low Sodium' }
];

// Edamam API supported health labels
export const EDAMAM_HEALTH_LABELS = [
  { value: 'alcohol-free', label: 'Alcohol-Free' },
  { value: 'dairy-free', label: 'Dairy-Free' },
  { value: 'egg-free', label: 'Egg-Free' },
  { value: 'fish-free', label: 'Fish-Free' },
  { value: 'gluten-free', label: 'Gluten-Free' },
  { value: 'keto-friendly', label: 'Keto-Friendly' },
  { value: 'kidney-friendly', label: 'Kidney-Friendly' },
  { value: 'kosher', label: 'Kosher' },
  { value: 'low-sugar', label: 'Low-Sugar' },
  { value: 'no-oil-added', label: 'No Oil Added' },
  { value: 'paleo', label: 'Paleo' },
  { value: 'peanut-free', label: 'Peanut-Free' },
  { value: 'pescatarian', label: 'Pescatarian' },
  { value: 'pork-free', label: 'Pork-Free' },
  { value: 'red-meat-free', label: 'Red-Meat-Free' },
  { value: 'sesame-free', label: 'Sesame-Free' },
  { value: 'shellfish-free', label: 'Shellfish-Free' },
  { value: 'soy-free', label: 'Soy-Free' },
  { value: 'sugar-conscious', label: 'Sugar-Conscious' },
  { value: 'tree-nut-free', label: 'Tree-Nut-Free' },
  { value: 'vegan', label: 'Vegan' },
  { value: 'vegetarian', label: 'Vegetarian' },
  { value: 'wheat-free', label: 'Wheat-Free' }
];

// Meal types
export const MEAL_TYPES = [
  { value: 'breakfast', label: 'Breakfast' },
  { value: 'lunch', label: 'Lunch' },
  { value: 'dinner', label: 'Dinner' }
];

// Budget levels
export const BUDGET_LEVELS = [
  { value: 'low', label: 'Low ($5-10 per meal)' },
  { value: 'medium', label: 'Medium ($10-15 per meal)' },
  { value: 'high', label: 'High ($15+ per meal)' }
];

// Cooking time
export const COOKING_TIMES = [
  { value: 'quick', label: 'Quick (< 30 min)' },
  { value: 'medium', label: 'Medium (30-60 min)' },
  { value: 'long', label: 'Long (> 60 min)' }
];
