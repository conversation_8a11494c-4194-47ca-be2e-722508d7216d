-- Combined Diagnostic SQL Script for Supabase (Fixed Version)
-- Run this script in the Supabase SQL Editor to get a comprehensive overview of your database

-- Output a header for the results
SELECT '=== DATABASE DIAGNOSTIC RESULTS ===' AS "Diagnostic Results";
SELECT NOW()::text AS "Timestamp";
SELECT current_database() AS "Database";
SELECT current_user AS "User";

-- Check database size
SELECT '=== DATABASE SIZE ===' AS section;
SELECT pg_size_pretty(pg_database_size(current_database())) AS database_size;

-- Check extensions
SELECT '=== INSTALLED EXTENSIONS ===' AS section;
SELECT extname AS extension_name, extversion AS version
FROM pg_extension
ORDER BY extname;

-- Check which tables exist in the public schema
SELECT '=== TABLES IN PUBLIC SCHEMA ===' AS section;
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public'
AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- Check which tables have RLS enabled
SELECT '=== ROW LEVEL SECURITY STATUS ===' AS section;
SELECT tablename AS table_name, 
       CASE WHEN rowsecurity THEN 'Enabled' ELSE 'Disabled' END AS rls_status
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY tablename;

-- Check all columns for each table in the public schema
SELECT '=== TABLE COLUMNS ===' AS section;
SELECT table_name,
       string_agg(column_name || ' (' || data_type || 
                 CASE WHEN is_nullable = 'NO' THEN ', NOT NULL' ELSE '' END || 
                 CASE WHEN column_default IS NOT NULL THEN ', DEFAULT: ' || column_default ELSE '' END || ')', 
                 E'\n') AS columns
FROM information_schema.columns
WHERE table_schema = 'public'
GROUP BY table_name
ORDER BY table_name;

-- Check all RLS policies
SELECT '=== ROW LEVEL SECURITY POLICIES ===' AS section;
SELECT tablename AS table_name,
       policyname AS policy_name,
       permissive,
       cmd AS operation,
       array_to_string(roles, ', ') AS roles,
       qual AS using_expression,
       with_check AS with_check_expression
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- Check all foreign key constraints (FIXED)
SELECT '=== FOREIGN KEY CONSTRAINTS ===' AS section;
SELECT DISTINCT
       tc.table_schema,
       tc.constraint_name,
       tc.table_name,
       kcu.column_name,
       rc.table_schema AS foreign_table_schema,
       rc.table_name AS foreign_table_name,
       rc.column_name AS foreign_column_name
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
  ON tc.constraint_schema = kcu.constraint_schema
  AND tc.constraint_name = kcu.constraint_name
JOIN information_schema.referential_constraints rc
  ON tc.constraint_schema = rc.constraint_schema
  AND tc.constraint_name = rc.constraint_name
JOIN information_schema.key_column_usage rcu
  ON rc.unique_constraint_schema = rcu.constraint_schema
  AND rc.unique_constraint_name = rcu.constraint_name
  AND kcu.ordinal_position = rcu.ordinal_position
WHERE tc.constraint_type = 'FOREIGN KEY'
  AND tc.table_schema = 'public'
ORDER BY tc.table_name, kcu.column_name;

-- Check all indexes
SELECT '=== INDEXES ===' AS section;
SELECT tablename AS table_name,
       indexname AS index_name,
       indexdef AS index_definition
FROM pg_indexes
WHERE schemaname = 'public'
ORDER BY tablename, indexname;

-- Check all triggers
SELECT '=== TRIGGERS ===' AS section;
SELECT event_object_table AS table_name,
       trigger_name,
       action_timing AS timing,
       event_manipulation AS event,
       action_statement AS definition
FROM information_schema.triggers
WHERE trigger_schema = 'public'
ORDER BY event_object_table, trigger_name;

-- Check for tables with user_id column but missing RLS policies
SELECT '=== TABLES WITH user_id BUT NO RLS POLICIES ===' AS section;
SELECT t.table_name
FROM information_schema.tables t
JOIN information_schema.columns c 
  ON t.table_name = c.table_name 
  AND t.table_schema = c.table_schema
LEFT JOIN pg_policies p 
  ON t.table_name = p.tablename 
  AND p.schemaname = 'public'
WHERE t.table_schema = 'public'
  AND t.table_type = 'BASE TABLE'
  AND c.column_name = 'user_id'
GROUP BY t.table_name
HAVING COUNT(p.policyname) = 0;

-- Check for tables with RLS enabled but no policies
SELECT '=== TABLES WITH RLS ENABLED BUT NO POLICIES ===' AS section;
SELECT t.tablename AS table_name
FROM pg_tables t
LEFT JOIN pg_policies p 
  ON t.tablename = p.tablename 
  AND t.schemaname = p.schemaname
WHERE t.schemaname = 'public'
  AND t.rowsecurity = true
GROUP BY t.tablename
HAVING COUNT(p.policyname) = 0;

-- Check for tables with policies assigned to public role instead of authenticated
SELECT '=== POLICIES ASSIGNED TO PUBLIC ROLE ===' AS section;
SELECT tablename AS table_name,
       policyname AS policy_name,
       array_to_string(roles, ', ') AS roles
FROM pg_policies
WHERE schemaname = 'public'
  AND 'public' = ANY(roles)
ORDER BY tablename, policyname;

-- Check for type mismatches in RLS policies (SIMPLIFIED)
SELECT '=== TYPE MISMATCHES IN RLS POLICIES ===' AS section;
SELECT p.tablename AS table_name,
       p.policyname AS policy_name,
       c.data_type AS user_id_data_type,
       CASE
         WHEN c.data_type = 'uuid' AND p.qual::text LIKE '%auth.uid()%' AND p.qual::text NOT LIKE '%::text%'
           THEN 'UUID column without text casting'
         WHEN c.data_type = 'text' AND p.qual::text LIKE '%auth.uid()::uuid%'
           THEN 'TEXT column with UUID casting'
         ELSE 'No issue detected'
       END AS issue
FROM pg_policies p
JOIN information_schema.columns c 
  ON p.tablename = c.table_name
WHERE p.schemaname = 'public'
  AND c.column_name = 'user_id'
  AND c.table_schema = 'public'
  AND (p.qual::text LIKE '%auth.uid()%' OR p.with_check::text LIKE '%auth.uid()%');

-- Check for missing indexes on foreign keys (FIXED)
SELECT '=== MISSING INDEXES ON FOREIGN KEYS ===' AS section;
WITH fk_columns AS (
  SELECT
    tc.table_schema,
    tc.table_name,
    kcu.column_name
  FROM information_schema.table_constraints tc
  JOIN information_schema.key_column_usage kcu
    ON tc.constraint_schema = kcu.constraint_schema
    AND tc.constraint_name = kcu.constraint_name
  WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
),
indexed_columns AS (
  SELECT
    schemaname,
    tablename,
    array_agg(indexdef) AS index_definitions
  FROM pg_indexes
  WHERE schemaname = 'public'
  GROUP BY schemaname, tablename
)
SELECT
  fk.table_name,
  fk.column_name,
  'Missing index' AS issue
FROM fk_columns fk
LEFT JOIN indexed_columns idx
  ON fk.table_schema = idx.schemaname
  AND fk.table_name = idx.tablename
WHERE idx.index_definitions IS NULL
   OR NOT EXISTS (
     SELECT 1
     FROM unnest(idx.index_definitions) indexdef
     WHERE indexdef LIKE '%' || fk.column_name || '%'
   );

-- Check for missing updated_at triggers
SELECT '=== TABLES WITH updated_at COLUMN BUT NO TRIGGER ===' AS section;
SELECT t.table_name
FROM information_schema.tables t
JOIN information_schema.columns c 
  ON t.table_name = c.table_name 
  AND t.table_schema = c.table_schema
LEFT JOIN pg_trigger tr 
  ON tr.tgrelid = (t.table_schema || '.' || t.table_name)::regclass
  AND tr.tgname LIKE 'update_' || t.table_name || '_updated_at'
WHERE t.table_schema = 'public'
  AND t.table_type = 'BASE TABLE'
  AND c.column_name = 'updated_at'
  AND tr.tgname IS NULL;

-- Check for missing permissions for authenticated users
SELECT '=== TABLES MISSING PERMISSIONS FOR AUTHENTICATED USERS ===' AS section;
SELECT t.table_name
FROM information_schema.tables t
LEFT JOIN information_schema.table_privileges p 
  ON t.table_name = p.table_name 
  AND t.table_schema = p.table_schema 
  AND p.grantee = 'authenticated'
WHERE t.table_schema = 'public'
  AND t.table_type = 'BASE TABLE'
GROUP BY t.table_name
HAVING COUNT(p.privilege_type) < 4; -- Less than all privileges (SELECT, INSERT, UPDATE, DELETE)

-- Check for meal plan related tables
SELECT '=== MEAL PLAN RELATED TABLES ===' AS section;
SELECT table_name,
       CASE WHEN table_name IN (
         SELECT table_name 
         FROM information_schema.tables 
         WHERE table_schema = 'public'
         AND table_type = 'BASE TABLE'
       ) THEN 'Exists' ELSE 'Missing' END AS status
FROM (VALUES 
  ('meal_plans'),
  ('meal_plan_assignments'),
  ('meal_completions'),
  ('recipes'),
  ('favorite_meals'),
  ('shopping_lists'),
  ('shopping_items'),
  ('pantry_items'),
  ('meal_notes'),
  ('meal_generation_preferences'),
  ('user_preferences'),
  ('search_history')
) AS t(table_name)
ORDER BY table_name;

-- Summary of findings
SELECT '=== SUMMARY ===' AS section;
SELECT 
  (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE') AS total_tables,
  (SELECT COUNT(*) FROM pg_tables WHERE schemaname = 'public' AND rowsecurity = true) AS tables_with_rls,
  (SELECT COUNT(*) FROM pg_policies WHERE schemaname = 'public') AS total_policies,
  (SELECT COUNT(*) FROM information_schema.table_constraints WHERE table_schema = 'public' AND constraint_type = 'FOREIGN KEY') AS foreign_keys,
  (SELECT COUNT(*) FROM pg_indexes WHERE schemaname = 'public') AS total_indexes,
  (SELECT COUNT(*) FROM information_schema.triggers WHERE trigger_schema = 'public') AS total_triggers;
