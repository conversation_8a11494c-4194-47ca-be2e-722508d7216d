import { NextRequest, NextResponse } from 'next/server';
import { estimateRecipeCostWithAI } from '@/lib/api/openai';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, ingredients } = body;
    
    if (!name || !ingredients || !Array.isArray(ingredients)) {
      return NextResponse.json(
        { error: 'Invalid request. Expected recipe name and an array of ingredients.' },
        { status: 400 }
      );
    }
    
    const cost = await estimateRecipeCostWithAI({ name, ingredients });
    
    return NextResponse.json(cost);
  } catch (error: any) {
    console.error('Error in OpenAI estimate cost API route:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while estimating the cost' },
      { status: 500 }
    );
  }
}
