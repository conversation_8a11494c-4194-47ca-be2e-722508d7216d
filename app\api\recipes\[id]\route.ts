import { NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import type { Database } from "@/types/supabase";
import { z } from "zod";
import { recipeService, ClientType, getSupabaseClient } from "@/app/services/database-server";

// Define the schema for recipe validation
const recipeUpdateSchema = z.object({
  name: z.string().min(3, "Recipe name must be at least 3 characters").optional(),
  description: z.string().min(10, "Description must be at least 10 characters").optional(),
  prep_time: z.number().min(1, "Prep time must be at least 1 minute").optional(),
  cook_time: z.number().min(0, "Cook time cannot be negative").optional(),
  servings: z.number().min(1, "Servings must be at least 1").optional(),
  cost_per_serving: z.number().min(0, "Cost cannot be negative").optional(),
  cuisine: z.string().min(1, "Please select a cuisine").optional(),
  difficulty: z.string().min(1, "Please select a difficulty level").optional(),
  dietary_restrictions: z.array(z.string()).optional(),
  instructions: z.array(z.string()).min(1, "At least one instruction is required").optional(),
  ingredients: z.array(
    z.object({
      name: z.string().min(1, "Ingredient name is required"),
      amount: z.string().min(1, "Amount is required"),
      unit: z.string().optional(),
    })
  ).min(1, "At least one ingredient is required").optional(),
  nutrition: z.object({
    calories: z.number().min(0, "Calories cannot be negative"),
    protein: z.number().min(0, "Protein cannot be negative"),
    carbs: z.number().min(0, "Carbs cannot be negative"),
    fat: z.number().min(0, "Fat cannot be negative"),
    fiber: z.number().min(0, "Fiber cannot be negative"),
  }).optional(),
  image_url: z.string().optional(),
  is_favorite: z.boolean().optional(),
});

// GET handler to fetch a specific recipe
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

    // Get the current user
    const { data: { session } } = await supabase.auth.getSession();

    // Use the recipe service to fetch the recipe
    const { data: recipe, error } = await recipeService.getRecipeById(params.id);

    if (error) {
      console.error("Error fetching recipe:", error);
      return NextResponse.json(
        { error: "Recipe not found" },
        { status: 404 }
      );
    }

    // Check if the user has permission to view this recipe
    if (session?.user && recipe.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "You don't have permission to view this recipe" },
        { status: 403 }
      );
    }

    return NextResponse.json(recipe);
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PATCH handler to update a recipe
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();

    if (authError || !session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if the recipe exists and belongs to the user
    const { data: recipe, error: fetchError } = await recipeService.getRecipeById(params.id);

    if (fetchError) {
      console.error("Error fetching recipe:", fetchError);
      return NextResponse.json(
        { error: "Recipe not found" },
        { status: 404 }
      );
    }

    if (recipe.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "You don't have permission to update this recipe" },
        { status: 403 }
      );
    }

    // Parse and validate the request body
    const body = await request.json();

    try {
      const validatedData = recipeUpdateSchema.parse(body);

      // Use the recipe service to update the recipe
      const { data: updatedRecipe, error } = await recipeService.updateRecipe(params.id, validatedData);

      if (error) {
        console.error("Error updating recipe:", error);
        return NextResponse.json(
          { error: "Failed to update recipe" },
          { status: 500 }
        );
      }

      return NextResponse.json(updatedRecipe);
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Validation error", details: validationError.format() },
          { status: 400 }
        );
      }
      throw validationError;
    }
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE handler to delete a recipe
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();

    if (authError || !session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if the recipe exists and belongs to the user
    const { data: recipe, error: fetchError } = await recipeService.getRecipeById(params.id);

    if (fetchError) {
      console.error("Error fetching recipe:", fetchError);
      return NextResponse.json(
        { error: "Recipe not found" },
        { status: 404 }
      );
    }

    if (recipe.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "You don't have permission to delete this recipe" },
        { status: 403 }
      );
    }

    // Use the recipe service to delete the recipe
    const { success, error } = await recipeService.deleteRecipe(params.id);

    if (!success) {
      console.error("Error deleting recipe:", error);
      return NextResponse.json(
        { error: "Failed to delete recipe" },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: "Recipe deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
