-- Migration: [DESCRIPTION]
-- Version: [TIMES<PERSON><PERSON>]
-- Author: [AUTHOR]
-- Date: [DATE]

-- First, record this migration
INSERT INTO public.schema_migrations (version, description)
VALUES ('[TIMESTAMP]', '[DESCRIPTION]')
ON CONFLICT (version) DO NOTHING;

-- [SECTION 1: TABLE CREATION]
-- Example:
-- CREATE TABLE IF NOT EXISTS public.[table_name] (
--     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
--     user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
--     [other_columns],
--     created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
--     updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
-- );

-- [SECTION 2: TABLE MODIFICATION]
-- Example:
-- ALTER TABLE public.[table_name]
-- ADD COLUMN IF NOT EXISTS [column_name] [data_type];

-- [SECTION 3: R<PERSON> POLICIES]
-- Example:
-- ALTER TABLE public.[table_name] ENABLE ROW LEVEL SECURITY;
-- 
-- CREATE POLICY "Users can view their own [table_name]"
-- ON public.[table_name] FOR SELECT
-- USING (user_id = auth.uid());
-- 
-- CREATE POLICY "Users can insert their own [table_name]"
-- ON public.[table_name] FOR INSERT
-- WITH CHECK (user_id = auth.uid());
-- 
-- CREATE POLICY "Users can update their own [table_name]"
-- ON public.[table_name] FOR UPDATE
-- USING (user_id = auth.uid())
-- WITH CHECK (user_id = auth.uid());
-- 
-- CREATE POLICY "Users can delete their own [table_name]"
-- ON public.[table_name] FOR DELETE
-- USING (user_id = auth.uid());

-- [SECTION 4: FUNCTIONS AND PROCEDURES]
-- Example:
-- CREATE OR REPLACE FUNCTION public.[function_name]([parameters])
-- RETURNS [return_type]
-- LANGUAGE [language]
-- SECURITY DEFINER
-- AS $$
-- BEGIN
--     [function_body]
-- END;
-- $$;

-- [SECTION 5: PERMISSIONS]
-- Example:
-- GRANT ALL ON public.[table_name] TO authenticated;
-- GRANT EXECUTE ON FUNCTION public.[function_name] TO authenticated;

-- [SECTION 6: DATA MIGRATION]
-- Example:
-- INSERT INTO public.[table_name] ([columns])
-- SELECT [columns]
-- FROM public.[old_table]
-- WHERE [condition];

-- [SECTION 7: CLEANUP]
-- Example:
-- DROP TABLE IF EXISTS public.[old_table];
-- DROP FUNCTION IF EXISTS public.[old_function];
