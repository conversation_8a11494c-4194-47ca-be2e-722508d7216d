"use client";

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export default function AuthTestPage() {
  const [user, setUser] = useState<any>(null);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [authStatus, setAuthStatus] = useState<string>('Checking...');
  
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const supabase = createClientComponentClient();
        
        // Test the connection to Supabase
        try {
          const { data, error } = await supabase.from('meal_plans').select('count(*)');
          if (error) {
            console.error('Error connecting to Supabase:', error);
            setAuthStatus('Error connecting to Supabase');
          } else {
            console.log('Successfully connected to Supabase');
            setAuthStatus('Connected to Supabase');
          }
        } catch (connError) {
          console.error('Connection error:', connError);
          setAuthStatus('Connection error');
        }
        
        // Check if user is authenticated
        try {
          const { data, error } = await supabase.auth.getUser();
          
          if (error) {
            console.error('Error getting user:', error);
            setAuthStatus('Error getting user');
          } else if (data.user) {
            console.log('User is authenticated:', data.user);
            setUser(data.user);
            setAuthStatus('Authenticated');
          } else {
            console.log('User is not authenticated');
            setAuthStatus('Not authenticated');
          }
        } catch (authError) {
          console.error('Auth error:', authError);
          setAuthStatus('Auth error');
        }
      } catch (error) {
        console.error('Unexpected error:', error);
        setAuthStatus('Unexpected error');
      }
    };
    
    checkAuth();
  }, []);
  
  const handleSignIn = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      const supabase = createClientComponentClient();
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {
        console.error('Sign in error:', error);
        setError(error.message);
        return;
      }
      
      setSuccess('Signed in successfully');
      setUser(data.user);
      setAuthStatus('Authenticated');
    } catch (err: any) {
      console.error('Error signing in:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  const handleSignOut = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      const supabase = createClientComponentClient();
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error('Sign out error:', error);
        setError(error.message);
        return;
      }
      
      setSuccess('Signed out successfully');
      setUser(null);
      setAuthStatus('Not authenticated');
    } catch (err: any) {
      console.error('Error signing out:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  const testSupabaseConnection = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      const supabase = createClientComponentClient();
      
      // Test a simple query
      const { data, error } = await supabase.from('meal_plans').select('id').limit(1);
      
      if (error) {
        console.error('Error testing Supabase connection:', error);
        setError(error.message);
        return;
      }
      
      setSuccess(`Successfully connected to Supabase. Found ${data.length} meal plans.`);
    } catch (err: any) {
      console.error('Error testing Supabase connection:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Authentication Test</h1>
      
      <div className="p-4 border rounded-md mb-6">
        <h2 className="text-lg font-bold mb-2">Authentication Status</h2>
        <p className={`${authStatus.includes('Error') || authStatus === 'Not authenticated' ? 'text-red-600' : 'text-green-600'}`}>
          {authStatus}
        </p>
      </div>
      
      {user ? (
        <div className="p-4 border rounded-md mb-6">
          <h2 className="text-lg font-bold mb-2">User Information</h2>
          <p>Email: {user.email}</p>
          <p>ID: {user.id}</p>
          <p>Last Sign In: {new Date(user.last_sign_in_at).toLocaleString()}</p>
          
          <Button onClick={handleSignOut} disabled={loading} className="mt-4">
            Sign Out
          </Button>
        </div>
      ) : (
        <div className="p-4 border rounded-md mb-6">
          <h2 className="text-lg font-bold mb-4">Sign In</h2>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input 
                id="email"
                type="email"
                value={email} 
                onChange={(e) => setEmail(e.target.value)} 
                placeholder="Enter your email"
              />
            </div>
            
            <div>
              <Label htmlFor="password">Password</Label>
              <Input 
                id="password"
                type="password"
                value={password} 
                onChange={(e) => setPassword(e.target.value)} 
                placeholder="Enter your password"
              />
            </div>
            
            <Button onClick={handleSignIn} disabled={loading}>
              Sign In
            </Button>
          </div>
        </div>
      )}
      
      <div className="p-4 border rounded-md mb-6">
        <h2 className="text-lg font-bold mb-2">Test Supabase Connection</h2>
        <Button onClick={testSupabaseConnection} disabled={loading}>
          Test Connection
        </Button>
      </div>
      
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md mb-6">
          <h2 className="text-lg font-bold text-red-600 mb-2">Error</h2>
          <p className="text-red-600">{error}</p>
        </div>
      )}
      
      {success && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-md mb-6">
          <h2 className="text-lg font-bold text-green-600 mb-2">Success</h2>
          <p className="text-green-600">{success}</p>
        </div>
      )}
      
      <div className="p-4 border rounded-md">
        <h2 className="text-lg font-bold mb-2">Supabase Configuration</h2>
        <p>URL: {process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not set'}</p>
        <p>Anon Key: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set (not shown)' : 'Not set'}</p>
      </div>
    </div>
  );
}
