"use client";

import { MealGenerationOptions, MealPlan } from '@/types/new-meal-plan';
import { generateMealPlanWithAI, generateRecipeWithAI } from '@/lib/api/openai';
import { searchRecipes } from '@/lib/api/edamam';
import { estimateRecipeCost } from '@/lib/api/spoonacular';
import { DEFAULT_RECIPES } from '@/lib/mock-data/default-recipes';
import { toast } from 'sonner';

/**
 * Generate a meal plan using the hybrid approach (OpenAI + Edamam + Spoonacular)
 */
export async function generateMealPlanWithHybrid(options: MealGenerationOptions): Promise<MealPlan> {
  try {
    // Step 1: Generate a meal plan structure using OpenAI
    console.log('Step 1: Generating meal plan structure with OpenAI...');
    const aiMealPlan = await generateMealPlanWithAI(options);

    // Step 2: Enhance the meal plan with real recipes from Edamam
    console.log('Step 2: Enhancing meal plan with real recipes from Edamam...');
    const enhancedMealPlan = await enhanceMealPlanWithRealRecipes(aiMealPlan, options);

    // Step 3: Add accurate cost information using Spoonacular
    console.log('Step 3: Adding accurate cost information with Spoonacular...');
    const mealPlanWithAccurateCosts = await addAccurateCostInfo(enhancedMealPlan);

    return mealPlanWithAccurateCosts;
  } catch (error) {
    console.error('Error generating meal plan with hybrid approach:', error);

    // Fallback to default recipes
    console.log('Falling back to default recipes...');
    toast.error('API error: Using default recipes as fallback');

    return generateDefaultMealPlan(options);
  }
}

/**
 * Generate a default meal plan using the default recipes
 */
function generateDefaultMealPlan(options: MealGenerationOptions): MealPlan {
  const mealPlan: MealPlan = {};
  const startDate = new Date();

  // Generate a meal plan for each day
  for (let i = 0; i < options.days; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];

    mealPlan[dateStr] = {};

    // Add breakfast
    const breakfastIndex = i % DEFAULT_RECIPES.breakfast.length;
    mealPlan[dateStr]['breakfast'] = DEFAULT_RECIPES.breakfast[breakfastIndex];

    // Add lunch
    const lunchIndex = i % DEFAULT_RECIPES.lunch.length;
    mealPlan[dateStr]['lunch'] = DEFAULT_RECIPES.lunch[lunchIndex];

    // Add dinner
    const dinnerIndex = i % DEFAULT_RECIPES.dinner.length;
    mealPlan[dateStr]['dinner'] = DEFAULT_RECIPES.dinner[dinnerIndex];
  }

  return mealPlan;
}

/**
 * Enhance a meal plan with real recipes from Edamam
 */
async function enhanceMealPlanWithRealRecipes(mealPlan: MealPlan, options: MealGenerationOptions): Promise<MealPlan> {
  const enhancedMealPlan = { ...mealPlan };

  // Process each day in the meal plan
  for (const dateStr in enhancedMealPlan) {
    // Process each meal in the day
    for (const mealType in enhancedMealPlan[dateStr]) {
      const meal = enhancedMealPlan[dateStr][mealType];

      try {
        // Search for a similar real recipe using Edamam
        const searchResults = await searchRecipes({
          query: meal.name,
          mealType: [mealType],
          diet: options.dietaryPreferences,
          calories: `${meal.calories - 100}-${meal.calories + 100}`,
          excluded: options.excludeIngredients
        });

        // Check if we found any results
        const hits = searchResults.hits || [];
        if (hits.length > 0) {
          // Get the first result
          const recipe = hits[0].recipe;

          // Transform the recipe to our internal format
          const transformedRecipe = {
            id: recipe.uri.split('#recipe_')[1],
            name: recipe.label,
            image: recipe.image,
            ingredients: recipe.ingredients.map((ingredient: any) => ({
              name: ingredient.food,
              amount: ingredient.quantity,
              unit: ingredient.measure || 'unit'
            })),
            instructions: recipe.ingredientLines || [],
            cost: meal.cost, // Keep the AI-estimated cost for now
            calories: Math.round(recipe.calories / recipe.yield),
            prepTime: meal.prepTime,
            cookTime: recipe.totalTime || meal.cookTime,
            macros: {
              protein: Math.round(recipe.totalNutrients.PROCNT?.quantity / recipe.yield) || meal.macros.protein,
              carbs: Math.round(recipe.totalNutrients.CHOCDF?.quantity / recipe.yield) || meal.macros.carbs,
              fat: Math.round(recipe.totalNutrients.FAT?.quantity / recipe.yield) || meal.macros.fats
            },
            nutrition: {
              protein: Math.round(recipe.totalNutrients.PROCNT?.quantity / recipe.yield) || meal.nutrition.protein,
              carbs: Math.round(recipe.totalNutrients.CHOCDF?.quantity / recipe.yield) || meal.nutrition.carbs,
              fat: Math.round(recipe.totalNutrients.FAT?.quantity / recipe.yield) || meal.nutrition.fat
            },
            status: null
          };

          // Update the meal in the meal plan
          enhancedMealPlan[dateStr][mealType] = transformedRecipe;
        }
      } catch (error) {
        console.error(`Error enhancing meal ${meal.name} with real recipe:`, error);
        // Keep the AI-generated meal if we couldn't find a real recipe
      }
    }
  }

  return enhancedMealPlan;
}

/**
 * Add accurate cost information to a meal plan using Spoonacular
 */
async function addAccurateCostInfo(mealPlan: MealPlan): Promise<MealPlan> {
  const mealPlanWithAccurateCosts = { ...mealPlan };

  // Process each day in the meal plan
  for (const dateStr in mealPlanWithAccurateCosts) {
    // Process each meal in the day
    for (const mealType in mealPlanWithAccurateCosts[dateStr]) {
      const meal = mealPlanWithAccurateCosts[dateStr][mealType];

      try {
        // Estimate the cost of the meal using Spoonacular
        const cost = await estimateRecipeCost(meal.ingredients);

        // Update the meal with the accurate cost
        mealPlanWithAccurateCosts[dateStr][mealType] = {
          ...meal,
          cost
        };
      } catch (error) {
        console.error(`Error adding accurate cost info for meal ${meal.name}:`, error);
        // Keep the AI-estimated cost if we couldn't get an accurate cost
      }
    }
  }

  return mealPlanWithAccurateCosts;
}

/**
 * Generate a single meal using the hybrid approach
 */
export async function generateMealWithHybrid(options: {
  mealType: string;
  calories: number;
  dietaryPreferences: string[];
  excludeIngredients: string[];
  cookingTime: string;
}): Promise<any> {
  try {
    // Step 1: Generate a recipe using OpenAI
    const aiRecipeResponse = await generateRecipeWithAI(options);
    const aiRecipe = aiRecipeResponse.recipe;

    // Step 2: Search for a similar real recipe using Edamam
    const searchResults = await searchRecipes({
      query: aiRecipe.name,
      mealType: [options.mealType],
      diet: options.dietaryPreferences,
      calories: `${options.calories - 100}-${options.calories + 100}`,
      excluded: options.excludeIngredients
    });

    // Check if we found any results
    const hits = searchResults.hits || [];
    if (hits.length > 0) {
      // Get the first result
      const recipe = hits[0].recipe;

      // Transform the recipe to our internal format
      const transformedRecipe = {
        id: recipe.uri.split('#recipe_')[1],
        name: recipe.label,
        image: recipe.image,
        ingredients: recipe.ingredients.map((ingredient: any) => ({
          name: ingredient.food,
          amount: ingredient.quantity,
          unit: ingredient.measure || 'unit'
        })),
        instructions: recipe.ingredientLines || aiRecipe.instructions,
        cost: 0, // To be filled by Spoonacular
        calories: Math.round(recipe.calories / recipe.yield),
        prepTime: aiRecipe.prepTime || 0,
        cookTime: recipe.totalTime || aiRecipe.cookTime || 0,
        macros: {
          protein: Math.round(recipe.totalNutrients.PROCNT?.quantity / recipe.yield) || aiRecipe.nutrition.protein,
          carbs: Math.round(recipe.totalNutrients.CHOCDF?.quantity / recipe.yield) || aiRecipe.nutrition.carbs,
          fat: Math.round(recipe.totalNutrients.FAT?.quantity / recipe.yield) || aiRecipe.nutrition.fat
        },
        nutrition: {
          protein: Math.round(recipe.totalNutrients.PROCNT?.quantity / recipe.yield) || aiRecipe.nutrition.protein,
          carbs: Math.round(recipe.totalNutrients.CHOCDF?.quantity / recipe.yield) || aiRecipe.nutrition.carbs,
          fat: Math.round(recipe.totalNutrients.FAT?.quantity / recipe.yield) || aiRecipe.nutrition.fat
        },
        status: null
      };

      // Step 3: Add accurate cost information using Spoonacular
      const cost = await estimateRecipeCost(transformedRecipe.ingredients);
      transformedRecipe.cost = cost;

      return transformedRecipe;
    }

    // If we couldn't find a real recipe, use the AI-generated one
    return {
      id: `ai-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      name: aiRecipe.name,
      image: '', // AI doesn't provide images
      ingredients: aiRecipe.ingredients,
      instructions: aiRecipe.instructions,
      cost: aiRecipe.totalCost,
      calories: aiRecipe.nutrition.calories,
      prepTime: aiRecipe.prepTime || 0,
      cookTime: aiRecipe.cookTime || 0,
      macros: {
        protein: aiRecipe.nutrition.protein,
        carbs: aiRecipe.nutrition.carbs,
        fats: aiRecipe.nutrition.fat
      },
      nutrition: {
        protein: aiRecipe.nutrition.protein,
        carbs: aiRecipe.nutrition.carbs,
        fat: aiRecipe.nutrition.fat
      },
      status: null
    };
  } catch (error) {
    console.error('Error generating meal with hybrid approach:', error);

    // Fallback to default recipes
    console.log('Falling back to default recipes...');
    toast.error('API error: Using default recipe as fallback');

    // Get a default recipe based on the meal type
    if (options.mealType === 'breakfast') {
      return DEFAULT_RECIPES.breakfast[0];
    } else if (options.mealType === 'lunch') {
      return DEFAULT_RECIPES.lunch[0];
    } else if (options.mealType === 'dinner') {
      return DEFAULT_RECIPES.dinner[0];
    } else {
      // If meal type is not recognized, return a breakfast recipe as default
      return DEFAULT_RECIPES.breakfast[0];
    }
  }
}
