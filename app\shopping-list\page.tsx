"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useSupabase } from "@/components/supabase-provider";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { ChevronLeft, ShoppingCart, Plus, Trash2, Download, Check, Package } from "lucide-react";
import { ShoppingListService } from "@/lib/services/shopping-list-service";
import { ShoppingList, ShoppingItem, ShoppingCategory, NewShoppingItem } from "@/types/shopping-list";
import { shoppingListService } from "@/app/services/shopping-list-service";
import ShoppingListFallback from "./fallback";

// Using types imported from @/types/shopping-list

export default function ShoppingListPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const [isLoading, setIsLoading] = useState(true);
  const [shoppingLists, setShoppingLists] = useState<ShoppingList[]>([]);
  const [activeList, setActiveList] = useState<ShoppingList | null>(null);
  const [categories, setCategories] = useState<ShoppingCategory[]>([]);
  const [newItem, setNewItem] = useState<NewShoppingItem>({
    name: "",
    quantity: "1",
    unit: "item",
    category: "Other",
    checked: false,
    in_pantry: false
  });
  const [pantryItems, setPantryItems] = useState<any[]>([]);
  const [fromPantry, setFromPantry] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [useFallback, setUseFallback] = useState(false);
  const [fallbackMealPlanId, setFallbackMealPlanId] = useState<string | null>(null);

  useEffect(() => {
    // Don't fetch if Supabase is still loading or not available
    if (isSupabaseLoading || !supabase) {
      return;
    }

    // Check if we have a shopping list ID in the query params
    const listId = searchParams?.get('id');
    if (listId) {
      // Create an async function to handle the check
      const checkShoppingList = async () => {
        try {
          // Try to fetch the shopping list first to see if it exists
          const { data, error } = await shoppingListService.getShoppingListWithItems(listId);

          if (error || !data) {
            console.warn('Shopping list not found or error:', error);
            // If there's an error, show a fallback instead of redirecting
            setUseFallback(true);
            setFallbackMealPlanId(listId);
          } else {
            // If the shopping list exists, redirect to the dynamic route
            router.push(`/shopping-list/${listId}`);
          }
        } catch (error) {
          console.error('Error checking shopping list:', error);
          setUseFallback(true);
          setFallbackMealPlanId(listId);
        }
      };

      // Call the async function
      checkShoppingList();
      return; // Exit early if we're handling a query parameter
    }

    const fetchShoppingLists = async () => {
      try {
        setIsLoading(true);

        // Get the current user
        let userId = '';
        try {
          const { data: { user }, error: authError } = await supabase.auth.getUser();

          if (authError) {
            console.error('Auth error:', authError);
            throw authError;
          }

          if (!user) {
            console.log('No user found, using mock user');
            userId = 'mock-user';
          } else {
            userId = user.id;
          }
        } catch (authError) {
          console.error('Error getting user:', authError);
          // Use a mock user ID for testing
          userId = 'mock-user';
        }

        // Check if we're coming from a meal plan or pantry page
        const fromPage = searchParams.get('from');
        const mealPlanId = searchParams.get('mealPlanId');

        if (fromPage === 'pantry') {
          setFromPantry(true);
          await fetchPantryItems(userId);
        }

        if (mealPlanId) {
          // Load the specific meal plan and generate a shopping list
          await handleGenerateFromMealPlan(userId, mealPlanId);
          return;
        }

        try {
          // Fetch all shopping lists for the user
          const lists = await ShoppingListService.getShoppingLists(userId);
          setShoppingLists(lists);

          // If there are lists, load the most recent one
          if (lists.length > 0) {
            await loadShoppingList(lists[0].id);
          } else {
            // Create a new empty shopping list
            const newList = await ShoppingListService.createShoppingList(userId, {
              name: 'Shopping List'
            });
            await loadShoppingList(newList.id);
          }
        } catch (error) {
          console.error('Error handling shopping lists:', error);
          // Create mock data for demonstration
          createMockData();
        }
      } catch (error) {
        console.error('Error fetching shopping lists:', error);
        toast.error('Failed to load shopping lists');
        // Create mock data for demonstration
        createMockData();
      } finally {
        setIsLoading(false);
      }
    };

    fetchShoppingLists();
  }, [supabase, isSupabaseLoading, router, searchParams]);

  /**
   * Load a shopping list by ID
   */
  const loadShoppingList = async (listId: string) => {
    try {
      const list = await ShoppingListService.getShoppingListWithItems(listId);
      setActiveList(list);
      processItemsIntoCategories(list.items || []);

      // If we're coming from the pantry page, check items against pantry
      if (fromPantry) {
        const { data: { user } } = await supabase.auth.getUser();
        await ShoppingListService.checkAgainstPantry(user.id, listId);
      }
    } catch (error) {
      console.error('Error loading shopping list:', error);
      toast.error('Failed to load shopping list');
    }
  };

  /**
   * Fetch pantry items for the user
   */
  const fetchPantryItems = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('pantry_items')
        .select('*')
        .eq('user_id', userId);

      if (error) throw error;
      setPantryItems(data || []);
    } catch (error) {
      console.error('Error fetching pantry items:', error);
      toast.error('Failed to load pantry items');
    }
  };

  /**
   * Generate a shopping list from a meal plan
   */
  const handleGenerateFromMealPlan = async (userId: string, mealPlanId: string) => {
    try {
      // Fetch the meal plan data
      const { data: mealPlanData, error } = await supabase
        .from('meal_plans')
        .select('*')
        .eq('id', mealPlanId)
        .single();

      if (error) throw error;

      // Generate a shopping list from the meal plan
      const list = await ShoppingListService.generateFromMealPlan(
        userId,
        mealPlanId,
        mealPlanData.meal_data
      );

      setActiveList(list);
      processItemsIntoCategories(list.items || []);
      toast.success('Shopping list generated from meal plan');
    } catch (error) {
      console.error('Error generating shopping list:', error);
      toast.error('Failed to generate shopping list');
    }
  };

  /**
   * Create mock data for demonstration
   */
  const createMockData = () => {
    const mockList: ShoppingList = {
      id: 'mock-list-1',
      user_id: 'mock-user',
      name: 'Shopping List',
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      items: [
        {
          id: 'mock-item-1',
          shopping_list_id: 'mock-list-1',
          name: 'Apples',
          quantity: '6',
          unit: 'item',
          category: 'Produce',
          checked: false,
          in_pantry: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'mock-item-2',
          shopping_list_id: 'mock-list-1',
          name: 'Milk',
          quantity: '1',
          unit: 'gallon',
          category: 'Dairy & Eggs',
          checked: false,
          in_pantry: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'mock-item-3',
          shopping_list_id: 'mock-list-1',
          name: 'Bread',
          quantity: '1',
          unit: 'loaf',
          category: 'Bakery',
          checked: false,
          in_pantry: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ]
    };

    setActiveList(mockList);
    processItemsIntoCategories(mockList.items || []);
  };

  /**
   * Process items into categories
   */
  const processItemsIntoCategories = (items: ShoppingItem[]) => {
    // Group items by category
    const categoryMap = new Map<string, ShoppingItem[]>();

    // Filter items by search query if provided
    const filteredItems = searchQuery
      ? items.filter(item =>
          item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.category.toLowerCase().includes(searchQuery.toLowerCase()))
      : items;

    // Group items by category
    filteredItems.forEach(item => {
      const category = item.category || 'Other';
      if (!categoryMap.has(category)) {
        categoryMap.set(category, []);
      }
      categoryMap.get(category)!.push(item);
    });

    // Convert map to array and sort items within each category
    const categoriesArray: ShoppingCategory[] = [];
    categoryMap.forEach((items, name) => {
      // Sort items by name
      items.sort((a, b) => a.name.localeCompare(b.name));

      categoriesArray.push({
        name,
        items
      });
    });

    // Sort categories
    const categoryOrder = [
      'Produce',
      'Meat & Seafood',
      'Dairy & Eggs',
      'Bakery',
      'Pantry',
      'Frozen',
      'Beverages',
      'Snacks',
      'Condiments & Spices'
    ];

    categoriesArray.sort((a, b) => {
      const aIndex = categoryOrder.indexOf(a.name);
      const bIndex = categoryOrder.indexOf(b.name);

      // If both categories are in the order list, sort by their order
      if (aIndex !== -1 && bIndex !== -1) {
        return aIndex - bIndex;
      }

      // If only one category is in the order list, it comes first
      if (aIndex !== -1) return -1;
      if (bIndex !== -1) return 1;

      // If neither category is in the order list, sort alphabetically
      return a.name.localeCompare(b.name);
    });

    setCategories(categoriesArray);
  };

  /**
   * Add a new item to the shopping list
   */
  const handleAddItem = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newItem.name.trim()) {
      toast.error('Please enter an item name');
      return;
    }

    if (!activeList) {
      toast.error('No active shopping list');
      return;
    }

    try {
      const item = await ShoppingListService.addItemToList(activeList.id, newItem);

      // Update the active list with the new item
      const updatedItems = [...(activeList.items || []), item];
      setActiveList({
        ...activeList,
        items: updatedItems
      });

      // Update categories
      processItemsIntoCategories(updatedItems);

      // Reset form
      setNewItem({
        name: '',
        quantity: '1',
        unit: 'item',
        category: 'Other',
        checked: false,
        in_pantry: false
      });

      toast.success('Item added to shopping list');
    } catch (error) {
      console.error('Error adding item to shopping list:', error);
      toast.error('Failed to add item to shopping list');
    }
  };

  /**
   * Toggle the checked status of an item
   */
  const toggleItemChecked = async (itemId: string, checked: boolean) => {
    if (!activeList) return;

    try {
      // Update the item in the database
      const updatedItem = await ShoppingListService.toggleItemChecked(itemId, checked);

      // Update the active list
      const updatedItems = activeList.items?.map(item =>
        item.id === itemId ? { ...updatedItem } : item
      ) || [];

      setActiveList({
        ...activeList,
        items: updatedItems
      });

      // Update categories
      processItemsIntoCategories(updatedItems);
    } catch (error) {
      console.error('Error updating item:', error);
      toast.error('Failed to update item');

      // Update the UI optimistically even if the server update fails
      if (activeList?.items) {
        const updatedItems = activeList.items.map(item =>
          item.id === itemId ? { ...item, checked } : item
        );

        setActiveList({
          ...activeList,
          items: updatedItems
        });

        // Update categories
        processItemsIntoCategories(updatedItems);
      }
    }
  };

  /**
   * Delete an item from the shopping list
   */
  const handleDeleteItem = async (itemId: string) => {
    if (!activeList) return;

    try {
      // Delete the item from the database
      await ShoppingListService.deleteItem(itemId);

      // Update the active list
      const updatedItems = activeList.items?.filter(item => item.id !== itemId) || [];

      setActiveList({
        ...activeList,
        items: updatedItems
      });

      // Update categories
      processItemsIntoCategories(updatedItems);

      toast.success('Item removed from shopping list');
    } catch (error) {
      console.error('Error deleting item:', error);
      toast.error('Failed to delete item');
    }
  };

  /**
   * Handle search input change
   */
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    if (activeList?.items) {
      processItemsIntoCategories(activeList.items);
    }
  };

  /**
   * Handle done button click
   */
  const handleDone = () => {
    try {
      toast.success("Shopping list saved");
      router.refresh();
      router.push("/dashboard");
    } catch (error) {
      console.error("Error saving shopping list:", error);
      toast.error("Failed to save shopping list");
    }
  };

  /**
   * Handle adding items to pantry
   */
  const handleAddToPantry = async () => {
    if (!activeList) return;

    try {
      // Get checked items
      const checkedItems = activeList.items?.filter(item => item.checked) || [];

      if (checkedItems.length === 0) {
        toast.error('No items selected to add to pantry');
        return;
      }

      // Get the current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        router.push('/login');
        return;
      }

      // Add each checked item to the pantry
      for (const item of checkedItems) {
        await supabase
          .from('pantry_items')
          .insert({
            user_id: user.id,
            name: item.name,
            category: item.category,
            quantity: item.quantity,
            unit: item.unit
          });
      }

      toast.success(`${checkedItems.length} items added to pantry`);

      // Mark these items as in_pantry
      const updatedItems = activeList.items?.map(item =>
        item.checked ? { ...item, in_pantry: true } : item
      ) || [];

      setActiveList({
        ...activeList,
        items: updatedItems
      });

      // Update categories
      processItemsIntoCategories(updatedItems);
    } catch (error) {
      console.error('Error adding items to pantry:', error);
      toast.error('Failed to add items to pantry');
    }
  };

  // Use the fallback component if needed
  if (useFallback) {
    return <ShoppingListFallback mealPlanId={fallbackMealPlanId || undefined} />;
  }

  if (isLoading || isSupabaseLoading) {
    return (
      <div className="container max-w-4xl py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl py-8">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center">
            <Button variant="ghost" onClick={handleDone} className="mr-2">
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <CardTitle>Shopping List</CardTitle>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleAddToPantry}>
              <Package className="h-4 w-4 mr-2" />
              Add to Pantry
            </Button>
            <Button onClick={handleDone}>
              <Check className="h-4 w-4 mr-2" />
              Done
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search and Add Item Form */}
          <div className="mb-6">
            <div className="flex items-center space-x-2 mb-4">
              <div className="relative flex-1">
                <Input
                  type="search"
                  placeholder="Search items..."
                  value={searchQuery}
                  onChange={handleSearch}
                  className="pl-10"
                />
                <ShoppingCart className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              </div>
            </div>

            <form onSubmit={handleAddItem} className="grid grid-cols-1 md:grid-cols-12 gap-3">
              <div className="md:col-span-5">
                <Input
                  placeholder="Item name"
                  value={newItem.name}
                  onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                  required
                />
              </div>
              <div className="md:col-span-2">
                <Input
                  type="text"
                  placeholder="Qty"
                  value={newItem.quantity}
                  onChange={(e) => setNewItem({ ...newItem, quantity: e.target.value })}
                />
              </div>
              <div className="md:col-span-2">
                <Input
                  type="text"
                  placeholder="Unit"
                  value={newItem.unit}
                  onChange={(e) => setNewItem({ ...newItem, unit: e.target.value })}
                />
              </div>
              <div className="md:col-span-2">
                <Input
                  type="text"
                  placeholder="Category"
                  value={newItem.category}
                  onChange={(e) => setNewItem({ ...newItem, category: e.target.value })}
                  list="categories"
                />
                <datalist id="categories">
                  <option value="Produce" />
                  <option value="Meat & Seafood" />
                  <option value="Dairy & Eggs" />
                  <option value="Bakery" />
                  <option value="Pantry" />
                  <option value="Frozen" />
                  <option value="Beverages" />
                  <option value="Snacks" />
                  <option value="Condiments & Spices" />
                  <option value="Other" />
                </datalist>
              </div>
              <div className="md:col-span-1">
                <Button type="submit" className="w-full" size="icon">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </form>
          </div>

          {/* Shopping List */}
          {categories.length === 0 ? (
            <div className="text-center py-12">
              <ShoppingCart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Your shopping list is empty</h3>
              <p className="text-muted-foreground mb-4">Add items to your shopping list to get started</p>
            </div>
          ) : (
            <div className="space-y-6">
              {categories.map((category) => (
                <div key={category.name} className="space-y-2">
                  <h3 className="font-medium text-sm text-muted-foreground">{category.name}</h3>
                  <div className="space-y-1">
                    {category.items.map((item) => (
                      <div
                        key={item.id}
                        className={`flex items-center justify-between p-3 rounded-md ${item.checked ? 'bg-muted/50' : 'hover:bg-muted/30'} ${item.in_pantry ? 'border-l-4 border-green-500' : ''}`}
                      >
                        <div className="flex items-center space-x-3 flex-1">
                          <Checkbox
                            checked={item.checked}
                            onCheckedChange={(checked) => toggleItemChecked(item.id, checked as boolean)}
                            className="h-5 w-5"
                          />
                          <div className={item.checked ? 'line-through text-muted-foreground' : ''}>
                            <span className="font-medium">{item.name}</span>
                            <span className="text-sm text-muted-foreground ml-2">
                              {item.quantity} {item.unit}
                            </span>
                            {item.in_pantry && (
                              <span className="ml-2 text-xs text-green-600 font-medium">
                                In Pantry
                              </span>
                            )}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteItem(item.id)}
                          className="h-8 w-8 text-muted-foreground hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
