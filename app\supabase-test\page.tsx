'use client';

import React, { useEffect, useState } from 'react';
import { useSupabase } from '@/components/supabase-provider';
import { SupabaseDebug } from '@/components/supabase-debug';
import { withSupabase } from '@/components/with-supabase';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

function SupabaseTestContent() {
  const [testResult, setTestResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();

  const runTest = async () => {
    try {
      setTestResult(null);
      setError(null);

      // Test 1: Check if supabase client exists
      if (!supabase) {
        throw new Error('Supabase client is null or undefined');
      }

      // Test 2: Try to get the current user
      const { data, error: authError } = await supabase.auth.getUser();

      if (authError) {
        throw new Error(`Auth error: ${authError.message}`);
      }

      setTestResult(
        `Success! User: ${data.user ? data.user.email || data.user.id : 'Not logged in'}`
      );
    } catch (err: any) {
      console.error('Test error:', err);
      setError(err.message || 'Unknown error');
    }
  };

  return (
    <div className="container max-w-4xl py-8">
      <h1 className="text-2xl font-bold mb-6">Supabase Provider Test</h1>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Provider Status</CardTitle>
          <CardDescription>
            Check if the Supabase provider is working correctly
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div>
              <span className="font-semibold">Is loading:</span> {isSupabaseLoading ? 'Yes' : 'No'}
            </div>
            <div>
              <span className="font-semibold">Supabase client exists:</span> {supabase ? 'Yes' : 'No'}
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={runTest}>Run Test</Button>
        </CardFooter>
      </Card>

      {testResult && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-md text-green-700 mb-6">
          {testResult}
        </div>
      )}

      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md text-red-700 mb-6">
          Error: {error}
        </div>
      )}

      <SupabaseDebug />
    </div>
  );
}

export default withSupabase(SupabaseTestContent);
