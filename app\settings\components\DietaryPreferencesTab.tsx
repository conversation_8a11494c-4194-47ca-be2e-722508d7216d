"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";

// Define common dietary restrictions
const dietaryRestrictions = [
  { id: "vegetarian", label: "Vegetarian" },
  { id: "vegan", label: "Vegan" },
  { id: "pescatarian", label: "Pescatarian" },
  { id: "gluten-free", label: "Gluten-Free" },
  { id: "dairy-free", label: "Dairy-Free" },
  { id: "nut-free", label: "Nut-Free" },
  { id: "egg-free", label: "Egg-Free" },
  { id: "soy-free", label: "Soy-Free" },
  { id: "shellfish-free", label: "Shellfish-Free" },
  { id: "kosher", label: "Kosher" },
  { id: "halal", label: "Halal" },
];

// Define common allergens
const commonAllergens = [
  { id: "peanuts", label: "Peanuts" },
  { id: "tree-nuts", label: "Tree Nuts" },
  { id: "milk", label: "Milk" },
  { id: "eggs", label: "Eggs" },
  { id: "fish", label: "Fish" },
  { id: "shellfish", label: "Shellfish" },
  { id: "soy", label: "Soy" },
  { id: "wheat", label: "Wheat" },
  { id: "sesame", label: "Sesame" },
  { id: "mustard", label: "Mustard" },
  { id: "celery", label: "Celery" },
  { id: "lupin", label: "Lupin" },
  { id: "sulfites", label: "Sulfites" },
];

// Define the form schema
const dietaryPreferencesSchema = z.object({
  dietary_preference: z.string(),
  dietary_restrictions: z.array(z.string()),
  allergens: z.array(z.string()),
  custom_exclusions: z.array(z.string()),
});

type DietaryPreferencesFormValues = z.infer<typeof dietaryPreferencesSchema>;

interface DietaryPreferencesTabProps {
  userData: any;
  userPreferences: any;
  supabase: any;
  onUpdate: (data: any) => void;
}

export default function DietaryPreferencesTab({ userData, userPreferences, supabase, onUpdate }: DietaryPreferencesTabProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [newAllergen, setNewAllergen] = useState("");
  const [newExclusion, setNewExclusion] = useState("");

  // Log data for debugging
  console.log("DietaryPreferencesTab received userData:", userData);
  console.log("DietaryPreferencesTab received userPreferences:", userPreferences);

  // Initialize form with user data
  const form = useForm<DietaryPreferencesFormValues>({
    resolver: zodResolver(dietaryPreferencesSchema),
    defaultValues: {
      dietary_preference: userPreferences?.dietary_preference || "omnivore",
      dietary_restrictions: userData?.dietary_restrictions || [],
      allergens: userPreferences?.allergens || [],
      custom_exclusions: userPreferences?.custom_exclusions || [],
    },
  });

  const { watch, setValue } = form;
  const watchedAllergens = watch("allergens");
  const watchedExclusions = watch("custom_exclusions");

  const handleAddAllergen = () => {
    if (!newAllergen.trim()) return;

    // Check if allergen already exists
    if (!watchedAllergens.includes(newAllergen)) {
      setValue("allergens", [...watchedAllergens, newAllergen]);
      setNewAllergen("");
      toast.success(`Added ${newAllergen} to allergens`);
    } else {
      toast.error(`${newAllergen} is already in your allergens list`);
    }
  };

  const handleRemoveAllergen = (allergen: string) => {
    setValue(
      "allergens",
      watchedAllergens.filter((a) => a !== allergen)
    );
    toast.success(`Removed ${allergen} from allergens`);
  };

  const handleAddExclusion = () => {
    if (!newExclusion.trim()) return;

    // Check if exclusion already exists
    if (!watchedExclusions.includes(newExclusion)) {
      setValue("custom_exclusions", [...watchedExclusions, newExclusion]);
      setNewExclusion("");
      toast.success(`Added ${newExclusion} to exclusions`);
    } else {
      toast.error(`${newExclusion} is already in your exclusions list`);
    }
  };

  const handleRemoveExclusion = (exclusion: string) => {
    setValue(
      "custom_exclusions",
      watchedExclusions.filter((e) => e !== exclusion)
    );
    toast.success(`Removed ${exclusion} from exclusions`);
  };

  const onSubmit = async (data: DietaryPreferencesFormValues) => {
    try {
      setIsLoading(true);

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        toast.error("Authentication error. Please log in again.");
        return;
      }

      // Update dietary restrictions in users table
      const { error: userUpdateError } = await supabase
        .from('users')
        .update({
          dietary_restrictions: data.dietary_restrictions,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (userUpdateError) {
        throw userUpdateError;
      }

      // Prepare preferences data
      const preferencesData = {
        user_id: user.id,
        dietary_preference: data.dietary_preference,
        allergens: data.allergens,
        custom_exclusions: data.custom_exclusions,
        updated_at: new Date().toISOString(),
      };

      // Check if user preferences exist
      if (userPreferences?.id) {
        // Update existing preferences
        const { error: preferencesUpdateError } = await supabase
          .from('user_preferences')
          .update(preferencesData)
          .eq('id', userPreferences.id);

        if (preferencesUpdateError) {
          throw preferencesUpdateError;
        }
      } else {
        // Insert new preferences
        const { error: preferencesInsertError } = await supabase
          .from('user_preferences')
          .insert({
            ...preferencesData,
            created_at: new Date().toISOString(),
          });

        if (preferencesInsertError) {
          throw preferencesInsertError;
        }
      }

      // Update local state
      onUpdate({
        ...preferencesData,
        dietary_restrictions: data.dietary_restrictions,
      });

      toast.success("Dietary preferences updated successfully");
    } catch (error) {
      console.error("Error updating dietary preferences:", error);
      toast.error("Failed to update dietary preferences");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Dietary Preferences</CardTitle>
          <CardDescription>
            Set your dietary preferences and restrictions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="dietary_preference"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Dietary Preference</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select your dietary preference" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="omnivore">Omnivore</SelectItem>
                        <SelectItem value="vegetarian">Vegetarian</SelectItem>
                        <SelectItem value="vegan">Vegan</SelectItem>
                        <SelectItem value="pescatarian">Pescatarian</SelectItem>
                        <SelectItem value="flexitarian">Flexitarian</SelectItem>
                        <SelectItem value="keto">Keto</SelectItem>
                        <SelectItem value="paleo">Paleo</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Your primary dietary preference
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dietary_restrictions"
                render={() => (
                  <FormItem>
                    <div className="mb-4">
                      <FormLabel>Dietary Restrictions</FormLabel>
                      <FormDescription>
                        Select any dietary restrictions you follow
                      </FormDescription>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {dietaryRestrictions.map((restriction) => (
                        <FormField
                          key={restriction.id}
                          control={form.control}
                          name="dietary_restrictions"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={restriction.id}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(restriction.id)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...field.value, restriction.id])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== restriction.id
                                            )
                                          );
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {restriction.label}
                                </FormLabel>
                              </FormItem>
                            );
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="allergens"
                render={() => (
                  <FormItem>
                    <div className="mb-4">
                      <FormLabel>Allergens</FormLabel>
                      <FormDescription>
                        Select any food allergens you need to avoid
                      </FormDescription>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {commonAllergens.map((allergen) => (
                        <FormField
                          key={allergen.id}
                          control={form.control}
                          name="allergens"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={allergen.id}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(allergen.id)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...field.value, allergen.id])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== allergen.id
                                            )
                                          );
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {allergen.label}
                                </FormLabel>
                              </FormItem>
                            );
                          }}
                        />
                      ))}
                    </div>

                    <div className="mt-4">
                      <FormLabel>Custom Allergens</FormLabel>
                      <div className="flex space-x-2 mt-1">
                        <Input
                          placeholder="Add a custom allergen"
                          value={newAllergen}
                          onChange={(e) => setNewAllergen(e.target.value)}
                          className="flex-1"
                        />
                        <Button
                          type="button"
                          variant="secondary"
                          onClick={handleAddAllergen}
                        >
                          Add
                        </Button>
                      </div>

                      {watchedAllergens.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-3">
                          {watchedAllergens
                            .filter(allergen => !commonAllergens.some(a => a.id === allergen))
                            .map((allergen) => (
                              <Badge key={allergen} variant="secondary" className="px-3 py-1">
                                {allergen}
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="h-4 w-4 ml-1 hover:bg-destructive/20"
                                  onClick={() => handleRemoveAllergen(allergen)}
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </Badge>
                            ))}
                        </div>
                      )}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="custom_exclusions"
                render={() => (
                  <FormItem>
                    <div className="mb-4">
                      <FormLabel>Food Exclusions</FormLabel>
                      <FormDescription>
                        Add any specific foods you want to exclude from your meal plans
                      </FormDescription>
                    </div>

                    <div className="flex space-x-2">
                      <Input
                        placeholder="Add a food to exclude (e.g., mushrooms, cilantro)"
                        value={newExclusion}
                        onChange={(e) => setNewExclusion(e.target.value)}
                        className="flex-1"
                      />
                      <Button
                        type="button"
                        variant="secondary"
                        onClick={handleAddExclusion}
                      >
                        Add
                      </Button>
                    </div>

                    {watchedExclusions.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-3">
                        {watchedExclusions.map((exclusion) => (
                          <Badge key={exclusion} variant="secondary" className="px-3 py-1">
                            {exclusion}
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="h-4 w-4 ml-1 hover:bg-destructive/20"
                              onClick={() => handleRemoveExclusion(exclusion)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : "Save Changes"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
