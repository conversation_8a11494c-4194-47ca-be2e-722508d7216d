import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

export interface SignUpData {
  email: string;
  password: string;
  weeklyBudget?: number;
  householdSize?: number;
  dietaryRestrictions?: string[];
}

export class AuthService {
  static async createUser(data: SignUpData) {
    // Create Supabase admin client for server-side operations
    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Create Supabase auth user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: data.email,
      password: data.password,
    });

    if (authError || !authData.user) {
      throw new Error(authError?.message || 'Failed to create auth user');
    }

    // Create user record in the users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email: data.email,
        weekly_budget: data.weeklyBudget || 0,
        household_size: data.householdSize || 1,
        dietary_restrictions: data.dietaryRestrictions || [],
      })
      .select()
      .single();

    if (userError) {
      throw new Error(userError.message || 'Failed to create user record');
    }

    return { user: authData.user, profile: userData };
  }
}