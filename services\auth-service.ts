import { createServerSupabaseClient } from '@/lib/supabase';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';

export interface SignUpData {
  email: string;
  password: string;
  weeklyBudget?: number;
  householdSize?: number;
  dietaryRestrictions?: string[];
}

export class AuthService {
  static async createUser(data: SignUpData) {
    const supabase = createServerSupabaseClient();
    
    // Create Supabase auth user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: data.email,
      password: data.password,
    });

    if (authError || !authData.user) {
      throw new Error(authError?.message || 'Failed to create auth user');
    }

    // Create Prisma user record
    const user = await prisma.user.create({
      data: {
        id: authData.user.id,
        email: data.email,
        hashed_password: await bcrypt.hash(data.password, 10),
        weekly_budget: data.weeklyBudget || 0,
        household_size: data.householdSize || 1,
        dietary_restrictions: data.dietaryRestrictions || [],
      },
    });

    return { authData, user };
  }
}