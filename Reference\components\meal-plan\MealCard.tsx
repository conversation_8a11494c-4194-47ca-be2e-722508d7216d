"use client";

import { useState } from "react";
import { useDrag } from "react-dnd";
import { Utensils, DollarSign, Flame, X, Check, Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { useMealPlanStore } from "@/lib/stores/meal-plan-store";
import { Meal } from "@/types/meal-plan";
import MealDetailsModal from "./MealDetailsModal";

interface MealCardProps {
  meal: Meal;
  day: string;
  mealType: string;
}

export default function MealCard({ meal, day, mealType }: MealCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const { setMealStatus, removeMeal } = useMealPlanStore();

  const [{ isDragging }, drag] = useDrag(() => ({
    type: "MEAL",
    item: { id: meal.id, day, mealType, meal },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging()
    })
  }));

  const handleMarkAsCooked = (e: React.MouseEvent) => {
    e.stopPropagation();
    setMealStatus(day, mealType, "cooked");
  };

  const handleMarkAsSkipped = (e: React.MouseEvent) => {
    e.stopPropagation();
    setMealStatus(day, mealType, "skipped");
  };

  const handleRemoveMeal = (e: React.MouseEvent) => {
    e.stopPropagation();
    removeMeal(day, mealType);
  };

  const getStatusColor = () => {
    if (meal.status === "cooked") return "bg-green-500/10 border-green-500/30";
    if (meal.status === "skipped") return "bg-red-500/10 border-red-500/30";
    return "";
  };

  return (
    <>
      <Card 
        ref={drag}
        className={cn(
          "relative overflow-hidden cursor-move h-20 transition-all duration-200",
          getStatusColor(),
          isDragging ? "opacity-50" : "opacity-100"
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={() => setIsDetailsModalOpen(true)}
      >
        <CardContent className="p-2 h-full flex flex-col justify-between">
          <div className="flex justify-between items-start">
            <h4 className="font-medium text-sm line-clamp-1">{meal.name}</h4>
            {(isHovered || meal.status) && (
              <div className="flex space-x-1">
                {meal.status === "cooked" ? (
                  <div className="h-5 w-5 rounded-full bg-green-500 flex items-center justify-center">
                    <Check className="h-3 w-3 text-white" />
                  </div>
                ) : meal.status === "skipped" ? (
                  <div className="h-5 w-5 rounded-full bg-red-500 flex items-center justify-center">
                    <X className="h-3 w-3 text-white" />
                  </div>
                ) : (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button size="icon" variant="ghost" className="h-5 w-5 p-0" onClick={handleMarkAsCooked}>
                          <Check className="h-3 w-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Mark as cooked</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
                
                {!meal.status && (
                  <>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button size="icon" variant="ghost" className="h-5 w-5 p-0" onClick={handleMarkAsSkipped}>
                            <X className="h-3 w-3" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Mark as skipped</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button size="icon" variant="ghost" className="h-5 w-5 p-0 text-destructive" onClick={handleRemoveMeal}>
                            <X className="h-3 w-3" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Remove meal</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </>
                )}
              </div>
            )}
          </div>
          
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <DollarSign className="h-3 w-3" />
              <span>${meal.cost.toFixed(2)}</span>
            </div>
            <div className="flex items-center gap-1">
              <Flame className="h-3 w-3" />
              <span>{meal.calories} cal</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {isDetailsModalOpen && (
        <MealDetailsModal 
          isOpen={isDetailsModalOpen}
          onClose={() => setIsDetailsModalOpen(false)}
          meal={meal}
          day={day}
          mealType={mealType}
        />
      )}
    </>
  );
}