'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useMealPlanner } from "@/app/context/MealPlannerContext";
import { useEffect } from "react";

export function UpcomingMealsPreview() {
  const { todaysMeals, loadTodaysMeals, isLoading, error } = useMealPlanner();

  useEffect(() => {
    loadTodaysMeals();
  }, [loadTodaysMeals]);

  if (isLoading) {
    return <div>Loading meals...</div>;
  }

  if (error) {
    return <div>Error loading meals</div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Today's Meals</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {todaysMeals.map((meal) => (
            <div key={meal.id} className="flex justify-between items-center">
              <div>
                <h3 className="font-medium">{meal.name}</h3>
                <p className="text-sm text-muted-foreground">{meal.time}</p>
              </div>
              <span className="text-sm">{meal.calories} cal</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}


