'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  format,
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  isSameDay,
  addWeeks,
  subWeeks,
  isWithinInterval,
  parseISO,
  isBefore,
  startOfDay
} from 'date-fns';
import { ChevronLeft, ChevronRight, Plus, Calendar, X } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { mealPlanService } from '@/lib/supabase/meal-plan-service';
import { CalendarAssignmentModal } from './calendar-assignment-modal';
import { useSupabase } from '@/components/supabase-provider';

interface MealPlan {
  id: string;
  user_id: string;
  start_date: string;
  end_date: string;
  total_cost: number;
  meal_data: any;
  status: string;
  created_at: string;
  updated_at: string;
  name?: string;
  description?: string;
}

interface MealPlanAssignment {
  id: string;
  user_id: string;
  meal_plan_id: string;
  start_date: string;
  end_date: string;
  created_at: string;
  updated_at: string;
  meal_plans: MealPlan;
}

interface WeeklyCalendarViewProps {
  mealPlans: MealPlan[];
  onSelectPlan: (plan: MealPlan) => void;
  onRefresh?: () => void;
}

export function WeeklyCalendarView({
  mealPlans,
  onSelectPlan,
  onRefresh
}: WeeklyCalendarViewProps) {
  const { supabase } = useSupabase();
  const [currentWeek, setCurrentWeek] = useState<Date>(new Date());
  const [weekDays, setWeekDays] = useState<Date[]>([]);
  const [assignments, setAssignments] = useState<MealPlanAssignment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPlan, setSelectedPlan] = useState<MealPlan | null>(null);
  const [showAssignModal, setShowAssignModal] = useState(false);

  // Update week days when current week changes
  useEffect(() => {
    const start = startOfWeek(currentWeek, { weekStartsOn: 1 }); // Start on Monday
    const end = endOfWeek(currentWeek, { weekStartsOn: 1 }); // End on Sunday
    const days = eachDayOfInterval({ start, end });
    setWeekDays(days);
  }, [currentWeek]);

  // Fetch meal plan assignments
  useEffect(() => {
    const fetchAssignments = async () => {
      try {
        setIsLoading(true);

        // First, check for demo assignments in local storage
        try {
          const storedAssignments = localStorage.getItem('demoMealPlanAssignments');
          if (storedAssignments) {
            const demoAssignments = JSON.parse(storedAssignments);
            if (demoAssignments && demoAssignments.length > 0) {
              console.log('Found demo assignments in local storage:', demoAssignments.length);
              setAssignments(demoAssignments as any);
            }
          }
        } catch (localStorageError) {
          console.error('Error reading from local storage:', localStorageError);
          // Continue with normal flow if local storage fails
        }

        // Check if supabase is available
        if (!supabase) {
          console.warn('Supabase client not available');
          setIsLoading(false);
          return;
        }

        try {
          // Get the current user
          const { data: authData, error: authError } = await supabase.auth.getUser();

          if (authError) {
            console.error('Auth error in fetchAssignments:', authError);
            setIsLoading(false);
            return;
          }

          if (!authData.user) {
            console.warn('No authenticated user found');
            setIsLoading(false);
            return;
          }

          try {
            // Fetch assignments with the authenticated user ID
            const assignments = await mealPlanService.getMealPlanAssignments(authData.user.id);

            if (assignments && assignments.length > 0) {
              // If we have real assignments, use those instead of demo ones
              setAssignments(assignments);
            }
          } catch (fetchError) {
            // This is a fallback for any unexpected errors
            console.error('Unexpected error in fetchAssignments:', fetchError);
            // Don't show error toast for authentication issues
            if (fetchError instanceof Error && !fetchError.message.includes('Authentication')) {
              toast('Failed to load calendar data');
            }
          }
        } catch (error) {
          console.error('Error in fetchAssignments:', error);
          if (error instanceof Error && !error.message.includes('Authentication')) {
            toast('Failed to load calendar data');
          }
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssignments();
  }, [supabase]);

  // Navigate to previous week
  const prevWeek = () => {
    setCurrentWeek(subWeeks(currentWeek, 1));
  };

  // Navigate to next week
  const nextWeek = () => {
    setCurrentWeek(addWeeks(currentWeek, 1));
  };

  // Go to current week
  const goToToday = () => {
    setCurrentWeek(new Date());
  };

  // Get assignments for a specific day
  const getAssignmentsForDay = (day: Date) => {
    return assignments.filter(assignment => {
      const start = parseISO(assignment.start_date);
      const end = parseISO(assignment.end_date);

      return isWithinInterval(day, { start, end });
    });
  };

  // Handle removing an assignment
  const handleRemoveAssignment = async (assignmentId: string) => {
    try {
      // Check if this is a demo assignment (id starts with 'demo-')
      if (assignmentId.startsWith('demo-')) {
        try {
          // Get existing assignments from local storage
          const storedAssignments = localStorage.getItem('demoMealPlanAssignments');
          if (storedAssignments) {
            const assignments = JSON.parse(storedAssignments);

            // Filter out the assignment to remove
            const updatedAssignments = assignments.filter((a: any) => a.id !== assignmentId);

            // Save back to local storage
            localStorage.setItem('demoMealPlanAssignments', JSON.stringify(updatedAssignments));

            // Update the assignments list
            setAssignments(prevAssignments => prevAssignments.filter(a => a.id !== assignmentId));

            toast.success('Removed from calendar');

            // Call the refresh callback if provided
            if (onRefresh) {
              onRefresh();
            }

            return;
          }
        } catch (localStorageError) {
          console.error('Error with local storage:', localStorageError);
          // Continue with normal flow if local storage fails
        }
      }

      // Handle regular assignments
      const success = await mealPlanService.removeMealPlanAssignment(assignmentId);

      if (success) {
        toast.success('Removed from calendar');

        // Update the assignments list
        setAssignments(prevAssignments => prevAssignments.filter(a => a.id !== assignmentId));

        // Call the refresh callback if provided
        if (onRefresh) {
          onRefresh();
        }
      } else {
        toast.error('Failed to remove from calendar');
      }
    } catch (error: any) {
      toast(`Error: ${error.message || 'Failed to remove from calendar'}`);
    }
  };

  // Handle adding a plan to the calendar
  const handleAddToCalendar = (plan: MealPlan) => {
    setSelectedPlan(plan);
    setShowAssignModal(true);
  };

  // Handle successful assignment
  const handleAssignmentSuccess = () => {
    // First check for demo assignments in local storage
    try {
      const storedAssignments = localStorage.getItem('demoMealPlanAssignments');
      if (storedAssignments) {
        const demoAssignments = JSON.parse(storedAssignments);
        if (demoAssignments && demoAssignments.length > 0) {
          console.log('Found demo assignments in local storage after update:', demoAssignments.length);
          setAssignments(demoAssignments as any);

          // Call the refresh callback if provided
          if (onRefresh) {
            onRefresh();
          }

          return;
        }
      }
    } catch (localStorageError) {
      console.error('Error reading from local storage:', localStorageError);
      // Continue with normal flow if local storage fails
    }

    // Refresh assignments from database
    const fetchAssignments = async () => {
      try {
        // Check if supabase is available
        if (!supabase) {
          console.warn('Supabase client not available');
          return;
        }

        try {
          // Get the current user
          const { data: authData, error: authError } = await supabase.auth.getUser();

          if (authError || !authData.user) {
            console.error('Auth error in handleAssignmentSuccess:', authError);
            return;
          }

          try {
            // Fetch assignments with the authenticated user ID
            const assignments = await mealPlanService.getMealPlanAssignments(authData.user.id);

            if (assignments && assignments.length > 0) {
              setAssignments(assignments);
            }
          } catch (fetchError) {
            console.error('Unexpected error in handleAssignmentSuccess:', fetchError);
          }

          // Call the refresh callback if provided
          if (onRefresh) {
            onRefresh();
          }
        } catch (error) {
          console.error('Error in fetchAssignments:', error);
        }
      } catch (error) {
        console.error('Error in handleAssignmentSuccess:', error);
      }
    };

    fetchAssignments();
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={prevWeek} aria-label="Previous week">
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={nextWeek} aria-label="Next week">
            <ChevronRight className="h-4 w-4" />
          </Button>
          <h2 className="text-lg font-medium ml-2">
            {format(weekDays[0] || currentWeek, 'MMM d')} - {format(weekDays[6] || currentWeek, 'MMM d, yyyy')}
          </h2>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={goToToday}>
            Today
          </Button>
          <select
            className="border rounded-md px-2 py-1 text-sm"
            value={format(currentWeek, 'yyyy-MM')}
            onChange={(e) => {
              const [year, month] = e.target.value.split('-').map(Number);
              const newDate = new Date(year, month - 1, 1);
              setCurrentWeek(newDate);
            }}
          >
            {Array.from({ length: 12 }, (_, i) => {
              const date = new Date();
              date.setMonth(date.getMonth() - 6 + i);
              return (
                <option key={i} value={format(date, 'yyyy-MM')}>
                  {format(date, 'MMMM yyyy')}
                </option>
              );
            })}
          </select>
        </div>
      </div>

      <div className="grid grid-cols-7 gap-4">
        {weekDays.map((day) => {
          const dayAssignments = getAssignmentsForDay(day);
          const isToday = isSameDay(day, new Date());
          const hasMealPlans = dayAssignments.length > 0;
          const isPastDay = isBefore(day, startOfDay(new Date()));

          return (
            <Card
              key={day.toISOString()}
              className={`transition-all ${isToday ? 'border-primary ring-1 ring-primary' : ''}
                ${hasMealPlans ? 'bg-primary/5' : isPastDay ? 'bg-muted/30' : ''}`}
            >
              <CardHeader className="p-3">
                <CardTitle className={`text-sm font-medium flex justify-between items-center
                  ${isToday ? 'text-primary' : isPastDay && !hasMealPlans ? 'text-muted-foreground' : ''}`}
                >
                  <span>{format(day, 'EEE')}</span>
                  <span className={`rounded-full w-8 h-8 flex items-center justify-center
                    ${isToday ? 'bg-primary text-primary-foreground' : ''}`}
                  >
                    {format(day, 'd')}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-3 pt-0">
                {isLoading ? (
                  <div className="space-y-2">
                    <Skeleton className="h-12 w-full" />
                    <Skeleton className="h-12 w-full" />
                  </div>
                ) : dayAssignments.length > 0 ? (
                  <ScrollArea className="h-[150px]">
                    <div className="space-y-2">
                      {dayAssignments.map((assignment) => (
                        <div
                          key={assignment.id}
                          className="p-2 border rounded-md bg-muted/50 relative group"
                        >
                          <div
                            className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => handleRemoveAssignment(assignment.id)}
                              aria-label="Remove from calendar"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>

                          <div
                            className="cursor-pointer"
                            onClick={() => onSelectPlan(assignment.meal_plans)}
                          >
                            <p className="font-medium text-sm truncate pr-6">
                              {assignment.meal_plans.name || 'Meal Plan'}
                            </p>
                            <div className="flex items-center gap-1 mt-1">
                              <Badge variant="outline" className="text-xs">
                                {assignment.meal_plans.meal_data?.mealPlan?.meals?.length || 0} meals
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                ${assignment.meal_plans.total_cost.toFixed(2)}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                ) : (
                  <div className="flex flex-col items-center justify-center h-[150px] text-muted-foreground">
                    <p className="text-xs mb-2">No meal plans</p>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-7 text-xs"
                      onClick={() => {
                        if (mealPlans.length > 0) {
                          handleAddToCalendar(mealPlans[0]);
                        } else {
                          toast.error('Create a meal plan first');
                        }
                      }}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Add Plan
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="flex justify-between items-center">
        <div className="text-sm text-muted-foreground">
          {assignments.length} meal plan{assignments.length !== 1 ? 's' : ''} assigned to calendar
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            if (mealPlans.length > 0) {
              handleAddToCalendar(mealPlans[0]);
            } else {
              toast.error('Create a meal plan first');
            }
          }}
        >
          <Calendar className="h-4 w-4 mr-2" />
          Add Plan to Calendar
        </Button>
      </div>

      {selectedPlan && (
        <CalendarAssignmentModal
          isOpen={showAssignModal}
          onClose={() => setShowAssignModal(false)}
          mealPlan={selectedPlan}
          onSuccess={handleAssignmentSuccess}
        />
      )}
    </div>
  );
}
