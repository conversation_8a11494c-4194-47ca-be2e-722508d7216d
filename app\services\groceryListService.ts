import { GroceryCategory, GroceryItem } from '../types/mealPlanner';

class GroceryListService {
  private async fetchWithAuth(endpoint: string, options: RequestInit = {}) {
    const response = await fetch(`/api/grocery${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async getGroceryList(): Promise<GroceryCategory[]> {
    return this.fetchWithAuth('/list');
  }

  async toggleItemCheck(categoryId: string, itemId: string, checked: boolean): Promise<GroceryItem> {
    return this.fetchWithAuth(`/item/${itemId}`, {
      method: 'PATCH',
      body: JSON.stringify({ checked }),
    });
  }

  async addItem(categoryId: string, name: string): Promise<GroceryItem> {
    return this.fetchWithAuth(`/category/${categoryId}/items`, {
      method: 'POST',
      body: JSON.stringify({ name }),
    });
  }

  async removeItem(itemId: string): Promise<void> {
    await this.fetchWithAuth(`/item/${itemId}`, {
      method: 'DELETE',
    });
  }
}

export const groceryListService = new GroceryListService();