import { toast } from '@/components/ui/use-toast';

export class WebSocketClient {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout = 1000;
  private messageQueue: any[] = [];

  constructor(private url: string) {
    this.connect();
  }

  private connect() {
    try {
      this.ws = new WebSocket(this.url);

      this.ws.onopen = () => {
        this.reconnectAttempts = 0;
        this.processMessageQueue();
      };

      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        this.handleMessage(data);
      };

      this.ws.onclose = () => {
        this.handleDisconnect();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    } catch (error) {
      console.error('WebSocket connection error:', error);
      this.handleDisconnect();
    }
  }

  private handleDisconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => this.connect(), this.reconnectTimeout * this.reconnectAttempts);
    } else {
      toast({
        title: "Connection Lost",
        description: "Unable to maintain real-time connection. Please refresh the page.",
        variant: "destructive",
      });
    }
  }

  private handleMessage(data: any) {
    switch (data.type) {
      case 'MEAL_PLAN_UPDATE':
        this.handleMealPlanUpdate(data.payload);
        break;
      case 'SHOPPING_LIST_UPDATE':
        this.handleShoppingListUpdate(data.payload);
        break;
      case 'NOTIFICATION':
        this.handleNotification(data.payload);
        break;
      default:
        console.warn('Unknown message type:', data.type);
    }
  }

  private handleMealPlanUpdate(payload: any) {
    // Dispatch event for meal plan components
    window.dispatchEvent(new CustomEvent('mealPlanUpdate', { detail: payload }));
  }

  private handleShoppingListUpdate(payload: any) {
    // Dispatch event for shopping list components
    window.dispatchEvent(new CustomEvent('shoppingListUpdate', { detail: payload }));
  }

  private handleNotification(payload: any) {
    toast({
      title: payload.title,
      description: payload.message,
      variant: payload.variant || "default",
    });
  }

  private processMessageQueue() {
    while (this.messageQueue.length > 0 && this.ws?.readyState === WebSocket.OPEN) {
      const message = this.messageQueue.shift();
      this.send(message);
    }
  }

  public send(message: any) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      this.messageQueue.push(message);
    }
  }

  public disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}