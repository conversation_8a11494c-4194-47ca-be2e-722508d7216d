"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Database, FileJson, FileText, Check, AlertCircle } from 'lucide-react';

export default function DiagnosticPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const runDiagnostic = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/diagnostic');
      const data = await response.json();
      
      if (response.ok) {
        setResults(data);
      } else {
        setError(data.error || 'Failed to run diagnostic');
      }
    } catch (err) {
      setError('An error occurred while running the diagnostic');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Database Diagnostic</h1>
      
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Run Database Diagnostic</CardTitle>
          <CardDescription>
            This will run a comprehensive diagnostic on your Supabase database to check tables, columns, policies, and more.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            The diagnostic will check:
          </p>
          <ul className="list-disc pl-5 space-y-1 text-sm text-muted-foreground mb-4">
            <li>Existing tables and their structure</li>
            <li>Row Level Security (RLS) policies</li>
            <li>Foreign key constraints</li>
            <li>Indexes and triggers</li>
            <li>User permissions</li>
            <li>Potential issues with data types and policies</li>
          </ul>
        </CardContent>
        <CardFooter>
          <Button 
            onClick={runDiagnostic} 
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Running Diagnostic...
              </>
            ) : (
              <>
                <Database className="h-4 w-4" />
                Run Diagnostic
              </>
            )}
          </Button>
        </CardFooter>
      </Card>

      {error && (
        <Card className="mb-8 border-red-200 bg-red-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-red-700 flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700">{error}</p>
          </CardContent>
        </Card>
      )}

      {results && (
        <div className="space-y-6">
          <Card className="border-green-200 bg-green-50">
            <CardHeader className="pb-2">
              <CardTitle className="text-green-700 flex items-center gap-2">
                <Check className="h-5 w-5" />
                Diagnostic Complete
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-green-700">
                Diagnostic ran successfully at {new Date(results.timestamp).toLocaleString()}
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                Results saved to: {results.resultsPath}
              </p>
            </CardContent>
          </Card>

          <h2 className="text-2xl font-bold mt-8 mb-4">Results</h2>

          <Tabs defaultValue="tables">
            <TabsList className="mb-4">
              <TabsTrigger value="tables">Tables</TabsTrigger>
              <TabsTrigger value="rls">RLS Policies</TabsTrigger>
              <TabsTrigger value="constraints">Constraints</TabsTrigger>
              <TabsTrigger value="indexes">Indexes</TabsTrigger>
              <TabsTrigger value="issues">Potential Issues</TabsTrigger>
              <TabsTrigger value="all">All Results</TabsTrigger>
            </TabsList>

            <TabsContent value="tables" className="space-y-4">
              {renderTablesSection(results)}
            </TabsContent>

            <TabsContent value="rls" className="space-y-4">
              {renderRLSSection(results)}
            </TabsContent>

            <TabsContent value="constraints" className="space-y-4">
              {renderConstraintsSection(results)}
            </TabsContent>

            <TabsContent value="indexes" className="space-y-4">
              {renderIndexesSection(results)}
            </TabsContent>

            <TabsContent value="issues" className="space-y-4">
              {renderIssuesSection(results)}
            </TabsContent>

            <TabsContent value="all" className="space-y-4">
              {renderAllResults(results)}
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
}

function renderTablesSection(results: any) {
  const tablesResult = results.results.find((r: any) => 
    r.title === 'Check which tables exist in the public schema'
  );
  
  const columnsResult = results.results.find((r: any) => 
    r.title === 'Check all columns for each table in the public schema'
  );

  if (!tablesResult?.data || !columnsResult?.data) {
    return <p>No table information found</p>;
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Existing Tables</CardTitle>
          <CardDescription>
            Tables found in the public schema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-muted">
                  <th className="border p-2 text-left">Table Name</th>
                </tr>
              </thead>
              <tbody>
                {tablesResult.data.map((table: any, i: number) => (
                  <tr key={i} className="border-b">
                    <td className="border p-2">{table.table_name}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Table Columns</CardTitle>
          <CardDescription>
            Columns for each table in the public schema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-muted">
                  <th className="border p-2 text-left">Table</th>
                  <th className="border p-2 text-left">Column</th>
                  <th className="border p-2 text-left">Data Type</th>
                  <th className="border p-2 text-left">Nullable</th>
                  <th className="border p-2 text-left">Default</th>
                </tr>
              </thead>
              <tbody>
                {columnsResult.data.map((column: any, i: number) => (
                  <tr key={i} className="border-b">
                    <td className="border p-2">{column.table_name}</td>
                    <td className="border p-2">{column.column_name}</td>
                    <td className="border p-2">{column.data_type}</td>
                    <td className="border p-2">{column.is_nullable}</td>
                    <td className="border p-2">{column.column_default || 'NULL'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </>
  );
}

function renderRLSSection(results: any) {
  const rlsEnabledResult = results.results.find((r: any) => 
    r.title === 'Check which tables have RLS enabled'
  );
  
  const rlsPoliciesResult = results.results.find((r: any) => 
    r.title === 'Check all RLS policies'
  );

  if (!rlsEnabledResult?.data || !rlsPoliciesResult?.data) {
    return <p>No RLS information found</p>;
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>RLS Status</CardTitle>
          <CardDescription>
            Tables with Row Level Security enabled
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-muted">
                  <th className="border p-2 text-left">Table Name</th>
                  <th className="border p-2 text-left">RLS Enabled</th>
                </tr>
              </thead>
              <tbody>
                {rlsEnabledResult.data.map((table: any, i: number) => (
                  <tr key={i} className="border-b">
                    <td className="border p-2">{table.tablename}</td>
                    <td className="border p-2">{table.rowsecurity ? 'Yes' : 'No'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>RLS Policies</CardTitle>
          <CardDescription>
            Row Level Security policies defined in the database
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-muted">
                  <th className="border p-2 text-left">Table</th>
                  <th className="border p-2 text-left">Policy Name</th>
                  <th className="border p-2 text-left">Command</th>
                  <th className="border p-2 text-left">Roles</th>
                </tr>
              </thead>
              <tbody>
                {rlsPoliciesResult.data.map((policy: any, i: number) => (
                  <tr key={i} className="border-b">
                    <td className="border p-2">{policy.tablename}</td>
                    <td className="border p-2">{policy.policyname}</td>
                    <td className="border p-2">{policy.cmd}</td>
                    <td className="border p-2">{policy.roles.join(', ')}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </>
  );
}

function renderConstraintsSection(results: any) {
  const constraintsResult = results.results.find((r: any) => 
    r.title === 'Check all foreign key constraints'
  );

  if (!constraintsResult?.data) {
    return <p>No constraint information found</p>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Foreign Key Constraints</CardTitle>
        <CardDescription>
          Foreign key relationships between tables
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted">
                <th className="border p-2 text-left">Table</th>
                <th className="border p-2 text-left">Column</th>
                <th className="border p-2 text-left">References Table</th>
                <th className="border p-2 text-left">References Column</th>
              </tr>
            </thead>
            <tbody>
              {constraintsResult.data.map((constraint: any, i: number) => (
                <tr key={i} className="border-b">
                  <td className="border p-2">{constraint.table_name}</td>
                  <td className="border p-2">{constraint.column_name}</td>
                  <td className="border p-2">{constraint.foreign_table_name}</td>
                  <td className="border p-2">{constraint.foreign_column_name}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}

function renderIndexesSection(results: any) {
  const indexesResult = results.results.find((r: any) => 
    r.title === 'Check all indexes'
  );

  if (!indexesResult?.data) {
    return <p>No index information found</p>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Indexes</CardTitle>
        <CardDescription>
          Indexes defined on tables
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted">
                <th className="border p-2 text-left">Table</th>
                <th className="border p-2 text-left">Index Name</th>
                <th className="border p-2 text-left">Definition</th>
              </tr>
            </thead>
            <tbody>
              {indexesResult.data.map((index: any, i: number) => (
                <tr key={i} className="border-b">
                  <td className="border p-2">{index.tablename}</td>
                  <td className="border p-2">{index.indexname}</td>
                  <td className="border p-2">{index.indexdef}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}

function renderIssuesSection(results: any) {
  const missingRlsResult = results.results.find((r: any) => 
    r.title === 'Check for tables with user_id column but missing RLS policies'
  );
  
  const rlsNoPolicesResult = results.results.find((r: any) => 
    r.title === 'Check for tables with RLS enabled but no policies'
  );
  
  const publicRoleResult = results.results.find((r: any) => 
    r.title === 'Check for tables with policies assigned to public role instead of authenticated'
  );
  
  const typeMismatchResult = results.results.find((r: any) => 
    r.title === 'Check for type mismatches in RLS policies'
  );

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Missing RLS Policies</CardTitle>
          <CardDescription>
            Tables with user_id column but missing RLS policies
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!missingRlsResult?.data || missingRlsResult.data.length === 0 ? (
            <p className="text-green-600">No issues found</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-muted">
                    <th className="border p-2 text-left">Table Name</th>
                  </tr>
                </thead>
                <tbody>
                  {missingRlsResult.data.map((table: any, i: number) => (
                    <tr key={i} className="border-b">
                      <td className="border p-2">{table.table_name}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>RLS Enabled but No Policies</CardTitle>
          <CardDescription>
            Tables with RLS enabled but no policies defined
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!rlsNoPolicesResult?.data || rlsNoPolicesResult.data.length === 0 ? (
            <p className="text-green-600">No issues found</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-muted">
                    <th className="border p-2 text-left">Table Name</th>
                  </tr>
                </thead>
                <tbody>
                  {rlsNoPolicesResult.data.map((table: any, i: number) => (
                    <tr key={i} className="border-b">
                      <td className="border p-2">{table.tablename}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Public Role Policies</CardTitle>
          <CardDescription>
            Tables with policies assigned to public role instead of authenticated
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!publicRoleResult?.data || publicRoleResult.data.length === 0 ? (
            <p className="text-green-600">No issues found</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-muted">
                    <th className="border p-2 text-left">Table Name</th>
                    <th className="border p-2 text-left">Policy Name</th>
                    <th className="border p-2 text-left">Roles</th>
                  </tr>
                </thead>
                <tbody>
                  {publicRoleResult.data.map((policy: any, i: number) => (
                    <tr key={i} className="border-b">
                      <td className="border p-2">{policy.tablename}</td>
                      <td className="border p-2">{policy.policyname}</td>
                      <td className="border p-2">{policy.roles.join(', ')}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Type Mismatches in RLS Policies</CardTitle>
          <CardDescription>
            Potential type mismatches in RLS policy expressions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!typeMismatchResult?.data || typeMismatchResult.data.length === 0 ? (
            <p className="text-green-600">No issues found</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-muted">
                    <th className="border p-2 text-left">Table</th>
                    <th className="border p-2 text-left">Policy</th>
                    <th className="border p-2 text-left">Data Type</th>
                    <th className="border p-2 text-left">Issue</th>
                  </tr>
                </thead>
                <tbody>
                  {typeMismatchResult.data.map((mismatch: any, i: number) => (
                    <tr key={i} className="border-b">
                      <td className="border p-2">{mismatch.tablename}</td>
                      <td className="border p-2">{mismatch.policyname}</td>
                      <td className="border p-2">{mismatch.user_id_data_type}</td>
                      <td className="border p-2">{mismatch.type_check}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
}

function renderAllResults(results: any) {
  return (
    <div className="space-y-6">
      {results.results.map((result: any, i: number) => (
        <Card key={i}>
          <CardHeader>
            <CardTitle>{result.title}</CardTitle>
          </CardHeader>
          <CardContent>
            {result.error ? (
              <p className="text-red-600">Error: {result.error}</p>
            ) : !result.data || result.data.length === 0 ? (
              <p className="text-muted-foreground">No results found</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-muted">
                      {Object.keys(result.data[0]).map((key, j) => (
                        <th key={j} className="border p-2 text-left">{key}</th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {result.data.map((row: any, k: number) => (
                      <tr key={k} className="border-b">
                        {Object.values(row).map((value: any, l: number) => (
                          <td key={l} className="border p-2">
                            {value === null ? 'NULL' : 
                             typeof value === 'object' ? JSON.stringify(value) : 
                             String(value)}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
