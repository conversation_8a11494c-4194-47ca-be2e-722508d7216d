"use client";

import { useState } from 'react';
import { SimpleMealPlanHeader } from '@/components/meal-plan/SimpleMealPlanHeader';

export default function TestPage2() {
  const [currentView, setCurrentView] = useState<'calendar' | 'list'>('calendar');
  const [currentLayout, setCurrentLayout] = useState<'grid' | 'list'>('grid');

  return (
    <div className="container py-6 space-y-6">
      <SimpleMealPlanHeader
        onViewChange={setCurrentView}
        onLayoutChange={setCurrentLayout}
        onGeneratePlan={() => alert('Generate Plan')}
        onAddMeal={() => alert('Add Meal')}
        onViewShoppingList={() => alert('View Shopping List')}
        currentView={currentView}
        currentLayout={currentLayout}
      />
      
      <div className="p-4 border rounded-md">
        <p>Current View: {currentView}</p>
        <p>Current Layout: {currentLayout}</p>
      </div>
    </div>
  );
}
