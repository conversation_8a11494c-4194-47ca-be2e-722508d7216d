import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const updatePreferencesSchema = z.object({
  cooking_skill_level: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  max_cooking_time: z.number().min(5).max(180).optional(),
  preferred_cuisines: z.array(z.string()).optional(),
  excluded_ingredients: z.array(z.string()).optional(),
  notification_preferences: z.object({
    email: z.boolean().optional(),
    push: z.boolean().optional(),
    meal_reminders: z.boolean().optional(),
  }).optional(),
  privacy_settings: z.object({
    data_sharing: z.boolean().optional(),
    analytics: z.boolean().optional(),
  }).optional(),
  units_preference: z.enum(['metric', 'imperial']).optional(),
  currency_preference: z.string().optional(),
});

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get current user
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user preferences
    const { data, error } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', session.user.id)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
      console.error('Error fetching user preferences:', error);
      return NextResponse.json(
        { error: 'Failed to fetch user preferences' },
        { status: 500 }
      );
    }

    // If no preferences exist, return default values
    if (!data) {
      const defaultPreferences = {
        cooking_skill_level: 'intermediate',
        max_cooking_time: 30,
        preferred_cuisines: [],
        excluded_ingredients: [],
        notification_preferences: {
          email: true,
          push: false,
          meal_reminders: true
        },
        privacy_settings: {
          data_sharing: false,
          analytics: true
        },
        units_preference: 'metric',
        currency_preference: 'USD'
      };

      return NextResponse.json({ data: defaultPreferences });
    }

    return NextResponse.json({ data });

  } catch (error) {
    console.error('User preferences GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get current user
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const updates = updatePreferencesSchema.parse(body);

    // Check if preferences exist
    const { data: existingPreferences, error: fetchError } = await supabase
      .from('user_preferences')
      .select('id')
      .eq('user_id', session.user.id)
      .single();

    if (existingPreferences) {
      // Update existing preferences
      const { data, error } = await supabase
        .from('user_preferences')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', session.user.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating user preferences:', error);
        return NextResponse.json(
          { error: 'Failed to update user preferences' },
          { status: 500 }
        );
      }

      return NextResponse.json({ data, message: 'Preferences updated successfully' });
    } else {
      // Create new preferences
      const defaultPreferences = {
        user_id: session.user.id,
        cooking_skill_level: 'intermediate',
        max_cooking_time: 30,
        preferred_cuisines: [],
        excluded_ingredients: [],
        notification_preferences: {
          email: true,
          push: false,
          meal_reminders: true
        },
        privacy_settings: {
          data_sharing: false,
          analytics: true
        },
        units_preference: 'metric',
        currency_preference: 'USD',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        ...updates
      };

      const { data, error } = await supabase
        .from('user_preferences')
        .insert(defaultPreferences)
        .select()
        .single();

      if (error) {
        console.error('Error creating user preferences:', error);
        return NextResponse.json(
          { error: 'Failed to create user preferences' },
          { status: 500 }
        );
      }

      return NextResponse.json({ data, message: 'Preferences created successfully' });
    }

  } catch (error) {
    console.error('User preferences PUT error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
