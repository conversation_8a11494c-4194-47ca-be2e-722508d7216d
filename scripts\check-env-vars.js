// Script to check environment variables
require('dotenv').config(); // Load environment variables from .env file

// List of environment variables to check
const envVars = [
  'NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES',
  'NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES',
  'NEXT_PUBLIC_EDAMAM_APP_ID_FOOD_DATABASE',
  'NEXT_PUBLIC_EDAMAM_APP_KEY_FOOD_DATABASE',
  'NEXT_PUBLIC_SPOONACULAR_API_KEY',
  'NEXT_PUBLIC_OPENAI_API_KEY'
];

console.log('Checking environment variables...');
console.log('================================');

let allVarsPresent = true;

envVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    // Show first few characters of the value for verification
    const displayValue = value.substring(0, 5) + '...' + value.substring(value.length - 3);
    console.log(`✅ ${varName}: ${displayValue}`);
  } else {
    console.log(`❌ ${varName}: Not set`);
    allVarsPresent = false;
  }
});

console.log('================================');
if (allVarsPresent) {
  console.log('All environment variables are set.');
} else {
  console.log('Some environment variables are missing!');
}

// Check if the variables are accessible in the process
console.log('\nChecking process.env access...');
console.log('================================');

try {
  // Try to access the variables directly
  const recipeAppId = process.env.NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES;
  const recipeAppKey = process.env.NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES;
  
  console.log(`Direct access to NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES: ${recipeAppId ? 'Success' : 'Failed'}`);
  console.log(`Direct access to NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES: ${recipeAppKey ? 'Success' : 'Failed'}`);
  
  // Try to access the variables through a function
  function getEnvVar(name) {
    return process.env[name];
  }
  
  const foodDbAppId = getEnvVar('NEXT_PUBLIC_EDAMAM_APP_ID_FOOD_DATABASE');
  const foodDbAppKey = getEnvVar('NEXT_PUBLIC_EDAMAM_APP_KEY_FOOD_DATABASE');
  
  console.log(`Function access to NEXT_PUBLIC_EDAMAM_APP_ID_FOOD_DATABASE: ${foodDbAppId ? 'Success' : 'Failed'}`);
  console.log(`Function access to NEXT_PUBLIC_EDAMAM_APP_KEY_FOOD_DATABASE: ${foodDbAppKey ? 'Success' : 'Failed'}`);
} catch (error) {
  console.error('Error accessing environment variables:', error);
}

console.log('================================');
