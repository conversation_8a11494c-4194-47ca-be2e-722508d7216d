"use client";

import { useState, useEffect } from 'react';
import { format, addDays, subDays, startOfWeek, endOfWeek, addWeeks, subWeeks } from 'date-fns';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, LayoutGrid, List, ShoppingCart, Plus, Calendar } from 'lucide-react';
import { PlanSummary } from '@/components/meal-plan/PlanSummary';
import { MealPlanCalendarView } from '@/components/meal-plan/MealPlanCalendarView';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { useMealPlanStore } from '@/lib/stores/meal-plan-store';
import { ViewShoppingListModal } from '@/components/meal-plan/ViewShoppingListModal';
import { GenerateMealPlanModal } from '@/components/meal-plan/GenerateMealPlanModal';
import { generateShoppingList, getMealPlan, generateMealPlan } from '@/lib/meal-plan-utils';

export default function CombinedMealPlanPage() {
  const { mealPlan, isLoading, setMealPlan, setIsLoading, setUserId } = useMealPlanStore();

  // State for date navigation
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'week' | 'month'>('week');
  const [calendarLayout, setCalendarLayout] = useState<'grid' | 'list'>('grid');

  // State to force recalculation of stats
  const [statsKey, setStatsKey] = useState(0);

  // Initialize with mock data if no meal plan is loaded
  useEffect(() => {
    const initializeMealPlan = async () => {
      if (!mealPlan) {
        setIsLoading(true);
        try {
          // Set a fallback user ID
          setUserId('00000000-0000-0000-0000-000000000000');

          // Get mock meal plan data
          const mockData = await getMealPlan();
          setMealPlan(mockData);
        } catch (error) {
          console.error('Error initializing meal plan:', error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    initializeMealPlan();
  }, [mealPlan, setMealPlan, setIsLoading, setUserId]);

  // Force recalculation of stats when view mode changes
  useEffect(() => {
    // Increment the key to force a recalculation of stats
    setStatsKey(prev => prev + 1);
  }, [viewMode, currentDate]);

  // Shopping list state
  const [isShoppingListModalOpen, setIsShoppingListModalOpen] = useState(false);
  const [shoppingList, setShoppingList] = useState<any[]>([]);
  const [pantryItems, setPantryItems] = useState<any[]>([]);

  // Generate meal plan state
  const [isGeneratePlanModalOpen, setIsGeneratePlanModalOpen] = useState(false);
  const [isGeneratingPlan, setIsGeneratingPlan] = useState(false);
  const [generatedPlans, setGeneratedPlans] = useState<any[]>([]);

  // Handle generate meal plan
  const handleGeneratePlan = async (options: any) => {
    try {
      setIsGeneratingPlan(true);

      // Generate meal plan
      const newPlan = await generateMealPlan(options);

      // Add to generated plans
      const planWithMetadata = {
        id: `plan-${Date.now()}`,
        name: options.name,
        createdAt: new Date(),
        options,
        plan: newPlan,
        tags: options.dietaryPreferences || []
      };

      setGeneratedPlans(prev => [planWithMetadata, ...prev]);
      setIsGeneratePlanModalOpen(false);
      toast.success('Meal plan generated successfully!');
    } catch (error) {
      console.error('Error generating meal plan:', error);
      toast.error('Failed to generate meal plan');
    } finally {
      setIsGeneratingPlan(false);
    }
  };

  // Handle use generated plan
  const handleUsePlan = (planId: string) => {
    const plan = generatedPlans.find(p => p.id === planId);
    if (plan) {
      setMealPlan(plan.plan);
      toast.success(`Using meal plan: ${plan.name}`);
    }
  };

  // Calculate date range
  const startDate = startOfWeek(currentDate, { weekStartsOn: 1 });
  const endDate = endOfWeek(currentDate, { weekStartsOn: 1 });

  // Navigation functions
  const goToPreviousWeek = () => {
    setCurrentDate(prevDate => subWeeks(prevDate, 1));
  };

  const goToNextWeek = () => {
    setCurrentDate(prevDate => addWeeks(prevDate, 1));
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  // Handle view shopping list
  const handleViewShoppingList = async () => {
    try {
      // Generate shopping list based on the current meal plan
      const { shoppingList: newShoppingList, pantryItems: newPantryItems } = await generateShoppingList(mealPlan || undefined);
      setShoppingList(newShoppingList);
      setPantryItems(newPantryItems);
      setIsShoppingListModalOpen(true);
    } catch (error) {
      console.error('Error generating shopping list:', error);
    }
  };

  // Calculate plan stats based on view mode (weekly or monthly)
  const calculatePlanStats = () => {
    if (!mealPlan) {
      return {
        totalCost: 0,
        averageCalories: 0,
        macros: { protein: 30, carbs: 40, fats: 30 }, // Default values
        mealCount: 0,
        daysWithMeals: 0,
        totalDays: viewMode === 'week' ? 7 : 30
      };
    }

    let totalCost = 0;
    let totalCalories = 0;
    let mealCount = 0;
    let totalProtein = 0;
    let totalCarbs = 0;
    let totalFats = 0;
    const daysWithMealsSet = new Set();

    // Process all days in the meal plan
    Object.entries(mealPlan).forEach(([dateStr, dayMeals]) => {
      // For weekly view, only include days in the current week
      if (viewMode === 'week') {
        const date = new Date(dateStr);
        const weekStart = startOfWeek(currentDate, { weekStartsOn: 1 });
        const weekEnd = endOfWeek(currentDate, { weekStartsOn: 1 });

        // Skip if the date is not in the current week
        if (date < weekStart || date > weekEnd) {
          return;
        }
      }

      // If we have any meals for this day, count it as a day with meals
      if (Object.keys(dayMeals).length > 0) {
        daysWithMealsSet.add(dateStr);
      }

      // Process each meal in the day
      Object.values(dayMeals).forEach(meal => {
        totalCost += meal.cost || 0;
        totalCalories += meal.calories || 0;
        totalProtein += meal.nutrition?.protein || 0;
        totalCarbs += meal.nutrition?.carbs || 0;
        totalFats += meal.nutrition?.fat || 0;
        mealCount++;
      });
    });

    const averageCalories = mealCount > 0 ? Math.round(totalCalories / mealCount) : 0;

    // Calculate macro percentages
    const totalMacros = totalProtein + totalCarbs + totalFats;
    const macros = {
      protein: totalMacros > 0 ? Math.round((totalProtein / totalMacros) * 100) : 30,
      carbs: totalMacros > 0 ? Math.round((totalCarbs / totalMacros) * 100) : 40,
      fats: totalMacros > 0 ? Math.round((totalFats / totalMacros) * 100) : 30
    };

    // Total days depends on the view mode
    const totalDays = viewMode === 'week' ? 7 : 30;

    return {
      totalCost: parseFloat(totalCost.toFixed(2)),
      averageCalories,
      macros,
      mealCount,
      daysWithMeals: daysWithMealsSet.size,
      totalDays
    };
  };

  const planStats = calculatePlanStats();

  return (
    <div className="container py-6 space-y-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">{viewMode === 'week' ? 'Weekly' : 'Monthly'} Meal Plan</h1>
      </div>

      {/* Plan Summary Section - Changes based on view mode */}
      <div className="border rounded-lg p-6 bg-white">
        <PlanSummary
          totalCost={planStats.totalCost}
          averageCalories={planStats.averageCalories}
          macros={planStats.macros}
          mealCount={planStats.mealCount}
          daysWithMeals={planStats.daysWithMeals}
          totalDays={planStats.totalDays}
          viewMode={viewMode}
          onGenerateShoppingList={handleViewShoppingList}
        />
      </div>

      {/* Meal Plan Cards Section */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Generated Meal Plans Card - 25% width */}
        <div className="border rounded-lg p-6 bg-white md:col-span-1">
          <div className="flex flex-col space-y-2 mb-4">
            <h2 className="text-lg font-semibold">Generated Meal Plans</h2>
            <Button
              variant="outline"
              size="sm"
              className="w-full text-xs py-1 h-8"
              onClick={() => setIsGeneratePlanModalOpen(true)}
            >
              <Plus className="h-3 w-3 mr-1" />
              Generate New
            </Button>
          </div>

          <div className="space-y-3 max-h-[300px] overflow-y-auto pr-2">
            {generatedPlans.length > 0 ? (
              generatedPlans.map((plan) => (
                <div
                  key={plan.id}
                  className="border rounded-md p-2 hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer"
                >
                  <div className="mb-2">
                    <h3 className="font-medium text-sm truncate">{plan.name}</h3>
                    <p className="text-xs text-muted-foreground">Generated on {format(plan.createdAt, 'MMM d')}</p>
                    <div className="flex flex-wrap items-center gap-1 mt-1">
                      {plan.tags.slice(0, 2).map((tag: string) => (
                        <Badge
                          key={tag}
                          variant="outline"
                          className="text-[10px] px-1 py-0 bg-blue-50 text-blue-700 border-blue-200"
                        >
                          {tag.charAt(0).toUpperCase() + tag.slice(1)}
                        </Badge>
                      ))}
                      {plan.tags.length === 0 && (
                        <Badge variant="outline" className="text-[10px] px-1 py-0 bg-green-50 text-green-700 border-green-200">
                          Standard
                        </Badge>
                      )}
                      {plan.tags.length > 2 && (
                        <Badge variant="outline" className="text-[10px] px-1 py-0">
                          +{plan.tags.length - 2} more
                        </Badge>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 text-[10px] w-full"
                    onClick={() => handleUsePlan(plan.id)}
                  >
                    <Calendar className="h-3 w-3 mr-1" />
                    Use Plan
                  </Button>
                </div>
              ))
            ) : (
              // Sample meal plans when no generated plans exist
              [1, 2, 3].map((plan) => (
                <div
                  key={`sample-plan-${plan}`}
                  className="border rounded-md p-2 hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer"
                >
                  <div className="mb-2">
                    <h3 className="font-medium text-sm truncate">Weekly Meal Plan {plan}</h3>
                    <p className="text-xs text-muted-foreground">Generated on {format(subDays(new Date(), plan), 'MMM d')}</p>
                    <div className="flex flex-wrap items-center gap-1 mt-1">
                      <Badge variant="outline" className="text-[10px] px-1 py-0 bg-green-50 text-green-700 border-green-200">
                        Low Carb
                      </Badge>
                      <Badge variant="outline" className="text-[10px] px-1 py-0 bg-blue-50 text-blue-700 border-blue-200">
                        High Protein
                      </Badge>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 text-[10px] w-full"
                    onClick={() => setIsGeneratePlanModalOpen(true)}
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Generate Plan
                  </Button>
                </div>
              ))
            )}
          </div>
        </div>

        {/* My Meal Plan Card - 75% width */}
        <div className="border rounded-lg p-6 bg-white md:col-span-3">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <h2 className="text-xl font-semibold">My Meal Plan</h2>
              <div className="ml-2">
                <Tabs
                  defaultValue={viewMode}
                  onValueChange={(value) => setViewMode(value as 'week' | 'month')}
                  className="w-[200px]"
                >
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="week">Week</TabsTrigger>
                    <TabsTrigger value="month">Month</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </div>
          </div>

          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
            <div className="text-sm font-medium bg-gray-50 px-3 py-1.5 rounded-md border border-gray-100">
              {viewMode === 'week' ? (
                <>
                  <span className="font-semibold">{format(startDate, 'MMM d')} - {format(endDate, 'MMM d, yyyy')}</span>
                </>
              ) : (
                <>
                  <span className="font-semibold">{format(currentDate, 'MMMM yyyy')}</span>
                </>
              )}
            </div>
            <div className="flex items-center gap-2">
              <div className="flex border rounded-md overflow-hidden">
                <Button variant="ghost" size="icon" className="rounded-none border-0" onClick={goToPreviousWeek}>
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" className="rounded-none border-0 border-x px-3" onClick={goToToday}>
                  Today
                </Button>
                <Button variant="ghost" size="icon" className="rounded-none border-0" onClick={goToNextWeek}>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              {viewMode === 'week' && (
                <div className="ml-2">
                  <Tabs
                    defaultValue={calendarLayout}
                    onValueChange={(value) => setCalendarLayout(value as 'grid' | 'list')}
                  >
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="grid" className="px-2">
                        <LayoutGrid className="h-4 w-4" />
                      </TabsTrigger>
                      <TabsTrigger value="list" className="px-2">
                        <List className="h-4 w-4" />
                      </TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
              )}
            </div>
          </div>

          {/* Calendar View */}
          <div className="border rounded-lg overflow-hidden">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Loading meal plan...</p>
                </div>
              </div>
            ) : (
              <MealPlanCalendarView
                currentDate={currentDate}
                viewMode={viewMode}
                layout={calendarLayout}
                mealPlan={mealPlan}
              />
            )}
          </div>
        </div>
      </div>

      {/* Shopping List Modal */}
      <ViewShoppingListModal
        isOpen={isShoppingListModalOpen}
        onClose={() => setIsShoppingListModalOpen(false)}
        shoppingList={shoppingList}
        pantryItems={pantryItems}
        newMealPlan={mealPlan}
      />

      {/* Generate Meal Plan Modal */}
      <GenerateMealPlanModal
        isOpen={isGeneratePlanModalOpen}
        onClose={() => setIsGeneratePlanModalOpen(false)}
        onGenerate={handleGeneratePlan}
        isGenerating={isGeneratingPlan}
      />
    </div>
  );
}
