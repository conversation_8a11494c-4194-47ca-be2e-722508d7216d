"use client";

import { MealGenerationOptions, MealPlan } from '@/types/new-meal-plan';
import { generateMealPlanWithEdamam } from '@/lib/api/edamam';
import { addCostInfoToMealPlan } from '@/lib/api/spoonacular';
import { DEFAULT_RECIPES } from '@/lib/mock-data/default-recipes';
import { toast } from 'sonner';

/**
 * Generate a meal plan using the EdaSpoon approach (Edamam + Spoonacular)
 */
export async function generateMealPlanWithEdaSpoon(options: MealGenerationOptions): Promise<MealPlan> {
  try {
    // Step 1: Generate a meal plan using Edamam API
    console.log('Step 1: Generating meal plan with Edamam API...');
    console.log('Edamam API Keys:', {
      APP_ID: process.env.NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES,
      APP_KEY: process.env.NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES
    });

    // Check if we have valid API keys
    if (!process.env.NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES || !process.env.NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES) {
      console.error('Missing Edamam API keys');
      toast.error('Missing Edamam API keys. Using default recipes as fallback.');
      return generateDefaultMealPlan(options);
    }

    try {
      const mealPlanWithoutCost = await generateMealPlanWithEdamam(options);

      // Step 2: Add cost information using Spoonacular API
      console.log('Step 2: Adding cost information with Spoonacular API...');
      const mealPlanWithCost = await addCostInfoToMealPlan(mealPlanWithoutCost);

      return mealPlanWithCost;
    } catch (edamamError: any) {
      console.error('Error with Edamam API:', edamamError);

      // Check for specific error types
      if (edamamError.response) {
        console.error('Edamam API response error:', {
          status: edamamError.response.status,
          statusText: edamamError.response.statusText,
          data: edamamError.response.data
        });

        // Handle 402 Payment Required error
        if (edamamError.response.status === 402) {
          toast.error('Edamam API rate limit exceeded. Using default recipes as fallback.');
        } else {
          toast.error(`Edamam API error (${edamamError.response.status}): Using default recipes as fallback.`);
        }
      } else {
        toast.error(`Edamam API error: ${edamamError.message}. Using default recipes as fallback.`);
      }

      return generateDefaultMealPlan(options);
    }
  } catch (error: any) {
    console.error('Error generating meal plan with EdaSpoon approach:', error);

    // Fallback to default recipes
    console.log('Falling back to default recipes...');
    toast.error(`API error: ${error.message}. Using default recipes as fallback.`);

    return generateDefaultMealPlan(options);
  }
}

/**
 * Generate a default meal plan using the default recipes
 */
function generateDefaultMealPlan(options: MealGenerationOptions): MealPlan {
  const mealPlan: MealPlan = {};
  const startDate = new Date();

  // Generate a meal plan for each day
  for (let i = 0; i < options.days; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];

    mealPlan[dateStr] = {};

    // Add breakfast
    const breakfastIndex = i % DEFAULT_RECIPES.breakfast.length;
    mealPlan[dateStr]['breakfast'] = DEFAULT_RECIPES.breakfast[breakfastIndex];

    // Add lunch
    const lunchIndex = i % DEFAULT_RECIPES.lunch.length;
    mealPlan[dateStr]['lunch'] = DEFAULT_RECIPES.lunch[lunchIndex];

    // Add dinner
    const dinnerIndex = i % DEFAULT_RECIPES.dinner.length;
    mealPlan[dateStr]['dinner'] = DEFAULT_RECIPES.dinner[dinnerIndex];
  }

  return mealPlan;
}

/**
 * Generate a single meal using the EdaSpoon approach
 */
export async function generateMealWithEdaSpoon(options: {
  mealType: string;
  calories: number;
  dietaryPreferences: string[];
  excludeIngredients: string[];
}): Promise<any> {
  try {
    // Check if we have valid API keys
    if (!process.env.NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES || !process.env.NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES) {
      console.error('Missing Edamam API keys');
      toast.error('Missing Edamam API keys. Using default recipe as fallback.');
      return getDefaultRecipe(options.mealType);
    }

    // Calculate target calories for this meal
    let targetCalories = options.calories;
    if (options.mealType === 'breakfast') {
      targetCalories = Math.round(options.calories * 0.25);
    } else if (options.mealType === 'lunch') {
      targetCalories = Math.round(options.calories * 0.35);
    } else if (options.mealType === 'dinner') {
      targetCalories = Math.round(options.calories * 0.4);
    }

    console.log(`Searching for ${options.mealType} recipes with calories ${targetCalories - 100}-${targetCalories + 100}`);

    // Search for recipes using Edamam API
    const searchUrl = `/api/edamam/search?query=&mealType=${options.mealType}&calories=${targetCalories - 100}-${targetCalories + 100}&diet=${options.dietaryPreferences.join(',')}&excluded=${options.excludeIngredients.join(',')}&random=true`;
    console.log('Search URL:', searchUrl);

    const searchResponse = await fetch(searchUrl);

    // Check if the response is OK
    if (!searchResponse.ok) {
      const errorText = await searchResponse.text();
      console.error(`Edamam API error (${searchResponse.status}):`, errorText);

      if (searchResponse.status === 402) {
        toast.error('Edamam API rate limit exceeded. Using default recipe as fallback.');
      } else {
        toast.error(`Edamam API error (${searchResponse.status}): Using default recipe as fallback.`);
      }

      return getDefaultRecipe(options.mealType);
    }

    const searchResults = await searchResponse.json();
    console.log('Search results:', searchResults);

    // Get a random recipe from the results
    const hits = searchResults.hits || [];
    if (hits.length > 0) {
      console.log(`Found ${hits.length} recipes`);
      const randomIndex = Math.floor(Math.random() * hits.length);
      const recipe = hits[randomIndex].recipe;
      console.log('Selected recipe:', recipe.label);

      // Transform the recipe to our internal format
      const transformedRecipe = {
        id: recipe.uri.split('#recipe_')[1],
        name: recipe.label,
        image: recipe.image,
        ingredients: recipe.ingredients.map((ingredient: any) => ({
          name: ingredient.food,
          amount: ingredient.quantity,
          unit: ingredient.measure || 'unit'
        })),
        instructions: recipe.ingredientLines || [],
        cost: 0, // To be filled by Spoonacular
        calories: Math.round(recipe.calories / recipe.yield),
        prepTime: 0, // Edamam doesn't provide prep time
        cookTime: recipe.totalTime || 0,
        macros: {
          protein: Math.round(recipe.totalNutrients.PROCNT?.quantity / recipe.yield) || 0,
          carbs: Math.round(recipe.totalNutrients.CHOCDF?.quantity / recipe.yield) || 0,
          fat: Math.round(recipe.totalNutrients.FAT?.quantity / recipe.yield) || 0
        },
        nutrition: {
          protein: Math.round(recipe.totalNutrients.PROCNT?.quantity / recipe.yield) || 0,
          carbs: Math.round(recipe.totalNutrients.CHOCDF?.quantity / recipe.yield) || 0,
          fat: Math.round(recipe.totalNutrients.FAT?.quantity / recipe.yield) || 0
        },
        status: null
      };

      try {
        // Add cost information using Spoonacular API
        console.log('Adding cost information with Spoonacular API...');
        const costResponse = await fetch(`/api/spoonacular/estimate-cost`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            ingredients: transformedRecipe.ingredients
          })
        });

        if (!costResponse.ok) {
          console.error(`Spoonacular API error (${costResponse.status})`);
          transformedRecipe.cost = 5.99; // Default cost
        } else {
          const cost = await costResponse.json();
          transformedRecipe.cost = cost;
        }
      } catch (costError) {
        console.error('Error getting cost information:', costError);
        transformedRecipe.cost = 5.99; // Default cost
      }

      return transformedRecipe;
    } else {
      console.log('No recipes found in search results');
      toast.error('No recipes found. Using default recipe as fallback.');
      return getDefaultRecipe(options.mealType);
    }
  } catch (error: any) {
    console.error('Error generating meal with EdaSpoon approach:', error);

    // Fallback to default recipes
    console.log('Falling back to default recipes...');
    toast.error(`API error: ${error.message}. Using default recipe as fallback.`);

    return getDefaultRecipe(options.mealType);
  }
}

/**
 * Get a default recipe based on the meal type
 */
function getDefaultRecipe(mealType: string): any {
  if (mealType === 'breakfast') {
    return DEFAULT_RECIPES.breakfast[0];
  } else if (mealType === 'lunch') {
    return DEFAULT_RECIPES.lunch[0];
  } else if (mealType === 'dinner') {
    return DEFAULT_RECIPES.dinner[0];
  } else {
    // If meal type is not recognized, return a breakfast recipe as default
    return DEFAULT_RECIPES.breakfast[0];
  }
}
