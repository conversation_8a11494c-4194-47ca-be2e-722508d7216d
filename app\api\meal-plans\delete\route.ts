import { NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { createClient } from '@supabase/supabase-js';
import { cookies } from "next/headers";
import type { Database } from "@/types/supabase";

// POST handler to delete a meal plan
export async function POST(request: Request) {
  try {
    // Parse the request body to get the meal plan ID
    let id;
    try {
      const body = await request.json();
      id = body.id;

      if (!id) {
        console.error('Missing ID in request body');
        return NextResponse.json(
          { error: "Missing ID in request body" },
          { status: 400 }
        );
      }
    } catch (parseError) {
      console.error('Error parsing request body:', parseError);
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      );
    }

    console.log('Delete meal plan API called with ID:', id);

    // Get the authenticated user - properly await cookies
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

    const { data: { session }, error: authError } = await supabase.auth.getSession();

    if (authError || !session) {
      console.error('Authentication error:', authError);
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    console.log('Authenticated user ID:', userId);

    // First check if the meal plan exists and belongs to the user
    const { data: mealPlan, error: fetchError } = await supabase
      .from('meal_plans')
      .select('user_id')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error('Error fetching meal plan:', fetchError);
      return NextResponse.json(
        { error: fetchError.message || "Meal plan not found" },
        { status: 404 }
      );
    }

    // Verify ownership
    if (mealPlan.user_id !== userId) {
      console.error('Unauthorized access attempt. Plan belongs to:', mealPlan.user_id, 'Request from:', userId);
      return NextResponse.json(
        { error: "You don't have permission to delete this meal plan" },
        { status: 403 }
      );
    }

    // Now use the admin client to delete the meal plan (bypassing RLS)
    const supabaseAdmin = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    console.log('Attempting to delete meal plan with ID:', id);

    // We've already verified ownership with the regular client, now delete with admin client
    console.log('Using admin client to delete the meal plan');

    // Try different approaches to delete the meal plan
    let error;

    // Approach 1: Use raw SQL query
    try {
      const { error: sqlError } = await supabaseAdmin.rpc(
        'execute_sql',
        { query: `DELETE FROM meal_plans WHERE id = '${id}'` }
      );

      if (!sqlError) {
        console.log('Successfully deleted meal plan using raw SQL');
        error = null;
      } else {
        console.error('Error using raw SQL:', sqlError);
        error = sqlError;

        // Approach 2: Try direct delete
        const { error: deleteError } = await supabaseAdmin
          .from('meal_plans')
          .delete()
          .eq('id', id);

        if (!deleteError) {
          console.log('Successfully deleted meal plan using direct delete');
          error = null;
        } else {
          console.error('Error using direct delete:', deleteError);
          error = deleteError;
        }
      }
    } catch (e) {
      console.error('Error in delete approaches:', e);
      error = e;
    }

    if (error) {
      console.error("Error deleting meal plan:", error);
      return NextResponse.json(
        { error: error.message || "Failed to delete meal plan" },
        { status: 500 }
      );
    }

    console.log('Meal plan deleted successfully');
    return NextResponse.json(
      { message: "Meal plan deleted successfully" },
      { status: 200 }
    );
  } catch (error: any) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}
