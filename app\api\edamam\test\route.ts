import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { searchRecipesServer } from '@/lib/api/edamam-server';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query') || '';
    const diet = searchParams.get('diet')?.split(',').filter(Boolean) || [];
    const calories = searchParams.get('calories') || '';
    const mealType = searchParams.get('mealType') || '';
    const excluded = searchParams.get('excluded')?.split(',').filter(Boolean) || [];
    const direct = searchParams.get('direct') === 'true';

    console.log('Edamam test API route called with params:', {
      query,
      diet,
      calories,
      mealType,
      excluded,
      direct
    });

    // Get API keys from environment variables
    const appId = process.env.NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES;
    const appKey = process.env.NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES;

    // Check if we have valid API keys
    if (!appId || !appKey) {
      console.error('Missing Edamam API keys');
      return NextResponse.json(
        { error: 'Missing Edamam API keys', hits: [] },
        { status: 500 }
      );
    }

    // Construct the URL for the Edamam API
    const baseUrl = 'https://api.edamam.com/api/recipes/v2';
    const queryParams = new URLSearchParams({
      type: 'public',
      app_id: appId,
      app_key: appKey,
      q: query || mealType || 'meal' // Use query, mealType, or 'meal' as fallback
    });

    // Add optional parameters
    if (diet.length > 0) {
      diet.forEach(d => queryParams.append('diet', d));
    }

    if (calories) {
      queryParams.append('calories', calories);
    }

    if (mealType) {
      queryParams.append('mealType', mealType);
    }

    if (excluded.length > 0) {
      excluded.forEach(e => queryParams.append('excluded', e));
    }

    const url = `${baseUrl}?${queryParams.toString()}`;
    console.log('Edamam API request URL:', url);

    try {
      // Use the server-side function to make the request
      const results = await searchRecipesServer({
        query: query || mealType || 'meal',
        diet,
        health: [],
        calories,
        mealType: mealType ? [mealType] : [],
        excluded,
        random: false
      });

      console.log('Edamam API response received');

      // Return the response data
      return NextResponse.json(results);
    } catch (apiError: any) {
      console.error('Error calling Edamam API:', apiError.message);

      // If we have a response from the API, return that status code and data
      if (apiError.response) {
        console.error('Response status:', apiError.response.status);
        console.error('Response data:', apiError.response.data);

        return NextResponse.json(
          {
            error: apiError.message,
            status: apiError.response.status,
            data: apiError.response.data,
            hits: []
          },
          { status: apiError.response.status }
        );
      }

      // Otherwise, return a generic 500 error
      return NextResponse.json(
        { error: apiError.message || 'An error occurred while calling the Edamam API', hits: [] },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Error in Edamam test API route:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred in the test API route', hits: [] },
      { status: 500 }
    );
  }
}
