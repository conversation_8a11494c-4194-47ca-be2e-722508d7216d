-- Add any missing columns safely
ALTER TABLE "public"."meal_plans" 
ADD COLUMN IF NOT EXISTS "meal_data" JSONB,
ADD COLUMN IF NOT EXISTS "status" TEXT DEFAULT 'active';

-- Add indexes if they don't exist
CREATE INDEX IF NOT EXISTS "meal_plans_status_idx" ON "public"."meal_plans"("status");
CREATE INDEX IF NOT EXISTS "meal_plans_user_id_idx" ON "public"."meal_plans"("user_id");

-- Add any missing foreign key constraints safely
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints 
        WHERE constraint_name = 'meals_recipe_id_fkey'
    ) THEN
        ALTER TABLE "public"."meals" 
        ADD CONSTRAINT "meals_recipe_id_fkey" 
        FOREIGN KEY ("recipe_id") 
        REFERENCES "public"."recipes"("id") 
        ON DELETE SET NULL;
    END IF;
END $$;