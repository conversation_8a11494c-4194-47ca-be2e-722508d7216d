"use client";

import axios from 'axios';

// Spoonacular API configuration
const SPOONACULAR_API_KEY = process.env.NEXT_PUBLIC_SPOONACULAR_API_KEY;
const SPOONACULAR_BASE_URL = 'https://api.spoonacular.com';

// Cache for API responses to reduce API calls
const responseCache = new Map<string, any>();

/**
 * Get price breakdown for a recipe using the Spoonacular API
 */
export async function getRecipePriceBreakdown(recipeId: number): Promise<any> {
  try {
    // Create a cache key based on the recipe ID
    const cacheKey = `price-${recipeId}`;
    
    // Check if we have a cached response
    if (responseCache.has(cacheKey)) {
      console.log('Using cached price breakdown');
      return responseCache.get(cacheKey);
    }
    
    console.log(`Getting price breakdown for recipe ${recipeId} from Spoonacular API...`);
    
    // Make the API request
    const response = await axios.get(
      `${SPOONACULAR_BASE_URL}/recipes/${recipeId}/priceBreakdownWidget.json`,
      {
        params: {
          apiKey: SPOONACULAR_API_KEY
        }
      }
    );
    
    // Cache the response
    responseCache.set(cacheKey, response.data);
    
    return response.data;
  } catch (error) {
    console.error(`Error getting price breakdown for recipe ${recipeId} from Spoonacular API:`, error);
    throw error;
  }
}

/**
 * Get information about an ingredient using the Spoonacular API
 */
export async function getIngredientInformation(
  ingredientId: number,
  amount: number,
  unit: string
): Promise<any> {
  try {
    // Create a cache key based on the ingredient ID, amount, and unit
    const cacheKey = `ingredient-${ingredientId}-${amount}-${unit}`;
    
    // Check if we have a cached response
    if (responseCache.has(cacheKey)) {
      console.log('Using cached ingredient information');
      return responseCache.get(cacheKey);
    }
    
    console.log(`Getting information for ingredient ${ingredientId} from Spoonacular API...`);
    
    // Make the API request
    const response = await axios.get(
      `${SPOONACULAR_BASE_URL}/food/ingredients/${ingredientId}/information`,
      {
        params: {
          apiKey: SPOONACULAR_API_KEY,
          amount,
          unit
        }
      }
    );
    
    // Cache the response
    responseCache.set(cacheKey, response.data);
    
    return response.data;
  } catch (error) {
    console.error(`Error getting information for ingredient ${ingredientId} from Spoonacular API:`, error);
    throw error;
  }
}

/**
 * Search for ingredients using the Spoonacular API
 */
export async function searchIngredients(query: string): Promise<any> {
  try {
    // Create a cache key based on the query
    const cacheKey = `ingredient-search-${query}`;
    
    // Check if we have a cached response
    if (responseCache.has(cacheKey)) {
      console.log('Using cached ingredient search results');
      return responseCache.get(cacheKey);
    }
    
    console.log(`Searching for ingredients with query "${query}" using Spoonacular API...`);
    
    // Make the API request
    const response = await axios.get(
      `${SPOONACULAR_BASE_URL}/food/ingredients/search`,
      {
        params: {
          apiKey: SPOONACULAR_API_KEY,
          query,
          number: 10
        }
      }
    );
    
    // Cache the response
    responseCache.set(cacheKey, response.data);
    
    return response.data;
  } catch (error) {
    console.error(`Error searching for ingredients with query "${query}" using Spoonacular API:`, error);
    throw error;
  }
}

/**
 * Estimate the cost of a recipe using the Spoonacular API
 */
export async function estimateRecipeCost(ingredients: { name: string; amount: number; unit: string }[]): Promise<number> {
  try {
    let totalCost = 0;
    
    // Process each ingredient
    for (const ingredient of ingredients) {
      // Search for the ingredient
      const searchResults = await searchIngredients(ingredient.name);
      
      // Get the first result
      const ingredientId = searchResults.results[0]?.id;
      
      if (ingredientId) {
        // Get information about the ingredient
        const ingredientInfo = await getIngredientInformation(
          ingredientId,
          ingredient.amount,
          ingredient.unit
        );
        
        // Add the cost to the total
        totalCost += ingredientInfo.estimatedCost?.value || 0;
      }
    }
    
    return totalCost / 100; // Convert cents to dollars
  } catch (error) {
    console.error('Error estimating recipe cost with Spoonacular API:', error);
    throw error;
  }
}

/**
 * Add cost information to a meal plan using the Spoonacular API
 */
export async function addCostInfoToMealPlan(mealPlan: any): Promise<any> {
  try {
    const updatedMealPlan = { ...mealPlan };
    
    // Process each day in the meal plan
    for (const dateStr in updatedMealPlan) {
      // Process each meal in the day
      for (const mealType in updatedMealPlan[dateStr]) {
        const meal = updatedMealPlan[dateStr][mealType];
        
        // Estimate the cost of the meal
        const cost = await estimateRecipeCost(meal.ingredients);
        
        // Update the meal with the cost information
        updatedMealPlan[dateStr][mealType] = {
          ...meal,
          cost
        };
      }
    }
    
    return updatedMealPlan;
  } catch (error) {
    console.error('Error adding cost information to meal plan with Spoonacular API:', error);
    throw error;
  }
}
