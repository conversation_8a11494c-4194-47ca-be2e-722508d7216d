"use client";

import { useState } from 'react';
import { Heart, Plus, Clock, Users, Search } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { FavoriteButton } from '@/components/ui/favorite-button';
import { useFavorites } from '@/app/hooks/useFavorites';
import Image from 'next/image';
import Link from 'next/link';

interface FavoriteMealsProps {
  onSelectMeal?: (meal: any) => void;
  showAddButton?: boolean;
}

export function FavoriteMeals({ onSelectMeal, showAddButton = false }: FavoriteMealsProps) {
  const { favorites, isLoading, error } = useFavorites();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCuisine, setSelectedCuisine] = useState<string>('');

  // Filter favorites based on search and cuisine
  const filteredFavorites = favorites.filter(favorite => {
    const recipe = favorite.recipes;
    if (!recipe) return false;

    const matchesSearch = recipe.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         recipe.description?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCuisine = !selectedCuisine ||
                          recipe.tags?.includes(selectedCuisine);

    return matchesSearch && matchesCuisine;
  });

  // Get unique cuisines from favorites
  const cuisines = Array.from(new Set(
    favorites.flatMap(fav => fav.recipes?.tags || [])
  )).filter(Boolean);

  const handleMealSelect = (recipe: any) => {
    if (onSelectMeal) {
      // Convert recipe to meal format for compatibility
      const meal = {
        id: recipe.id,
        name: recipe.name,
        description: recipe.description,
        prepTime: recipe.prep_time,
        cookTime: recipe.cook_time,
        servings: recipe.servings,
        nutrition: {
          calories: recipe.calories_per_serving,
          protein: recipe.protein_per_serving,
          carbs: recipe.carbs_per_serving,
          fat: recipe.fat_per_serving,
        },
        tags: recipe.tags,
        imageUrl: recipe.image_url,
        ingredients: recipe.ingredients,
        instructions: recipe.instructions,
      };
      onSelectMeal(meal);
    }
  };

  if (isLoading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">Loading your favorites...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Favorites</h3>
        <p className="text-gray-600">{error}</p>
      </div>
    );
  }

  if (favorites.length === 0) {
    return (
      <div className="text-center py-12">
        <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Favorite Recipes Yet</h3>
        <p className="text-gray-600 mb-6">
          Start exploring recipes and add them to your favorites!
        </p>
        <Link href="/recipes">
          <Button>
            Browse Recipes
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold flex items-center">
          <Heart className="h-5 w-5 text-red-500 mr-2" />
          Favorite Recipes ({favorites.length})
        </h2>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search your favorite recipes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {cuisines.length > 0 && (
          <div className="flex gap-2 flex-wrap">
            <Button
              variant={selectedCuisine === '' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCuisine('')}
            >
              All
            </Button>
            {cuisines.slice(0, 4).map(cuisine => (
              <Button
                key={cuisine}
                variant={selectedCuisine === cuisine ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCuisine(cuisine)}
              >
                {cuisine}
              </Button>
            ))}
          </div>
        )}
      </div>

      {filteredFavorites.length === 0 ? (
        <div className="text-center py-8">
          <Search className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-600">No recipes match your current search.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredFavorites.map((favorite) => {
            const recipe = favorite.recipes;
            if (!recipe) return null;

            return (
              <Card key={favorite.id} className="hover:shadow-md transition-shadow">
                <div className="relative">
                  {recipe.image_url && (
                    <div className="relative h-32 w-full">
                      <Image
                        src={recipe.image_url}
                        alt={recipe.name}
                        fill
                        className="object-cover rounded-t-lg"
                      />
                    </div>
                  )}
                  <div className="absolute top-2 right-2">
                    <FavoriteButton recipeId={recipe.id} size="sm" />
                  </div>
                </div>

                <CardContent className="p-4">
                  <h3 className="font-semibold text-lg line-clamp-2 mb-2">{recipe.name}</h3>

                  {recipe.description && (
                    <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                      {recipe.description}
                    </p>
                  )}

                  <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                    {recipe.prep_time && (
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {recipe.prep_time + (recipe.cook_time || 0)}m
                      </div>
                    )}
                    {recipe.servings && (
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        {recipe.servings}
                      </div>
                    )}
                    {recipe.calories_per_serving && (
                      <div>
                        {recipe.calories_per_serving} cal
                      </div>
                    )}
                  </div>

                  {recipe.tags && recipe.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mb-3">
                      {recipe.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}

                  <div className="flex gap-2">
                    <Button
                      onClick={() => handleMealSelect(recipe)}
                      className="flex-1"
                      size="sm"
                    >
                      {showAddButton ? (
                        <>
                          <Plus className="h-4 w-4 mr-1" />
                          Add to Plan
                        </>
                      ) : (
                        'View Recipe'
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}
