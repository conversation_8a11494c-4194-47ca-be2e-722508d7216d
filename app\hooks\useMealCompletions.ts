import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { MealCompletion, UpdateMealCompletionRequest } from '@/types/database-extended';

export function useMealCompletions(mealPlanId?: string, status?: string) {
  const queryClient = useQueryClient();

  const {
    data: completions,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['meal-completions', mealPlanId, status],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (mealPlanId) params.append('meal_plan_id', mealPlanId);
      if (status) params.append('status', status);
      
      const url = `/api/meal-completions${params.toString() ? `?${params.toString()}` : ''}`;
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Failed to fetch meal completions');
      }
      const result = await response.json();
      return result.data as MealCompletion[];
    },
    staleTime: 1 * 60 * 1000, // 1 minute
  });

  const updateCompletionMutation = useMutation({
    mutationFn: async ({ meal_plan_id, meal_id, status }: UpdateMealCompletionRequest) => {
      const response = await fetch('/api/meal-completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          meal_plan_id,
          meal_id,
          status,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update meal completion');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['meal-completions'] });
    },
  });

  const markMealCompleted = (mealPlanId: string, mealId: string) => {
    return updateCompletionMutation.mutateAsync({
      meal_plan_id: mealPlanId,
      meal_id: mealId,
      status: 'completed'
    });
  };

  const markMealSkipped = (mealPlanId: string, mealId: string) => {
    return updateCompletionMutation.mutateAsync({
      meal_plan_id: mealPlanId,
      meal_id: mealId,
      status: 'skipped'
    });
  };

  const markMealPending = (mealPlanId: string, mealId: string) => {
    return updateCompletionMutation.mutateAsync({
      meal_plan_id: mealPlanId,
      meal_id: mealId,
      status: 'pending'
    });
  };

  const getMealCompletion = (mealPlanId: string, mealId: string) => {
    return completions?.find(
      completion => completion.meal_plan_id === mealPlanId && completion.meal_id === mealId
    );
  };

  const getMealStatus = (mealPlanId: string, mealId: string): 'completed' | 'skipped' | 'pending' => {
    const completion = getMealCompletion(mealPlanId, mealId);
    return completion?.status || 'pending';
  };

  const isMealCompleted = (mealPlanId: string, mealId: string) => {
    return getMealStatus(mealPlanId, mealId) === 'completed';
  };

  const isMealSkipped = (mealPlanId: string, mealId: string) => {
    return getMealStatus(mealPlanId, mealId) === 'skipped';
  };

  const getCompletionStats = (mealPlanId?: string) => {
    const relevantCompletions = mealPlanId 
      ? completions?.filter(c => c.meal_plan_id === mealPlanId) || []
      : completions || [];

    const total = relevantCompletions.length;
    const completed = relevantCompletions.filter(c => c.status === 'completed').length;
    const skipped = relevantCompletions.filter(c => c.status === 'skipped').length;
    const pending = relevantCompletions.filter(c => c.status === 'pending').length;

    return {
      total,
      completed,
      skipped,
      pending,
      completionRate: total > 0 ? (completed / total) * 100 : 0,
      skipRate: total > 0 ? (skipped / total) * 100 : 0,
    };
  };

  return {
    completions: completions || [],
    isLoading,
    error,
    refetch,
    markMealCompleted,
    markMealSkipped,
    markMealPending,
    getMealCompletion,
    getMealStatus,
    isMealCompleted,
    isMealSkipped,
    getCompletionStats,
    isUpdating: updateCompletionMutation.isPending,
  };
}

export function useMealCompletion(mealPlanId: string, mealId: string) {
  const queryClient = useQueryClient();

  const {
    data: completion,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['meal-completion', mealPlanId, mealId],
    queryFn: async () => {
      const response = await fetch(`/api/meal-completions?meal_plan_id=${mealPlanId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch meal completion');
      }
      const result = await response.json();
      const completions = result.data as MealCompletion[];
      return completions.find(c => c.meal_id === mealId) || null;
    },
    enabled: !!mealPlanId && !!mealId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });

  const updateStatusMutation = useMutation({
    mutationFn: async (status: 'completed' | 'skipped' | 'pending') => {
      const response = await fetch('/api/meal-completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          meal_plan_id: mealPlanId,
          meal_id: mealId,
          status,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update meal completion');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['meal-completion', mealPlanId, mealId] });
      queryClient.invalidateQueries({ queryKey: ['meal-completions'] });
    },
  });

  const markCompleted = () => updateStatusMutation.mutateAsync('completed');
  const markSkipped = () => updateStatusMutation.mutateAsync('skipped');
  const markPending = () => updateStatusMutation.mutateAsync('pending');

  const status = completion?.status || 'pending';
  const isCompleted = status === 'completed';
  const isSkipped = status === 'skipped';
  const isPending = status === 'pending';

  return {
    completion,
    status,
    isCompleted,
    isSkipped,
    isPending,
    isLoading,
    error,
    refetch,
    markCompleted,
    markSkipped,
    markPending,
    isUpdating: updateStatusMutation.isPending,
  };
}
