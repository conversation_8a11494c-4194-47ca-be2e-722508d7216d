// This script creates a new migration file
// Run this script with: node scripts/create_migration.js "Description of migration"

const fs = require('fs');
const path = require('path');

// Get the migration description from command line arguments
const description = process.argv[2];

if (!description) {
  console.error('Error: Migration description is required.');
  console.error('Usage: node scripts/create_migration.js "Description of migration"');
  process.exit(1);
}

// Generate a version based on the current date and time
const now = new Date();
const version = now.getFullYear().toString() +
               (now.getMonth() + 1).toString().padStart(2, '0') +
               now.getDate().toString().padStart(2, '0') +
               now.getHours().toString().padStart(2, '0') +
               now.getMinutes().toString().padStart(2, '0') +
               now.getSeconds().toString().padStart(2, '0');

// Create a filename based on the version and description
const filename = `${version}_${description.toLowerCase().replace(/[^a-z0-9]+/g, '_')}.sql`;

// Create the migration file
const migrationsDir = path.join(__dirname, '../supabase/migrations');
const filePath = path.join(migrationsDir, filename);

// Create the migrations directory if it doesn't exist
if (!fs.existsSync(migrationsDir)) {
  fs.mkdirSync(migrationsDir, { recursive: true });
}

// Create the migration file content
const content = `-- Migration: ${description}
-- Version: ${version}

-- First, record this migration
INSERT INTO public.schema_migrations (version, description)
VALUES ('${version}', '${description}')
ON CONFLICT (version) DO NOTHING;

-- Write your migration SQL here
-- Example:
-- CREATE TABLE IF NOT EXISTS public.new_table (
--     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
--     name TEXT NOT NULL,
--     created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
--     updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
-- );

-- Don't forget to add RLS policies if needed
-- Example:
-- ALTER TABLE public.new_table ENABLE ROW LEVEL SECURITY;
-- 
-- CREATE POLICY "Users can view their own items"
-- ON public.new_table
-- FOR SELECT
-- USING (user_id = auth.uid());
`;

// Write the file
fs.writeFileSync(filePath, content);

console.log(`Migration file created: ${filePath}`);
console.log('To run this migration, use: node scripts/run_migrations.js');
