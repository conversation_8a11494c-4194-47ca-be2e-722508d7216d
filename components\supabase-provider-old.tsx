'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { SupabaseClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';

type SupabaseContext = {
  supabase: SupabaseClient<Database>;
  isInitialized: boolean;
};

// Create a context with a default value that indicates the provider is not initialized
// This helps prevent the "must be used inside SupabaseProvider" error during SSR
export const Context = createContext<SupabaseContext>({
  supabase: null as unknown as SupabaseClient<Database>,
  isInitialized: false,
});

/**
 * SupabaseProvider component
 *
 * This component creates a Supabase client and provides it to all child components
 * through React Context. Any component that needs to access Supabase should be a
 * descendant of this provider.
 *
 * @example
 * // In your root layout
 * export default function RootLayout({ children }) {
 *   return (
 *     <html>
 *       <body>
 *         <SupabaseProvider>
 *           {children}
 *         </SupabaseProvider>
 *       </body>
 *     </html>
 *   );
 * }
 */
export function SupabaseProvider({ children }: { children: React.ReactNode }) {
  // Create the Supabase client only once using useState
  const [supabase] = useState(() => {
    console.log('Creating Supabase client');
    try {
      return createClientComponentClient<Database>();
    } catch (error) {
      console.error('Error creating Supabase client:', error);
      // Return a placeholder client that will be replaced on client-side
      return null as unknown as SupabaseClient<Database>;
    }
  });

  const [isInitialized, setIsInitialized] = useState(false);

  // Mark the provider as initialized after mounting
  useEffect(() => {
    console.log('%c[SupabaseProvider] Component mounted', 'color: green; font-weight: bold');

    // Add a small delay to ensure the component is fully mounted
    const timer = setTimeout(() => {
      console.log('%c[SupabaseProvider] Setting isInitialized to true', 'color: green; font-weight: bold');
      setIsInitialized(true);

      // Log the current state to help with debugging
      console.log('%c[SupabaseProvider] Supabase client:', 'color: green', supabase ? 'Created ✅' : 'Not created ❌');
    }, 100); // Small delay to ensure component is mounted

    // Return cleanup function
    return () => {
      clearTimeout(timer);
      console.log('%c[SupabaseProvider] Component unmounting', 'color: orange; font-weight: bold');
    };
  }, [supabase]);

  // Create a memoized context value to prevent unnecessary re-renders
  const contextValue = React.useMemo(() => {
    return { supabase, isInitialized };
  }, [supabase, isInitialized]);

  return (
    <Context.Provider value={contextValue}>
      {children}
    </Context.Provider>
  );
}

/**
 * useSupabase hook
 *
 * This hook provides access to the Supabase client from any component that is a
 * descendant of the SupabaseProvider.
 *
 * @example
 * // In your component
 * function MyComponent() {
 *   const { supabase } = useSupabase();
 *
 *   // Now you can use the supabase client
 *   useEffect(() => {
 *     const fetchData = async () => {
 *       const { data } = await supabase.from('table').select('*');
 *       // ...
 *     };
 *
 *     fetchData();
 *   }, [supabase]);
 *
 *   return <div>My Component</div>;
 * }
 */
export const useSupabase = () => {
  const context = useContext(Context);

  // First check if context exists at all
  if (!context) {
    throw new Error(
      'useSupabase must be used inside SupabaseProvider. ' +
      'Context is undefined - make sure your component is wrapped in the SupabaseProvider component.'
    );
  }

  // Then check if it's initialized
  if (!context.isInitialized) {
    // Instead of throwing immediately, log a warning and return the context anyway
    // This helps with SSR and initial render issues
    console.warn(
      'useSupabase: SupabaseProvider is not yet initialized. ' +
      'This might cause issues if you try to use the Supabase client immediately.'
    );
  }

  return context;
};
