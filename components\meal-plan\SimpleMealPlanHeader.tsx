"use client";

import { But<PERSON> } from '@/components/ui/button';
import { 
  Calendar, 
  LayoutGrid, 
  List, 
  Plus, 
  ShoppingCart, 
  RefreshCw 
} from 'lucide-react';

interface SimpleMealPlanHeaderProps {
  onViewChange: (view: 'calendar' | 'list') => void;
  onLayoutChange: (layout: 'grid' | 'list') => void;
  onGeneratePlan: () => void;
  onAddMeal: () => void;
  onViewShoppingList: () => void;
  currentView: 'calendar' | 'list';
  currentLayout: 'grid' | 'list';
}

export function SimpleMealPlanHeader({
  onViewChange,
  onLayoutChange,
  onGeneratePlan,
  onAddMeal,
  onViewShoppingList,
  currentView,
  currentLayout
}: SimpleMealPlanHeaderProps) {
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
      <div>
        <h1 className="text-2xl font-bold">Meal Plan</h1>
        <p className="text-muted-foreground">Plan and organize your meals</p>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
        <div className="flex items-center gap-2">
          <div className="flex gap-2">
            <Button 
              variant={currentView === 'calendar' ? 'default' : 'outline'} 
              size="sm"
              onClick={() => onViewChange('calendar')}
              className="flex items-center gap-1"
            >
              <Calendar className="h-4 w-4" />
              <span className="hidden sm:inline">Calendar</span>
            </Button>
            
            <Button 
              variant={currentView === 'list' ? 'default' : 'outline'} 
              size="sm"
              onClick={() => onViewChange('list')}
              className="flex items-center gap-1"
            >
              <List className="h-4 w-4" />
              <span className="hidden sm:inline">List</span>
            </Button>
          </div>
          
          {currentView === 'calendar' && (
            <div className="flex gap-2">
              <Button 
                variant={currentLayout === 'grid' ? 'default' : 'outline'} 
                size="sm"
                onClick={() => onLayoutChange('grid')}
                className="flex items-center gap-1"
              >
                <LayoutGrid className="h-4 w-4" />
                <span className="hidden sm:inline">Grid</span>
              </Button>
              
              <Button 
                variant={currentLayout === 'list' ? 'default' : 'outline'} 
                size="sm"
                onClick={() => onLayoutChange('list')}
                className="flex items-center gap-1"
              >
                <List className="h-4 w-4" />
                <span className="hidden sm:inline">List</span>
              </Button>
            </div>
          )}
        </div>
        
        <div className="flex gap-2 ml-auto">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onViewShoppingList}
            className="flex items-center gap-1"
          >
            <ShoppingCart className="h-4 w-4" />
            <span className="hidden sm:inline">Shopping List</span>
          </Button>
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onAddMeal}
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            <span className="hidden sm:inline">Add Meal</span>
          </Button>
          
          <Button 
            variant="default" 
            size="sm" 
            onClick={onGeneratePlan}
            className="flex items-center gap-1"
          >
            <RefreshCw className="h-4 w-4" />
            <span className="hidden sm:inline">Generate Plan</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
