"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { ChevronLeft, Search, BookOpen, MessageCircle, FileText, Video } from "lucide-react";

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
}

interface Guide {
  id: string;
  title: string;
  description: string;
  link: string;
  icon: 'video' | 'text';
}

export default function HelpCenterPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("faqs");

  const faqs: FAQ[] = [
    {
      id: "1",
      question: "How do I create a meal plan?",
      answer: "To create a meal plan, go to the dashboard and click on the 'Create Plan' button in the top right corner. Follow the steps to set your preferences, dietary restrictions, and budget. The system will generate a personalized meal plan based on your inputs.",
      category: "meal-planning"
    },
    {
      id: "2",
      question: "Can I customize my meal plan?",
      answer: "Yes, you can customize your meal plan. After a plan is generated, you can swap meals, adjust portions, or remove items you don't like. Click on any meal to see options for customization.",
      category: "meal-planning"
    },
    {
      id: "3",
      question: "How do I generate a shopping list?",
      answer: "A shopping list is automatically generated based on your meal plan. Go to the 'Shopping List' section to view all ingredients needed for your planned meals. You can add or remove items as needed.",
      category: "shopping"
    },
    {
      id: "4",
      question: "Can I set dietary restrictions?",
      answer: "Yes, you can set dietary restrictions in your profile settings. Go to 'Profile Settings' and update your preferences. These will be applied to all future meal plans you generate.",
      category: "preferences"
    },
    {
      id: "5",
      question: "How do I track my nutrition?",
      answer: "Your nutrition is automatically tracked based on the meals in your plan. Visit the 'Statistics' page to see detailed breakdowns of calories, macronutrients, and other nutritional information.",
      category: "nutrition"
    },
    {
      id: "6",
      question: "Can I share my meal plan with others?",
      answer: "Currently, sharing meal plans is not available but we're working on adding this feature soon. You can export your shopping list to print or send to others.",
      category: "sharing"
    }
  ];

  const guides: Guide[] = [
    {
      id: "1",
      title: "Getting Started with LeanEats",
      description: "Learn the basics of using LeanEats to create meal plans and save money.",
      link: "/guides/getting-started",
      icon: "video"
    },
    {
      id: "2",
      title: "Creating Your First Meal Plan",
      description: "A step-by-step guide to creating a personalized meal plan.",
      link: "/guides/first-meal-plan",
      icon: "text"
    },
    {
      id: "3",
      title: "Managing Your Shopping List",
      description: "Tips and tricks for efficient grocery shopping with LeanEats.",
      link: "/guides/shopping-list",
      icon: "text"
    },
    {
      id: "4",
      title: "Understanding Nutrition Tracking",
      description: "How to interpret and use the nutrition data in LeanEats.",
      link: "/guides/nutrition-tracking",
      icon: "video"
    }
  ];

  const handleBack = () => {
    router.push('/dashboard');
  };

  const filteredFAQs = faqs.filter(faq => 
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) || 
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredGuides = guides.filter(guide => 
    guide.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
    guide.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="container max-w-4xl py-8">
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={handleBack} className="mr-2">
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-2xl font-bold">Help Center</h1>
      </div>

      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Search for help..."
          className="pl-10"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      <Tabs defaultValue="faqs" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="faqs" className="flex items-center">
            <BookOpen className="h-4 w-4 mr-2" />
            FAQs
          </TabsTrigger>
          <TabsTrigger value="guides" className="flex items-center">
            <FileText className="h-4 w-4 mr-2" />
            Guides
          </TabsTrigger>
          <TabsTrigger value="contact" className="flex items-center">
            <MessageCircle className="h-4 w-4 mr-2" />
            Contact Support
          </TabsTrigger>
        </TabsList>

        <TabsContent value="faqs">
          {filteredFAQs.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center">
                <p className="text-muted-foreground">No FAQs found matching your search.</p>
              </CardContent>
            </Card>
          ) : (
            <Accordion type="single" collapsible className="w-full">
              {filteredFAQs.map((faq) => (
                <AccordionItem key={faq.id} value={faq.id}>
                  <AccordionTrigger>{faq.question}</AccordionTrigger>
                  <AccordionContent>
                    <p>{faq.answer}</p>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          )}
        </TabsContent>

        <TabsContent value="guides">
          {filteredGuides.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center">
                <p className="text-muted-foreground">No guides found matching your search.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredGuides.map((guide) => (
                <Card key={guide.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{guide.title}</CardTitle>
                      {guide.icon === 'video' ? (
                        <Video className="h-5 w-5 text-blue-500" />
                      ) : (
                        <FileText className="h-5 w-5 text-green-500" />
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="mb-4">{guide.description}</CardDescription>
                    <Button variant="outline" className="w-full" onClick={() => router.push(guide.link)}>
                      View Guide
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="contact">
          <Card>
            <CardHeader>
              <CardTitle>Contact Support</CardTitle>
              <CardDescription>
                Get help from our support team. We typically respond within 24 hours.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="name" className="text-sm font-medium">Name</label>
                    <Input id="name" placeholder="Your name" />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="email" className="text-sm font-medium">Email</label>
                    <Input id="email" type="email" placeholder="Your email" />
                  </div>
                </div>
                <div className="space-y-2">
                  <label htmlFor="subject" className="text-sm font-medium">Subject</label>
                  <Input id="subject" placeholder="What's your question about?" />
                </div>
                <div className="space-y-2">
                  <label htmlFor="message" className="text-sm font-medium">Message</label>
                  <textarea
                    id="message"
                    className="w-full min-h-[120px] rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    placeholder="Describe your issue or question in detail"
                  />
                </div>
                <Button type="submit" className="w-full">Send Message</Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
