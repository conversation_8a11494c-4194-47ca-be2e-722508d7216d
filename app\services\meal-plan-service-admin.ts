

import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

// Create a singleton instance for the admin client
let adminClient: ReturnType<typeof createClient<Database>> | null = null;

/**
 * Get a Supabase admin client that bypasses RLS
 */
export function getAdminClient() {
  if (!adminClient) {
    adminClient = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );
  }
  return adminClient;
}

/**
 * Service for fetching meal plans with admin privileges
 */
export const mealPlanAdminService = {
  /**
   * Get meal plans for a user using admin privileges to bypass RLS
   * @param userId User ID
   * @returns Array of meal plans
   */
  async getMealPlans(userId: string) {
    try {
      console.log('Using admin service to fetch meal plans for user ID:', userId);
      const client = getAdminClient();

      // Add timeout for the query
      const timeoutPromise = new Promise<{ data: null; error: string }>((resolve) => {
        setTimeout(() => {
          resolve({ data: null, error: 'Database query timed out after 10 seconds' });
        }, 10000); // 10 second timeout
      });

      // Create the actual query
      console.log('Creating query for meal_plans with user_id:', userId);
      const queryPromise = client
        .from('meal_plans')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      // Race the query against the timeout
      console.log('Executing query...');
      const { data, error } = await Promise.race([queryPromise, timeoutPromise]);
      console.log('Query result:', { dataLength: data ? data.length : 0, error: error ? JSON.stringify(error) : 'null' });

      if (error) {
        console.error('Database error in getMealPlans:', error);
        return { data: null, error: 'Database error: ' + (typeof error === 'string' ? error : error.message) };
      }

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching meal plans:', error);
      return { data: null, error: 'Error fetching meal plans: ' + (error.message || 'Unknown error') };
    }
  }
};
