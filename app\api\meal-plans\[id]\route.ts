import { NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';
import type { Database } from "@/types/supabase";

// DELETE handler to delete a meal plan
export async function DELETE(
  _request: Request, // Prefix with underscore to indicate it's not used
  context: { params: { id: string } }
) {
  try {
    // Access the ID directly from context.params
    const id = context.params.id;

    console.log('DELETE API called with ID:', id);

    // Create a direct Supabase client with the service role key
    const supabaseAdmin = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Delete the meal plan directly
    console.log('Attempting to delete meal plan with ID:', id);
    const { error } = await supabaseAdmin
      .from('meal_plans')
      .delete()
      .eq('id', id);

    console.log('Delete result:', { error });

    if (error) {
      console.error("Error deleting meal plan:", error);
      return NextResponse.json(
        { error: "Failed to delete meal plan" },
        { status: 500 }
      );
    }

    console.log('Meal plan deleted successfully');
    return NextResponse.json(
      { message: "Meal plan deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
