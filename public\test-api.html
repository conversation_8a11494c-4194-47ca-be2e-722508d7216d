<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2 {
            color: #333;
        }
        button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        .test-section {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>API Test</h1>
    
    <div class="test-section">
        <h2>Recipe API</h2>
        
        <div>
            <h3>Get Recipes</h3>
            <button id="getRecipesBtn">Get Recipes</button>
            <pre id="getRecipesResult"></pre>
        </div>
        
        <div>
            <h3>Create Recipe</h3>
            <button id="createRecipeBtn">Create Recipe</button>
            <pre id="createRecipeResult"></pre>
        </div>
        
        <div>
            <h3>Get Recipe by ID</h3>
            <input type="text" id="recipeId" placeholder="Recipe ID">
            <button id="getRecipeBtn">Get Recipe</button>
            <pre id="getRecipeResult"></pre>
        </div>
        
        <div>
            <h3>Update Recipe</h3>
            <input type="text" id="updateRecipeId" placeholder="Recipe ID">
            <button id="updateRecipeBtn">Update Recipe</button>
            <pre id="updateRecipeResult"></pre>
        </div>
        
        <div>
            <h3>Toggle Favorite</h3>
            <input type="text" id="favoriteRecipeId" placeholder="Recipe ID">
            <button id="toggleFavoriteBtn">Toggle Favorite</button>
            <pre id="toggleFavoriteResult"></pre>
        </div>
        
        <div>
            <h3>Delete Recipe</h3>
            <input type="text" id="deleteRecipeId" placeholder="Recipe ID">
            <button id="deleteRecipeBtn">Delete Recipe</button>
            <pre id="deleteRecipeResult"></pre>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Grocery API</h2>
        
        <div>
            <h3>Get Grocery Lists</h3>
            <button id="getGroceryListsBtn">Get Grocery Lists</button>
            <pre id="getGroceryListsResult"></pre>
        </div>
        
        <div>
            <h3>Create Grocery List</h3>
            <input type="text" id="groceryListName" placeholder="List Name">
            <button id="createGroceryListBtn">Create Grocery List</button>
            <pre id="createGroceryListResult"></pre>
        </div>
    </div>
    
    <script>
        // Test data
        const testRecipe = {
            name: 'Test Recipe',
            description: 'A test recipe created by the API test script',
            instructions: JSON.stringify([
                'Step 1: Test the API',
                'Step 2: Verify it works',
                'Step 3: Celebrate'
            ]),
            ingredients: JSON.stringify([
                { name: 'Test Ingredient 1', quantity: 1, unit: 'cup' },
                { name: 'Test Ingredient 2', quantity: 2, unit: 'tbsp' }
            ]),
            prep_time: 10,
            cook_time: 20,
            servings: 4,
            calories_per_serving: 300,
            protein_per_serving: 15,
            carbs_per_serving: 30,
            fat_per_serving: 10,
            image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c',
            tags: JSON.stringify(['test', 'api', 'recipe'])
        };
        
        // Helper function to display results
        function displayResult(elementId, data, success = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = success ? 'success' : 'error';
        }
        
        // Recipe API Tests
        document.getElementById('getRecipesBtn').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/recipes');
                const data = await response.json();
                displayResult('getRecipesResult', data);
            } catch (error) {
                displayResult('getRecipesResult', { error: error.message }, false);
            }
        });
        
        document.getElementById('createRecipeBtn').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/recipes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testRecipe)
                });
                const data = await response.json();
                displayResult('createRecipeResult', data);
                
                // Auto-fill recipe ID fields
                if (data.id) {
                    document.getElementById('recipeId').value = data.id;
                    document.getElementById('updateRecipeId').value = data.id;
                    document.getElementById('favoriteRecipeId').value = data.id;
                    document.getElementById('deleteRecipeId').value = data.id;
                }
            } catch (error) {
                displayResult('createRecipeResult', { error: error.message }, false);
            }
        });
        
        document.getElementById('getRecipeBtn').addEventListener('click', async () => {
            const recipeId = document.getElementById('recipeId').value;
            if (!recipeId) {
                displayResult('getRecipeResult', { error: 'Recipe ID is required' }, false);
                return;
            }
            
            try {
                const response = await fetch(`/api/recipes/${recipeId}`);
                const data = await response.json();
                displayResult('getRecipeResult', data);
            } catch (error) {
                displayResult('getRecipeResult', { error: error.message }, false);
            }
        });
        
        document.getElementById('updateRecipeBtn').addEventListener('click', async () => {
            const recipeId = document.getElementById('updateRecipeId').value;
            if (!recipeId) {
                displayResult('updateRecipeResult', { error: 'Recipe ID is required' }, false);
                return;
            }
            
            try {
                const response = await fetch(`/api/recipes/${recipeId}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: 'Updated Test Recipe',
                        description: 'This recipe has been updated'
                    })
                });
                const data = await response.json();
                displayResult('updateRecipeResult', data);
            } catch (error) {
                displayResult('updateRecipeResult', { error: error.message }, false);
            }
        });
        
        document.getElementById('toggleFavoriteBtn').addEventListener('click', async () => {
            const recipeId = document.getElementById('favoriteRecipeId').value;
            if (!recipeId) {
                displayResult('toggleFavoriteResult', { error: 'Recipe ID is required' }, false);
                return;
            }
            
            try {
                const response = await fetch(`/api/recipes/${recipeId}/favorite`, {
                    method: 'PATCH'
                });
                const data = await response.json();
                displayResult('toggleFavoriteResult', data);
            } catch (error) {
                displayResult('toggleFavoriteResult', { error: error.message }, false);
            }
        });
        
        document.getElementById('deleteRecipeBtn').addEventListener('click', async () => {
            const recipeId = document.getElementById('deleteRecipeId').value;
            if (!recipeId) {
                displayResult('deleteRecipeResult', { error: 'Recipe ID is required' }, false);
                return;
            }
            
            try {
                const response = await fetch(`/api/recipes/${recipeId}`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                displayResult('deleteRecipeResult', data);
            } catch (error) {
                displayResult('deleteRecipeResult', { error: error.message }, false);
            }
        });
        
        // Grocery API Tests
        document.getElementById('getGroceryListsBtn').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/grocery/list');
                const data = await response.json();
                displayResult('getGroceryListsResult', data);
            } catch (error) {
                displayResult('getGroceryListsResult', { error: error.message }, false);
            }
        });
        
        document.getElementById('createGroceryListBtn').addEventListener('click', async () => {
            const name = document.getElementById('groceryListName').value;
            if (!name) {
                displayResult('createGroceryListResult', { error: 'List name is required' }, false);
                return;
            }
            
            try {
                const response = await fetch('/api/grocery/list', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ name })
                });
                const data = await response.json();
                displayResult('createGroceryListResult', data);
            } catch (error) {
                displayResult('createGroceryListResult', { error: error.message }, false);
            }
        });
    </script>
</body>
</html>
