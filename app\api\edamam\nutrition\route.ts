import { NextRequest, NextResponse } from 'next/server';
import { getNutrition } from '@/lib/api/edamam';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { ingredients } = body;

    if (!ingredients || !Array.isArray(ingredients)) {
      return NextResponse.json(
        { error: 'Invalid request. Expected an array of ingredients.' },
        { status: 400 }
      );
    }

    const nutritionData = await getNutrition(ingredients);

    return NextResponse.json(nutritionData);
  } catch (error: any) {
    console.error('Error in Edamam nutrition API route:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while analyzing nutrition' },
      { status: 500 }
    );
  }
}
