'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { ShoppingCart, ArrowLeft } from 'lucide-react';

// This is a fallback component that will be used if the shopping list feature is not available
export default function ShoppingListFallback({ mealPlanId }: { mealPlanId?: string }) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  
  // Mock data for the shopping list
  const mockCategories = [
    {
      name: 'Fruits',
      items: [
        { id: '1', name: 'Apples', quantity: '4', unit: '', checked: false },
        { id: '2', name: '<PERSON><PERSON><PERSON>', quantity: '6', unit: '', checked: false },
        { id: '3', name: 'Oranges', quantity: '3', unit: '', checked: false }
      ]
    },
    {
      name: 'Vegetables',
      items: [
        { id: '4', name: 'Spinach', quantity: '1', unit: 'bag', checked: false },
        { id: '5', name: 'Carrots', quantity: '1', unit: 'bunch', checked: false },
        { id: '6', name: 'Onions', quantity: '2', unit: '', checked: false }
      ]
    },
    {
      name: 'Meat & Seafood',
      items: [
        { id: '7', name: 'Chicken breast', quantity: '2', unit: 'lbs', checked: false },
        { id: '8', name: 'Ground beef', quantity: '1', unit: 'lb', checked: false }
      ]
    },
    {
      name: 'Dairy',
      items: [
        { id: '9', name: 'Milk', quantity: '1', unit: 'gallon', checked: false },
        { id: '10', name: 'Eggs', quantity: '1', unit: 'dozen', checked: false },
        { id: '11', name: 'Cheese', quantity: '8', unit: 'oz', checked: false }
      ]
    },
    {
      name: 'Grains & Bread',
      items: [
        { id: '12', name: 'Brown rice', quantity: '2', unit: 'cups', checked: false },
        { id: '13', name: 'Whole wheat bread', quantity: '1', unit: 'loaf', checked: false }
      ]
    },
    {
      name: 'Condiments & Spices',
      items: [
        { id: '14', name: 'Olive oil', quantity: '1', unit: 'bottle', checked: false },
        { id: '15', name: 'Salt', quantity: '1', unit: 'tsp', checked: false },
        { id: '16', name: 'Black pepper', quantity: '1', unit: 'tsp', checked: false }
      ]
    }
  ];
  
  const [categories, setCategories] = useState(mockCategories);
  
  const handleToggleItem = (categoryIndex: number, itemIndex: number) => {
    const newCategories = [...categories];
    newCategories[categoryIndex].items[itemIndex].checked = !newCategories[categoryIndex].items[itemIndex].checked;
    setCategories(newCategories);
  };
  
  const handlePrint = () => {
    window.print();
    toast.success('Printing shopping list');
  };
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Shopping List</h1>
          <p className="text-muted-foreground">
            {mealPlanId ? 'Generated from your meal plan' : 'Demo shopping list'}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.push('/meal-plan/view')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Meal Plans
          </Button>
          <Button variant="outline" onClick={handlePrint}>
            Print List
          </Button>
        </div>
      </div>

      <Card className="mb-6">
        <CardHeader className="pb-2">
          <CardTitle>Shopping List</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            This is a demo shopping list. In the full version, you'll be able to add, edit, and remove items,
            check them off as you shop, and save your lists for future use.
          </p>
          
          {categories.map((category, categoryIndex) => (
            <div key={category.name} className="mb-6">
              <div className="flex items-center mb-2">
                <h3 className="text-lg font-medium">{category.name}</h3>
                <Badge variant="outline" className="ml-2">{category.items.length}</Badge>
              </div>
              <Separator className="mb-3" />
              <div className="space-y-2">
                {category.items.map((item, itemIndex) => (
                  <div key={item.id} className="flex items-center justify-between p-2 rounded-md hover:bg-muted">
                    <div className="flex items-center gap-3">
                      <Checkbox
                        checked={item.checked}
                        onCheckedChange={() => handleToggleItem(categoryIndex, itemIndex)}
                        id={`item-${item.id}`}
                      />
                      <div className={`flex flex-col ${item.checked ? 'line-through text-muted-foreground' : ''}`}>
                        <Label
                          htmlFor={`item-${item.id}`}
                          className="font-medium cursor-pointer"
                        >
                          {item.name}
                        </Label>
                        <span className="text-xs text-muted-foreground">
                          {item.quantity} {item.unit}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
      
      <div className="text-center mt-8">
        <p className="text-sm text-muted-foreground">
          Note: This is a demo version of the shopping list feature. The full version will be available soon.
        </p>
      </div>
    </div>
  );
}
