"use client";

import { create } from 'zustand';
import { Meal, MealPlan } from '@/types/new-meal-plan';
import { toast } from 'sonner';
import { mealPlanService } from '@/lib/supabase/meal-plan-service';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { sanitizeMealPlan, sanitizeMeal } from '@/lib/utils/meal-plan-sanitizer';

interface MealPlanState {
  mealPlan: MealPlan | null;
  isLoading: boolean;
  userId: string | null;
  favorites: Meal[];
  setMealPlan: (mealPlan: MealPlan) => void;
  setIsLoading: (isLoading: boolean) => void;
  setUserId: (userId: string) => void;
  addMeal: (day: string, mealType: string, meal: Meal) => void;
  removeMeal: (day: string, mealType: string) => void;
  setMealStatus: (day: string, mealType: string, status: 'cooked' | 'skipped' | null) => void;
  updateMealServings: (day: string, mealType: string, servings: number) => void;
  updateMealNotes: (day: string, mealType: string, notes: string) => void;
  toggleFavorite: (day: string, mealType: string) => void;
  getFavorites: () => Meal[];
  moveMeal: (sourceMeal: { day: string, mealType: string }, targetMeal: { day: string, mealType: string }) => void;
  saveMealPlanToDatabase: () => Promise<boolean>;
  loadMealPlanFromDatabase: () => Promise<boolean>;
  loadFavoritesFromDatabase: () => Promise<boolean>;
  assignMealPlanToCalendar: (startDate: string, endDate: string) => Promise<boolean>;
}

export const useMealPlanStore = create<MealPlanState>((set, get) => ({
  mealPlan: null,
  isLoading: true,
  userId: null,
  favorites: [],

  setMealPlan: (mealPlan) => {
    set({ mealPlan });
  },

  setIsLoading: (isLoading) => {
    set({ isLoading });
  },

  setUserId: (userId) => {
    set({ userId });

    // Load meal plan and favorites when user ID is set
    if (userId) {
      setTimeout(() => {
        get().loadMealPlanFromDatabase();
        get().loadFavoritesFromDatabase();
      }, 500);
    }
  },

  addMeal: (day, mealType, meal) => {
    set((state) => {
      // Sanitize the meal to ensure it can be safely stored
      const sanitizedMeal = sanitizeMeal(meal);

      if (!state.mealPlan) {
        return { mealPlan: { [day]: { [mealType]: sanitizedMeal } } };
      }

      const newMealPlan = { ...state.mealPlan };

      if (!newMealPlan[day]) {
        newMealPlan[day] = {};
      }

      newMealPlan[day][mealType] = sanitizedMeal;

      // Auto-save to database after adding meal
      setTimeout(() => {
        get().saveMealPlanToDatabase();
      }, 500);

      return { mealPlan: newMealPlan };
    });
  },

  removeMeal: (day, mealType) => {
    set((state) => {
      if (!state.mealPlan || !state.mealPlan[day]) {
        return state;
      }

      const newMealPlan = { ...state.mealPlan };

      if (newMealPlan[day][mealType]) {
        delete newMealPlan[day][mealType];

        // If day is now empty, remove it
        if (Object.keys(newMealPlan[day]).length === 0) {
          delete newMealPlan[day];
        }

        // Auto-save to database after removing meal
        setTimeout(() => {
          get().saveMealPlanToDatabase();
        }, 500);

        return { mealPlan: newMealPlan };
      }

      return state;
    });
  },

  setMealStatus: (day, mealType, status) => {
    set((state) => {
      if (!state.mealPlan || !state.mealPlan[day] || !state.mealPlan[day][mealType]) {
        return state;
      }

      const newMealPlan = { ...state.mealPlan };
      const meal = { ...newMealPlan[day][mealType] };

      meal.status = status;
      newMealPlan[day][mealType] = meal;

      // Save to database
      if (state.userId) {
        mealPlanService.setMealStatus(state.userId, day, mealType, status);
      }

      return { mealPlan: newMealPlan };
    });
  },

  updateMealServings: (day, mealType, servings) => {
    set((state) => {
      if (!state.mealPlan || !state.mealPlan[day] || !state.mealPlan[day][mealType]) {
        return state;
      }

      const newMealPlan = { ...state.mealPlan };
      const meal = { ...newMealPlan[day][mealType] };
      const originalServings = meal.servings || 1;
      const scaleFactor = servings / originalServings;

      // Update servings
      meal.servings = servings;

      // Scale ingredients quantities
      if (meal.ingredients && Array.isArray(meal.ingredients)) {
        meal.ingredients = meal.ingredients.map(ingredient => {
          const newIngredient = { ...ingredient };
          // Only scale numeric quantities
          if (!isNaN(parseFloat(newIngredient.quantity))) {
            const scaledAmount = parseFloat(newIngredient.quantity) * scaleFactor;
            newIngredient.quantity = scaledAmount.toFixed(2);
          }
          return newIngredient;
        });
      }

      // Scale nutritional values and cost
      if (meal.nutrition) {
        meal.nutrition = {
          protein: Math.round(meal.nutrition.protein * scaleFactor),
          carbs: Math.round(meal.nutrition.carbs * scaleFactor),
          fat: Math.round(meal.nutrition.fat * scaleFactor)
        };
      }

      meal.calories = Math.round(meal.calories * scaleFactor);
      meal.cost = parseFloat((meal.cost * scaleFactor).toFixed(2));

      // Sanitize the meal to ensure it can be safely stored
      const sanitizedMeal = sanitizeMeal(meal);
      newMealPlan[day][mealType] = sanitizedMeal;

      // Save to database
      if (state.userId) {
        mealPlanService.updateMealServings(state.userId, day, mealType, sanitizedMeal.id, servings);
      }

      return { mealPlan: newMealPlan };
    });
  },

  updateMealNotes: (day, mealType, notes) => {
    set((state) => {
      if (!state.mealPlan || !state.mealPlan[day] || !state.mealPlan[day][mealType]) {
        return state;
      }

      const newMealPlan = { ...state.mealPlan };
      const meal = { ...newMealPlan[day][mealType] };

      // Update notes
      meal.notes = notes;

      // Sanitize the meal to ensure it can be safely stored
      const sanitizedMeal = sanitizeMeal(meal);
      newMealPlan[day][mealType] = sanitizedMeal;

      // Save to database
      if (state.userId) {
        mealPlanService.saveMealNotes(state.userId, day, mealType, sanitizedMeal.id, notes);
      }

      return { mealPlan: newMealPlan };
    });
  },

  toggleFavorite: (day, mealType) => {
    set((state) => {
      if (!state.mealPlan || !state.mealPlan[day] || !state.mealPlan[day][mealType]) {
        return state;
      }

      const newMealPlan = { ...state.mealPlan };
      const meal = { ...newMealPlan[day][mealType] };

      // Toggle favorite status
      meal.favorite = !meal.favorite;

      // Sanitize the meal to ensure it can be safely stored
      const sanitizedMeal = sanitizeMeal(meal);
      newMealPlan[day][mealType] = sanitizedMeal;

      // Update favorites list
      let newFavorites = [...state.favorites];

      if (sanitizedMeal.favorite) {
        // Add to favorites if not already there
        if (!newFavorites.some(fav => fav.id === sanitizedMeal.id)) {
          newFavorites.push(sanitizedMeal);
        }
      } else {
        // Remove from favorites
        newFavorites = newFavorites.filter(fav => fav.id !== sanitizedMeal.id);
      }

      // Save to database
      if (state.userId) {
        mealPlanService.toggleFavoriteMeal(state.userId, sanitizedMeal);
      }

      return {
        mealPlan: newMealPlan,
        favorites: newFavorites
      };
    });
  },

  getFavorites: () => {
    return get().favorites;
  },

  moveMeal: (sourceMeal, targetMeal) => {
    set((state) => {
      if (!state.mealPlan) return state;

      const { day: sourceDay, mealType: sourceMealType } = sourceMeal;
      const { day: targetDay, mealType: targetMealType } = targetMeal;

      // Check if source meal exists
      if (!state.mealPlan[sourceDay] || !state.mealPlan[sourceDay][sourceMealType]) {
        return state;
      }

      const newMealPlan = { ...state.mealPlan };

      // Get the source meal
      const sourceMealData = { ...newMealPlan[sourceDay][sourceMealType] };
      // Sanitize the source meal
      const sanitizedSourceMeal = sanitizeMeal(sourceMealData);

      // Check if target day exists, if not create it
      if (!newMealPlan[targetDay]) {
        newMealPlan[targetDay] = {};
      }

      // Check if target meal exists
      const targetMealExists = newMealPlan[targetDay] && newMealPlan[targetDay][targetMealType];
      let targetMealData: Meal | undefined = undefined;
      let sanitizedTargetMeal: Meal | undefined = undefined;

      if (targetMealExists) {
        // If target meal exists, store it for swapping
        targetMealData = { ...newMealPlan[targetDay][targetMealType] };
        // Sanitize the target meal
        sanitizedTargetMeal = sanitizeMeal(targetMealData);
      }

      // Move source meal to target
      newMealPlan[targetDay][targetMealType] = sanitizedSourceMeal;

      if (targetMealExists && sanitizedTargetMeal) {
        // If target meal existed, swap it to source
        newMealPlan[sourceDay][sourceMealType] = sanitizedTargetMeal;
      } else {
        // Otherwise, remove the source meal
        delete newMealPlan[sourceDay][sourceMealType];

        // If source day is now empty, remove it
        if (Object.keys(newMealPlan[sourceDay]).length === 0) {
          delete newMealPlan[sourceDay];
        }
      }

      // Save to database
      if (state.userId) {
        mealPlanService.moveMeal(state.userId, sourceMeal, targetMeal);
      }

      return { mealPlan: newMealPlan };
    });
  },

  saveMealPlanToDatabase: async () => {
    const state = get();

    console.log('saveMealPlanToDatabase called, state:', {
      hasMealPlan: !!state.mealPlan,
      userId: state.userId
    });

    if (!state.mealPlan) {
      console.error('No meal plan available');
      toast.error('No meal plan available');
      return false;
    }

    if (!state.userId) {
      console.error('No user ID available');

      // Try to get the user ID from Supabase
      try {
        const supabase = createClientComponentClient();
        const { data } = await supabase.auth.getUser();

        if (data.user) {
          console.log('Retrieved user ID from Supabase:', data.user.id);
          // Update the user ID in the store
          set({ userId: data.user.id });

          // Continue with the save operation using the retrieved user ID
          return await saveMealPlanWithUserId(state.mealPlan, data.user.id);
        } else {
          console.error('No authenticated user found');
          toast.error('You must be logged in to save a meal plan');
          return false;
        }
      } catch (authError) {
        console.error('Error getting authenticated user:', authError);
        toast.error('Failed to get authentication status');
        return false;
      }
    }

    // If we have both meal plan and user ID, proceed with saving
    return await saveMealPlanWithUserId(state.mealPlan, state.userId);

    // Helper function to save meal plan with a user ID
    async function saveMealPlanWithUserId(mealPlan: MealPlan, userId: string): Promise<boolean> {
      try {
        // Show a toast notification
        toast('Saving meal plan...', { duration: 2000 });

        // Sanitize the meal plan before saving
        console.log('Sanitizing meal plan before saving...');
        const sanitizedMealPlan = sanitizeMealPlan(mealPlan);

        console.log('Calling mealPlanService.saveMealPlan with userId:', userId);
        const success = await mealPlanService.saveMealPlan(sanitizedMealPlan, userId);

        console.log('Save result:', success);

        if (success) {
          toast.success('Meal plan saved successfully');
        }

        return success;
      } catch (error) {
        console.error('Error saving meal plan to database:', error);
        toast.error(`Failed to save meal plan: ${error instanceof Error ? error.message : 'Unknown error'}`);
        return false;
      }
    }
  },

  loadMealPlanFromDatabase: async () => {
    const state = get();

    console.log('loadMealPlanFromDatabase called, userId:', state.userId);

    let userId = state.userId;

    if (!userId) {
      console.log('No user ID in state, trying to get from Supabase');

      // Try to get the user ID from Supabase
      try {
        const supabase = createClientComponentClient();
        const { data } = await supabase.auth.getUser();

        if (data.user) {
          console.log('Retrieved user ID from Supabase:', data.user.id);
          userId = data.user.id;
          // Update the user ID in the store
          set({ userId: data.user.id });
        } else {
          console.error('No authenticated user found');
          toast.error('You must be logged in to load meal plans');
          set({ isLoading: false });
          return false;
        }
      } catch (authError) {
        console.error('Error getting authenticated user:', authError);
        toast.error('Failed to get authentication status');
        set({ isLoading: false });
        return false;
      }
    }

    try {
      set({ isLoading: true });

      console.log('Calling mealPlanService.getMealPlans with userId:', userId);
      const mealPlans = await mealPlanService.getMealPlans(userId);
      console.log('Meal plans received:', mealPlans);

      if (mealPlans.length > 0) {
        // Use the most recent meal plan
        console.log('Setting meal plan to:', mealPlans[0]);
        set({ mealPlan: mealPlans[0] });
        return true;
      } else {
        console.log('No meal plans found, setting mealPlan to null');
        set({ mealPlan: null });
        return false;
      }
    } catch (error) {
      console.error('Error loading meal plan from database:', error);
      toast.error(`Failed to load meal plan: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    } finally {
      set({ isLoading: false });
    }
  },

  loadFavoritesFromDatabase: async () => {
    const state = get();

    if (!state.userId) {
      toast.error('No user ID available');
      return false;
    }

    try {
      const favorites = await mealPlanService.getFavoriteMeals(state.userId);
      set({ favorites });
      return true;
    } catch (error) {
      console.error('Error loading favorites from database:', error);
      toast.error('Failed to load favorites');
      return false;
    }
  },

  assignMealPlanToCalendar: async (startDate, endDate) => {
    const state = get();

    console.log('assignMealPlanToCalendar called with dates:', { startDate, endDate });
    console.log('Current state:', {
      hasMealPlan: !!state.mealPlan,
      userId: state.userId
    });

    if (!state.mealPlan) {
      console.error('No meal plan available');
      toast.error('No meal plan available');
      return false;
    }

    // Get user ID, either from state or from Supabase
    let userId = state.userId;

    if (!userId) {
      console.log('No user ID in state, trying to get from Supabase');

      try {
        const supabase = createClientComponentClient();
        const { data } = await supabase.auth.getUser();

        if (data.user) {
          console.log('Retrieved user ID from Supabase:', data.user.id);
          userId = data.user.id;
          // Update the user ID in the store
          set({ userId: data.user.id });
        } else {
          console.error('No authenticated user found');
          toast.error('You must be logged in to assign meal plans to the calendar');
          return false;
        }
      } catch (authError) {
        console.error('Error getting authenticated user:', authError);
        toast.error('Failed to get authentication status');
        return false;
      }
    }

    try {
      // Show a toast notification
      toast('Assigning meal plan to calendar...', { duration: 2000 });

      // First, save the meal plan to ensure it's in the database
      console.log('Saving meal plan to database...');
      const saveMealPlanResult = await get().saveMealPlanToDatabase();

      if (!saveMealPlanResult) {
        console.error('Failed to save meal plan to database');
        return false;
      }

      console.log('Meal plan saved successfully, getting meal plan ID...');

      // Get the meal plan ID
      const supabase = createClientComponentClient();
      const { data, error } = await supabase
        .from('meal_plans')
        .select('id')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error || !data) {
        console.error('Error getting meal plan ID:', error);
        toast.error(`Failed to get meal plan ID: ${error?.message || 'Unknown error'}`);
        return false;
      }

      console.log('Got meal plan ID:', data.id);

      // Assign the meal plan to the calendar
      console.log('Assigning meal plan to calendar with params:', {
        userId,
        mealPlanId: data.id,
        startDate,
        endDate
      });

      const success = await mealPlanService.assignMealPlanToCalendar(
        userId,
        data.id,
        startDate,
        endDate
      );

      console.log('Meal plan assignment result:', success);

      if (success) {
        toast.success('Meal plan assigned to calendar successfully');
      }

      return success;
    } catch (error) {
      console.error('Error assigning meal plan to calendar:', error);
      toast.error(`Failed to assign meal plan to calendar: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    }
  }
}));
