import { WeeklyPlan } from '@/types/mealPlan';

export const mockWeeklyPlan: WeeklyPlan = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  startDate: '2024-01-01',
  endDate: '2024-01-07',
  user_id: 'user123',
  status: 'active',
  totalCost: 150.00,
  averageCalories: 2000,
  days: [
    {
      date: '2024-01-01',
      day: 'Monday',
      totalCalories: 2100,
      totalCost: 21.50,
      meals: [
        {
          id: 'meal1',
          type: 'Breakfast',
          name: 'Oatmeal with Berries',
          image: 'https://images.unsplash.com/photo-1234',
          prepTime: 15,
          cost: 5.50,
          servings: 1,
          nutrition: {
            calories: 350,
            protein: 12,
            carbs: 45,
            fats: 8
          }
        },
        // Add more meals...
      ],
      macros: {
        protein: 85,
        carbs: 250,
        fats: 65
      }
    },
    // Add more days...
  ]
};