"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { format, parseISO, addMinutes } from "date-fns";
import { Calendar, Download, Share2 } from "lucide-react";

interface CalendarExportProps {
  mealPlan: any;
  trigger?: React.ReactNode;
}

export function CalendarExport({ mealPlan, trigger }: CalendarExportProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [calendarType, setCalendarType] = useState("google");
  const [includeOptions, setIncludeOptions] = useState({
    mealDetails: true,
    nutritionInfo: true,
    prepInstructions: false,
    reminders: true,
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleExport = async () => {
    try {
      setIsLoading(true);
      
      if (!mealPlan || !mealPlan.meal_data?.mealPlan?.week) {
        toast.error("No meal plan data to export");
        return;
      }
      
      switch (calendarType) {
        case "google":
          exportToGoogleCalendar();
          break;
        case "ical":
          exportToICalendar();
          break;
        case "outlook":
          exportToOutlook();
          break;
        default:
          toast.error("Unsupported calendar type");
      }
      
      toast.success(`Exported to ${getCalendarName(calendarType)}`);
      setIsOpen(false);
    } catch (error) {
      console.error("Error exporting calendar:", error);
      toast.error("Failed to export calendar");
    } finally {
      setIsLoading(false);
    }
  };

  const getCalendarName = (type: string): string => {
    switch (type) {
      case "google":
        return "Google Calendar";
      case "ical":
        return "iCalendar";
      case "outlook":
        return "Outlook";
      default:
        return "Calendar";
    }
  };

  const exportToGoogleCalendar = () => {
    const events = createCalendarEvents();
    
    // For each event, create a Google Calendar URL
    const firstEvent = events[0];
    if (!firstEvent) return;
    
    // Format: https://calendar.google.com/calendar/render?action=TEMPLATE&text=Title&dates=20230101T120000Z/20230101T130000Z&details=Description
    const url = new URL("https://calendar.google.com/calendar/render");
    url.searchParams.append("action", "TEMPLATE");
    url.searchParams.append("text", firstEvent.title);
    
    // Format dates as YYYYMMDDTHHMMSSZ/YYYYMMDDTHHMMSSZ
    const startDate = firstEvent.start.replace(/[-:]/g, "");
    const endDate = firstEvent.end.replace(/[-:]/g, "");
    url.searchParams.append("dates", `${startDate}/${endDate}`);
    
    url.searchParams.append("details", firstEvent.description);
    
    // Open in a new tab
    window.open(url.toString(), "_blank");
    
    // If there are multiple events, show a message
    if (events.length > 1) {
      toast.info(`Exported first meal. Export each meal individually for multiple events.`);
    }
  };

  const exportToICalendar = () => {
    const events = createCalendarEvents();
    
    // Create iCalendar file content
    let icalContent = [
      "BEGIN:VCALENDAR",
      "VERSION:2.0",
      "PRODID:-//Meal Planner//EN",
      "CALSCALE:GREGORIAN",
      "METHOD:PUBLISH"
    ];
    
    // Add each event
    events.forEach(event => {
      icalContent = icalContent.concat([
        "BEGIN:VEVENT",
        `UID:${event.id}`,
        `DTSTAMP:${new Date().toISOString().replace(/[-:]/g, "").split(".")[0]}Z`,
        `DTSTART:${event.start.replace(/[-:]/g, "")}`,
        `DTEND:${event.end.replace(/[-:]/g, "")}`,
        `SUMMARY:${event.title}`,
        `DESCRIPTION:${event.description.replace(/\n/g, "\\n")}`,
        "END:VEVENT"
      ]);
    });
    
    // Close the calendar
    icalContent.push("END:VCALENDAR");
    
    // Create and download the file
    const blob = new Blob([icalContent.join("\r\n")], { type: "text/calendar" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${mealPlan.name || "meal-plan"}.ics`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportToOutlook = () => {
    // Outlook uses the same iCalendar format
    exportToICalendar();
  };

  const createCalendarEvents = () => {
    const events: Array<{
      id: string;
      title: string;
      description: string;
      start: string;
      end: string;
    }> = [];
    
    const week = mealPlan.meal_data.mealPlan.week;
    
    // For each day in the week
    week.forEach((day: any) => {
      const date = day.date;
      
      // For each meal in the day
      day.meals.forEach((meal: any, index: number) => {
        // Generate a default time based on meal type
        let startTime = "08:00:00";
        if (meal.type.toLowerCase().includes("lunch")) {
          startTime = "12:00:00";
        } else if (meal.type.toLowerCase().includes("dinner")) {
          startTime = "18:00:00";
        }
        
        // Create start and end times (assuming 30 minutes for each meal)
        const startDateTime = `${date}T${startTime}`;
        const endDateTime = format(
          addMinutes(parseISO(`${date}T${startTime}`), 30),
          "yyyy-MM-dd'T'HH:mm:ss"
        );
        
        // Create description based on include options
        let description = `Meal: ${meal.name}`;
        
        if (includeOptions.nutritionInfo) {
          description += `\n\nNutrition:\nCalories: ${meal.calories} kcal\nProtein: ${meal.protein}g\nCarbs: ${meal.carbs}g\nFat: ${meal.fat}g`;
        }
        
        if (includeOptions.prepInstructions && meal.instructions) {
          description += `\n\nInstructions:\n${meal.instructions}`;
        }
        
        if (includeOptions.mealDetails && meal.ingredients) {
          description += "\n\nIngredients:";
          meal.ingredients.forEach((ingredient: any) => {
            description += `\n- ${ingredient.quantity} ${ingredient.unit} ${ingredient.name}`;
          });
        }
        
        // Create event
        events.push({
          id: `meal-${date}-${meal.type}-${index}`,
          title: `${meal.type}: ${meal.name}`,
          description,
          start: startDateTime,
          end: endDateTime
        });
      });
    });
    
    return events;
  };

  const handleToggleIncludeOption = (option: keyof typeof includeOptions) => {
    setIncludeOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            Export to Calendar
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Export Meal Plan to Calendar</DialogTitle>
          <DialogDescription>
            Export your meal plan to your preferred calendar application.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="calendar-type">Calendar Type</Label>
            <Select
              value={calendarType}
              onValueChange={setCalendarType}
            >
              <SelectTrigger id="calendar-type">
                <SelectValue placeholder="Select calendar type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="google">Google Calendar</SelectItem>
                <SelectItem value="ical">Apple Calendar (iCal)</SelectItem>
                <SelectItem value="outlook">Microsoft Outlook</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Separator />
          
          <div className="space-y-3">
            <Label>Include in Calendar Events</Label>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-meal-details"
                checked={includeOptions.mealDetails}
                onCheckedChange={() => handleToggleIncludeOption("mealDetails")}
              />
              <Label htmlFor="include-meal-details">Meal details (ingredients)</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-nutrition-info"
                checked={includeOptions.nutritionInfo}
                onCheckedChange={() => handleToggleIncludeOption("nutritionInfo")}
              />
              <Label htmlFor="include-nutrition-info">Nutrition information</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-prep-instructions"
                checked={includeOptions.prepInstructions}
                onCheckedChange={() => handleToggleIncludeOption("prepInstructions")}
              />
              <Label htmlFor="include-prep-instructions">Preparation instructions</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-reminders"
                checked={includeOptions.reminders}
                onCheckedChange={() => handleToggleIncludeOption("reminders")}
              />
              <Label htmlFor="include-reminders">Set reminders (if supported)</Label>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={isLoading}>
            {isLoading ? (
              <span className="flex items-center">
                <span className="animate-spin mr-2">⏳</span> Exporting...
              </span>
            ) : (
              <span className="flex items-center">
                <Download className="h-4 w-4 mr-2" />
                Export
              </span>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
