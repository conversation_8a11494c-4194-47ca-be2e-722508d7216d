export interface Ingredient {
  name: string;
  quantity: string;
  unit: string;
}

export interface Nutrition {
  protein: number;
  carbs: number;
  fat: number;
}

export interface Meal {
  id: string;
  name: string;
  ingredients: Ingredient[];
  instructions: string[];
  prepTime: number;
  calories: number;
  cost: number;
  servings: number;
  nutrition: Nutrition;
  status?: 'cooked' | 'skipped' | null;
  image?: string;
}

export interface MealPlan {
  [day: string]: {
    [mealType: string]: Meal;
  };
}

export interface MealGenerationOptions {
  budget: number;
  caloriesPerDay: number;
  dietType: string;
  preferences: {
    usePantryItems: boolean;
    optimizeIngredients: boolean;
    includeLeftovers: boolean;
  };
}

export interface ShoppingItem {
  id: string;
  name: string;
  quantity: string;
  unit: string;
  category: string;
  cost: number;
  inPantry: boolean;
}

export interface PantryItem {
  id: string;
  name: string;
  quantity: string;
  unit: string;
  category: string;
  lowStock: boolean;
}