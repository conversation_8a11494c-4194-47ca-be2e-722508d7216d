"use client";

import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import { LucideIcon } from "lucide-react";

interface StatsCardProps {
  href: string;
  icon: LucideIcon;
  label: string;
  value: string | number;
}

export function StatsCard({ href, icon: Icon, label, value }: StatsCardProps) {
  return (
    <Link href={href}>
      <div className="focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2 rounded-lg transition-transform hover:scale-105 active:scale-95 duration-200">
        <Card className="transition-colors hover:bg-accent">
          <CardContent className="flex items-center p-6">
            <div className="bg-primary/10 p-3 rounded-full mr-4">
              <Icon className="h-6 w-6 text-primary" aria-hidden="true" />
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{label}</p>
              <h3 className="text-2xl font-bold">{value}</h3>
            </div>
          </CardContent>
        </Card>
      </div>
    </Link>
  );
}
