'use client';

import { useState } from 'react';
import { format, addDays, startOfWeek, endOfWeek } from 'date-fns';
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { getSupabaseClient, ClientType } from '@/app/services/database-client';
import { toast } from 'sonner';
import { MealPlan } from '@/types/meal-plan';

interface CalendarAssignmentModalProps {
  mealPlan: MealPlan | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function CalendarAssignmentModal({
  mealPlan,
  open,
  onOpenChange,
  onSuccess,
}: CalendarAssignmentModalProps) {
  // Use current week as default
  const today = new Date();
  const defaultStartDate = startOfWeek(today, { weekStartsOn: 1 }); // Monday
  const defaultEndDate = endOfWeek(today, { weekStartsOn: 1 }); // Sunday

  const [selectedWeek, setSelectedWeek] = useState<string>('current');
  const [startDate, setStartDate] = useState<Date>(
    mealPlan?.start_date ? new Date(mealPlan.start_date) : defaultStartDate
  );
  const [endDate, setEndDate] = useState<Date>(
    mealPlan?.end_date ? new Date(mealPlan.end_date) : defaultEndDate
  );
  const [isLoading, setIsLoading] = useState(false);
  const [currentMonth, setCurrentMonth] = useState<Date>(today);

  // Helper functions for week selection
  const handleWeekChange = (value: string) => {
    setSelectedWeek(value);

    switch (value) {
      case 'current':
        setStartDate(defaultStartDate);
        setEndDate(defaultEndDate);
        break;
      case 'next':
        setStartDate(addDays(defaultStartDate, 7));
        setEndDate(addDays(defaultEndDate, 7));
        break;
      case 'nextNext':
        setStartDate(addDays(defaultStartDate, 14));
        setEndDate(addDays(defaultEndDate, 14));
        break;
      case 'custom':
        // Keep current selection for custom
        break;
    }
  };

  // Function to get week display text
  const getWeekDisplayText = (start: Date, end: Date) => {
    return `${format(start, 'MMM d')} - ${format(end, 'MMM d, yyyy')}`;
  };

  const handleAssign = async () => {
    if (!mealPlan || !startDate || !endDate) {
      toast.error('Please select a week');
      return;
    }

    try {
      setIsLoading(true);

      // Use the Supabase client directly
      const client = getSupabaseClient(ClientType.USER);

      // Update the meal plan with the new dates
      const { data, error } = await client
        .from('meal_plans')
        .update({
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
        })
        .eq('id', mealPlan.id)
        .eq('user_id', mealPlan.user_id)
        .select();

      if (error) {
        console.error('Error assigning meal plan to calendar:', error);
        toast.error('Failed to assign meal plan to calendar');
        return;
      }

      if (data) {
        toast.success('Meal plan assigned to calendar successfully');
        onOpenChange(false);
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (err) {
      console.error('Error assigning meal plan to calendar:', err);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add to Calendar</DialogTitle>
          <DialogDescription>
            Select the week to add this meal plan to your calendar.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <div className="font-medium">Select Week</div>
            <Select value={selectedWeek} onValueChange={handleWeekChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select a week" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="current">Current Week ({getWeekDisplayText(defaultStartDate, defaultEndDate)})</SelectItem>
                <SelectItem value="next">Next Week ({getWeekDisplayText(addDays(defaultStartDate, 7), addDays(defaultEndDate, 7))})</SelectItem>
                <SelectItem value="nextNext">Week After Next ({getWeekDisplayText(addDays(defaultStartDate, 14), addDays(defaultEndDate, 14))})</SelectItem>
                <SelectItem value="custom">Custom Week</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {selectedWeek === 'custom' && (
            <div className="border rounded-md p-4">
              <div className="text-sm font-medium mb-2">Custom Week Selection</div>
              <div className="flex justify-between items-center mb-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1))}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="font-medium">
                  {format(currentMonth, 'MMMM yyyy')}
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1))}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              <Calendar
                mode="single"
                selected={startDate}
                onSelect={(date) => {
                  if (date) {
                    const newStartDate = startOfWeek(date, { weekStartsOn: 1 });
                    const newEndDate = endOfWeek(date, { weekStartsOn: 1 });
                    setStartDate(newStartDate);
                    setEndDate(newEndDate);
                  }
                }}
                month={currentMonth}
                initialFocus
              />
            </div>
          )}

          <div className="bg-muted p-4 rounded-md">
            <div className="text-sm font-medium mb-1">Selected Week</div>
            <div className="flex items-center">
              <CalendarIcon className="h-4 w-4 mr-2 text-muted-foreground" />
              <span>{getWeekDisplayText(startDate, endDate)}</span>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              This meal plan will be assigned to your calendar for the selected week.
            </p>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleAssign} disabled={isLoading || !startDate || !endDate}>
            {isLoading ? 'Adding...' : 'Add to Calendar'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}