"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSupabase } from "@/components/supabase-provider";
import { toast } from "sonner";
import Image from "next/image";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { ChevronLeft, Clock, Utensils, DollarSign, Star, Check, Bookmark, Share2, ShoppingCart } from "lucide-react";

interface Ingredient {
  name: string;
  amount: string;
  unit: string;
}

interface Nutrition {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
}

interface MealDetail {
  id: number;
  name: string;
  description: string;
  image: string;
  prepTime: number;
  cookTime: number;
  servings: number;
  difficulty: string;
  ingredients: Ingredient[];
  instructions: string[];
  nutrition: Nutrition;
  tags: string[];
  isFavorite: boolean;
  mealType: string;
  scheduledFor: string;
}

export default function MealDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { supabase } = useSupabase();
  const [isLoading, setIsLoading] = useState(true);
  const [meal, setMeal] = useState<MealDetail | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [completed, setCompleted] = useState(false);

  useEffect(() => {
    const fetchMealDetails = async () => {
      try {
        setIsLoading(true);

        // Helper function to create a default meal if we can't find the requested one
        const createDefaultMeal = (id: string): MealDetail => {
          return {
            id: parseInt(id),
            name: "Grilled Chicken Salad",
            description: "A healthy and delicious salad with grilled chicken breast, mixed greens, and a light vinaigrette dressing.",
            image: "https://images.unsplash.com/photo-1527477396000-e27163b481c2",
            prepTime: 15,
            cookTime: 20,
            servings: 2,
            difficulty: "Easy",
            ingredients: [
              { name: "Chicken breast", amount: "2", unit: "pieces" },
              { name: "Mixed greens", amount: "4", unit: "cups" },
              { name: "Cherry tomatoes", amount: "1", unit: "cup" },
              { name: "Cucumber", amount: "1", unit: "medium" },
              { name: "Red onion", amount: "1/4", unit: "cup" },
              { name: "Olive oil", amount: "2", unit: "tbsp" },
              { name: "Lemon juice", amount: "1", unit: "tbsp" },
              { name: "Salt", amount: "1/2", unit: "tsp" },
              { name: "Black pepper", amount: "1/4", unit: "tsp" }
            ],
            instructions: [
              "Season chicken breasts with salt and pepper.",
              "Grill chicken for 6-8 minutes per side until fully cooked.",
              "Let chicken rest for 5 minutes, then slice into strips.",
              "In a large bowl, combine mixed greens, tomatoes, cucumber, and red onion.",
              "In a small bowl, whisk together olive oil, lemon juice, salt, and pepper.",
              "Drizzle dressing over salad and toss to combine.",
              "Top with sliced grilled chicken and serve immediately."
            ],
            nutrition: {
              calories: 350,
              protein: 35,
              carbs: 15,
              fat: 18,
              fiber: 5
            },
            tags: ["high-protein", "low-carb", "gluten-free"],
            isFavorite: true,
            mealType: "Lunch",
            scheduledFor: "Today, 1:00 PM"
          };
        };

        // Fetch the recipe from Supabase
        const { data, error } = await supabase
          .from('recipes')
          .select('*')
          .eq('id', params.id)
          .single();

        let mealData: MealDetail;

        if (error || !data) {
          console.error('Error fetching recipe:', error);
          // If we can't find the recipe, use a default one
          mealData = createDefaultMeal(params.id);
        } else {
          // Transform the recipe data into the meal detail format
          mealData = {
            id: parseInt(params.id),
            name: data.name,
            description: data.description || '',
            image: data.image_url || 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c',
            prepTime: data.prep_time,
            cookTime: data.cook_time,
            servings: data.servings,
            difficulty: data.difficulty || 'Medium',
            ingredients: data.ingredients ? data.ingredients.map((ing: any) => ({
              name: ing.name,
              amount: ing.amount,
              unit: ing.unit
            })) : [
              { name: "Ingredient 1", amount: "1", unit: "cup" },
              { name: "Ingredient 2", amount: "2", unit: "tbsp" }
            ],
            instructions: data.instructions || ['No instructions provided'],
            nutrition: data.nutrition || {
              calories: 350,
              protein: 20,
              carbs: 30,
              fat: 15,
              fiber: 5
            },
            tags: data.dietary_restrictions || [],
            isFavorite: data.is_favorite || false,
            mealType: data.meal_type || 'Dinner',
            scheduledFor: 'Today'
          };
        }

        setMeal(mealData);
      } catch (error) {
        console.error('Error fetching meal details:', error);
        toast.error('Failed to load meal details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchMealDetails();
  }, [params.id, supabase]);

  const handleBack = () => {
    router.push('/dashboard');
  };

  const toggleFavorite = () => {
    if (meal) {
      setMeal({ ...meal, isFavorite: !meal.isFavorite });
      toast.success(meal.isFavorite ? 'Removed from favorites' : 'Added to favorites');
    }
  };

  const markAsCompleted = () => {
    setCompleted(!completed);
    toast.success(completed ? 'Meal marked as not completed' : 'Meal marked as completed');
  };

  const handleAddToShoppingList = async () => {
    try {
      if (!meal) return;

      // Get the current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        router.push('/login');
        return;
      }

      // Format ingredients for shopping list
      const shoppingItems = meal.ingredients.map(ingredient => ({
        name: ingredient.name,
        quantity: ingredient.amount,
        unit: ingredient.unit,
        category: getCategoryForIngredient(ingredient.name),
        checked: false
      }));

      // Create a new shopping list with these ingredients
      const { data, error } = await supabase
        .from('shopping_lists')
        .insert({
          user_id: user.id,
          meal_plan_id: null, // Not associated with a specific meal plan
          items: shoppingItems,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      toast.success('Ingredients added to shopping list');

      // Ask if the user wants to view the shopping list
      const shouldNavigate = window.confirm('Ingredients added to shopping list. Would you like to view your shopping list now?');
      if (shouldNavigate) {
        router.push('/shopping-list');
      }
    } catch (error) {
      console.error('Error adding ingredients to shopping list:', error);
      toast.error('Failed to add ingredients to shopping list');
    }
  };

  // Helper function to categorize ingredients
  const getCategoryForIngredient = (name: string): string => {
    const lowerName = name.toLowerCase();

    if (/meat|chicken|beef|pork|fish|turkey|lamb/i.test(lowerName)) {
      return "Meat & Seafood";
    } else if (/milk|cheese|yogurt|cream|butter/i.test(lowerName)) {
      return "Dairy";
    } else if (/apple|banana|orange|berry|fruit|grape/i.test(lowerName)) {
      return "Fruits";
    } else if (/lettuce|carrot|onion|potato|vegetable|tomato|pepper|cucumber/i.test(lowerName)) {
      return "Vegetables";
    } else if (/bread|pasta|rice|cereal|flour|grain/i.test(lowerName)) {
      return "Grains & Bread";
    } else if (/oil|vinegar|sauce|condiment|spice|herb|salt|pepper/i.test(lowerName)) {
      return "Condiments & Spices";
    }

    return "Other";
  };

  const handleSubstitute = () => {
    toast('Substitution options coming soon!');
  };

  if (isLoading || !meal) {
    return (
      <div className="container max-w-4xl py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl py-8">
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={handleBack} className="mr-2">
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-2xl font-bold">{meal.name}</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="md:col-span-2">
          <div className="relative w-full h-[300px] rounded-lg overflow-hidden mb-4">
            <Image
              src={meal.image}
              alt={meal.name}
              fill
              style={{ objectFit: "cover" }}
              priority
            />
          </div>

          <div className="flex flex-wrap gap-2 mb-4">
            {meal.tags.map((tag, index) => (
              <Badge key={index} variant="secondary">
                {tag}
              </Badge>
            ))}
          </div>

          <p className="text-muted-foreground mb-4">{meal.description}</p>

          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6">
            <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg">
              <Clock className="h-5 w-5 mb-1 text-muted-foreground" />
              <span className="text-sm font-medium">{meal.prepTime + meal.cookTime} min</span>
              <span className="text-xs text-muted-foreground">Total Time</span>
            </div>
            <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg">
              <Utensils className="h-5 w-5 mb-1 text-muted-foreground" />
              <span className="text-sm font-medium">{meal.difficulty}</span>
              <span className="text-xs text-muted-foreground">Difficulty</span>
            </div>
            <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg">
              <DollarSign className="h-5 w-5 mb-1 text-muted-foreground" />
              <span className="text-sm font-medium">$8.50</span>
              <span className="text-xs text-muted-foreground">Per Serving</span>
            </div>
            <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg">
              <Star className="h-5 w-5 mb-1 text-muted-foreground" />
              <span className="text-sm font-medium">{meal.nutrition.calories}</span>
              <span className="text-xs text-muted-foreground">Calories</span>
            </div>
          </div>

          <div className="flex space-x-2 mb-6">
            <Button variant="outline" onClick={toggleFavorite}>
              <Bookmark className={`h-4 w-4 mr-2 ${meal.isFavorite ? "fill-primary" : ""}`} />
              {meal.isFavorite ? "Saved" : "Save"}
            </Button>
            <Button variant="outline" onClick={markAsCompleted}>
              <Check className={`h-4 w-4 mr-2 ${completed ? "text-green-500" : ""}`} />
              {completed ? "Completed" : "Mark as Completed"}
            </Button>
            <Button variant="outline">
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
          </div>
        </div>

        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Nutrition Facts</CardTitle>
              <CardDescription>Per serving</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span>Calories</span>
                  <span className="font-medium">{meal.nutrition.calories}</span>
                </div>
                <Progress value={meal.nutrition.calories / 25} className="h-2" />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span>Protein</span>
                  <span className="font-medium">{meal.nutrition.protein}g</span>
                </div>
                <Progress value={meal.nutrition.protein / 0.5} className="h-2 bg-blue-100">
                  <div className="h-full bg-blue-500 rounded-full" />
                </Progress>
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span>Carbs</span>
                  <span className="font-medium">{meal.nutrition.carbs}g</span>
                </div>
                <Progress value={meal.nutrition.carbs / 1.5} className="h-2 bg-green-100">
                  <div className="h-full bg-green-500 rounded-full" />
                </Progress>
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span>Fat</span>
                  <span className="font-medium">{meal.nutrition.fat}g</span>
                </div>
                <Progress value={meal.nutrition.fat / 0.7} className="h-2 bg-yellow-100">
                  <div className="h-full bg-yellow-500 rounded-full" />
                </Progress>
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span>Fiber</span>
                  <span className="font-medium">{meal.nutrition.fiber}g</span>
                </div>
                <Progress value={meal.nutrition.fiber / 0.3} className="h-2 bg-purple-100">
                  <div className="h-full bg-purple-500 rounded-full" />
                </Progress>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="ingredients">Ingredients</TabsTrigger>
          <TabsTrigger value="instructions">Instructions</TabsTrigger>
        </TabsList>
        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>Meal Overview</CardTitle>
              <CardDescription>Quick summary of this meal</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Description</h3>
                  <p className="text-muted-foreground">{meal.description}</p>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Preparation Time</h3>
                  <p className="text-muted-foreground">
                    Prep: {meal.prepTime} minutes | Cook: {meal.cookTime} minutes | Total: {meal.prepTime + meal.cookTime} minutes
                  </p>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Servings</h3>
                  <p className="text-muted-foreground">This recipe makes {meal.servings} servings.</p>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Scheduled For</h3>
                  <p className="text-muted-foreground">{meal.scheduledFor}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="ingredients">
          <Card>
            <CardHeader>
              <CardTitle>Ingredients</CardTitle>
              <CardDescription>For {meal.servings} servings</CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {meal.ingredients.map((ingredient, index) => (
                  <li key={index} className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-primary mr-2"></div>
                    <span>
                      {ingredient.amount} {ingredient.unit} {ingredient.name}
                    </span>
                  </li>
                ))}
              </ul>

              <Button
                className="w-full mt-6"
                onClick={handleAddToShoppingList}
                variant="default"
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                Add Ingredients to Shopping List
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="instructions">
          <Card>
            <CardHeader>
              <CardTitle>Cooking Instructions</CardTitle>
              <CardDescription>Step-by-step guide</CardDescription>
            </CardHeader>
            <CardContent>
              <ol className="space-y-4">
                {meal.instructions.map((instruction, index) => (
                  <li key={index} className="flex">
                    <span className="flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center mr-3 mt-0.5">
                      {index + 1}
                    </span>
                    <p>{instruction}</p>
                  </li>
                ))}
              </ol>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
