'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSupabase } from '@/components/supabase-provider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { shoppingListService, ShoppingList, ShoppingItem } from '@/app/services/shopping-list-service';
import ShoppingListFallback from '../fallback';
import {
  ShoppingCart,
  ArrowLeft,
  Plus,
  Trash2,
  Check,
  Save,
  Download,
  Package,
  Filter,
  Search,
  RefreshCw
} from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';

export default function ShoppingListPage() {
  const params = useParams();
  const router = useRouter();
  const { supabase } = useSupabase();
  const [isLoading, setIsLoading] = useState(true);
  const [shoppingList, setShoppingList] = useState<ShoppingList | null>(null);
  const [categories, setCategories] = useState<{ name: string; items: ShoppingItem[] }[]>([]);
  const [newItem, setNewItem] = useState({ name: '', quantity: '1', unit: 'item', category: 'Other' });
  const [searchQuery, setSearchQuery] = useState('');
  const [filterOptions, setFilterOptions] = useState({
    hideChecked: false,
    hidePantryItems: false,
    showOnlyNeeded: false
  });
  const [activeTab, setActiveTab] = useState('list');
  const [totalEstimatedCost, setTotalEstimatedCost] = useState(0);
  const [completionPercentage, setCompletionPercentage] = useState(0);
  const [useFallback, setUseFallback] = useState(false);

  useEffect(() => {
    if (!params.id) return;

    const fetchShoppingList = async () => {
      try {
        setIsLoading(true);

        // Log the ID for debugging
        console.log('Fetching shopping list with ID:', params.id);

        const { data, error } = await shoppingListService.getShoppingListWithItems(params.id as string);

        if (error) {
          console.error('Error fetching shopping list:', error);
          toast.error(`Error: ${error}`);
          setUseFallback(true);
          return;
        }

        if (data) {
          console.log('Shopping list data received:', data);
          setShoppingList(data);
          processItemsIntoCategories(data.items || []);
        } else {
          console.warn('No shopping list data received');
          toast.error('Shopping list not found');
          // Redirect to the main shopping list page after a delay
          setTimeout(() => {
            router.push('/shopping-list');
          }, 2000);
        }
      } catch (error: any) {
        console.error('Error fetching shopping list:', error);
        toast.error(`Error: ${error.message || 'Failed to load shopping list'}`);
        setUseFallback(true);
      } finally {
        setIsLoading(false);
      }
    };

    fetchShoppingList();
  }, [params.id, supabase, router]);

  useEffect(() => {
    if (shoppingList?.items) {
      // Calculate completion percentage
      const totalItems = shoppingList.items.length;
      const checkedItems = shoppingList.items.filter(item => item.checked).length;
      const percentage = totalItems > 0 ? Math.round((checkedItems / totalItems) * 100) : 0;
      setCompletionPercentage(percentage);
    }
  }, [shoppingList]);

  const processItemsIntoCategories = (items: ShoppingItem[]) => {
    // Group items by category
    const groupedItems = items.reduce((acc: Record<string, ShoppingItem[]>, item) => {
      const category = item.category || 'Other';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(item);
      return acc;
    }, {});

    // Convert to array and sort categories
    const categoryOrder = [
      'Fruits',
      'Vegetables',
      'Meat & Seafood',
      'Dairy',
      'Grains & Bread',
      'Condiments & Spices',
      'Other'
    ];

    const categoriesArray = Object.keys(groupedItems).map(categoryName => ({
      name: categoryName,
      items: groupedItems[categoryName].sort((a, b) => a.name.localeCompare(b.name))
    }));

    // Sort categories
    categoriesArray.sort((a, b) => {
      const indexA = categoryOrder.indexOf(a.name);
      const indexB = categoryOrder.indexOf(b.name);
      return (indexA === -1 ? 999 : indexA) - (indexB === -1 ? 999 : indexB);
    });

    setCategories(categoriesArray);

    // Calculate total estimated cost
    const totalCost = items.reduce((sum, item) => {
      const estimatedPrice = item.estimatedPrice || 0;
      return sum + estimatedPrice;
    }, 0);

    setTotalEstimatedCost(totalCost);
  };

  const handleToggleItem = async (itemId: string, checked: boolean) => {
    try {
      const { data, error } = await shoppingListService.toggleItemChecked(itemId, checked);

      if (error) {
        toast.error(`Error: ${error}`);
        return;
      }

      if (data) {
        // Update the shopping list with the updated item
        setShoppingList(prev => {
          if (!prev) return prev;

          const updatedItems = prev.items?.map(item =>
            item.id === itemId ? { ...item, checked } : item
          ) || [];

          return {
            ...prev,
            items: updatedItems
          };
        });

        // Update categories
        setCategories(prev => {
          return prev.map(category => ({
            ...category,
            items: category.items.map(item =>
              item.id === itemId ? { ...item, checked } : item
            )
          }));
        });
      }
    } catch (error: any) {
      console.error('Error toggling item:', error);
      toast.error(`Error: ${error.message || 'Failed to update item'}`);
    }
  };

  const handleAddItem = async () => {
    if (!newItem.name.trim()) {
      toast.error('Please enter an item name');
      return;
    }

    if (!shoppingList) {
      toast.error('No active shopping list');
      return;
    }

    try {
      const { data, error } = await shoppingListService.addItemToList(shoppingList.id, {
        name: newItem.name,
        quantity: newItem.quantity,
        unit: newItem.unit,
        category: newItem.category
      });

      if (error) {
        toast.error(`Error: ${error}`);
        return;
      }

      if (data) {
        // Update the shopping list with the new item
        setShoppingList(prev => {
          if (!prev) return prev;

          const updatedItems = [...(prev.items || []), data];

          return {
            ...prev,
            items: updatedItems
          };
        });

        // Update categories
        processItemsIntoCategories([...(shoppingList.items || []), data]);

        // Reset form
        setNewItem({ name: '', quantity: '1', unit: 'item', category: 'Other' });

        toast.success('Item added to shopping list');
      }
    } catch (error: any) {
      console.error('Error adding item:', error);
      toast.error(`Error: ${error.message || 'Failed to add item'}`);
    }
  };

  const handleDeleteItem = async (itemId: string) => {
    try {
      const { success, error } = await shoppingListService.deleteItem(itemId);

      if (error) {
        toast.error(`Error: ${error}`);
        return;
      }

      if (success) {
        // Update the shopping list without the deleted item
        setShoppingList(prev => {
          if (!prev) return prev;

          const updatedItems = prev.items?.filter(item => item.id !== itemId) || [];

          return {
            ...prev,
            items: updatedItems
          };
        });

        // Update categories
        setCategories(prev => {
          const updatedCategories = prev.map(category => ({
            ...category,
            items: category.items.filter(item => item.id !== itemId)
          })).filter(category => category.items.length > 0);

          return updatedCategories;
        });

        toast.success('Item removed from shopping list');
      }
    } catch (error: any) {
      console.error('Error deleting item:', error);
      toast.error(`Error: ${error.message || 'Failed to delete item'}`);
    }
  };

  const handleCheckAgainstPantry = async () => {
    if (!shoppingList) {
      toast.error('No active shopping list');
      return;
    }

    try {
      toast('Checking against pantry...');

      const { data, error } = await shoppingListService.checkAgainstPantry(shoppingList.id);

      if (error) {
        toast.error(`Error: ${error}`);
        return;
      }

      if (data) {
        setShoppingList(data);
        processItemsIntoCategories(data.items || []);
        toast.success('Shopping list updated with pantry items');
      }
    } catch (error: any) {
      console.error('Error checking against pantry:', error);
      toast.error(`Error: ${error.message || 'Failed to check against pantry'}`);
    }
  };

  const filteredCategories = categories.map(category => {
    let filteredItems = category.items;

    // Apply search filter
    if (searchQuery) {
      filteredItems = filteredItems.filter(item =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply other filters
    if (filterOptions.hideChecked) {
      filteredItems = filteredItems.filter(item => !item.checked);
    }

    if (filterOptions.hidePantryItems) {
      filteredItems = filteredItems.filter(item => !item.in_pantry);
    }

    if (filterOptions.showOnlyNeeded) {
      filteredItems = filteredItems.filter(item => !item.checked && !item.in_pantry);
    }

    return {
      ...category,
      items: filteredItems
    };
  }).filter(category => category.items.length > 0);

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <ShoppingCart className="h-12 w-12 mx-auto mb-4 animate-pulse text-primary" />
            <h2 className="text-2xl font-bold mb-2">Loading Shopping List...</h2>
            <p className="text-muted-foreground">Please wait while we fetch your shopping list.</p>
          </div>
        </div>
      </div>
    );
  }

  // Use the fallback component if there was an error or if the shopping list doesn't exist
  if (useFallback || !shoppingList) {
    return <ShoppingListFallback mealPlanId={params.id as string} />;
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">{shoppingList.name || 'Shopping List'}</h1>
          <p className="text-muted-foreground">
            {shoppingList.items?.length || 0} items • {shoppingList.items?.filter(i => i.checked).length || 0} checked
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.push('/meal-plan/view')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Meal Plans
          </Button>
          <Button variant="outline" onClick={handleCheckAgainstPantry}>
            <Package className="h-4 w-4 mr-2" />
            Check Pantry
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Completion</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Progress value={completionPercentage} className="h-2" />
              <div className="text-sm text-muted-foreground text-right">{completionPercentage}% complete</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Estimated Cost</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalEstimatedCost.toFixed(2)}</div>
            <div className="text-sm text-muted-foreground">Based on average prices</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Button variant="outline" size="sm" className="flex-1">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm" className="flex-1">
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
              <Button variant="outline" size="sm" className="flex-1">
                <Check className="h-4 w-4 mr-2" />
                Check All
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="list" value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList>
          <TabsTrigger value="list">Shopping List</TabsTrigger>
          <TabsTrigger value="add">Add Items</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search items..."
                className="pl-9"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex gap-4 items-center">
              <div className="flex items-center space-x-2">
                <Switch
                  id="hide-checked"
                  checked={filterOptions.hideChecked}
                  onCheckedChange={(checked) => setFilterOptions(prev => ({ ...prev, hideChecked: checked }))}
                />
                <Label htmlFor="hide-checked" className="text-sm">Hide checked</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="hide-pantry"
                  checked={filterOptions.hidePantryItems}
                  onCheckedChange={(checked) => setFilterOptions(prev => ({ ...prev, hidePantryItems: checked }))}
                />
                <Label htmlFor="hide-pantry" className="text-sm">Hide pantry items</Label>
              </div>
            </div>
          </div>

          {filteredCategories.length === 0 ? (
            <div className="text-center py-12">
              <ShoppingCart className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-xl font-medium mb-2">No items found</h3>
              <p className="text-muted-foreground mb-4">Try adjusting your filters or add some items to your shopping list.</p>
              <Button onClick={() => setActiveTab('add')}>
                <Plus className="h-4 w-4 mr-2" />
                Add Items
              </Button>
            </div>
          ) : (
            filteredCategories.map((category) => (
              <div key={category.name} className="mb-6">
                <div className="flex items-center mb-2">
                  <h3 className="text-lg font-medium">{category.name}</h3>
                  <Badge variant="outline" className="ml-2">{category.items.length}</Badge>
                </div>
                <Separator className="mb-3" />
                <div className="space-y-2">
                  {category.items.map((item) => (
                    <div key={item.id} className="flex items-center justify-between p-2 rounded-md hover:bg-muted">
                      <div className="flex items-center gap-3">
                        <Checkbox
                          checked={item.checked}
                          onCheckedChange={(checked) => handleToggleItem(item.id, checked === true)}
                          id={`item-${item.id}`}
                        />
                        <div className={`flex flex-col ${item.checked ? 'line-through text-muted-foreground' : ''}`}>
                          <Label
                            htmlFor={`item-${item.id}`}
                            className="font-medium cursor-pointer"
                          >
                            {item.name}
                          </Label>
                          <span className="text-xs text-muted-foreground">
                            {item.quantity} {item.unit}
                            {item.in_pantry && ' • In Pantry'}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {item.estimatedPrice && (
                          <span className="text-sm text-muted-foreground">
                            ${item.estimatedPrice.toFixed(2)}
                          </span>
                        )}
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteItem(item.id)}
                        >
                          <Trash2 className="h-4 w-4 text-muted-foreground" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))
          )}
        </TabsContent>

        <TabsContent value="add">
          <Card>
            <CardHeader>
              <CardTitle>Add New Item</CardTitle>
              <CardDescription>Add items to your shopping list</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="item-name">Item Name</Label>
                    <Input
                      id="item-name"
                      placeholder="Enter item name"
                      value={newItem.name}
                      onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="item-category">Category</Label>
                    <Select
                      value={newItem.category}
                      onValueChange={(value) => setNewItem({ ...newItem, category: value })}
                    >
                      <SelectTrigger id="item-category">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Fruits">Fruits</SelectItem>
                        <SelectItem value="Vegetables">Vegetables</SelectItem>
                        <SelectItem value="Meat & Seafood">Meat & Seafood</SelectItem>
                        <SelectItem value="Dairy">Dairy</SelectItem>
                        <SelectItem value="Grains & Bread">Grains & Bread</SelectItem>
                        <SelectItem value="Condiments & Spices">Condiments & Spices</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="item-quantity">Quantity</Label>
                    <Input
                      id="item-quantity"
                      type="text"
                      placeholder="1"
                      value={newItem.quantity}
                      onChange={(e) => setNewItem({ ...newItem, quantity: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="item-unit">Unit</Label>
                    <Select
                      value={newItem.unit}
                      onValueChange={(value) => setNewItem({ ...newItem, unit: value })}
                    >
                      <SelectTrigger id="item-unit">
                        <SelectValue placeholder="Select unit" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="item">item</SelectItem>
                        <SelectItem value="g">g</SelectItem>
                        <SelectItem value="kg">kg</SelectItem>
                        <SelectItem value="oz">oz</SelectItem>
                        <SelectItem value="lb">lb</SelectItem>
                        <SelectItem value="cup">cup</SelectItem>
                        <SelectItem value="tbsp">tbsp</SelectItem>
                        <SelectItem value="tsp">tsp</SelectItem>
                        <SelectItem value="ml">ml</SelectItem>
                        <SelectItem value="l">l</SelectItem>
                        <SelectItem value="bunch">bunch</SelectItem>
                        <SelectItem value="can">can</SelectItem>
                        <SelectItem value="bottle">bottle</SelectItem>
                        <SelectItem value="package">package</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => setActiveTab('list')}>Cancel</Button>
              <Button onClick={handleAddItem}>
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Shopping List Settings</CardTitle>
              <CardDescription>Customize your shopping experience</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="list-name">List Name</Label>
                <Input
                  id="list-name"
                  placeholder="Shopping List"
                  value={shoppingList.name}
                  onChange={() => {}} // This would need a handler to update the list name
                />
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Display Options</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Switch id="show-prices" />
                    <Label htmlFor="show-prices">Show estimated prices</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="group-by-category" defaultChecked />
                    <Label htmlFor="group-by-category">Group by category</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="show-pantry-badge" defaultChecked />
                    <Label htmlFor="show-pantry-badge">Show pantry indicators</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="auto-check-pantry" />
                    <Label htmlFor="auto-check-pantry">Auto-check pantry items</Label>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                <Save className="h-4 w-4 mr-2" />
                Save Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
