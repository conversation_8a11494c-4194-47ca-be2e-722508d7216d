'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';

interface MealPreview {
  id: string;
  name: string;
  type: string;
  image?: string;
  calories?: number;
}

interface DayMeals {
  date: string;
  meals: MealPreview[];
}

interface ScrollableDatePickerProps {
  selectedDate: string | null;
  onDateSelect: (date: string) => void;
  mealDates: string[]; // Dates that have meals
  month?: number; // 0-11
  year?: number;
  onMonthYearChange?: (month: number, year: number) => void;
  mealData?: DayMeals[]; // Detailed meal data for previews
  onMealClick?: (mealId: string) => void; // Callback for meal click in preview
}

export function ScrollableDatePicker({
  selectedDate,
  onDateSelect,
  mealDates,
  month: initialMonth,
  year: initialYear,
  onMonthYearChange,
  mealData = [],
  onMealClick
}: ScrollableDatePickerProps) {
  const today = new Date();
  const [month, setMonth] = useState<number>(initialMonth !== undefined ? initialMonth : today.getMonth());
  const [year, setYear] = useState<number>(initialYear !== undefined ? initialYear : today.getFullYear());
  const [weeks, setWeeks] = useState<Date[][]>([]);
  const [currentWeekIndex, setCurrentWeekIndex] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Generate all weeks for the selected month
  useEffect(() => {
    const weeks: Date[][] = [];
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);

    // Start from the beginning of the week containing the first day of the month
    const start = new Date(firstDay);
    start.setDate(start.getDate() - start.getDay()); // Go to previous Sunday

    // End at the end of the week containing the last day of the month
    const end = new Date(lastDay);
    const daysToAdd = 6 - end.getDay();
    end.setDate(end.getDate() + daysToAdd); // Go to next Saturday

    // Generate all weeks
    let currentWeek: Date[] = [];
    let currentDate = new Date(start);

    while (currentDate <= end) {
      if (currentWeek.length === 7) {
        weeks.push(currentWeek);
        currentWeek = [];
      }

      currentWeek.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    if (currentWeek.length > 0) {
      weeks.push(currentWeek);
    }

    setWeeks(weeks);

    // Find the week containing today or the selected date
    const targetDate = selectedDate ? new Date(selectedDate) : today;
    const weekIndex = weeks.findIndex(week =>
      week.some(date =>
        date.getDate() === targetDate.getDate() &&
        date.getMonth() === targetDate.getMonth() &&
        date.getFullYear() === targetDate.getFullYear()
      )
    );

    setCurrentWeekIndex(weekIndex >= 0 ? weekIndex : 0);
  }, [month, year, selectedDate]);

  // Handle month/year change
  const handleMonthChange = (value: string) => {
    const newMonth = parseInt(value);
    setMonth(newMonth);
    if (onMonthYearChange) {
      onMonthYearChange(newMonth, year);
    }
  };

  const handleYearChange = (value: string) => {
    const newYear = parseInt(value);
    setYear(newYear);
    if (onMonthYearChange) {
      onMonthYearChange(month, newYear);
    }
  };

  // Navigate between weeks
  const goToPreviousWeek = () => {
    if (currentWeekIndex > 0) {
      setCurrentWeekIndex(currentWeekIndex - 1);
    } else {
      // Go to previous month
      const newMonth = month === 0 ? 11 : month - 1;
      const newYear = month === 0 ? year - 1 : year;
      setMonth(newMonth);
      setYear(newYear);
      if (onMonthYearChange) {
        onMonthYearChange(newMonth, newYear);
      }
      // Will show the last week of the previous month
      setCurrentWeekIndex(0);
    }
  };

  const goToNextWeek = () => {
    if (currentWeekIndex < weeks.length - 1) {
      setCurrentWeekIndex(currentWeekIndex + 1);
    } else {
      // Go to next month
      const newMonth = month === 11 ? 0 : month + 1;
      const newYear = month === 11 ? year + 1 : year;
      setMonth(newMonth);
      setYear(newYear);
      if (onMonthYearChange) {
        onMonthYearChange(newMonth, newYear);
      }
      // Will show the first week of the next month
      setCurrentWeekIndex(0);
    }
  };

  // Format date as string (YYYY-MM-DD)
  const formatDateString = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };

  // Check if a date has meals
  const hasMeals = (dateStr: string): boolean => {
    return mealDates.includes(dateStr);
  };

  // Get meals for a specific date
  const getMealsForDate = (dateStr: string): MealPreview[] => {
    const dayData = mealData.find(day => day.date === dateStr);
    return dayData?.meals || [];
  };

  // Check if a date is today
  const isToday = (date: Date): boolean => {
    const today = new Date();
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
  };

  // Generate month options
  const monthOptions = [
    { value: '0', label: 'January' },
    { value: '1', label: 'February' },
    { value: '2', label: 'March' },
    { value: '3', label: 'April' },
    { value: '4', label: 'May' },
    { value: '5', label: 'June' },
    { value: '6', label: 'July' },
    { value: '7', label: 'August' },
    { value: '8', label: 'September' },
    { value: '9', label: 'October' },
    { value: '10', label: 'November' },
    { value: '11', label: 'December' },
  ];

  // Generate year options (current year ± 5 years)
  const currentYear = today.getFullYear();
  const yearOptions = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <Select value={month.toString()} onValueChange={handleMonthChange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Month" />
            </SelectTrigger>
            <SelectContent>
              {monthOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={year.toString()} onValueChange={handleYearChange}>
            <SelectTrigger className="w-[100px]">
              <SelectValue placeholder="Year" />
            </SelectTrigger>
            <SelectContent>
              {yearOptions.map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="icon"
            onClick={goToPreviousWeek}
            aria-label="Previous week"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={goToNextWeek}
            aria-label="Next week"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div
        ref={scrollContainerRef}
        className="flex overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-rounded scrollbar-thumb-gray-300"
      >
          <div className="flex space-x-1 min-w-full justify-between">
          {weeks[currentWeekIndex]?.map((date, index) => {
            const dateStr = formatDateString(date);
            const isSelected = selectedDate === dateStr;
            const isCurrentMonth = date.getMonth() === month;
            const hasMeal = hasMeals(dateStr);
            const dateIsToday = isToday(date);
            const meals = getMealsForDate(dateStr);

            return (
              <TooltipProvider key={index}>
                <Tooltip delayDuration={300}>
                  <TooltipTrigger asChild>
                    <Button
                      variant={isSelected ? "default" : dateIsToday ? "secondary" : "outline"}
                      className={cn(
                        "flex-1 h-auto py-2 px-1 flex flex-col items-center justify-center min-w-[60px] relative",
                        !isCurrentMonth && "opacity-50",
                        !hasMeal && "text-muted-foreground",
                        dateIsToday && !isSelected && "border-primary"
                      )}
                      onClick={() => onDateSelect(dateStr)}
                    >
                      {dateIsToday && !isSelected && (
                        <span className="absolute top-1 right-1 w-2 h-2 bg-primary rounded-full" />
                      )}
                      <span className="text-xs font-medium">
                        {date.toLocaleDateString('en-US', { weekday: 'short' })}
                      </span>
                      <span className={cn(
                        "text-lg font-bold mt-1",
                        isSelected ? "text-white" : hasMeal ? "text-foreground" : "text-muted-foreground"
                      )}>
                        {date.getDate()}
                      </span>
                      {hasMeal && (
                        <div className="flex space-x-0.5 mt-1">
                          {meals.length > 0 ? (
                            meals.slice(0, 3).map((_, i) => (
                              <span key={i} className="w-1 h-1 bg-primary rounded-full" />
                            ))
                          ) : (
                            <span className="w-1 h-1 bg-primary rounded-full" />
                          )}
                        </div>
                      )}
                    </Button>
                  </TooltipTrigger>
                  {hasMeal && meals.length > 0 && (
                    <TooltipContent side="bottom" className="p-0 w-64">
                      <div className="p-2">
                        <p className="font-medium text-sm mb-2">
                          {date.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' })}
                        </p>
                        <div className="space-y-2">
                          {meals.map((meal) => (
                            <div
                              key={meal.id}
                              className="flex items-center gap-2 cursor-pointer hover:bg-muted p-1 rounded"
                              onClick={(e) => {
                                e.stopPropagation();
                                onMealClick?.(meal.id);
                              }}
                            >
                              <Badge variant="outline" className="text-xs">{meal.type}</Badge>
                              <span className="text-sm">{meal.name}</span>
                              {meal.calories && (
                                <span className="text-xs text-muted-foreground ml-auto">{meal.calories} cal</span>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </TooltipContent>
                  )}
                </Tooltip>
              </TooltipProvider>
            );
          })}
        </div>
      </div>
    </div>
  );
}
