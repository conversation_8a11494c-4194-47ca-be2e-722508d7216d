"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
// Import the new hook instead of the old one
import { useSupabase } from "@/components/supabase-provider";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MultiSelect } from "@/components/ui/multi-select";
import { <PERSON><PERSON>ronLeft, Plus, Trash2, Upload } from "lucide-react";
import { Separator } from "@/components/ui/separator";

// Define the form schema
const recipeFormSchema = z.object({
  name: z.string().min(3, "Recipe name must be at least 3 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  prep_time: z.coerce.number().min(1, "Prep time must be at least 1 minute"),
  cook_time: z.coerce.number().min(0, "Cook time cannot be negative"),
  servings: z.coerce.number().min(1, "Servings must be at least 1"),
  cost_per_serving: z.coerce.number().min(0, "Cost cannot be negative"),
  cuisine: z.string().min(1, "Please select a cuisine"),
  difficulty: z.string().min(1, "Please select a difficulty level"),
  dietary_restrictions: z.array(z.string()).optional().default([]),
  instructions: z.array(z.string()).min(1, "At least one instruction is required"),
  ingredients: z.array(
    z.object({
      name: z.string().min(1, "Ingredient name is required"),
      amount: z.string().min(1, "Amount is required"),
      unit: z.string().optional(),
    })
  ).min(1, "At least one ingredient is required"),
  nutrition: z.object({
    calories: z.coerce.number().min(0, "Calories cannot be negative"),
    protein: z.coerce.number().min(0, "Protein cannot be negative"),
    carbs: z.coerce.number().min(0, "Carbs cannot be negative"),
    fat: z.coerce.number().min(0, "Fat cannot be negative"),
    fiber: z.coerce.number().min(0, "Fiber cannot be negative"),
  }),
  image: z.string().optional(),
});

// Define cuisine options
const cuisineOptions = [
  { label: "American", value: "American" },
  { label: "Italian", value: "Italian" },
  { label: "Mexican", value: "Mexican" },
  { label: "Asian", value: "Asian" },
  { label: "Mediterranean", value: "Mediterranean" },
  { label: "Indian", value: "Indian" },
  { label: "French", value: "French" },
  { label: "Greek", value: "Greek" },
  { label: "Spanish", value: "Spanish" },
  { label: "Middle Eastern", value: "Middle Eastern" },
  { label: "Thai", value: "Thai" },
  { label: "Japanese", value: "Japanese" },
  { label: "Chinese", value: "Chinese" },
  { label: "Korean", value: "Korean" },
  { label: "Vietnamese", value: "Vietnamese" },
  { label: "Other", value: "Other" },
];

// Define difficulty options
const difficultyOptions = [
  { label: "Easy", value: "Easy" },
  { label: "Medium", value: "Medium" },
  { label: "Hard", value: "Hard" },
];

// Define dietary restriction options
const dietaryRestrictionOptions = [
  { label: "Vegetarian", value: "vegetarian" },
  { label: "Vegan", value: "vegan" },
  { label: "Gluten-Free", value: "gluten-free" },
  { label: "Dairy-Free", value: "dairy-free" },
  { label: "Nut-Free", value: "nut-free" },
  { label: "Low-Carb", value: "low-carb" },
  { label: "Keto", value: "keto" },
  { label: "Paleo", value: "paleo" },
  { label: "High-Protein", value: "high-protein" },
  { label: "Low-Fat", value: "low-fat" },
  { label: "Low-Sodium", value: "low-sodium" },
  { label: "Sugar-Free", value: "sugar-free" },
];

type RecipeFormValues = z.infer<typeof recipeFormSchema>;

export default function CreateRecipePage() {
  const router = useRouter();
  // Use the new hook instead of the old one
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const [isLoading, setIsLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>("");
  const [imageFile, setImageFile] = useState<File | null>(null);

  // Initialize form with default values
  const form = useForm<RecipeFormValues>({
    resolver: zodResolver(recipeFormSchema),
    defaultValues: {
      name: "",
      description: "",
      prep_time: 15,
      cook_time: 30,
      servings: 4,
      cost_per_serving: 0,
      cuisine: "",
      difficulty: "Medium",
      dietary_restrictions: [],
      instructions: [""],
      ingredients: [{ name: "", amount: "", unit: "" }],
      nutrition: {
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0,
      },
      image: "",
    },
  });

  // Handle form submission
  const onSubmit = async (data: RecipeFormValues) => {
    try {
      setIsLoading(true);

      // Check if Supabase is available
      if (isSupabaseLoading || !supabase) {
        toast.error("Database connection not available. Please try again.");
        return;
      }

      // Check if user is authenticated
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        toast.error("You must be logged in to create a recipe");
        router.push("/login?returnTo=/recipes/create");
        return;
      }

      // Upload image if selected
      let finalImageUrl = imageUrl;
      if (imageFile) {
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from("recipe-images")
          .upload(`${user.id}/${Date.now()}-${imageFile.name}`, imageFile);

        if (uploadError) {
          console.error("Error uploading image:", uploadError);
          toast.error("Failed to upload image");
          return;
        }

        // Get public URL for the uploaded image
        const { data: { publicUrl } } = supabase.storage
          .from("recipe-images")
          .getPublicUrl(uploadData.path);

        finalImageUrl = publicUrl;
      }

      // Prepare recipe data
      const recipeData = {
        ...data,
        image_url: finalImageUrl || "https://images.unsplash.com/photo-1546069901-ba9599a7e63c", // Default image if none provided
        user_id: user.id,
        is_favorite: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Insert recipe into Supabase
      const { data: insertedRecipe, error: insertError } = await supabase
        .from("recipes")
        .insert(recipeData)
        .select()
        .single();

      if (insertError) {
        console.error("Error creating recipe:", insertError);
        toast.error("Failed to create recipe");
        return;
      }

      toast.success("Recipe created successfully");
      router.push(`/recipes`);
    } catch (error) {
      console.error("Error creating recipe:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("Image size must be less than 5MB");
      return;
    }

    // Check file type
    if (!file.type.startsWith("image/")) {
      toast.error("File must be an image");
      return;
    }

    setImageFile(file);

    // Create a preview URL
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        setImageUrl(event.target.result as string);
      }
    };
    reader.readAsDataURL(file);
  };

  // Add a new instruction field
  const addInstruction = () => {
    const currentInstructions = form.getValues("instructions");
    form.setValue("instructions", [...currentInstructions, ""]);
  };

  // Remove an instruction field
  const removeInstruction = (index: number) => {
    const currentInstructions = form.getValues("instructions");
    if (currentInstructions.length <= 1) return;

    const newInstructions = currentInstructions.filter((_, i) => i !== index);
    form.setValue("instructions", newInstructions);
  };

  // Add a new ingredient field
  const addIngredient = () => {
    const currentIngredients = form.getValues("ingredients");
    form.setValue("ingredients", [...currentIngredients, { name: "", amount: "", unit: "" }]);
  };

  // Remove an ingredient field
  const removeIngredient = (index: number) => {
    const currentIngredients = form.getValues("ingredients");
    if (currentIngredients.length <= 1) return;

    const newIngredients = currentIngredients.filter((_, i) => i !== index);
    form.setValue("ingredients", newIngredients);
  };

  // Handle navigation back to recipes page
  const handleBack = () => {
    router.push("/recipes");
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={handleBack} className="mr-2">
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-2xl font-bold">Create New Recipe</h1>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Left column - Basic info */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                  <CardDescription>
                    Enter the basic details of your recipe
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Recipe Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter recipe name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Briefly describe your recipe"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="prep_time"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Prep Time (minutes)</FormLabel>
                          <FormControl>
                            <Input type="number" min="0" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="cook_time"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Cook Time (minutes)</FormLabel>
                          <FormControl>
                            <Input type="number" min="0" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="servings"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Servings</FormLabel>
                          <FormControl>
                            <Input type="number" min="1" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="cost_per_serving"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Cost Per Serving ($)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              step="0.01"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="cuisine"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Cuisine</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select cuisine" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {cuisineOptions.map((option) => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="difficulty"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Difficulty</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select difficulty" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {difficultyOptions.map((option) => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="dietary_restrictions"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Dietary Restrictions</FormLabel>
                        <FormControl>
                          <MultiSelect
                            options={dietaryRestrictionOptions}
                            selected={field.value || []}
                            onChange={field.onChange}
                            placeholder="Select dietary restrictions"
                          />
                        </FormControl>
                        <FormDescription>
                          Select all that apply
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div>
                    <FormLabel>Recipe Image</FormLabel>
                    <div className="mt-2 flex items-center gap-4">
                      <div
                        className="h-32 w-32 rounded-md border border-dashed border-gray-300 flex items-center justify-center overflow-hidden"
                      >
                        {imageUrl ? (
                          <img
                            src={imageUrl}
                            alt="Recipe preview"
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <Upload className="h-8 w-8 text-gray-400" />
                        )}
                      </div>
                      <div className="flex-1">
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={handleImageUpload}
                          className="mb-2"
                        />
                        <FormDescription>
                          Upload an image of your recipe (max 5MB)
                        </FormDescription>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Nutrition Information</CardTitle>
                  <CardDescription>
                    Enter the nutritional details per serving
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="nutrition.calories"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Calories</FormLabel>
                          <FormControl>
                            <Input type="number" min="0" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="nutrition.protein"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Protein (g)</FormLabel>
                          <FormControl>
                            <Input type="number" min="0" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="nutrition.carbs"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Carbs (g)</FormLabel>
                          <FormControl>
                            <Input type="number" min="0" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="nutrition.fat"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Fat (g)</FormLabel>
                          <FormControl>
                            <Input type="number" min="0" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="nutrition.fiber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Fiber (g)</FormLabel>
                          <FormControl>
                            <Input type="number" min="0" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right column - Ingredients and Instructions */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Ingredients</CardTitle>
                  <CardDescription>
                    List all ingredients needed for this recipe
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {form.watch("ingredients").map((_, index) => (
                    <div key={index} className="flex items-end gap-2">
                      <FormField
                        control={form.control}
                        name={`ingredients.${index}.name`}
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormLabel className={index !== 0 ? "sr-only" : ""}>
                              Ingredient
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Ingredient name"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`ingredients.${index}.amount`}
                        render={({ field }) => (
                          <FormItem className="w-20">
                            <FormLabel className={index !== 0 ? "sr-only" : ""}>
                              Amount
                            </FormLabel>
                            <FormControl>
                              <Input placeholder="Amount" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`ingredients.${index}.unit`}
                        render={({ field }) => (
                          <FormItem className="w-24">
                            <FormLabel className={index !== 0 ? "sr-only" : ""}>
                              Unit
                            </FormLabel>
                            <FormControl>
                              <Input placeholder="Unit" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => removeIngredient(index)}
                        className="mb-2"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}

                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addIngredient}
                    className="mt-2"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Ingredient
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Instructions</CardTitle>
                  <CardDescription>
                    Provide step-by-step instructions for preparing this recipe
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {form.watch("instructions").map((_, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <div className="mt-2 bg-muted rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0">
                        <span className="text-sm">{index + 1}</span>
                      </div>
                      <FormField
                        control={form.control}
                        name={`instructions.${index}`}
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormControl>
                              <Textarea
                                placeholder={`Step ${index + 1}`}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => removeInstruction(index)}
                        className="mt-2"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}

                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addInstruction}
                    className="mt-2"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Step
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleBack}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || isSupabaseLoading}
            >
              {isLoading
                ? "Creating..."
                : isSupabaseLoading
                ? "Initializing..."
                : "Create Recipe"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
