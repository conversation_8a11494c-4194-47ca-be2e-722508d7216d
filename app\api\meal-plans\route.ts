import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';
import { ClientType, getSupabaseClient } from '@/app/services/database';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const status = searchParams.get('status') || 'active';
    const limit = parseInt(searchParams.get('limit') || '10');

    if (!userId) {
      return NextResponse.json(
        { error: 'userId is required' },
        { status: 400 }
      );
    }

    // Create a Supabase admin client
    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Fetch meal plans directly
    console.log('Fetching meal plans for user ID:', userId);
    const { data, error } = await supabase
      .from('meal_plans')
      .select('*')
      .eq('user_id', userId)
      .eq('status', status)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching meal plans:', error);
      return NextResponse.json(
        { error },
        { status: 500 }
      );
    }

    // Add cache control headers to prevent caching
    const response = NextResponse.json(data);
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    return response;
  } catch (error) {
    console.error('Unexpected error in GET meal-plans:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    // Get the request body
    const body = await request.json();

    // Validate required fields
    if (!body.user_id || !body.meal_data) {
      return NextResponse.json(
        { error: "Missing required fields: user_id and meal_data are required" },
        { status: 400 }
      );
    }

    // Get admin client to bypass RLS
    const supabase = getSupabaseClient(ClientType.ADMIN);

    // Calculate total cost from meal data if not provided
    let totalCost = body.total_cost;
    if (!totalCost && body.meal_data?.mealPlan?.summary?.totalCost) {
      totalCost = body.meal_data.mealPlan.summary.totalCost;
    }

    // Set default dates if not provided
    const now = new Date();
    const startDate = body.start_date || now.toISOString();
    const endDate = body.end_date || new Date(now.setDate(now.getDate() + 7)).toISOString();

    // Prepare the meal plan data
    const mealPlanData = {
      user_id: body.user_id,
      start_date: startDate,
      end_date: endDate,
      total_cost: totalCost || 0,
      meal_data: body.meal_data,
      status: body.status || 'active',
      name: body.name || 'Meal Plan',
      description: body.description || 'Generated meal plan'
    };

    // Insert the meal plan
    const { data, error } = await supabase
      .from('meal_plans')
      .insert(mealPlanData)
      .select()
      .single();

    if (error) {
      console.error('Error creating meal plan:', error);

      // Try the fallback method if there's an error
      try {
        console.log('Attempting fallback method for meal plan creation...');

        // Use the direct-mealplan endpoint as a fallback
        const fallbackResponse = await fetch(new URL('/api/direct-mealplan', request.url).toString(), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(mealPlanData),
        });

        if (fallbackResponse.ok) {
          const fallbackResult = await fallbackResponse.json();
          console.log('Meal plan created successfully using fallback method');
          return NextResponse.json(fallbackResult);
        } else {
          const fallbackError = await fallbackResponse.json();
          console.error('Fallback method failed:', fallbackError);
          return NextResponse.json(
            { error: "Failed to create meal plan using both primary and fallback methods" },
            { status: 500 }
          );
        }
      } catch (fallbackError) {
        console.error('Error in fallback method:', fallbackError);
        return NextResponse.json(
          { error: "Failed to create meal plan" },
          { status: 500 }
        );
      }
    }

    return NextResponse.json({
      message: "Meal plan created successfully",
      data
    });
  } catch (error) {
    console.error("Unexpected error in POST meal-plans:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: process.env.NODE_ENV === 'development'
          ? (error instanceof Error ? error.message : 'Unknown error occurred')
          : undefined
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    // Get the request body
    const body = await request.json();

    // Validate required fields
    if (!body.id) {
      return NextResponse.json(
        { error: "Missing required field: id" },
        { status: 400 }
      );
    }

    // Get admin client to bypass RLS
    const supabase = getSupabaseClient(ClientType.ADMIN);

    // Extract the ID and prepare update data
    const { id, ...updateData } = body;

    // Update the meal plan
    const { data, error } = await supabase
      .from('meal_plans')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating meal plan:', error);
      return NextResponse.json(
        { error: "Failed to update meal plan" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: "Meal plan updated successfully",
      data
    });
  } catch (error) {
    console.error("Unexpected error in PUT meal-plans:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: process.env.NODE_ENV === 'development'
          ? (error instanceof Error ? error.message : 'Unknown error occurred')
          : undefined
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: "Meal plan ID is required" },
        { status: 400 }
      );
    }

    // Get admin client to bypass RLS
    const supabase = getSupabaseClient(ClientType.ADMIN);

    // Delete the meal plan
    const { error } = await supabase
      .from('meal_plans')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting meal plan:', error);
      return NextResponse.json(
        { error: "Failed to delete meal plan" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: "Meal plan deleted successfully"
    });
  } catch (error) {
    console.error("Unexpected error in DELETE meal-plans:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: process.env.NODE_ENV === 'development'
          ? (error instanceof Error ? error.message : 'Unknown error occurred')
          : undefined
      },
      { status: 500 }
    );
  }
}