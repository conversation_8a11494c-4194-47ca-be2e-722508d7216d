import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/types/supabase';

// Client types for different use cases
export enum ClientType {
  CLIENT = 'client',
  ADMIN = 'admin'
}

// Create a singleton Supabase client for client-side operations
export const supabase = createClientComponentClient<Database>();

// Function to get different types of Supabase clients
export function getSupabaseClient(type: ClientType = ClientType.CLIENT) {
  switch (type) {
    case ClientType.ADMIN:
      // For admin operations, return the regular client
      // In a real app, you'd create an admin client with service role key
      return supabase;
    case ClientType.CLIENT:
    default:
      return supabase;
  }
}

// Helper functions for common Supabase operations
export class SupabaseClient {
  private client = supabase;

  // Authentication helpers
  async getCurrentUser() {
    const { data: { user }, error } = await this.client.auth.getUser();
    if (error) throw error;
    return user;
  }

  async getCurrentSession() {
    const { data: { session }, error } = await this.client.auth.getSession();
    if (error) throw error;
    return session;
  }

  async signOut() {
    const { error } = await this.client.auth.signOut();
    if (error) throw error;
  }

  // Database helpers
  async query(table: string) {
    return this.client.from(table);
  }

  async insert(table: string, data: any) {
    const { data: result, error } = await this.client
      .from(table)
      .insert(data)
      .select()
      .single();
    
    if (error) throw error;
    return result;
  }

  async update(table: string, id: string, data: any) {
    const { data: result, error } = await this.client
      .from(table)
      .update(data)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return result;
  }

  async delete(table: string, id: string) {
    const { error } = await this.client
      .from(table)
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }

  async findById(table: string, id: string) {
    const { data, error } = await this.client
      .from(table)
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  }

  async findByUserId(table: string, userId: string) {
    const { data, error } = await this.client
      .from(table)
      .select('*')
      .eq('user_id', userId);
    
    if (error) throw error;
    return data;
  }

  // Storage helpers
  async uploadFile(bucket: string, path: string, file: File) {
    const { data, error } = await this.client.storage
      .from(bucket)
      .upload(path, file);
    
    if (error) throw error;
    return data;
  }

  async downloadFile(bucket: string, path: string) {
    const { data, error } = await this.client.storage
      .from(bucket)
      .download(path);
    
    if (error) throw error;
    return data;
  }

  async getPublicUrl(bucket: string, path: string) {
    const { data } = this.client.storage
      .from(bucket)
      .getPublicUrl(path);
    
    return data.publicUrl;
  }

  async deleteFile(bucket: string, path: string) {
    const { error } = await this.client.storage
      .from(bucket)
      .remove([path]);
    
    if (error) throw error;
  }

  // Real-time subscriptions
  subscribeToTable(table: string, callback: (payload: any) => void) {
    return this.client
      .channel(`${table}_changes`)
      .on('postgres_changes', 
        { event: '*', schema: 'public', table }, 
        callback
      )
      .subscribe();
  }

  subscribeToUserData(userId: string, table: string, callback: (payload: any) => void) {
    return this.client
      .channel(`${table}_user_${userId}`)
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table,
          filter: `user_id=eq.${userId}`
        }, 
        callback
      )
      .subscribe();
  }

  unsubscribe(subscription: any) {
    return this.client.removeChannel(subscription);
  }

  // Utility methods
  async healthCheck() {
    try {
      const { data, error } = await this.client
        .from('users')
        .select('count')
        .limit(1);
      
      return !error;
    } catch {
      return false;
    }
  }

  async executeRPC(functionName: string, params: any = {}) {
    const { data, error } = await this.client.rpc(functionName, params);
    if (error) throw error;
    return data;
  }
}

// Export singleton instance
export const supabaseClient = new SupabaseClient();

// Export the raw client for direct access when needed
export { supabase as rawSupabaseClient };

// Default export
export default supabaseClient;
