"use client";

import { Me<PERSON>, <PERSON>al<PERSON><PERSON>, MealGenerationOptions } from "@/types/meal-plan";

// Mock data for development
const mockMealPlan: MealPlan = {
  "2025-05-04": {
    "breakfast": {
      id: "b1",
      name: "Greek Yogurt Parfait",
      ingredients: [
        { name: "Greek yogurt", quantity: "1", unit: "cup" },
        { name: "Mixed berries", quantity: "1/2", unit: "cup" },
        { name: "Granola", quantity: "1/4", unit: "cup" },
        { name: "Honey", quantity: "1", unit: "tbsp" }
      ],
      instructions: [
        "Layer yogurt, berries, and granola in a glass",
        "Drizzle with honey",
        "Serve immediately"
      ],
      prepTime: 5,
      calories: 320,
      cost: 2.75,
      servings: 1,
      nutrition: {
        protein: 18,
        carbs: 45,
        fat: 8
      }
    },
    "lunch": {
      id: "l1",
      name: "Mediterranean Chickpea Salad",
      ingredients: [
        { name: "Chick<PERSON>as", quantity: "1", unit: "can" },
        { name: "<PERSON><PERSON><PERSON><PERSON>", quantity: "1/2", unit: "cup" },
        { name: "Cherry tomatoes", quantity: "1/2", unit: "cup" },
        { name: "Red onion", quantity: "1/4", unit: "cup" },
        { name: "Feta cheese", quantity: "1/4", unit: "cup" },
        { name: "Olive oil", quantity: "1", unit: "tbsp" },
        { name: "Lemon juice", quantity: "1", unit: "tbsp" }
      ],
      instructions: [
        "Rinse and drain chickpeas",
        "Chop cucumber, tomatoes, and red onion",
        "Combine all ingredients in a bowl",
        "Drizzle with olive oil and lemon juice",
        "Season with salt and pepper to taste"
      ],
      prepTime: 15,
      calories: 420,
      cost: 3.50,
      servings: 2,
      nutrition: {
        protein: 15,
        carbs: 52,
        fat: 18
      }
    }
  },
  "2025-05-05": {
    "breakfast": {
      id: "b2",
      name: "Avocado Toast",
      ingredients: [
        { name: "Whole grain bread", quantity: "2", unit: "slices" },
        { name: "Avocado", quantity: "1", unit: "" },
        { name: "Cherry tomatoes", quantity: "4", unit: "" },
        { name: "Eggs", quantity: "2", unit: "" },
        { name: "Salt and pepper", quantity: "", unit: "to taste" }
      ],
      instructions: [
        "Toast bread until golden and crispy",
        "Mash avocado and spread on toast",
        "Top with sliced tomatoes",
        "Fry eggs sunny side up",
        "Place eggs on top of toast",
        "Season with salt and pepper"
      ],
      prepTime: 10,
      calories: 450,
      cost: 3.25,
      servings: 1,
      nutrition: {
        protein: 20,
        carbs: 35,
        fat: 28
      },
      status: "cooked"
    },
    "dinner": {
      id: "d1",
      name: "One-Pot Pasta Primavera",
      ingredients: [
        { name: "Pasta", quantity: "8", unit: "oz" },
        { name: "Broccoli florets", quantity: "1", unit: "cup" },
        { name: "Bell pepper", quantity: "1", unit: "" },
        { name: "Cherry tomatoes", quantity: "1", unit: "cup" },
        { name: "Garlic", quantity: "2", unit: "cloves" },
        { name: "Olive oil", quantity: "2", unit: "tbsp" },
        { name: "Parmesan cheese", quantity: "1/4", unit: "cup" }
      ],
      instructions: [
        "Boil pasta according to package instructions",
        "In a large skillet, heat olive oil over medium heat",
        "Add garlic and sauté until fragrant",
        "Add vegetables and cook until tender",
        "Drain pasta and add to skillet",
        "Toss to combine and top with parmesan cheese"
      ],
      prepTime: 25,
      calories: 580,
      cost: 4.75,
      servings: 2,
      nutrition: {
        protein: 22,
        carbs: 78,
        fat: 20
      },
      status: "skipped"
    }
  }
};

// This function would normally fetch data from the API/database
export const getMealPlan = async (): Promise<MealPlan> => {
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockMealPlan);
    }, 1000);
  });
};

// This function would normally call the OpenAI API to generate a meal plan
export const generateMealPlan = async (options: MealGenerationOptions): Promise<MealPlan> => {
  console.log("Generating meal plan with options:", options);
  
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockMealPlan);
    }, 2000);
  });
};

// This function would normally call the OpenAI API to generate a single meal
export const generateMeal = async ({ mealType }: { mealType: string }): Promise<Meal> => {
  console.log("Generating meal for type:", mealType);
  
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      const meals = [
        {
          id: `generated-${Date.now()}`,
          name: "Spinach & Feta Omelette",
          ingredients: [
            { name: "Eggs", quantity: "2", unit: "large" },
            { name: "Spinach", quantity: "1", unit: "cup" },
            { name: "Feta cheese", quantity: "2", unit: "tbsp" },
            { name: "Olive oil", quantity: "1", unit: "tsp" },
            { name: "Salt and pepper", quantity: "", unit: "to taste" }
          ],
          instructions: [
            "Whisk eggs in a bowl",
            "Heat oil in a non-stick pan over medium heat",
            "Add spinach and cook until wilted",
            "Pour eggs over spinach",
            "Sprinkle feta on top",
            "Cook until eggs are set",
            "Fold in half and serve"
          ],
          prepTime: 10,
          calories: 280,
          cost: 2.30,
          servings: 1,
          nutrition: {
            protein: 19,
            carbs: 3,
            fat: 22
          }
        },
        {
          id: `generated-${Date.now()}`,
          name: "Quinoa Buddha Bowl",
          ingredients: [
            { name: "Quinoa", quantity: "1/2", unit: "cup" },
            { name: "Sweet potato", quantity: "1/2", unit: "cup" },
            { name: "Chickpeas", quantity: "1/2", unit: "cup" },
            { name: "Avocado", quantity: "1/4", unit: "" },
            { name: "Kale", quantity: "1", unit: "cup" },
            { name: "Tahini", quantity: "1", unit: "tbsp" },
            { name: "Lemon juice", quantity: "1", unit: "tsp" }
          ],
          instructions: [
            "Cook quinoa according to package instructions",
            "Roast sweet potato and chickpeas at 400°F for 20 minutes",
            "Massage kale with a bit of olive oil and salt",
            "Arrange all ingredients in a bowl",
            "Mix tahini and lemon juice for dressing",
            "Drizzle dressing over the bowl"
          ],
          prepTime: 30,
          calories: 450,
          cost: 4.15,
          servings: 1,
          nutrition: {
            protein: 14,
            carbs: 65,
            fat: 18
          }
        },
        {
          id: `generated-${Date.now()}`,
          name: "Herb-Roasted Chicken Thighs",
          ingredients: [
            { name: "Chicken thighs", quantity: "4", unit: "" },
            { name: "Garlic", quantity: "3", unit: "cloves" },
            { name: "Fresh rosemary", quantity: "1", unit: "tbsp" },
            { name: "Fresh thyme", quantity: "1", unit: "tbsp" },
            { name: "Olive oil", quantity: "2", unit: "tbsp" },
            { name: "Lemon", quantity: "1", unit: "" },
            { name: "Salt and pepper", quantity: "", unit: "to taste" }
          ],
          instructions: [
            "Preheat oven to 425°F",
            "Mix herbs, garlic, oil, lemon juice in a bowl",
            "Season chicken with salt and pepper",
            "Brush herb mixture onto chicken thighs",
            "Roast for 25-30 minutes until internal temp reaches 165°F",
            "Let rest for 5 minutes before serving"
          ],
          prepTime: 40,
          calories: 520,
          cost: 5.80,
          servings: 2,
          nutrition: {
            protein: 45,
            carbs: 5,
            fat: 35
          }
        }
      ];

      // Return a random meal from the list
      resolve(meals[Math.floor(Math.random() * meals.length)]);
    }, 1500);
  });
};

// Function to add a custom meal
export const addCustomMeal = (meal: Meal): Meal => {
  return {
    ...meal,
    id: `custom-${Date.now()}`
  };
};