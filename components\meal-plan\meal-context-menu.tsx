'use client';

import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { Calendar, Copy, Edit, Trash2, ShoppingCart, RefreshCw } from "lucide-react";
import { toast } from "sonner";

interface MealContextMenuProps {
  children: React.ReactNode;
  onEdit?: () => void;
  onDelete?: () => void;
  onAddToCalendar?: () => void;
  onDuplicate?: () => void;
  onGenerateShoppingList?: () => void;
  onSwapMeal?: () => void;
  disableDelete?: boolean;
}

export function MealContextMenu({
  children,
  onEdit,
  onDelete,
  onAddToCalendar,
  onDuplicate,
  onGenerateShoppingList,
  onSwapMeal,
  disableDelete = false,
}: MealContextMenuProps) {
  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>{children}</ContextMenuTrigger>
      <ContextMenuContent className="w-64">
        {onEdit && (
          <ContextMenuItem onClick={onEdit} className="cursor-pointer">
            <Edit className="mr-2 h-4 w-4" />
            <span>Edit</span>
          </ContextMenuItem>
        )}
        
        {onSwapMeal && (
          <ContextMenuItem onClick={onSwapMeal} className="cursor-pointer">
            <RefreshCw className="mr-2 h-4 w-4" />
            <span>Swap Meal</span>
          </ContextMenuItem>
        )}
        
        {onAddToCalendar && (
          <ContextMenuItem onClick={onAddToCalendar} className="cursor-pointer">
            <Calendar className="mr-2 h-4 w-4" />
            <span>Add to Calendar</span>
          </ContextMenuItem>
        )}
        
        {onDuplicate && (
          <ContextMenuItem onClick={onDuplicate} className="cursor-pointer">
            <Copy className="mr-2 h-4 w-4" />
            <span>Duplicate</span>
          </ContextMenuItem>
        )}
        
        {onGenerateShoppingList && (
          <ContextMenuItem onClick={onGenerateShoppingList} className="cursor-pointer">
            <ShoppingCart className="mr-2 h-4 w-4" />
            <span>Generate Shopping List</span>
          </ContextMenuItem>
        )}
        
        {onDelete && !disableDelete && (
          <>
            <ContextMenuSeparator />
            <ContextMenuItem 
              onClick={onDelete} 
              className="cursor-pointer text-destructive focus:text-destructive"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              <span>Delete</span>
            </ContextMenuItem>
          </>
        )}
      </ContextMenuContent>
    </ContextMenu>
  );
}
