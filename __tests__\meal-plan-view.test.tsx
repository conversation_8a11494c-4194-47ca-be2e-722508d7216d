import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { useSupabase } from '@/components/supabase-provider';
import { mealPlanService } from '@/app/services/meal-plan-service';
import ViewMealPlanContent from '@/app/meal-plan/view/page';

// Mock the necessary dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

jest.mock('@/components/supabase-provider', () => ({
  useSupabase: jest.fn(),
}));

jest.mock('@/app/services/meal-plan-service', () => ({
  mealPlanService: {
    getMealPlans: jest.fn(),
  },
}));

jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

describe('ViewMealPlanContent', () => {
  const mockRouter = {
    push: jest.fn(),
  };

  const mockSupabase = {
    auth: {
      getSession: jest.fn(),
      getUser: jest.fn(),
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSupabase as jest.Mock).mockReturnValue({
      supabase: mockSupabase,
      isLoading: false,
    });
  });

  it('should show loading state initially', async () => {
    (useSupabase as jest.Mock).mockReturnValue({
      supabase: mockSupabase,
      isLoading: true,
    });

    render(<ViewMealPlanContent />);
    
    expect(screen.getByText('My Meal Plans')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Create New Plan' })).toBeDisabled();
  });

  it('should redirect to login if user is not authenticated', async () => {
    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: null },
      error: null,
    });

    render(<ViewMealPlanContent />);
    
    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith('/login');
    });
  });

  it('should show empty state when no meal plans are found', async () => {
    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: { user: { id: 'test-user-id' } } },
      error: null,
    });

    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'test-user-id' } },
      error: null,
    });

    (mealPlanService.getMealPlans as jest.Mock).mockResolvedValue({
      data: [],
      error: null,
    });

    render(<ViewMealPlanContent />);
    
    await waitFor(() => {
      expect(screen.getByText('No Meal Plans Found')).toBeInTheDocument();
      expect(screen.getByText('Create your first meal plan to get started')).toBeInTheDocument();
    });
  });

  it('should show meal plans when they are loaded successfully', async () => {
    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: { user: { id: 'test-user-id' } } },
      error: null,
    });

    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'test-user-id' } },
      error: null,
    });

    const mockMealPlans = [
      {
        id: '1',
        user_id: 'test-user-id',
        name: 'Test Meal Plan 1',
        status: 'active',
        start_date: '2023-01-01',
        end_date: '2023-01-07',
        total_cost: 100,
        meal_data: {
          mealPlan: {
            week: [],
            summary: {
              macros: {
                protein: '100g',
                carbs: '200g',
                fats: '50g',
              },
            },
          },
        },
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
      },
    ];

    (mealPlanService.getMealPlans as jest.Mock).mockResolvedValue({
      data: mockMealPlans,
      error: null,
    });

    render(<ViewMealPlanContent />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Meal Plan 1')).toBeInTheDocument();
    });
  });

  it('should show error state when there is an error fetching meal plans', async () => {
    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: { user: { id: 'test-user-id' } } },
      error: null,
    });

    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'test-user-id' } },
      error: null,
    });

    (mealPlanService.getMealPlans as jest.Mock).mockResolvedValue({
      data: null,
      error: 'Failed to fetch meal plans',
    });

    render(<ViewMealPlanContent />);
    
    await waitFor(() => {
      expect(screen.getByText('Failed to fetch meal plans')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Try Again' })).toBeInTheDocument();
    });
  });
});
