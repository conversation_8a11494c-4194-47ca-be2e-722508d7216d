"use client";

import React from "react";
import { Dialog, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Clock, Utensils, DollarSign, Flame, Carrot, Beef, Wheat } from "lucide-react";
import { Meal } from "@/types/meal-plan";

interface MealDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  meal: Meal;
  day: string;
  mealType: string;
}

export default function MealDetailsModal({ isOpen, onClose, meal, day, mealType }: MealDetailsModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">{meal.name}</DialogTitle>
          <DialogDescription>
            {new Date(day).toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })} - {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <div className="h-48 md:h-52 bg-muted rounded-lg overflow-hidden mb-4">
              {meal.image ? (
                <img 
                  src={meal.image} 
                  alt={meal.name} 
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <Utensils className="h-12 w-12 text-muted-foreground/50" />
                </div>
              )}
            </div>

            <div className="grid grid-cols-2 gap-2 mb-4">
              <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-lg">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground">Prep Time</p>
                  <p className="text-sm font-medium">{meal.prepTime} mins</p>
                </div>
              </div>
              <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-lg">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground">Estimated Cost</p>
                  <p className="text-sm font-medium">${meal.cost.toFixed(2)}</p>
                </div>
              </div>
              <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-lg">
                <Flame className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground">Calories</p>
                  <p className="text-sm font-medium">{meal.calories} cal</p>
                </div>
              </div>
              <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-lg">
                <Utensils className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground">Servings</p>
                  <p className="text-sm font-medium">{meal.servings}</p>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="font-medium">Nutrition Facts</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Carrot className="h-4 w-4 text-orange-500" />
                    <span className="text-sm">Carbs</span>
                  </div>
                  <span className="text-sm font-medium">{meal.nutrition?.carbs}g</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Beef className="h-4 w-4 text-red-500" />
                    <span className="text-sm">Protein</span>
                  </div>
                  <span className="text-sm font-medium">{meal.nutrition?.protein}g</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Wheat className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm">Fat</span>
                  </div>
                  <span className="text-sm font-medium">{meal.nutrition?.fat}g</span>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-medium mb-2">Ingredients</h3>
            <ul className="space-y-1 mb-6">
              {meal.ingredients.map((ingredient, i) => (
                <li key={i} className="text-sm flex items-start gap-2">
                  <span className="inline-block h-2 w-2 rounded-full bg-primary mt-1.5"></span>
                  {ingredient.quantity} {ingredient.unit} {ingredient.name}
                </li>
              ))}
            </ul>

            <h3 className="font-medium mb-2">Instructions</h3>
            <ol className="space-y-2 list-decimal list-inside">
              {meal.instructions.map((step, i) => (
                <li key={i} className="text-sm pl-1">{step}</li>
              ))}
            </ol>
          </div>
        </div>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}