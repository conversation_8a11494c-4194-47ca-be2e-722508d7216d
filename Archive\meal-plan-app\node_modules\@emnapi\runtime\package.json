{"name": "@emnapi/runtime", "version": "1.4.0", "description": "emnapi runtime", "main": "index.js", "module": "./dist/emnapi.esm-bundler.js", "types": "./dist/emnapi.d.ts", "sideEffects": false, "exports": {".": {"types": {"module": "./dist/emnapi.d.ts", "import": "./dist/emnapi.d.mts", "default": "./dist/emnapi.d.ts"}, "module": "./dist/emnapi.esm-bundler.js", "import": "./dist/emnapi.mjs", "default": "./index.js"}, "./dist/emnapi.cjs.min": {"types": "./dist/emnapi.d.ts", "default": "./dist/emnapi.cjs.min.js"}, "./dist/emnapi.min.mjs": {"types": "./dist/emnapi.d.mts", "default": "./dist/emnapi.min.mjs"}}, "dependencies": {"tslib": "^2.4.0"}, "scripts": {"build": "node ./script/build.js"}, "repository": {"type": "git", "url": "git+https://github.com/toyobayashi/emnapi.git"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/toyobayashi/emnapi/issues"}, "homepage": "https://github.com/toyobayashi/emnapi#readme", "publishConfig": {"access": "public"}}