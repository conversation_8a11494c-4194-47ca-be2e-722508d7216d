'use client';

import { CheckIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
  labels: string[];
}

export function ProgressIndicator({ currentStep, totalSteps, labels }: ProgressIndicatorProps) {
  return (
    <div className="w-full py-4">
      <div className="flex items-center justify-between">
        {Array.from({ length: totalSteps }).map((_, index) => {
          const stepNumber = index + 1;
          const isActive = stepNumber === currentStep;
          const isCompleted = stepNumber < currentStep;
          
          return (
            <div key={stepNumber} className="flex flex-col items-center">
              <div className="relative flex items-center justify-center">
                {/* Line before */}
                {stepNumber > 1 && (
                  <div 
                    className={cn(
                      "absolute right-full w-full h-1 -translate-y-1/2 top-1/2",
                      isCompleted ? "bg-primary" : "bg-muted"
                    )}
                    style={{ width: `${100 / (totalSteps - 1)}vw`, maxWidth: '100px' }}
                  />
                )}
                
                {/* Step circle */}
                <div 
                  className={cn(
                    "flex items-center justify-center w-10 h-10 rounded-full border-2 z-10",
                    isActive ? "border-primary bg-primary text-primary-foreground" : 
                    isCompleted ? "border-primary bg-primary text-primary-foreground" : 
                    "border-muted bg-background text-muted-foreground"
                  )}
                >
                  {isCompleted ? (
                    <CheckIcon className="w-5 h-5" />
                  ) : (
                    <span className="text-sm font-medium">{stepNumber}</span>
                  )}
                </div>
                
                {/* Line after */}
                {stepNumber < totalSteps && (
                  <div 
                    className={cn(
                      "absolute left-full w-full h-1 -translate-y-1/2 top-1/2",
                      isCompleted ? "bg-primary" : "bg-muted"
                    )}
                    style={{ width: `${100 / (totalSteps - 1)}vw`, maxWidth: '100px' }}
                  />
                )}
              </div>
              
              {/* Step label */}
              <span 
                className={cn(
                  "mt-2 text-xs font-medium text-center",
                  isActive ? "text-primary" : 
                  isCompleted ? "text-primary" : 
                  "text-muted-foreground"
                )}
              >
                {labels[index]}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}
