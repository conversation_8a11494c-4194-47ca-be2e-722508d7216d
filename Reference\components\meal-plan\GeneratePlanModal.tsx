"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { Spark<PERSON>, Loader2 } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { generateMealPlan } from "@/lib/meal-plan";
import { useMealPlanStore } from "@/lib/stores/meal-plan-store";
import { toast } from "sonner";

interface GeneratePlanModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function GeneratePlanModal({ isOpen, onClose }: GeneratePlanModalProps) {
  const [budget, setBudget] = useState([50]);
  const [caloriesPerDay, setCaloriesPerDay] = useState([2000]);
  const [dietType, setDietType] = useState("balanced");
  const [preferences, setPreferences] = useState({
    usePantryItems: true,
    optimizeIngredients: true,
    includeLeftovers: true
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const { setMealPlan } = useMealPlanStore();

  const handleGenerate = async () => {
    setIsGenerating(true);
    try {
      const generatedPlan = await generateMealPlan({
        budget: budget[0],
        caloriesPerDay: caloriesPerDay[0],
        dietType,
        preferences
      });
      
      setMealPlan(generatedPlan);
      toast.success("New meal plan generated successfully!");
      onClose();
    } catch (error) {
      console.error("Failed to generate meal plan:", error);
      toast.error("Failed to generate meal plan. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-yellow-500" />
            Generate New Meal Plan
          </DialogTitle>
          <DialogDescription>
            Customize your preferences to generate a personalized weekly meal plan.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="space-y-2">
            <Label htmlFor="budget">Weekly Budget: ${budget[0]}</Label>
            <Slider
              id="budget"
              min={30}
              max={200}
              step={5}
              value={budget}
              onValueChange={setBudget}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>$30</span>
              <span>$200</span>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="calories">Calories per Day: {caloriesPerDay[0]}</Label>
            <Slider
              id="calories"
              min={1200}
              max={3000}
              step={100}
              value={caloriesPerDay}
              onValueChange={setCaloriesPerDay}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>1200 cal</span>
              <span>3000 cal</span>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="diet-type">Diet Type</Label>
            <Select value={dietType} onValueChange={setDietType}>
              <SelectTrigger id="diet-type">
                <SelectValue placeholder="Select diet type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="balanced">Balanced</SelectItem>
                <SelectItem value="vegetarian">Vegetarian</SelectItem>
                <SelectItem value="vegan">Vegan</SelectItem>
                <SelectItem value="high-protein">High Protein</SelectItem>
                <SelectItem value="low-carb">Low Carb</SelectItem>
                <SelectItem value="keto">Keto</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <Label>Optimization Preferences</Label>
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="use-pantry" 
                checked={preferences.usePantryItems}
                onCheckedChange={(checked) => 
                  setPreferences(prev => ({ ...prev, usePantryItems: !!checked }))
                }
              />
              <label
                htmlFor="use-pantry"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Use items from my pantry
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="optimize-ingredients" 
                checked={preferences.optimizeIngredients}
                onCheckedChange={(checked) => 
                  setPreferences(prev => ({ ...prev, optimizeIngredients: !!checked }))
                }
              />
              <label
                htmlFor="optimize-ingredients"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Optimize ingredient usage between meals
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="include-leftovers" 
                checked={preferences.includeLeftovers}
                onCheckedChange={(checked) => 
                  setPreferences(prev => ({ ...prev, includeLeftovers: !!checked }))
                }
              />
              <label
                htmlFor="include-leftovers"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Include leftover meals
              </label>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isGenerating}>
            Cancel
          </Button>
          <Button onClick={handleGenerate} disabled={isGenerating}>
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Generate Plan
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}