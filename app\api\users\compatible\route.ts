import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import prisma from '@/lib/prisma';
import type { Database } from '@/types/supabase';

// Main handler for GET requests
export async function GET(request: Request) {
  try {
    // Initialize Supabase client
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });
    
    // Verify authentication
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the current user's data
    const user = await prisma.users.findUnique({
      where: { id: session.user.id },
      select: {
        dietary_restrictions: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Find compatible users (those without matching dietary restrictions)
    const compatibleUsers = await prisma.users.findMany({
      where: {
        // Exclude the current user
        id: { not: session.user.id },
        // Exclude users with matching dietary restrictions
        NOT: {
          dietary_restrictions: {
            hasSome: user.dietary_restrictions || []
          }
        }
      },
      select: {
        id: true,
        email: true,
        dietary_restrictions: true
      }
    });

    return NextResponse.json({
      users: compatibleUsers,
      count: compatibleUsers.length
    });

  } catch (error) {
    console.error('Error finding compatible users:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' 
          ? error instanceof Error 
            ? error.message 
            : 'Unknown error'
          : undefined
      },
      { status: 500 }
    );
  }
}

// Optional: Add other HTTP methods if needed
export async function HEAD(request: Request) {
  return new NextResponse(null, { status: 405 });
}

export async function POST(request: Request) {
  return new NextResponse(null, { status: 405 });
}

export async function PUT(request: Request) {
  return new NextResponse(null, { status: 405 });
}

export async function DELETE(request: Request) {
  return new NextResponse(null, { status: 405 });
}

