"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Calendar, LayoutGrid, List } from 'lucide-react';

export default function TestPage() {
  const [currentView, setCurrentView] = useState<'calendar' | 'list'>('calendar');
  const [currentLayout, setCurrentLayout] = useState<'grid' | 'list'>('grid');

  return (
    <div className="container py-6 space-y-6">
      <h1 className="text-2xl font-bold">Test Page</h1>
      
      <div className="flex gap-4">
        <Button 
          variant={currentView === 'calendar' ? 'default' : 'outline'} 
          onClick={() => setCurrentView('calendar')}
        >
          <Calendar className="h-4 w-4 mr-2" />
          Calendar
        </Button>
        
        <Button 
          variant={currentView === 'list' ? 'default' : 'outline'} 
          onClick={() => setCurrentView('list')}
        >
          <List className="h-4 w-4 mr-2" />
          List
        </Button>
      </div>
      
      {currentView === 'calendar' && (
        <div className="flex gap-4">
          <Button 
            variant={currentLayout === 'grid' ? 'default' : 'outline'} 
            onClick={() => setCurrentLayout('grid')}
          >
            <LayoutGrid className="h-4 w-4 mr-2" />
            Grid
          </Button>
          
          <Button 
            variant={currentLayout === 'list' ? 'default' : 'outline'} 
            onClick={() => setCurrentLayout('list')}
          >
            <List className="h-4 w-4 mr-2" />
            List
          </Button>
        </div>
      )}
      
      <div className="p-4 border rounded-md">
        <p>Current View: {currentView}</p>
        <p>Current Layout: {currentLayout}</p>
      </div>
    </div>
  );
}
