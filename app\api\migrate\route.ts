import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  return NextResponse.json({ message: 'Migration endpoint is working. Use POST to run migration.' });
}

export async function POST() {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Read the migration file
    const migrationPath = path.join(process.cwd(), 'supabase', 'migrations', '20250628000001_add_missing_tables_and_fixes.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`Executing ${statements.length} SQL statements...`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        console.log(`Executing statement ${i + 1}/${statements.length}`);
        const { error } = await supabase.rpc('exec_sql', { 
          sql_query: statement + ';' 
        });
        
        if (error) {
          console.error(`Error in statement ${i + 1}:`, error);
          // Continue with other statements for non-critical errors
          if (error.message.includes('already exists')) {
            console.log('Object already exists, continuing...');
            continue;
          }
          throw error;
        }
      }
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Migration completed successfully',
      statementsExecuted: statements.length
    });

  } catch (error) {
    console.error('Migration error:', error);
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}
