import { createServerSupabaseClient } from '@/lib/supabase';
import { NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { logger } from '@/lib/logger';

export interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
  };
}

export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number = 400,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export async function withAuth(
  handler: (req: AuthenticatedRequest) => Promise<Response>,
  req: Request
): Promise<Response> {
  try {
    const supabase = createServerSupabaseClient();
    const { data: { session }, error: authError } = await supabase.auth.getSession();

    if (authError || !session) {
      throw new ApiError('Unauthorized', 401);
    }

    // Extend request with user data
    const authenticatedReq = req as AuthenticatedRequest;
    authenticatedReq.user = {
      id: session.user.id,
      email: session.user.email!,
    };

    return await handler(authenticatedReq);
  } catch (error) {
    return handleError(error);
  }
}

export function handleError(error: unknown): Response {
  if (error instanceof ApiError) {
    logger.warn({
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
    });

    return NextResponse.json(
      { error: error.message, code: error.code },
      { status: error.statusCode }
    );
  }

  if (error instanceof ZodError) {
    logger.warn({
      message: 'Validation error',
      errors: error.errors,
    });

    return NextResponse.json(
      { error: 'Validation error', details: error.errors },
      { status: 400 }
    );
  }

  logger.error(error);

  return NextResponse.json(
    { error: 'Internal server error' },
    { status: 500 }
  );
}