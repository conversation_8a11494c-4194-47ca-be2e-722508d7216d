"use client";

import { useState } from "react";
import { 
  CirclePlus, 
  Sparkles, 
  ShoppingCart, 
  MoreVertical, 
  Settings, 
  EllipsisVertical 
} from "lucide-react";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface MealPlanActionsProps {
  onGeneratePlan: () => void;
  onViewShoppingList: () => void;
}

export default function MealPlanActions({ onGeneratePlan, onViewShoppingList }: MealPlanActionsProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleExportPlan = () => {
    toast.success("Meal plan exported successfully");
  };

  return (
    <div className="fixed bottom-6 right-6 z-10 flex flex-col items-end space-y-2">
      {isExpanded && (
        <>
          <Button
            variant="default"
            size="icon"
            className="rounded-full shadow-lg bg-blue-500 hover:bg-blue-600"
            onClick={onGeneratePlan}
          >
            <Sparkles className="h-5 w-5" />
          </Button>
          <Button
            variant="default"
            size="icon"
            className="rounded-full shadow-lg bg-emerald-500 hover:bg-emerald-600"
            onClick={onViewShoppingList}
          >
            <ShoppingCart className="h-5 w-5" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="default"
                size="icon"
                className="rounded-full shadow-lg bg-slate-800 hover:bg-slate-700"
              >
                <EllipsisVertical className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleExportPlan}>Export Plan</DropdownMenuItem>
              <DropdownMenuItem onClick={() => toast.info("Preferences will be available soon")}>
                <Settings className="mr-2 h-4 w-4" />
                Preferences
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </>
      )}
      <Button
        variant="default"
        size="icon"
        className={`rounded-full shadow-lg transition-all duration-200 ${
          isExpanded ? "bg-red-500 hover:bg-red-600 rotate-45" : "bg-primary hover:bg-primary/90"
        }`}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <CirclePlus className="h-6 w-6" />
      </Button>
    </div>
  );
}