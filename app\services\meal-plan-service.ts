'use client';

import { getSupabaseClient, ClientType } from './database-client';
import { shoppingListService } from './shopping-list-service';

export interface MealPlan {
  id: string;
  user_id: string;
  start_date: string;
  end_date: string;
  total_cost: number;
  meal_data: any;
  status: string;
  created_at: string;
  updated_at: string;
  name?: string;
  description?: string;
}

export interface MealPlanCreateParams {
  user_id: string;
  start_date: string;
  end_date: string;
  total_cost: number;
  meal_data: any;
  status?: string;
  name?: string;
  description?: string;
}

export interface MealPlanUpdateParams {
  id: string;
  start_date?: string;
  end_date?: string;
  total_cost?: number;
  meal_data?: any;
  status?: string;
  name?: string;
  description?: string;
}

export interface MealPlanAssignment {
  id: string;
  user_id: string;
  meal_plan_id: string;
  start_date: string;
  end_date: string;
  created_at: string;
  updated_at: string;
}

export interface MealPlanAssignmentParams {
  user_id: string;
  meal_plan_id: string;
  start_date: string;
  end_date: string;
}

/**
 * Service for managing meal plans
 */
export const mealPlanService = {
  /**
   * Get meal plans for a user
   * @param userId User ID (optional, will use current user if not provided)
   * @returns Array of meal plans
   */
  async getMealPlans(userId?: string): Promise<{ data: MealPlan[] | null; error: any }> {
    try {
      // Validate userId if provided
      if (userId && typeof userId !== 'string') {
        console.error('Invalid userId provided:', userId);
        return { data: [], error: 'Invalid user ID format' };
      }

      // Temporarily use ADMIN client to bypass RLS
      console.log('Using ADMIN client to bypass RLS temporarily');
      const client = getSupabaseClient(ClientType.ADMIN);
      if (!client) {
        console.error('Failed to initialize Supabase client');
        return { data: [], error: 'Database connection error' };
      }

      // If userId is not provided, get the current user
      if (!userId) {
        try {
          console.log('No userId provided, getting current user');
          const { data: authData, error: authError } = await client.auth.getUser();

          if (authError) {
            console.error('Auth error in getMealPlans:', authError);
            return { data: [], error: 'Authentication error: ' + authError.message };
          }

          if (!authData || !authData.user) {
            console.warn('No authenticated user found');
            return { data: [], error: 'No authenticated user found' };
          }

          userId = authData.user.id;
          console.log('Using authenticated user ID:', userId);
        } catch (authError: any) {
          console.error('Error getting user:', authError);
          return { data: [], error: 'Error getting user: ' + (authError.message || 'Unknown error') };
        }
      }

      // Now fetch the meal plans with the userId
      try {
        console.log('Fetching meal plans for user ID:', userId);

        // Add timeout for the query
        const timeoutPromise = new Promise<{ data: null; error: string }>((resolve) => {
          setTimeout(() => {
            resolve({ data: null, error: 'Database query timed out after 10 seconds' });
          }, 10000); // 10 second timeout
        });

        // Create the actual query
        console.log('Creating query for meal_plans with user_id:', userId);
        const queryPromise = client
          .from('meal_plans')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false });

        // Race the query against the timeout
        console.log('Executing query...');
        const { data, error } = await Promise.race([queryPromise, timeoutPromise]);
        console.log('Query result:', { dataLength: data ? data.length : 0, error: error ? JSON.stringify(error) : 'null' });

        if (error) {
          console.error('Database error in getMealPlans:', error);

          // Check if this is an RLS permission error
          const errorMessage = typeof error === 'string' ? error : error.message;
          if (errorMessage && errorMessage.includes('permission denied')) {
            return {
              data: [],
              error: 'Database error: permission denied for table meal_plans. This is likely due to incorrect Row Level Security (RLS) policies.'
            };
          }

          return { data: [], error: 'Database error: ' + (typeof error === 'string' ? error : error.message) };
        }

        // Check if data is null or undefined and return empty array instead
        if (!data) {
          console.log('No meal plans found for user');
          return { data: [], error: null };
        }

        console.log(`Successfully fetched ${data.length} meal plans`);
        return { data, error: null };
      } catch (dbError: any) {
        console.error('Error querying meal_plans table:', dbError);
        return { data: [], error: 'Error querying meal plans: ' + (dbError.message || 'Unknown error') };
      }
    } catch (error: any) {
      console.error('Error fetching meal plans:', error);
      return { data: [], error: 'Error fetching meal plans: ' + (error.message || 'Unknown error') };
    }
  },

  /**
   * Get a meal plan by ID
   * @param id Meal plan ID
   * @returns Meal plan
   */
  async getMealPlanById(id: string): Promise<{ data: MealPlan | null; error: any }> {
    try {
      // Temporarily use ADMIN client to bypass RLS
      console.log('Using ADMIN client to bypass RLS temporarily for getMealPlanById');
      const client = getSupabaseClient(ClientType.ADMIN);

      const { data, error } = await client
        .from('meal_plans')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Database error in getMealPlanById:', error);
        throw error;
      }

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching meal plan:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch meal plan'
      };
    }
  },

  /**
   * Create a new meal plan
   * @param params Meal plan parameters
   * @returns Created meal plan
   */
  async createMealPlan(params: MealPlanCreateParams): Promise<{ data: MealPlan | null; error: any }> {
    try {
      // Temporarily use ADMIN client to bypass RLS
      console.log('Using ADMIN client to bypass RLS temporarily for meal plan creation');
      const client = getSupabaseClient(ClientType.ADMIN);

      // Set default status if not provided
      if (!params.status) {
        params.status = 'active';
      }

      // Try to use the direct API endpoint instead of client-side Supabase
      try {
        console.log('Attempting to save meal plan using direct API...');
        const directResponse = await fetch('/api/direct-mealplan', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user_id: params.user_id,
            start_date: params.start_date,
            end_date: params.end_date,
            total_cost: params.total_cost,
            meal_data: params.meal_data,
            status: params.status || 'active'
          }),
        });

        if (directResponse.ok) {
          const result = await directResponse.json();
          console.log('Meal plan saved successfully using direct API');
          return { data: result.data, error: null };
        } else {
          // If direct API fails, try the regular Supabase client
          console.log('Direct API failed, trying regular Supabase client');
        }
      } catch (directError) {
        console.error('Error using direct API:', directError);
        // Continue with regular Supabase client
      }

      // Regular Supabase client approach
      const { data, error } = await client
        .from('meal_plans')
        .insert(params)
        .select()
        .single();

      if (error) {
        console.error('Database error in createMealPlan:', error);
        throw error;
      }

      return { data, error: null };
    } catch (error: any) {
      console.error('Error creating meal plan:', error);
      return {
        data: null,
        error: error.message || 'Failed to create meal plan'
      };
    }
  },

  /**
   * Update a meal plan
   * @param params Meal plan update parameters
   * @returns Updated meal plan
   */
  async updateMealPlan(params: MealPlanUpdateParams): Promise<{ data: MealPlan | null; error: any }> {
    try {
      const client = getSupabaseClient(ClientType.USER);

      const { id, ...updateData } = params;

      const { data, error } = await client
        .from('meal_plans')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Database error in updateMealPlan:', error);
        throw error;
      }

      return { data, error: null };
    } catch (error: any) {
      console.error('Error updating meal plan:', error);
      return {
        data: null,
        error: error.message || 'Failed to update meal plan'
      };
    }
  },

  /**
   * Delete a meal plan
   * @param id Meal plan ID
   * @returns Success status
   */
  async deleteMealPlan(id: string): Promise<{ success: boolean; error: any }> {
    try {
      // Use admin client to bypass RLS policies
      const client = getSupabaseClient(ClientType.ADMIN);

      // Check if we're in development mode with auth bypass
      const isDevelopmentWithBypass = process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_BYPASS_AUTH === 'true';

      let userId = null;

      if (!isDevelopmentWithBypass) {
        // Get the current user to verify ownership
        try {
          const userClient = getSupabaseClient(ClientType.USER);
          const { data: { user }, error: userError } = await userClient.auth.getUser();

          if (userError || !user) {
            console.error('Auth error in deleteMealPlan:', userError);
            return {
              success: false,
              error: 'Authentication error. Please log in and try again.'
            };
          }

          userId = user.id;
        } catch (authError) {
          console.error('Error getting user:', authError);
          return {
            success: false,
            error: 'Authentication error. Please log in and try again.'
          };
        }
      }

      // In development mode with auth bypass, we can delete any meal plan
      if (!isDevelopmentWithBypass) {
        // First, verify that the meal plan belongs to the current user
        try {
          const { data: mealPlan, error: fetchError } = await client
            .from('meal_plans')
            .select('user_id')
            .eq('id', id)
            .single();

          if (fetchError) {
            console.error('Error fetching meal plan:', fetchError);
            return {
              success: false,
              error: 'Meal plan not found or you do not have permission to delete it.'
            };
          }

          // Check if the meal plan belongs to the current user
          if (mealPlan.user_id !== userId) {
            console.error('User does not own this meal plan');
            return {
              success: false,
              error: 'You do not have permission to delete this meal plan.'
            };
          }
        } catch (fetchError) {
          console.error('Error fetching meal plan:', fetchError);
          return {
            success: false,
            error: 'Error verifying meal plan ownership.'
          };
        }
      }

      // Delete the meal plan
      try {
        const { error } = await client
          .from('meal_plans')
          .delete()
          .eq('id', id);

        if (error) {
          console.error('Database error in deleteMealPlan:', error);
          throw error;
        }

        return { success: true, error: null };
      } catch (deleteError) {
        console.error('Error deleting meal plan:', deleteError);
        return {
          success: false,
          error: deleteError instanceof Error ? deleteError.message : 'Failed to delete meal plan'
        };
      }
    } catch (error: any) {
      console.error('Error in deleteMealPlan:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete meal plan'
      };
    }
  },

  /**
   * Assign a meal plan to a specific date range in the calendar
   * @param params Meal plan assignment parameters
   * @returns Created assignment
   */
  async assignMealPlanToCalendar(params: MealPlanAssignmentParams): Promise<{ data: MealPlanAssignment | null; error: any }> {
    try {
      const client = getSupabaseClient(ClientType.USER);

      // Verify authentication before proceeding
      try {
        const { data: authData, error: authError } = await client.auth.getUser();

        if (authError) {
          console.error('Auth error in assignMealPlanToCalendar:', authError);
          return {
            data: null,
            error: 'Authentication error. Please log in and try again.'
          };
        }

        if (!authData.user) {
          console.warn('No authenticated user found');
          return {
            data: null,
            error: 'Authentication error. Please log in and try again.'
          };
        }

        // Ensure the user_id in params matches the authenticated user
        if (params.user_id !== authData.user.id) {
          params.user_id = authData.user.id; // Use the authenticated user's ID
        }
      } catch (authError) {
        console.error('Error getting user:', authError);
        return {
          data: null,
          error: 'Authentication error. Please log in and try again.'
        };
      }

      // Check if there's already an assignment for this date range
      try {
        const { data: existingAssignments, error: checkError } = await client
          .from('meal_plan_assignments')
          .select('*')
          .eq('user_id', params.user_id)
          .or(`start_date.lte.${params.end_date},end_date.gte.${params.start_date}`);

        if (checkError) {
          console.error('Database error checking existing assignments:', checkError);
          return {
            data: null,
            error: 'Error checking existing assignments. Please try again.'
          };
        }

        if (existingAssignments && existingAssignments.length > 0) {
          return {
            data: null,
            error: 'There is already a meal plan assigned to this date range'
          };
        }
      } catch (checkError) {
        console.error('Error checking existing assignments:', checkError);
        return {
          data: null,
          error: 'Error checking existing assignments. Please try again.'
        };
      }

      // Create the assignment
      try {
        const { data, error } = await client
          .from('meal_plan_assignments')
          .insert(params)
          .select()
          .single();

        if (error) {
          console.error('Database error in assignMealPlanToCalendar:', error);
          return {
            data: null,
            error: 'Error creating assignment. Please try again.'
          };
        }

        return { data, error: null };
      } catch (insertError) {
        console.error('Error inserting assignment:', insertError);
        return {
          data: null,
          error: 'Error creating assignment. Please try again.'
        };
      }
    } catch (error: any) {
      console.error('Error assigning meal plan to calendar:', error);
      return {
        data: null,
        error: 'An unexpected error occurred. Please try again.'
      };
    }
  },

  /**
   * Get meal plan assignments for a user
   * @param userId User ID (optional, will use current user if not provided)
   * @returns Array of meal plan assignments
   */
  async getMealPlanAssignments(userId?: string): Promise<{ data: MealPlanAssignment[] | null; error: any }> {
    try {
      const client = getSupabaseClient(ClientType.USER);

      // If userId is not provided, get the current user
      if (!userId) {
        try {
          const { data: authData, error: authError } = await client.auth.getUser();

          if (authError) {
            console.error('Auth error in getMealPlanAssignments:', authError);
            return { data: [], error: null }; // Return empty data instead of throwing
          }

          if (!authData.user) {
            console.warn('No authenticated user found');
            return { data: [], error: null }; // Return empty data instead of throwing
          }

          userId = authData.user.id;
        } catch (authError) {
          console.error('Error getting user:', authError);
          return { data: [], error: null }; // Return empty data instead of throwing
        }
      }

      // First, check if the meal_plan_assignments table exists
      try {
        // Use a safer approach - check if the table exists first
        const { data: tableExists, error: tableCheckError } = await client
          .from('information_schema.tables')
          .select('table_name')
          .eq('table_schema', 'public')
          .eq('table_name', 'meal_plan_assignments')
          .single();

        if (tableCheckError || !tableExists) {
          console.warn('meal_plan_assignments table does not exist yet');
          return { data: [], error: null }; // Return empty array if table doesn't exist
        }

        // If we get here, the table exists, so we can query it
        const { data, error } = await client
          .from('meal_plan_assignments')
          .select('*, meal_plans(*)')
          .eq('user_id', userId)
          .order('start_date', { ascending: true });

        if (error) {
          // Log the error but don't expose it to the client
          console.error('Database error in getMealPlanAssignments:', error);
          return { data: [], error: null }; // Return empty data instead of exposing the error
        }

        return { data: data || [], error: null };
      } catch (dbError) {
        // This is a fallback for any unexpected errors
        console.error('Error querying meal_plan_assignments table:', dbError);
        return { data: [], error: null }; // Return empty data instead of throwing
      }
    } catch (error: any) {
      console.error('Error fetching meal plan assignments:', error);
      return {
        data: [],
        error: null
      };
    }
  },

  /**
   * Remove a meal plan assignment from the calendar
   * @param id Assignment ID
   * @returns Success status
   */
  async removeMealPlanAssignment(id: string): Promise<{ success: boolean; error: any }> {
    try {
      const client = getSupabaseClient(ClientType.USER);

      const { error } = await client
        .from('meal_plan_assignments')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Database error in removeMealPlanAssignment:', error);
        throw error;
      }

      return { success: true, error: null };
    } catch (error: any) {
      console.error('Error removing meal plan assignment:', error);
      return {
        success: false,
        error: error.message || 'Failed to remove meal plan assignment'
      };
    }
  },

  /**
   * Duplicate a meal plan
   * @param id Meal plan ID to duplicate
   * @param newName Optional new name for the duplicated plan
   * @returns Duplicated meal plan
   */
  async duplicateMealPlan(id: string, newName?: string): Promise<{ data: MealPlan | null; error: any }> {
    try {
      const client = getSupabaseClient(ClientType.USER);

      // First, get the meal plan to duplicate
      const { data: originalPlan, error: fetchError } = await this.getMealPlanById(id);

      if (fetchError || !originalPlan) {
        throw new Error(fetchError || 'Meal plan not found');
      }

      // Get current user
      const { data: authData, error: authError } = await client.auth.getUser();

      if (authError || !authData.user) {
        throw new Error('Authentication error: ' + (authError?.message || 'No authenticated user found'));
      }

      // Create a new plan based on the original
      const newPlan: MealPlanCreateParams = {
        user_id: authData.user.id,
        start_date: originalPlan.start_date,
        end_date: originalPlan.end_date,
        total_cost: originalPlan.total_cost,
        meal_data: originalPlan.meal_data,
        status: 'active',
        name: newName || `${originalPlan.name || 'Meal Plan'} (Copy)`,
        description: originalPlan.description
      };

      // Create the new plan
      const { data, error } = await this.createMealPlan(newPlan);

      if (error) {
        throw new Error(error);
      }

      return { data, error: null };
    } catch (error: any) {
      console.error('Error duplicating meal plan:', error);
      return {
        data: null,
        error: error.message || 'Failed to duplicate meal plan'
      };
    }
  },

  /**
   * Archive a meal plan
   * @param id Meal plan ID
   * @returns Updated meal plan
   */
  async archiveMealPlan(id: string): Promise<{ data: MealPlan | null; error: any }> {
    try {
      return await this.updateMealPlan({
        id,
        status: 'archived'
      });
    } catch (error: any) {
      console.error('Error archiving meal plan:', error);
      return {
        data: null,
        error: error.message || 'Failed to archive meal plan'
      };
    }
  },

  /**
   * Restore an archived meal plan
   * @param id Meal plan ID
   * @returns Updated meal plan
   */
  async restoreMealPlan(id: string): Promise<{ data: MealPlan | null; error: any }> {
    try {
      return await this.updateMealPlan({
        id,
        status: 'active'
      });
    } catch (error: any) {
      console.error('Error restoring meal plan:', error);
      return {
        data: null,
        error: error.message || 'Failed to restore meal plan'
      };
    }
  },

  /**
   * Generate a shopping list from a meal plan
   * @param mealPlanId Meal plan ID
   * @returns Created shopping list with items
   */
  async generateShoppingList(mealPlanId: string): Promise<{ data: any | null; error: any }> {
    try {
      // Use the shopping list service to generate a shopping list from the meal plan
      return await shoppingListService.generateFromMealPlan(mealPlanId);
    } catch (error: any) {
      console.error('Error generating shopping list:', error);
      return { data: null, error: error.message || 'Failed to generate shopping list' };
    }
  },


};

// Helper functions moved to shopping-list-service.ts
