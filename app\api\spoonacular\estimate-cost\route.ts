import { NextRequest, NextResponse } from 'next/server';
import { estimateRecipeCost } from '@/lib/api/spoonacular';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { ingredients } = body;
    
    if (!ingredients || !Array.isArray(ingredients)) {
      return NextResponse.json(
        { error: 'Invalid request. Expected an array of ingredients.' },
        { status: 400 }
      );
    }
    
    const cost = await estimateRecipeCost(ingredients);

    return NextResponse.json(cost);
  } catch (error: any) {
    console.error('Error in Spoonacular estimate cost API route:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while estimating the cost' },
      { status: 500 }
    );
  }
}
