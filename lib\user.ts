// User Utilities and Types
export interface User {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  created_at: Date;
  updated_at: Date;
}

export interface UserProfile {
  id: string;
  user_id: string;
  first_name?: string;
  last_name?: string;
  bio?: string;
  location?: string;
  website?: string;
  phone?: string;
  date_of_birth?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface UserPreferences {
  id: string;
  user_id: string;
  cooking_skill_level: 'beginner' | 'intermediate' | 'advanced';
  max_cooking_time: number;
  preferred_cuisines: string[];
  excluded_ingredients: string[];
  notification_preferences: {
    email: boolean;
    push: boolean;
    meal_reminders: boolean;
  };
  privacy_settings: {
    data_sharing: boolean;
    analytics: boolean;
  };
  units_preference: 'metric' | 'imperial';
  currency_preference: string;
  created_at: Date;
  updated_at: Date;
}

// Utility functions
export function getFullName(profile: UserProfile): string {
  if (profile.first_name && profile.last_name) {
    return `${profile.first_name} ${profile.last_name}`;
  }
  if (profile.first_name) {
    return profile.first_name;
  }
  if (profile.last_name) {
    return profile.last_name;
  }
  return 'User';
}

export function getInitials(profile: UserProfile): string {
  const firstName = profile.first_name?.charAt(0).toUpperCase() || '';
  const lastName = profile.last_name?.charAt(0).toUpperCase() || '';
  return firstName + lastName || 'U';
}

export function formatUserLocation(profile: UserProfile): string | null {
  return profile.location || null;
}

export function isProfileComplete(profile: UserProfile): boolean {
  return !!(
    profile.first_name &&
    profile.last_name &&
    profile.bio
  );
}

export function calculateProfileCompleteness(profile: UserProfile): number {
  const fields = [
    'first_name',
    'last_name',
    'bio',
    'location',
    'website',
    'phone',
    'date_of_birth'
  ];
  
  const completedFields = fields.filter(field => {
    const value = profile[field as keyof UserProfile];
    return value !== null && value !== undefined && value !== '';
  });
  
  return Math.round((completedFields.length / fields.length) * 100);
}

export function formatPhoneNumber(phone: string): string {
  // Simple phone number formatting for US numbers
  const cleaned = phone.replace(/\D/g, '');
  
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  }
  
  return phone;
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePhoneNumber(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  return phoneRegex.test(phone);
}

export function getDefaultPreferences(): Omit<UserPreferences, 'id' | 'user_id' | 'created_at' | 'updated_at'> {
  return {
    cooking_skill_level: 'intermediate',
    max_cooking_time: 60,
    preferred_cuisines: [],
    excluded_ingredients: [],
    notification_preferences: {
      email: true,
      push: true,
      meal_reminders: true,
    },
    privacy_settings: {
      data_sharing: false,
      analytics: true,
    },
    units_preference: 'metric',
    currency_preference: 'USD',
  };
}

export function formatCookingSkillLevel(level: string): string {
  switch (level) {
    case 'beginner':
      return 'Beginner';
    case 'intermediate':
      return 'Intermediate';
    case 'advanced':
      return 'Advanced';
    default:
      return 'Unknown';
  }
}

export function formatCookingTime(minutes: number): string {
  if (minutes < 60) {
    return `${minutes} minutes`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours} hour${hours > 1 ? 's' : ''}`;
  }
  
  return `${hours}h ${remainingMinutes}m`;
}

export function getUserAvatarUrl(user: User, profile?: UserProfile): string {
  if (user.avatar_url) {
    return user.avatar_url;
  }
  
  // Generate a default avatar URL using the user's initials
  const initials = profile ? getInitials(profile) : user.email.charAt(0).toUpperCase();
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=random`;
}

export function canEditProfile(currentUserId: string, profileUserId: string): boolean {
  return currentUserId === profileUserId;
}

export function sanitizeUserInput(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}
