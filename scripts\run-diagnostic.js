// This script runs the database diagnostic and saves the results to a file
// Usage: node scripts/run-diagnostic.js

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Create the results directory if it doesn't exist
const resultsDir = path.join(__dirname, '..', 'supabase', 'diagnostic', 'results');
if (!fs.existsSync(resultsDir)) {
  fs.mkdirSync(resultsDir, { recursive: true });
}

// Get the current timestamp
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

// Path to the diagnostic SQL file
const sqlFilePath = path.join(__dirname, '..', 'supabase', 'diagnostic', 'check_database.sql');

// Path to the output file
const outputFilePath = path.join(resultsDir, `diagnostic_results_${timestamp}.txt`);

console.log('Running database diagnostic...');
console.log(`SQL file: ${sqlFilePath}`);
console.log(`Output file: ${outputFilePath}`);

try {
  // Check if we're running in a development environment with Supabase CLI
  const hasSupabaseCli = checkSupabaseCli();
  
  if (hasSupabaseCli) {
    // Run the diagnostic using Supabase CLI
    console.log('Using Supabase CLI to run diagnostic...');
    const output = execSync(`supabase db execute --file "${sqlFilePath}"`, { encoding: 'utf8' });
    fs.writeFileSync(outputFilePath, output);
  } else {
    // Run the diagnostic using the API endpoint
    console.log('Using API endpoint to run diagnostic...');
    console.log('Please start the development server and navigate to:');
    console.log('http://localhost:3000/diagnostic');
    
    // Write instructions to the output file
    fs.writeFileSync(outputFilePath, `
Database Diagnostic Instructions
===============================

To run the diagnostic, please:

1. Start the development server:
   npm run dev

2. Navigate to:
   http://localhost:3000/diagnostic

3. Click the "Run Diagnostic" button

The results will be displayed in the browser and saved to:
${resultsDir}
    `);
  }
  
  console.log('Diagnostic complete!');
  console.log(`Results saved to: ${outputFilePath}`);
} catch (error) {
  console.error('Error running diagnostic:', error.message);
  console.error('Please try running the diagnostic using the web interface:');
  console.error('1. Start the development server: npm run dev');
  console.error('2. Navigate to: http://localhost:3000/diagnostic');
  process.exit(1);
}

// Function to check if Supabase CLI is available
function checkSupabaseCli() {
  try {
    execSync('supabase --version', { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}
