'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Calendar, List, Filter, SortAsc, SortDesc, Star, Clock, DollarSign, Utensils } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

interface MealPlanTabsProps {
  listView: React.ReactNode;
  calendarView: React.ReactNode;
  onSearch: (query: string) => void;
  onFilter: (filters: any) => void;
  onSort?: (sortBy: string, direction: 'asc' | 'desc') => void;
  activeFilters?: string[];
  activeSortBy?: string;
  activeSortDirection?: 'asc' | 'desc';
  onClearFilters?: () => void;
  totalPlans?: number;
}

export function MealPlanTabs({
  listView,
  calendarView,
  onSearch,
  onFilter,
  onSort,
  activeFilters = [],
  activeSortBy = 'created_at',
  activeSortDirection = 'desc',
  onClearFilters,
  totalPlans = 0
}: MealPlanTabsProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTab, setSelectedTab] = useState('list');

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    onSearch(query);
  };

  const handleFilterSelect = (filterType: string, value: string) => {
    onFilter({ type: filterType, value });
  };

  const handleSortSelect = (sortBy: string) => {
    if (onSort) {
      // If clicking the same sort option, toggle direction
      const direction = sortBy === activeSortBy && activeSortDirection === 'desc' ? 'asc' : 'desc';
      onSort(sortBy, direction);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="relative w-full sm:w-auto sm:min-w-[300px]">
          <Input
            placeholder="Search meal plans..."
            value={searchQuery}
            onChange={handleSearch}
            className="pl-10"
          />
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="11" cy="11" r="8" />
              <path d="m21 21-4.3-4.3" />
            </svg>
          </div>
        </div>

        <div className="flex items-center gap-2 w-full sm:w-auto justify-end">
          {activeFilters.length > 0 && (
            <div className="flex items-center gap-2 mr-2">
              {activeFilters.map((filter) => (
                <Badge key={filter} variant="secondary">
                  {filter}
                </Badge>
              ))}
              {onClearFilters && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClearFilters}
                  className="h-6 px-2 text-xs"
                >
                  Clear
                </Button>
              )}
            </div>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Filter By</DropdownMenuLabel>
              <DropdownMenuSeparator />

              <DropdownMenuGroup>
                <DropdownMenuLabel className="text-xs font-normal px-2 py-1.5 text-muted-foreground">Status</DropdownMenuLabel>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.includes('Active')}
                  onClick={() => handleFilterSelect('status', 'active')}
                >
                  Active
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.includes('Archived')}
                  onClick={() => handleFilterSelect('status', 'archived')}
                >
                  Archived
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.includes('Draft')}
                  onClick={() => handleFilterSelect('status', 'draft')}
                >
                  Draft
                </DropdownMenuCheckboxItem>
              </DropdownMenuGroup>

              <DropdownMenuSeparator />

              <DropdownMenuGroup>
                <DropdownMenuLabel className="text-xs font-normal px-2 py-1.5 text-muted-foreground">Date Range</DropdownMenuLabel>
                <DropdownMenuRadioGroup value={activeFilters.find(f => f.includes('Week') || f.includes('Month')) || ''}>
                  <DropdownMenuRadioItem value="This Week" onClick={() => handleFilterSelect('date', 'this-week')}>
                    This Week
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="Next Week" onClick={() => handleFilterSelect('date', 'next-week')}>
                    Next Week
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="This Month" onClick={() => handleFilterSelect('date', 'this-month')}>
                    This Month
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="Last 30 Days" onClick={() => handleFilterSelect('date', 'last-30-days')}>
                    Last 30 Days
                  </DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuGroup>

              <DropdownMenuSeparator />

              <DropdownMenuGroup>
                <DropdownMenuLabel className="text-xs font-normal px-2 py-1.5 text-muted-foreground">Budget</DropdownMenuLabel>
                <DropdownMenuRadioGroup value={activeFilters.find(f => f.includes('$')) || ''}>
                  <DropdownMenuRadioItem value="Under $50" onClick={() => handleFilterSelect('budget', 'under50')}>
                    Under $50
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="$50 - $100" onClick={() => handleFilterSelect('budget', '50to100')}>
                    $50 - $100
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="Over $100" onClick={() => handleFilterSelect('budget', 'over100')}>
                    Over $100
                  </DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuGroup>

              <DropdownMenuSeparator />

              <DropdownMenuGroup>
                <DropdownMenuLabel className="text-xs font-normal px-2 py-1.5 text-muted-foreground">Tags</DropdownMenuLabel>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.includes('Favorites')}
                  onClick={() => handleFilterSelect('tag', 'favorite')}
                >
                  Favorites
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.includes('Family')}
                  onClick={() => handleFilterSelect('tag', 'family')}
                >
                  Family
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={activeFilters.includes('Budget-Friendly')}
                  onClick={() => handleFilterSelect('tag', 'budget')}
                >
                  Budget-Friendly
                </DropdownMenuCheckboxItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                {activeSortDirection === 'asc' ? <SortAsc className="h-4 w-4 mr-2" /> : <SortDesc className="h-4 w-4 mr-2" />}
                Sort
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Sort by</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuRadioGroup value={activeSortBy}>
                <DropdownMenuRadioItem value="created_at" onClick={() => handleSortSelect('created_at')}>
                  <Calendar className="h-4 w-4 mr-2" />
                  Date Created {activeSortBy === 'created_at' && (activeSortDirection === 'asc' ? '(Oldest first)' : '(Newest first)')}
                </DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="total_cost" onClick={() => handleSortSelect('total_cost')}>
                  <DollarSign className="h-4 w-4 mr-2" />
                  Budget {activeSortBy === 'total_cost' && (activeSortDirection === 'asc' ? '(Low to High)' : '(High to Low)')}
                </DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="total_meals" onClick={() => handleSortSelect('total_meals')}>
                  <Utensils className="h-4 w-4 mr-2" />
                  Meal Count {activeSortBy === 'total_meals' && (activeSortDirection === 'asc' ? '(Low to High)' : '(High to Low)')}
                </DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="prep_time" onClick={() => handleSortSelect('prep_time')}>
                  <Clock className="h-4 w-4 mr-2" />
                  Prep Time {activeSortBy === 'prep_time' && (activeSortDirection === 'asc' ? '(Low to High)' : '(High to Low)')}
                </DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Tabs
        defaultValue="list"
        className="w-full"
        value={selectedTab}
        onValueChange={(value) => setSelectedTab(value)}
      >
        <div className="flex justify-between items-center mb-4">
          <TabsList className="grid w-full max-w-md grid-cols-2">
            <TabsTrigger value="list" className="flex items-center">
              <List className="h-4 w-4 mr-2" />
              List View
            </TabsTrigger>
            <TabsTrigger value="calendar" className="flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              Calendar View
            </TabsTrigger>
          </TabsList>

          <div className="text-sm text-muted-foreground">
            {totalPlans} meal plan{totalPlans !== 1 ? 's' : ''}
          </div>
        </div>

        <TabsContent value="list" className="mt-0">
          {listView}
        </TabsContent>

        <TabsContent value="calendar" className="mt-0">
          {calendarView}
        </TabsContent>
      </Tabs>
    </div>
  );
}
