import { NextResponse } from "next/server";
import OpenAI from 'openai';
import { mealPlanService, ClientType, getSupabaseClient } from '@/app/services/database';
import { formSchema } from '@/app/meal-plan/generate/schema';
import { z } from 'zod';

// Define a schema for validating the OpenAI response
const mealPlanResponseSchema = z.object({
  mealPlan: z.object({
    week: z.array(
      z.object({
        date: z.string(),
        meals: z.array(
          z.object({
            type: z.string(),
            recipeId: z.string(),
            name: z.string(),
            image: z.string(),
            prepTime: z.number(),
            cost: z.number(),
            servings: z.number(),
            nutrition: z.object({
              calories: z.number(),
              protein: z.number(),
              carbs: z.number(),
              fats: z.number()
            })
          })
        )
      })
    ),
    summary: z.object({
      totalCost: z.number(),
      averageCalories: z.number(),
      macros: z.object({
        protein: z.string(),
        carbs: z.string(),
        fats: z.string()
      })
    })
  })
});

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: Request) {
  try {
    // Get the route handler client for authentication
    const supabase = getSupabaseClient(ClientType.ROUTE);

    // Get the current user
    let user;

    // In development mode, we can bypass authentication for testing
    if (process.env.BYPASS_AUTH === 'true') {
      console.log('Development mode: Bypassing authentication check');
      // Use a valid UUID for the test user
      user = { id: '00000000-0000-0000-0000-000000000000' };
    } else {
      const { data, error: authError } = await supabase.auth.getUser();

      if (authError || !data.user) {
        console.error('Authentication error:', authError || 'No user found');
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        );
      }

      user = data.user;
    }

    // Parse and validate the request body
    let body;
    try {
      body = await request.json();
      formSchema.parse(body);
    } catch (e) {
      console.error('Request validation error:', e);
      return NextResponse.json(
        {
          error: "Invalid request data",
          details: e instanceof Error ? e.message : 'Unknown validation error'
        },
        { status: 400 }
      );
    }

    // Fetch recipes from Supabase
    let recipes: { id: string; name: string; nutrition: any; prep_time: number; cost_per_serving: number; image_url: string }[] = [];
    try {
      // Get admin client to bypass RLS
      const adminClient = getSupabaseClient(ClientType.ADMIN);

      // First, check if the user has any recipes
      const { data, error } = await adminClient
        .from('recipes')
        .select('id, name, nutrition, prep_time, cost_per_serving, image_url')
        .eq('user_id', user.id);

      if (error) {
        console.warn('Error fetching recipes:', error);
      } else if (!data || data.length === 0) {
        // If no recipes found, seed sample recipes
        console.log('No recipes found, seeding sample recipes');

        try {
          // Call the seed-recipes endpoint
          const seedResponse = await fetch(new URL('/api/seed-recipes', request.url).toString(), {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ userId: user.id }),
          });

          const seedResult = await seedResponse.json();

          if (!seedResponse.ok) {
            console.error('Error seeding recipes:', seedResult);
          } else {
            console.log('Recipes seeded successfully:', seedResult);

            // Fetch the newly seeded recipes
            const { data: seededData, error: seededError } = await adminClient
              .from('recipes')
              .select('id, name, nutrition, prep_time, cost_per_serving, image_url')
              .eq('user_id', user.id);

            if (seededError) {
              console.error('Error fetching seeded recipes:', seededError);
            } else {
              recipes = seededData || [];
            }
          }
        } catch (seedError) {
          console.error('Error calling seed-recipes endpoint:', seedError);
        }
      } else {
        recipes = data;
      }
    } catch (error) {
      // Log the error but don't fail the request
      console.warn('Unexpected error fetching recipes:', error);
    }

    // Use real recipe IDs if available, otherwise use fallback IDs
    const recipeIds = recipes && recipes.length > 0
      ? recipes.map(recipe => recipe.id)
      : ['1', '2', '3', '4', '5'];

    // Create default recipe details if no recipes are found
    let recipeDetails: Record<string, any> = {};
    let recipeDetailsText = '';

    if (recipes && recipes.length > 0) {
      // Create a map of recipe details for the prompt
      recipeDetails = recipes.reduce((acc, recipe) => {
        acc[recipe.id] = {
          name: recipe.name,
          nutrition: recipe.nutrition || { calories: 300, protein: 15, carbs: 40, fat: 10 },
          prepTime: recipe.prep_time || 15,
          cost: recipe.cost_per_serving || 3.5,
          imageUrl: recipe.image_url || 'https://images.unsplash.com/photo-1484723091739-30a097e8f929'
        };
        return acc;
      }, {} as Record<string, any>);

      // Format recipe details for the prompt
      recipeDetailsText = Object.entries(recipeDetails)
        .map(([id, details]) => {
          return `Recipe ID: ${id}
` +
            `Name: ${details.name}
` +
            `Prep Time: ${details.prepTime} minutes
` +
            `Cost per Serving: $${details.cost}
` +
            `Nutrition: Calories: ${details.nutrition.calories}, Protein: ${details.nutrition.protein}g, ` +
            `Carbs: ${details.nutrition.carbs}g, Fat: ${details.nutrition.fat}g`;
        })
        .join('\n\n');
    } else {
      // Create default recipe details
      const defaultRecipes = [
        {
          id: '1',
          name: 'Greek Yogurt Parfait',
          nutrition: { calories: 320, protein: 20, carbs: 45, fat: 8 },
          prepTime: 10,
          cost: 3.50,
          imageUrl: 'https://images.unsplash.com/photo-1484723091739-30a097e8f929'
        },
        {
          id: '2',
          name: 'Caprese Salad',
          nutrition: { calories: 250, protein: 12, carbs: 10, fat: 18 },
          prepTime: 15,
          cost: 3.25,
          imageUrl: 'https://images.unsplash.com/photo-1592417817098-8fd3d9eb14a5'
        },
        {
          id: '3',
          name: 'Vegetable Stir-Fry',
          nutrition: { calories: 380, protein: 15, carbs: 55, fat: 12 },
          prepTime: 20,
          cost: 4.50,
          imageUrl: 'https://images.unsplash.com/photo-1512058564366-18510be2db19'
        },
        {
          id: '4',
          name: 'Pesto Pasta',
          nutrition: { calories: 420, protein: 12, carbs: 60, fat: 16 },
          prepTime: 15,
          cost: 3.75,
          imageUrl: 'https://images.unsplash.com/photo-1551892374-ecf8754cf8b0'
        },
        {
          id: '5',
          name: 'Avocado Toast',
          nutrition: { calories: 280, protein: 8, carbs: 25, fat: 18 },
          prepTime: 10,
          cost: 2.50,
          imageUrl: 'https://images.unsplash.com/photo-1525351484163-7529414344d8'
        }
      ];

      // Add default recipes to recipeDetails
      defaultRecipes.forEach(recipe => {
        recipeDetails[recipe.id] = recipe;
      });

      // Format recipe details for the prompt
      recipeDetailsText = defaultRecipes
        .map(recipe => {
          return `Recipe ID: ${recipe.id}
` +
            `Name: ${recipe.name}
` +
            `Prep Time: ${recipe.prepTime} minutes
` +
            `Cost per Serving: $${recipe.cost}
` +
            `Nutrition: Calories: ${recipe.nutrition.calories}, Protein: ${recipe.nutrition.protein}g, ` +
            `Carbs: ${recipe.nutrition.carbs}g, Fat: ${recipe.nutrition.fat}g`;
        })
        .join('\n\n');
    }

    // OpenAI API call with retry logic
    let completion;
    let retryCount = 0;
    const maxRetries = 3;
    const baseDelay = 1000; // 1 second

    while (retryCount <= maxRetries) {
      try {
        console.log(`Attempt ${retryCount + 1} to generate meal plan with OpenAI`);

        completion = await openai.chat.completions.create({
          model: "gpt-3.5-turbo",
          messages: [
            {
              role: "system",
              content: `You are a professional nutritionist and meal planner. Generate a 7-day meal plan with exactly 3 meals per day (breakfast, lunch, dinner).
              Each meal should include complete details including nutritional information and costs.
              The meal plan should be varied, nutritionally balanced, and fit within the specified budget.
              Ensure recipes are appropriate for the specified cooking skill level and prep time.

              Pay special attention to the user's dietary preferences, restrictions, and allergens. Do not include any ingredients that the user has specified as allergens or exclusions.
              If the user has specified dietary restrictions like vegetarian, vegan, etc., ensure all meals comply with these restrictions.

              Use the following constraints:
              - Weekly Budget: ${body.budget} ${body.currency}
              - Household size: ${body.householdSize}
              - Dietary restrictions: ${body.dietaryRestrictions?.join(', ') || 'None'}
              - Allergies: ${body.allergies || 'None'}
              - Cuisine preferences: ${body.cuisinePreferences?.join(', ') || 'Any'}
              - Food exclusions: ${body.exclusions || 'None'}
              - Skill level: ${body.skillLevel || 'Beginner'}
              - Maximum prep time: ${body.prepTime || 30} minutes
              - Meal frequency: ${body.mealFrequency || '3 meals per day'}
              - Portion size: ${body.portionSize || 'Regular'}
              - Prioritize ingredients: ${body.prioritizeIngredients || 'None'}
              - Calorie target: ${body.calorieTarget || 'No preference'}
              - Meal prep friendly: ${body.mealPrepFriendly ? 'Yes' : 'No'}

              Here are the available recipes with their details:
              ${recipeDetailsText}

              Use these recipe IDs for the meal plan: ${recipeIds.join(', ')}

              IMPORTANT RULES:
              1. You MUST use ONLY the recipe IDs provided above.
              2. Each meal MUST have a valid recipeId from the list.
              3. Try to use a variety of recipes throughout the week.
              4. Ensure the meal plan respects all dietary restrictions and preferences.
              5. The total cost must stay within the weekly budget.
              6. Use the EXACT nutrition values, prep times, and costs from the recipe details provided.
              7. Use the recipe names exactly as provided in the recipe details.`
            },
            {
              role: "user",
              content: `Generate a meal plan in this exact JSON format:
              {
                "mealPlan": {
                  "week": [
                    {
                      "day": "Monday",
                      "date": "Monday",
                      "meals": [
                        {
                          "type": "breakfast",
                          "recipeId": "${recipeIds[0]}",
                          "name": "${recipes.length > 0 ? recipes[0].name : 'Greek Yogurt Parfait'}",
                          "image": "${recipes.length > 0 ? recipes[0].image_url : 'https://images.unsplash.com/photo-1484723091739-30a097e8f929'}",
                          "prepTime": ${recipes.length > 0 ? recipes[0].prep_time : 10},
                          "cost": ${recipes.length > 0 ? recipes[0].cost_per_serving : 3.5},
                          "servings": 1,
                          "nutrition": {
                            "calories": ${recipes.length > 0 ? recipes[0].nutrition.calories : 320},
                            "protein": ${recipes.length > 0 ? recipes[0].nutrition.protein : 20},
                            "carbs": ${recipes.length > 0 ? recipes[0].nutrition.carbs : 45},
                            "fats": ${recipes.length > 0 ? recipes[0].nutrition.fat : 8}
                          }
                        }
                      ]
                    }
                  ],
                  "summary": {
                    "totalCost": number,
                    "averageCalories": number,
                    "macros": {
                      "protein": "string",
                      "carbs": "string",
                      "fats": "string"
                    }
                  }
                }
              }

              IMPORTANT: Use only the recipe IDs provided earlier. The recipeId field must be one of these values: ${recipeIds.join(', ')}`
            }
          ],
          temperature: 0.5, // Lower temperature for more consistent results
          response_format: { type: "json_object" }
        });

        // If we get here, the API call was successful
        break;
      } catch (error) {
        console.error(`OpenAI API error (attempt ${retryCount + 1}):`, error);

        // Check if we've reached max retries
        if (retryCount === maxRetries) {
          console.error('Max retries reached for OpenAI API call');
          return NextResponse.json(
            {
              error: 'Failed to generate meal plan after multiple attempts',
              details: error instanceof Error ? error.message : 'Unknown OpenAI error'
            },
            { status: 500 }
          );
        }

        // Exponential backoff with jitter
        const delay = baseDelay * Math.pow(2, retryCount) + Math.random() * 1000;
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        retryCount++;
      }
    }

    // Check if we have a valid response
    if (!completion?.choices[0]?.message?.content) {
      return NextResponse.json(
        { error: "No response from OpenAI" },
        { status: 500 }
      );
    }

    // Parse and validate the OpenAI response
    let mealPlanData;
    try {
      mealPlanData = JSON.parse(completion.choices[0].message.content);

      // Validate the response against our schema
      try {
        mealPlanResponseSchema.parse(mealPlanData);
        console.log("Meal plan validation successful");
      } catch (validationError) {
        console.error("Meal plan validation failed:", validationError);
        return NextResponse.json(
          {
            error: "Generated meal plan has invalid format",
            details: validationError instanceof Error ? validationError.message : 'Schema validation failed'
          },
          { status: 500 }
        );
      }

      // Validate that all recipe IDs are from our provided list
      const allMeals = mealPlanData.mealPlan.week.flatMap((day: any) => day.meals);
      const invalidRecipeIds = allMeals.filter((meal: any) => !recipeIds.includes(meal.recipeId));

      if (invalidRecipeIds.length > 0) {
        console.error("Invalid recipe IDs found:", invalidRecipeIds);
        return NextResponse.json(
          {
            error: "Generated meal plan contains invalid recipe IDs",
            invalidIds: invalidRecipeIds.map((meal: any) => meal.recipeId)
          },
          { status: 500 }
        );
      }

      // Ensure each day has the 'day' property set correctly
      mealPlanData.mealPlan.week.forEach((dayPlan: any) => {
        // Make sure the day property is set
        if (!dayPlan.day && dayPlan.date) {
          dayPlan.day = dayPlan.date;
        }

        // Ensure each meal has the correct properties
        dayPlan.meals.forEach((meal: any) => {
          const recipeId = meal.recipeId;
          const recipeDetail = recipeDetails[recipeId];

          if (recipeDetail) {
            // Update meal with recipe details
            meal.name = recipeDetail.name;
            meal.image = recipeDetail.imageUrl;
            meal.prepTime = recipeDetail.prepTime;
            meal.cost = recipeDetail.cost;
            meal.nutrition = {
              calories: recipeDetail.nutrition.calories,
              protein: recipeDetail.nutrition.protein,
              carbs: recipeDetail.nutrition.carbs,
              fats: recipeDetail.nutrition.fat
            };
          } else {
            // If recipe details are not found, use default values
            console.warn(`Recipe details not found for ID: ${recipeId}. Using default values.`);

            // Set default values if not already set
            meal.name = meal.name || `Recipe ${recipeId}`;
            meal.image = meal.image || 'https://images.unsplash.com/photo-1484723091739-30a097e8f929';
            meal.prepTime = meal.prepTime || 15;
            meal.cost = meal.cost || 3.5;
            meal.nutrition = meal.nutrition || {
              calories: 300,
              protein: 15,
              carbs: 40,
              fats: 10
            };
          }
        });
      });

      // Log the meal plan structure for debugging
      console.log('Meal plan structure:', JSON.stringify(mealPlanData.mealPlan.week[0], null, 2));
    } catch (e) {
      console.error('Meal plan parsing error:', e);
      return NextResponse.json(
        {
          error: "Invalid meal plan data",
          details: e instanceof Error ? e.message : 'Unknown error occurred'
        },
        { status: 500 }
      );
    }

    // Store the meal plan in Supabase using the meal-plans API
    try {
      const now = new Date().toISOString();

      // Log user information for debugging
      console.log('User ID type:', typeof user.id);
      console.log('User ID value:', user.id);

      // In development mode, we can bypass user checks
      if (process.env.BYPASS_AUTH === 'true') {
        console.log('Development mode: Bypassing user checks');
      } else {
        // First, ensure the user exists in the users table
        try {
          // Create a server-side Supabase client
          const adminClient = getSupabaseClient(ClientType.ADMIN);

          // Check if the user exists
          const { data: existingUser, error: userCheckError } = await adminClient
            .from('users')
            .select('id')
            .eq('id', user.id)
            .single();

          if (userCheckError || !existingUser) {
            console.log('User not found, creating user record first');

            // Create the user record with more fields to satisfy any constraints
            const { error: createUserError } = await adminClient
              .from('users')
              .insert({
                id: user.id,
                email: `user-${user.id.substring(0, 8)}@example.com`,
                name: `Test User ${user.id.substring(0, 6)}`,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                avatar_url: null,
                preferences: { theme: 'light' }
              });

            if (createUserError) {
              console.error('Error creating user record:', createUserError);
              // If we're in development mode, we can continue and use the fallback method
              if (process.env.BYPASS_AUTH !== 'true') {
                return NextResponse.json(
                  { error: `Failed to create user record: ${createUserError.message}` },
                  { status: 500 }
                );
              }
            } else {
              console.log('User record created successfully');
            }
          } else {
            console.log('User record already exists');
          }
        } catch (userError) {
          console.error('Error checking/creating user:', userError);

          // If we're in development mode, we can continue and use the fallback method
          if (process.env.BYPASS_AUTH !== 'true') {
            return NextResponse.json(
              { error: `Error checking/creating user: ${userError instanceof Error ? userError.message : 'Unknown error'}` },
              { status: 500 }
            );
          }
          // Otherwise, continue anyway and try the meal plan creation
        }
      }

      // In development mode, we can return the meal plan data without saving it
      if (process.env.BYPASS_AUTH === 'true' && process.env.NODE_ENV === 'development') {
        console.log('Development mode: Returning meal plan data without saving to database');
        return NextResponse.json({
          ...mealPlanData,
          devMode: true
        });
      }

      // Use the meal-plans API to save the meal plan
      try {
        console.log('Saving meal plan using meal-plans API...');

        // Create a meal plan name and description based on the request
        const mealPlanName = `Meal Plan (${new Date().toLocaleDateString()})`;
        const mealPlanDescription = `Generated meal plan with ${body.dietaryRestrictions?.join(', ') || 'no'} dietary restrictions and a budget of ${body.budget} ${body.currency}.`;

        // Call the meal-plans API
        const mealPlanResponse = await fetch(new URL('/api/meal-plans', request.url).toString(), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user_id: user.id,
            start_date: now,
            end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            total_cost: mealPlanData.mealPlan.summary.totalCost,
            meal_data: mealPlanData,
            name: mealPlanName,
            description: mealPlanDescription,
            status: 'active'
          }),
        });

        if (!mealPlanResponse.ok) {
          const errorData = await mealPlanResponse.json();
          console.error('Error saving meal plan with meal-plans API:', errorData);
          throw new Error(errorData.error || 'Failed to save meal plan');
        }

        const savedMealPlan = await mealPlanResponse.json();
        console.log('Meal plan saved successfully with meal-plans API');

        // Return the meal plan data with the saved meal plan ID
        return NextResponse.json({
          ...mealPlanData,
          savedMealPlan: savedMealPlan.data
        });
      } catch (apiError) {
        console.error('Error using meal-plans API:', apiError);

        // Try fallback method using direct API
        try {
          console.log('Attempting fallback method with direct-mealplan API...');

          // Create a direct API call to the direct-mealplan endpoint
          const response = await fetch(new URL('/api/direct-mealplan', request.url).toString(), {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              user_id: user.id,
              start_date: now,
              end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
              total_cost: mealPlanData.mealPlan.summary.totalCost,
              meal_data: mealPlanData
            }),
          });

          const result = await response.json();

          if (!response.ok) {
            console.error('Error using direct-mealplan fallback method:', result);
            throw new Error(result.error || 'Failed to save meal plan using fallback method');
          }

          console.log('Meal plan saved successfully using direct-mealplan fallback method');
          return NextResponse.json({
            ...mealPlanData,
            savedMealPlan: result.data
          });
        } catch (fallbackError) {
          console.error('Error using direct-mealplan fallback method:', fallbackError);

          // As a last resort, try using the meal plan service
          try {
            console.log('Attempting last resort with meal plan service...');

            const { data: mealPlan, error: serviceError } = await mealPlanService.createMealPlan({
              userId: user.id,
              startDate: now,
              endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
              totalCost: mealPlanData.mealPlan.summary.totalCost,
              mealData: mealPlanData,
              status: 'active'
            });

            if (serviceError) {
              console.error('Error using meal plan service:', serviceError);
              throw new Error(serviceError);
            }

            console.log('Meal plan saved successfully using meal plan service');
            return NextResponse.json({
              ...mealPlanData,
              savedMealPlan: mealPlan
            });
          } catch (serviceError) {
            console.error('Error using meal plan service:', serviceError);

            // In development mode with auth bypass, we can return the meal plan data even if saving fails
            if (process.env.BYPASS_AUTH === 'true') {
              console.log('Development mode: Bypassing database error and returning meal plan data');
              return NextResponse.json({
                ...mealPlanData,
                devMode: true,
                dbError: serviceError instanceof Error ? serviceError.message : 'Unknown error'
              });
            }

            throw new Error(serviceError instanceof Error ? serviceError.message : 'Failed to save meal plan');
          }
        }
      }
    } catch (e) {
      console.error('Database operation error:', e);

      // In development mode with auth bypass, we can return the meal plan data even if saving fails
      if (process.env.BYPASS_AUTH === 'true') {
        console.log('Development mode: Bypassing database error and returning meal plan data');
        return NextResponse.json({
          ...mealPlanData,
          devMode: true,
          dbError: e instanceof Error ? e.message : 'Unknown database error'
        });
      }

      return NextResponse.json(
        {
          error: "Failed to save meal plan",
          details: e instanceof Error ? e.message : 'Unknown error occurred'
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Unexpected error in meal plan generation:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: process.env.NODE_ENV === 'development'
          ? (error instanceof Error ? error.message : 'Unknown error occurred')
          : undefined
      },
      { status: 500 }
    );
  }
}
