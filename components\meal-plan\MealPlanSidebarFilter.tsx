"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '@/components/ui/dropdown-menu';
import { Search, SlidersHorizontal, ArrowUpDown, X } from 'lucide-react';

export type SortOption = 'newest' | 'oldest' | 'name-asc' | 'name-desc' | 'cost-asc' | 'cost-desc';
export type FilterOption = 'all' | 'active' | 'archived';

interface MealPlanSidebarFilterProps {
  onSearch: (query: string) => void;
  onSort: (option: SortOption) => void;
  onFilter: (option: FilterOption) => void;
  currentSort: SortOption;
  currentFilter: FilterOption;
}

export function MealPlanSidebarFilter({
  onSearch,
  onSort,
  onFilter,
  currentSort,
  currentFilter
}: MealPlanSidebarFilterProps) {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    onSearch(query);
  };

  const clearSearch = () => {
    setSearchQuery('');
    onSearch('');
  };

  const getSortLabel = (sort: SortOption): string => {
    switch (sort) {
      case 'newest': return 'Newest First';
      case 'oldest': return 'Oldest First';
      case 'name-asc': return 'Name (A-Z)';
      case 'name-desc': return 'Name (Z-A)';
      case 'cost-asc': return 'Cost (Low to High)';
      case 'cost-desc': return 'Cost (High to Low)';
      default: return 'Sort';
    }
  };

  const getFilterLabel = (filter: FilterOption): string => {
    switch (filter) {
      case 'all': return 'All Plans';
      case 'active': return 'Active Plans';
      case 'archived': return 'Archived Plans';
      default: return 'Filter';
    }
  };

  return (
    <div className="p-2 border-b">
      <div className="relative mb-2">
        <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search plans..."
          className="pl-8 h-9 text-sm"
          value={searchQuery}
          onChange={handleSearch}
        />
        {searchQuery && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0"
            onClick={clearSearch}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
      
      <div className="flex gap-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="w-full text-xs">
              <SlidersHorizontal className="h-3.5 w-3.5 mr-1" />
              {getFilterLabel(currentFilter)}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-48">
            <DropdownMenuLabel>Filter Plans</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              className={currentFilter === 'all' ? 'bg-muted' : ''}
              onClick={() => onFilter('all')}
            >
              All Plans
            </DropdownMenuItem>
            <DropdownMenuItem 
              className={currentFilter === 'active' ? 'bg-muted' : ''}
              onClick={() => onFilter('active')}
            >
              Active Plans
            </DropdownMenuItem>
            <DropdownMenuItem 
              className={currentFilter === 'archived' ? 'bg-muted' : ''}
              onClick={() => onFilter('archived')}
            >
              Archived Plans
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="w-full text-xs">
              <ArrowUpDown className="h-3.5 w-3.5 mr-1" />
              {getSortLabel(currentSort)}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuLabel>Sort By</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              className={currentSort === 'newest' ? 'bg-muted' : ''}
              onClick={() => onSort('newest')}
            >
              Newest First
            </DropdownMenuItem>
            <DropdownMenuItem 
              className={currentSort === 'oldest' ? 'bg-muted' : ''}
              onClick={() => onSort('oldest')}
            >
              Oldest First
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              className={currentSort === 'name-asc' ? 'bg-muted' : ''}
              onClick={() => onSort('name-asc')}
            >
              Name (A-Z)
            </DropdownMenuItem>
            <DropdownMenuItem 
              className={currentSort === 'name-desc' ? 'bg-muted' : ''}
              onClick={() => onSort('name-desc')}
            >
              Name (Z-A)
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              className={currentSort === 'cost-asc' ? 'bg-muted' : ''}
              onClick={() => onSort('cost-asc')}
            >
              Cost (Low to High)
            </DropdownMenuItem>
            <DropdownMenuItem 
              className={currentSort === 'cost-desc' ? 'bg-muted' : ''}
              onClick={() => onSort('cost-desc')}
            >
              Cost (High to Low)
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
