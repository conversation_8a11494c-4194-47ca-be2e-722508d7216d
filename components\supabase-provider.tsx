'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { SupabaseClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';

// Define the context type
type SupabaseContextType = {
  supabase: SupabaseClient<Database> | null;
  isLoading: boolean;
};

// Create the context with a default value
export const Context = createContext<SupabaseContextType & { isInitialized?: boolean }>({
  supabase: null,
  isLoading: true,
});

// Create a hook to use the context
export const useSupabase = () => {
  const context = useContext(Context);

  if (!context) {
    throw new Error('useSupabase must be used within a SupabaseProvider');
  }

  return context;
};

// Create the provider component
export function SupabaseProvider({ children }: { children: React.ReactNode }) {
  // State to hold the Supabase client and loading state
  const [state, setState] = useState<SupabaseContextType>({
    supabase: null,
    isLoading: true,
  });

  // Initialize the Supabase client
  useEffect(() => {
    console.log('SupabaseProvider: Initializing Supabase client');

    // Create the client
    try {
      const supabase = createClientComponentClient<Database>();
      console.log('SupabaseProvider: Supabase client created successfully');

      // Update the state
      setState({
        supabase,
        isLoading: false,
      });
    } catch (error) {
      console.error('SupabaseProvider: Error creating Supabase client', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }

    // Cleanup function
    return () => {
      console.log('SupabaseProvider: Cleaning up');
    };
  }, []);

  // Provide the context value to children
  return (
    <Context.Provider value={{ ...state, isInitialized: !state.isLoading }}>
      {children}
    </Context.Provider>
  );
}

// For backward compatibility
export const useSupabaseNew = useSupabase;
