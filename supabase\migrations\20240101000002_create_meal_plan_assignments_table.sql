-- Create meal_plan_assignments table
CREATE TABLE IF NOT EXISTS meal_plan_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  meal_plan_id UUID NOT NULL REFERENCES meal_plans(id) ON DELETE CASCADE,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE meal_plan_assignments ENABLE ROW LEVEL SECURITY;

-- Policy for selecting meal plan assignments (users can only see their own)
CREATE POLICY "Users can view their own meal plan assignments"
  ON meal_plan_assignments
  FOR SELECT
  USING (user_id::text = auth.uid()::text);

-- Policy for inserting meal plan assignments
CREATE POLICY "Users can insert their own meal plan assignments"
  ON meal_plan_assignments
  FOR INSERT
  WITH CHECK (user_id::text = auth.uid()::text);

-- Policy for updating meal plan assignments
CREATE POLICY "Users can update their own meal plan assignments"
  ON meal_plan_assignments
  FOR UPDATE
  USING (user_id::text = auth.uid()::text);

-- Policy for deleting meal plan assignments
CREATE POLICY "Users can delete their own meal plan assignments"
  ON meal_plan_assignments
  FOR DELETE
  USING (user_id::text = auth.uid()::text);

-- Create index for faster queries
CREATE INDEX meal_plan_assignments_user_id_idx ON meal_plan_assignments(user_id);
CREATE INDEX meal_plan_assignments_meal_plan_id_idx ON meal_plan_assignments(meal_plan_id);
CREATE INDEX meal_plan_assignments_date_range_idx ON meal_plan_assignments(start_date, end_date);

-- Grant permissions to authenticated users
GRANT ALL ON meal_plan_assignments TO authenticated;
