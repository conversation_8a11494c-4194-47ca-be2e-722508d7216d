'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Search, X, Filter } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

interface QuickFiltersProps {
  onFilterChange: (filters: {
    search: string;
    mealTypes: string[];
    tags: string[];
    calorieRange: [number | null, number | null];
  }) => void;
  availableMealTypes: string[];
  availableTags: string[];
  className?: string;
}

export function QuickFilters({
  onFilterChange,
  availableMealTypes,
  availableTags,
  className
}: QuickFiltersProps) {
  const [search, setSearch] = useState('');
  const [selectedMealTypes, setSelectedMealTypes] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [calorieRange, setCalorieRange] = useState<[number | null, number | null]>([null, null]);
  
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearch(value);
    applyFilters(value, selectedMealTypes, selectedTags, calorieRange);
  };
  
  const toggleMealType = (mealType: string) => {
    const newSelectedMealTypes = selectedMealTypes.includes(mealType)
      ? selectedMealTypes.filter(type => type !== mealType)
      : [...selectedMealTypes, mealType];
    
    setSelectedMealTypes(newSelectedMealTypes);
    applyFilters(search, newSelectedMealTypes, selectedTags, calorieRange);
  };
  
  const toggleTag = (tag: string) => {
    const newSelectedTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag];
    
    setSelectedTags(newSelectedTags);
    applyFilters(search, selectedMealTypes, newSelectedTags, calorieRange);
  };
  
  const setCalorieMin = (value: string) => {
    const min = value === '' ? null : parseInt(value);
    const newRange: [number | null, number | null] = [min, calorieRange[1]];
    setCalorieRange(newRange);
    applyFilters(search, selectedMealTypes, selectedTags, newRange);
  };
  
  const setCalorieMax = (value: string) => {
    const max = value === '' ? null : parseInt(value);
    const newRange: [number | null, number | null] = [calorieRange[0], max];
    setCalorieRange(newRange);
    applyFilters(search, selectedMealTypes, selectedTags, newRange);
  };
  
  const clearFilters = () => {
    setSearch('');
    setSelectedMealTypes([]);
    setSelectedTags([]);
    setCalorieRange([null, null]);
    applyFilters('', [], [], [null, null]);
  };
  
  const applyFilters = (
    search: string,
    mealTypes: string[],
    tags: string[],
    calorieRange: [number | null, number | null]
  ) => {
    onFilterChange({
      search,
      mealTypes,
      tags,
      calorieRange
    });
  };
  
  const hasActiveFilters = search || selectedMealTypes.length > 0 || selectedTags.length > 0 || 
    calorieRange[0] !== null || calorieRange[1] !== null;
  
  return (
    <div className={cn('flex flex-col space-y-2', className)}>
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search meals..."
            className="pl-9"
            value={search}
            onChange={handleSearchChange}
          />
          {search && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1.5 h-6 w-6 p-0"
              onClick={() => {
                setSearch('');
                applyFilters('', selectedMealTypes, selectedTags, calorieRange);
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="gap-1">
              <Filter className="h-4 w-4" />
              <span className="hidden sm:inline">Filters</span>
              {hasActiveFilters && (
                <Badge variant="secondary" className="ml-1 h-5 px-1">
                  {selectedMealTypes.length + selectedTags.length + 
                   (calorieRange[0] !== null ? 1 : 0) + 
                   (calorieRange[1] !== null ? 1 : 0)}
                </Badge>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56">
            <DropdownMenuLabel>Meal Types</DropdownMenuLabel>
            <DropdownMenuGroup>
              {availableMealTypes.map(mealType => (
                <DropdownMenuItem
                  key={mealType}
                  className="flex items-center justify-between cursor-pointer"
                  onClick={() => toggleMealType(mealType)}
                >
                  <span>{mealType}</span>
                  {selectedMealTypes.includes(mealType) && (
                    <Badge variant="secondary" className="ml-2">
                      Selected
                    </Badge>
                  )}
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuLabel>Tags</DropdownMenuLabel>
            <DropdownMenuGroup className="max-h-40 overflow-y-auto">
              {availableTags.map(tag => (
                <DropdownMenuItem
                  key={tag}
                  className="flex items-center justify-between cursor-pointer"
                  onClick={() => toggleTag(tag)}
                >
                  <span>{tag}</span>
                  {selectedTags.includes(tag) && (
                    <Badge variant="secondary" className="ml-2">
                      Selected
                    </Badge>
                  )}
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuLabel>Calories</DropdownMenuLabel>
            <div className="p-2 flex items-center space-x-2">
              <Input
                type="number"
                placeholder="Min"
                className="w-20 h-8"
                value={calorieRange[0] === null ? '' : calorieRange[0]}
                onChange={(e) => setCalorieMin(e.target.value)}
              />
              <span>to</span>
              <Input
                type="number"
                placeholder="Max"
                className="w-20 h-8"
                value={calorieRange[1] === null ? '' : calorieRange[1]}
                onChange={(e) => setCalorieMax(e.target.value)}
              />
            </div>
            
            <DropdownMenuSeparator />
            
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-center"
              onClick={clearFilters}
              disabled={!hasActiveFilters}
            >
              Clear All Filters
            </Button>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      {/* Active filters */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {selectedMealTypes.map(type => (
            <Badge key={type} variant="secondary" className="gap-1">
              {type}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1"
                onClick={() => toggleMealType(type)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          
          {selectedTags.map(tag => (
            <Badge key={tag} variant="secondary" className="gap-1">
              {tag}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1"
                onClick={() => toggleTag(tag)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          
          {calorieRange[0] !== null && (
            <Badge variant="secondary" className="gap-1">
              Min: {calorieRange[0]} cal
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1"
                onClick={() => setCalorieMin('')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          
          {calorieRange[1] !== null && (
            <Badge variant="secondary" className="gap-1">
              Max: {calorieRange[1]} cal
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1"
                onClick={() => setCalorieMax('')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
