'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { ChevronLeft } from 'lucide-react';
import { mealPlanService } from '@/app/services/meal-plan-service';
import { useSupabase } from '@/components/supabase-provider';

// Import the components we need
import { MealPlanTabs } from '@/components/meal-plan/meal-plan-tabs';
import { MealPlanCard } from '@/components/meal-plan/meal-plan-card';
import { MealPlanDetail } from '@/components/meal-plan/meal-plan-detail';
import { WeeklyCalendarView } from '@/components/meal-plan/weekly-calendar-view';
import { CalendarAssignmentModal } from '@/components/meal-plan/calendar-assignment-modal';
import { ShoppingListGeneratorFixed as ShoppingListGenerator } from '@/components/meal-plan/shopping-list-generator-fixed';

interface MealPlan {
  id: string;
  user_id: string;
  start_date: string;
  end_date: string;
  total_cost: number;
  meal_data: any;
  status: string;
  created_at: string;
  updated_at: string;
  name?: string;
  description?: string;
}

function ViewMealPlanContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const [isLoading, setIsLoading] = useState(true);
  const [mealPlans, setMealPlans] = useState<MealPlan[]>([]);
  const [filteredPlans, setFilteredPlans] = useState<MealPlan[]>([]);
  const [activePlan, setActivePlan] = useState<MealPlan | null>(null);
  const [error, setError] = useState<string | null>(null);

  // State for UI controls
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<string>('created_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showDetailView, setShowDetailView] = useState(false);

  // State for modals
  const [showCalendarModal, setShowCalendarModal] = useState(false);
  const [showShoppingListModal, setShowShoppingListModal] = useState(false);
  const [selectedPlanForModal, setSelectedPlanForModal] = useState<MealPlan | null>(null);

  // Function to generate demo meal plans
  const getDemoMealPlans = (): MealPlan[] => {
    const today = new Date();
    const oneWeekLater = new Date(today);
    oneWeekLater.setDate(today.getDate() + 7);

    const twoWeeksLater = new Date(today);
    twoWeeksLater.setDate(today.getDate() + 14);

    const threeWeeksLater = new Date(today);
    threeWeeksLater.setDate(today.getDate() + 21);

    return [
      {
        id: '1',
        user_id: '000000000000000',
        name: 'Weekly Meal Plan',
        description: 'A balanced meal plan for the week',
        start_date: today.toISOString(),
        end_date: oneWeekLater.toISOString(),
        total_cost: 89.99,
        status: 'active',
        created_at: today.toISOString(),
        updated_at: today.toISOString(),
        meal_data: {
          dietaryPreferences: ['Balanced', 'High Protein'],
          mealPlan: {
            meals: Array(21).fill(null).map((_, i) => ({
              id: `meal-${i+1}`,
              name: `Demo Meal ${i+1}`,
              type: i % 3 === 0 ? 'Breakfast' : i % 3 === 1 ? 'Lunch' : 'Dinner',
              calories: Math.floor(Math.random() * 400) + 300,
              protein: Math.floor(Math.random() * 30) + 15,
              carbs: Math.floor(Math.random() * 40) + 20,
              fat: Math.floor(Math.random() * 20) + 10,
              prepTime: Math.floor(Math.random() * 30) + 10,
              image: `https://source.unsplash.com/random/300x200?food=${i+1}`
            })),
            week: Array(7).fill(null).map((_, i) => {
              const date = new Date(today);
              date.setDate(today.getDate() + i);
              return {
                date: date.toISOString().split('T')[0],
                meals: Array(3).fill(null).map((_, j) => ({
                  id: `meal-${i*3+j+1}`,
                  name: `Demo Meal ${i*3+j+1}`,
                  type: j === 0 ? 'Breakfast' : j === 1 ? 'Lunch' : 'Dinner',
                  calories: Math.floor(Math.random() * 400) + 300,
                  protein: Math.floor(Math.random() * 30) + 15,
                  carbs: Math.floor(Math.random() * 40) + 20,
                  fat: Math.floor(Math.random() * 20) + 10,
                  prepTime: Math.floor(Math.random() * 30) + 10,
                  image: `https://source.unsplash.com/random/300x200?food=${i*3+j+1}`
                }))
              };
            }),
            totalCalories: 14000,
            totalPrepTime: 420,
            nutritionSummary: {
              calories: 14000,
              protein: 700,
              carbs: 1400,
              fat: 500
            }
          }
        }
      },
      {
        id: '2',
        user_id: '000000000000000',
        name: 'Low Carb Plan',
        description: 'A low carb meal plan for weight loss',
        start_date: oneWeekLater.toISOString(),
        end_date: twoWeeksLater.toISOString(),
        total_cost: 75.50,
        status: 'active',
        created_at: today.toISOString(),
        updated_at: today.toISOString(),
        meal_data: {
          dietaryPreferences: ['Low Carb', 'Keto-Friendly'],
          mealPlan: {
            meals: Array(21).fill(null).map((_, i) => ({
              id: `meal-${i+100}`,
              name: `Low Carb Meal ${i+1}`,
              type: i % 3 === 0 ? 'Breakfast' : i % 3 === 1 ? 'Lunch' : 'Dinner',
              calories: Math.floor(Math.random() * 300) + 200,
              protein: Math.floor(Math.random() * 40) + 20,
              carbs: Math.floor(Math.random() * 10) + 5,
              fat: Math.floor(Math.random() * 30) + 15,
              prepTime: Math.floor(Math.random() * 30) + 10,
              image: `https://source.unsplash.com/random/300x200?keto=${i+1}`
            })),
            week: Array(7).fill(null).map((_, i) => {
              const date = new Date(oneWeekLater);
              date.setDate(oneWeekLater.getDate() + i);
              return {
                date: date.toISOString().split('T')[0],
                meals: Array(3).fill(null).map((_, j) => ({
                  id: `meal-${i*3+j+100}`,
                  name: `Low Carb Meal ${i*3+j+1}`,
                  type: j === 0 ? 'Breakfast' : j === 1 ? 'Lunch' : 'Dinner',
                  calories: Math.floor(Math.random() * 300) + 200,
                  protein: Math.floor(Math.random() * 40) + 20,
                  carbs: Math.floor(Math.random() * 10) + 5,
                  fat: Math.floor(Math.random() * 30) + 15,
                  prepTime: Math.floor(Math.random() * 30) + 10,
                  image: `https://source.unsplash.com/random/300x200?keto=${i*3+j+1}`
                }))
              };
            }),
            totalCalories: 10500,
            totalPrepTime: 380,
            nutritionSummary: {
              calories: 10500,
              protein: 840,
              carbs: 350,
              fat: 630
            }
          }
        }
      }
    ];
  };

  useEffect(() => {
    // Check if we're coming from the meal plan generation page
    const refreshParam = searchParams?.get('refresh');
    if (refreshParam) {
      console.log('Refreshing meal plans after generation:', refreshParam);
      // Show a toast to inform the user
      toast('Meal plan created successfully!', {
        style: {
          background: "#DCFCE7", // Light green background
          border: "1px solid #22C55E", // Green border
          color: "#166534" // Green text
        },
        duration: 5000
      });
    }

    const fetchMealPlans = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Always prepare demo data as fallback
        const demoPlans = getDemoMealPlans();

        // Make sure supabase is available
        if (!supabase) {
          console.log('Supabase client not available, using demo data');
          setMealPlans(demoPlans);
          setFilteredPlans(demoPlans);
          setActivePlan(demoPlans[0]);
          return;
        }

        // If we're coming from the meal plan generation page, we've already shown a toast
        // No need to do anything special here, just fetch the meal plans

        try {
          // Try to get meal plans directly from the service
          // The service will handle authentication internally
          // Get meal plans from the service
          const { data } = await mealPlanService.getMealPlans();

          if (data && data.length > 0) {
            console.log('Successfully loaded meal plans from database');
            setMealPlans(data);
            setFilteredPlans(data);
            setActivePlan(data[0]);
          } else {
            // If no meal plans found, use demo data
            console.log('No meal plans found, using demo data');
            setMealPlans(demoPlans);
            setFilteredPlans(demoPlans);
            setActivePlan(demoPlans[0]);

            // Show a toast to inform the user
            toast('Using demo meal plans - create your own plans to get started!');
          }
        } catch (error: any) {
          console.error('Error in meal plan fetch:', error);
          // Use demo data as fallback
          console.log('Using demo meal plans due to error');
          setMealPlans(demoPlans);
          setFilteredPlans(demoPlans);
          setActivePlan(demoPlans[0]);

          // Show a toast instead of setting error state to allow the page to render
          toast('Using demo meal plans - sign in to create your own plans');
        }
      } catch (error: any) {
        console.error('Error in fetchMealPlans:', error);
        const demoPlans = getDemoMealPlans();
        setMealPlans(demoPlans);
        setFilteredPlans(demoPlans);
        setActivePlan(demoPlans[0]);
      } finally {
        setIsLoading(false);
      }
    };

    // Only fetch meal plans if supabase is not loading
    if (!isSupabaseLoading) {
      fetchMealPlans();
    }
  }, [supabase, isSupabaseLoading, router, searchParams]);

  const handleBack = () => {
    if (showDetailView && activePlan) {
      setShowDetailView(false);
    } else {
      router.push('/dashboard');
    }
  };

  const handleCreatePlan = () => {
    router.push('/meal-plan/generate');
  };

  // Apply all filters, search, and sorting in one function
  const applyFiltersAndSort = (query: string, filters: string[], sort: string, direction: 'asc' | 'desc') => {
    // Start with all meal plans
    let filtered = [...mealPlans];

    // Apply search filter
    if (query && query.trim()) {
      const searchLower = query.toLowerCase();
      filtered = filtered.filter(plan => {
        const planName = plan.name || `Meal Plan (${new Date(plan.start_date).toLocaleDateString()})`;
        return (
          planName.toLowerCase().includes(searchLower) ||
          (plan.description && plan.description.toLowerCase().includes(searchLower)) ||
          (plan.meal_data?.dietaryPreferences?.some((pref: string) =>
            pref.toLowerCase().includes(searchLower)
          ))
        );
      });
    }

    // Apply active filters
    if (filters.length > 0) {
      filtered = filtered.filter(plan => {
        return filters.every(filterStr => {
          const [type, value] = filterStr.split(': ');

          switch (type) {
            case 'status':
              return plan.status === value;
            case 'date':
              const now = new Date();
              const startDate = new Date(plan.start_date);
              const endDate = new Date(plan.end_date);

              if (value === 'this-week') {
                const startOfWeek = new Date(now);
                startOfWeek.setDate(now.getDate() - now.getDay() + 1); // Monday
                const endOfWeek = new Date(startOfWeek);
                endOfWeek.setDate(startOfWeek.getDate() + 6); // Sunday

                return startDate <= endOfWeek && endDate >= startOfWeek;
              } else if (value === 'next-week') {
                const startOfNextWeek = new Date(now);
                startOfNextWeek.setDate(now.getDate() - now.getDay() + 8); // Next Monday
                const endOfNextWeek = new Date(startOfNextWeek);
                endOfNextWeek.setDate(startOfNextWeek.getDate() + 6); // Next Sunday

                return startDate <= endOfNextWeek && endDate >= startOfNextWeek;
              } else if (value === 'this-month') {
                const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
                const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

                return startDate <= endOfMonth && endDate >= startOfMonth;
              } else if (value === 'last-30-days') {
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(now.getDate() - 30);

                return new Date(plan.created_at) >= thirtyDaysAgo;
              }
              return true;
            case 'budget':
              if (value === 'under50') {
                return plan.total_cost < 50;
              } else if (value === '50to100') {
                return plan.total_cost >= 50 && plan.total_cost <= 100;
              } else if (value === 'over100') {
                return plan.total_cost > 100;
              }
              return true;
            case 'tag':
              // This is a placeholder - in a real app, you would check if the plan has this tag
              return true;
            default:
              return true;
          }
        });
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sort) {
        case 'created_at':
          comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
          break;
        case 'total_cost':
          comparison = a.total_cost - b.total_cost;
          break;
        case 'total_meals':
          const aMeals = a.meal_data?.mealPlan?.meals?.length || 0;
          const bMeals = b.meal_data?.mealPlan?.meals?.length || 0;
          comparison = aMeals - bMeals;
          break;
        case 'prep_time':
          const aPrepTime = a.meal_data?.mealPlan?.totalPrepTime || 0;
          const bPrepTime = b.meal_data?.mealPlan?.totalPrepTime || 0;
          comparison = aPrepTime - bPrepTime;
          break;
        default:
          comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      }

      return direction === 'asc' ? comparison : -comparison;
    });

    setFilteredPlans(filtered);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    applyFiltersAndSort(query, activeFilters, sortBy, sortDirection);
  };

  const handleSort = (sort: string, direction: 'asc' | 'desc') => {
    setSortBy(sort);
    setSortDirection(direction);
    applyFiltersAndSort(searchQuery, activeFilters, sort, direction);
  };

  const handleFilter = (filter: { type: string, value: string }) => {
    let newFilters = [...activeFilters];

    // Check if filter already exists
    const existingIndex = newFilters.findIndex(f => f.includes(filter.value));

    if (existingIndex >= 0) {
      // Remove existing filter
      newFilters.splice(existingIndex, 1);
    } else {
      // Add new filter
      newFilters.push(`${filter.type}: ${filter.value}`);
    }

    setActiveFilters(newFilters);

    // Apply all filters, search, and sorting
    applyFiltersAndSort(searchQuery, newFilters, sortBy, sortDirection);
  };

  const handleClearFilters = () => {
    setActiveFilters([]);
    applyFiltersAndSort(searchQuery, [], sortBy, sortDirection);
  };

  // This function is no longer needed as we're using the WeeklyCalendarView component

  const handleViewPlanDetail = (plan: MealPlan) => {
    setActivePlan(plan);
    setShowDetailView(true);
  };

  const handleAddToCalendar = (plan: MealPlan) => {
    setSelectedPlanForModal(plan);
    setShowCalendarModal(true);
  };

  const handleDuplicatePlan = async (plan: MealPlan) => {
    try {
      const { data, error } = await mealPlanService.duplicateMealPlan(plan.id);

      if (error) {
        toast.error(`Failed to duplicate plan: ${error}`);
        return;
      }

      if (data) {
        toast.success('Plan duplicated successfully');

        // Refresh meal plans
        const { data: updatedPlans } = await mealPlanService.getMealPlans();
        if (updatedPlans) {
          setMealPlans(updatedPlans);
          setFilteredPlans(updatedPlans);
        }
      }
    } catch (error: any) {
      toast.error(`Error: ${error.message || 'Failed to duplicate plan'}`);
    }
  };

  const handleEditPlan = (plan: MealPlan) => {
    // Navigate to edit page
    router.push(`/meal-plan/edit/${plan.id}`);
  };

  const handleArchivePlan = async (plan: MealPlan) => {
    try {
      const { data, error } = await mealPlanService.archiveMealPlan(plan.id);

      if (error) {
        toast.error(`Failed to archive plan: ${error}`);
        return;
      }

      if (data) {
        toast.success('Plan archived successfully');

        // Refresh meal plans
        const { data: updatedPlans } = await mealPlanService.getMealPlans();
        if (updatedPlans) {
          setMealPlans(updatedPlans);
          setFilteredPlans(updatedPlans);
        }
      }
    } catch (error: any) {
      toast.error(`Error: ${error.message || 'Failed to archive plan'}`);
    }
  };

  const handleGenerateShoppingList = async (plan: MealPlan) => {
    try {
      // Show loading toast
      toast('Generating shopping list...');

      // Generate shopping list
      const { data, error } = await mealPlanService.generateShoppingList(plan.id);

      if (error) {
        toast.error(`Failed to generate shopping list: ${error}`);
        return;
      }

      if (data) {
        toast.success('Shopping list generated successfully');

        // Redirect to the shopping list page using the dynamic route
        router.push(`/shopping-list/${data.id}`);

        // Also log the data for debugging
        console.log('Shopping list generated:', data);
      } else {
        // If no data was returned but also no error, show a message
        toast('No shopping list data returned. Please try again.');
      }
    } catch (error: any) {
      console.error('Error generating shopping list:', error);
      toast.error(`Error: ${error.message || 'Failed to generate shopping list'}`);
    }
  };

  const handleDeletePlan = async (planId: string) => {
    try {
      const { success, error } = await mealPlanService.deleteMealPlan(planId);

      if (error) {
        throw new Error(error);
      }

      if (!success) {
        throw new Error('Failed to delete meal plan');
      }

      // Update both mealPlans and filteredPlans state
      const updatedMealPlans = mealPlans.filter(plan => plan.id !== planId);
      setMealPlans(updatedMealPlans);
      setFilteredPlans(updatedMealPlans);

      if (activePlan?.id === planId) {
        const remainingPlans = updatedMealPlans;
        if (remainingPlans.length > 0) {
          setActivePlan(remainingPlans[0]);
        } else {
          setActivePlan(null);
        }
      }

      toast.success('Meal plan deleted successfully');
      router.refresh();
    } catch (error: any) {
      console.error('Error deleting meal plan:', error);
      const errorMessage = error?.message || 'Unknown error';
      toast.error(`Failed to delete meal plan: ${errorMessage}`);
    }
  };

  // Use the components we imported at the top of the file

  // If we're showing the detail view, render the detail component
  if (showDetailView && activePlan) {
    return (
      <div className="container max-w-6xl py-8">
        <MealPlanDetail
          plan={activePlan}
          onBack={handleBack}
          onEdit={handleEditPlan}
          onAddToCalendar={handleAddToCalendar}
          onGenerateShoppingList={handleGenerateShoppingList}
        />
      </div>
    );
  }

  return (
    <div className="container max-w-6xl py-8">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4">
        <div className="flex items-center">
          <Button variant="ghost" onClick={handleBack} className="mr-2" aria-label="Go back to dashboard">
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">Meal Plans</h1>
        </div>
        <div className="flex items-center">
          <Button onClick={handleCreatePlan}>Create New Plan</Button>
        </div>
      </div>

      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md text-red-700 mb-6">
          Error: {error}
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : mealPlans.length === 0 ? (
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold mb-4">No Meal Plans Found</h2>
          <p className="text-muted-foreground mb-6">Create your first meal plan to get started.</p>
          <Button onClick={handleCreatePlan}>Create New Plan</Button>
        </div>
      ) : (
        <MealPlanTabs
          listView={
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              {filteredPlans.map((plan) => (
                <MealPlanCard
                  key={plan.id}
                  plan={plan}
                  isActive={activePlan?.id === plan.id}
                  onSelect={handleViewPlanDetail}
                  onDelete={handleDeletePlan}
                  onAddToCalendar={handleAddToCalendar}
                  onDuplicate={handleDuplicatePlan}
                  onEdit={handleEditPlan}
                  onArchive={handleArchivePlan}
                  onFavorite={() => toast.success('Added to favorites')}
                />
              ))}
            </div>
          }
          onSort={handleSort}
          activeSortBy={sortBy}
          activeSortDirection={sortDirection}
          totalPlans={filteredPlans.length}
          calendarView={
            <WeeklyCalendarView
              mealPlans={mealPlans}
              onSelectPlan={handleViewPlanDetail}
              onRefresh={() => {
                // Refresh meal plans
                const fetchMealPlans = async () => {
                  try {
                    setIsLoading(true);
                    const { data, error } = await mealPlanService.getMealPlans();

                    if (error) {
                      throw new Error(error);
                    }

                    if (data && data.length > 0) {
                      setMealPlans(data);
                      setFilteredPlans(data);
                    }
                  } catch (error) {
                    console.error('Error refreshing meal plans:', error);
                    toast.error('Failed to refresh meal plans');
                  } finally {
                    setIsLoading(false);
                  }
                };

                fetchMealPlans();
              }}
            />
          }
          onSearch={handleSearch}
          onFilter={handleFilter}
          activeFilters={activeFilters}
          onClearFilters={handleClearFilters}
        />
      )}

      {/* Debug information */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-8 p-4 bg-gray-50 border border-gray-200 rounded-md">
          <h3 className="font-bold mb-2">Supabase Debug Info</h3>
          <p><strong>Supabase Client:</strong> {supabase ? 'Available ✅' : 'Not Available ❌'}</p>
        </div>
      )}

      {/* Calendar Assignment Modal */}
      {selectedPlanForModal && (
        <CalendarAssignmentModal
          isOpen={showCalendarModal}
          onClose={() => setShowCalendarModal(false)}
          mealPlan={selectedPlanForModal}
          onSuccess={() => {
            // Refresh meal plans
            const fetchMealPlans = async () => {
              try {
                const { data, error } = await mealPlanService.getMealPlans();

                if (error) {
                  throw new Error(error);
                }

                if (data && data.length > 0) {
                  setMealPlans(data);
                  setFilteredPlans(data);
                }
              } catch (error) {
                console.error('Error refreshing meal plans:', error);
              }
            };

            fetchMealPlans();
          }}
        />
      )}

      {/* Shopping List Generator Modal */}
      {selectedPlanForModal && (
        <ShoppingListGenerator
          isOpen={showShoppingListModal}
          onClose={() => setShowShoppingListModal(false)}
          mealPlan={selectedPlanForModal}
        />
      )}
    </div>
  );
}

/**
 * Export the ViewMealPlan component directly
 *
 * The component uses the useSupabase hook to access the Supabase client.
 */
export default function ViewMealPlanPage() {
  const { isLoading } = useSupabase();

  if (isLoading) {
    return (
      <div className="container max-w-6xl py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          <p className="ml-4 text-muted-foreground">Initializing Supabase...</p>
        </div>
      </div>
    );
  }

  return <ViewMealPlanContent />;
}
