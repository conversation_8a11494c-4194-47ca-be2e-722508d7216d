'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import {
  Calendar,
  ChevronRight,
  Clock,
  DollarSign,
  Heart,
  MoreHorizontal,
  ShoppingCart,
  Trash2,
  Copy,
  Edit,
  Archive
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { AddToCalendarButton } from './add-to-calendar-button';
import { Progress } from '@/components/ui/progress';

interface MealPlan {
  id: string;
  user_id: string;
  start_date: string;
  end_date: string;
  total_cost: number;
  meal_data: any;
  status: string;
  created_at: string;
  updated_at: string;
  name?: string;
  description?: string;
}

interface MealPlanCardProps {
  plan: MealPlan;
  isActive?: boolean;
  onSelect: (plan: MealPlan) => void;
  onDelete: (planId: string) => void;
  onAddToCalendar?: (plan: MealPlan) => void;
  onDuplicate?: (plan: MealPlan) => void;
  onEdit?: (plan: MealPlan) => void;
  onArchive?: (plan: MealPlan) => void;
  onFavorite?: (plan: MealPlan) => void;
  onGenerateShoppingList?: (plan: MealPlan) => void;
}

export function MealPlanCard({
  plan,
  isActive = false,
  onSelect,
  onDelete,
  onAddToCalendar,
  onDuplicate,
  onEdit,
  onArchive,
  onFavorite,
  onGenerateShoppingList
}: MealPlanCardProps) {
  const [isFavorite, setIsFavorite] = useState(false);

  const startDate = new Date(plan.start_date || plan.created_at);
  const endDate = new Date(plan.end_date || new Date(plan.created_at).setDate(new Date(plan.created_at).getDate() + 7));

  // Calculate meal plan stats
  // Get all meals from all days
  const allMeals = plan.meal_data?.mealPlan?.week?.flatMap((day: any) => day.meals) || [];
  const totalMeals = allMeals.length || 0;

  // Calculate total calories
  const totalCalories = allMeals.reduce((sum: number, meal: any) => {
    return sum + (meal.nutrition?.calories || 0);
  }, 0);

  const avgCaloriesPerDay = totalCalories / (plan.meal_data?.mealPlan?.week?.length || 7);

  // Calculate total prep time
  const totalPrepTime = allMeals.reduce((sum: number, meal: any) => {
    return sum + (meal.prepTime || 0);
  }, 0);

  // Get macronutrient data if available
  const macros = plan.meal_data?.mealPlan?.summary?.macros || {
    protein: '0g',
    carbs: '0g',
    fats: '0g'
  };

  // Extract protein, carbs, and fats as numbers
  const proteinValue = parseInt(macros.protein) || 0;
  const carbsValue = parseInt(macros.carbs) || 0;
  const fatsValue = parseInt(macros.fats) || 0;
  const totalMacros = proteinValue + carbsValue + fatsValue;

  // Calculate macronutrient percentages
  const proteinPercentage = totalMacros > 0 ? (proteinValue / totalMacros) * 100 : 0;
  const carbsPercentage = totalMacros > 0 ? (carbsValue / totalMacros) * 100 : 0;
  const fatsPercentage = totalMacros > 0 ? (fatsValue / totalMacros) * 100 : 0;

  // Calculate completion percentage (mock data - in a real app this would come from the database)
  const completedMeals = Math.floor(Math.random() * (totalMeals + 1));
  const completionPercentage = totalMeals > 0 ? (completedMeals / totalMeals) * 100 : 0;

  const handleFavoriteToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsFavorite(!isFavorite);
    if (onFavorite) {
      onFavorite(plan);
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(plan.id);
  };

  const handleAddToCalendar = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onAddToCalendar) {
      onAddToCalendar(plan);
    } else {
      toast('Calendar integration coming soon');
    }
  };

  const handleDuplicate = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDuplicate) {
      onDuplicate(plan);
    } else {
      toast('Duplicate functionality coming soon');
    }
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEdit) {
      onEdit(plan);
    } else {
      toast('Edit functionality coming soon');
    }
  };

  const handleArchive = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onArchive) {
      onArchive(plan);
    } else {
      toast('Archive functionality coming soon');
    }
  };

  return (
    <Card
      className={`overflow-hidden transition-all duration-200 hover:shadow-md cursor-pointer ${
        isActive ? 'ring-2 ring-primary' : ''
      }`}
      onClick={() => onSelect(plan)}
    >
      <CardContent className="p-0">
        <div className="p-4 border-b">
          <div className="flex justify-between items-start">
            <div>
              <div className="flex items-center gap-2">
                <h3 className="font-medium text-lg">
                  {plan.name || `Meal Plan (${format(startDate, 'MMM d')})`}
                </h3>
                <Badge variant={plan.status === 'active' ? 'default' : plan.status === 'archived' ? 'secondary' : 'outline'}>
                  {plan.status.charAt(0).toUpperCase() + plan.status.slice(1)}
                </Badge>
              </div>
              <div className="text-sm text-muted-foreground mt-1">
                {format(startDate, 'MMM d')} - {format(endDate, 'MMM d, yyyy')}
              </div>
            </div>

            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleFavoriteToggle}
                aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"}
              >
                <Heart className={`h-4 w-4 ${isFavorite ? "fill-red-500 text-red-500" : ""}`} />
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleEdit}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleDuplicate}>
                    <Copy className="h-4 w-4 mr-2" />
                    Duplicate
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleAddToCalendar}>
                    <Calendar className="h-4 w-4 mr-2" />
                    Add to Calendar
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleArchive}>
                    <Archive className="h-4 w-4 mr-2" />
                    Archive
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleDelete}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {plan.description && (
            <p className="text-sm text-muted-foreground mt-2">{plan.description}</p>
          )}

          <div className="flex flex-wrap gap-2 mt-3">
            <Badge variant="outline" className="bg-primary/10">
              {totalMeals} meals
            </Badge>
            {plan.meal_data?.dietaryPreferences?.map((pref: string, index: number) => (
              <Badge key={index} variant="outline">
                {pref}
              </Badge>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-3 divide-x">
          <div className="p-3 text-center">
            <div className="flex flex-col items-center">
              <DollarSign className="h-4 w-4 text-muted-foreground mb-1" />
              <span className="font-medium">${plan.total_cost.toFixed(2)}</span>
              <span className="text-xs text-muted-foreground">Total Cost</span>
            </div>
          </div>
          <div className="p-3 text-center">
            <div className="flex flex-col items-center">
              <Clock className="h-4 w-4 text-muted-foreground mb-1" />
              <span className="font-medium">{totalPrepTime || '--'} min</span>
              <span className="text-xs text-muted-foreground">Prep Time</span>
            </div>
          </div>
          <div className="p-3 text-center">
            <div className="flex flex-col items-center">
              <svg className="h-4 w-4 text-muted-foreground mb-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M4.5 9.5V5.5C4.5 4.4 5.4 3.5 6.5 3.5H17.5C18.6 3.5 19.5 4.4 19.5 5.5V9.5" />
                <path d="M4.5 14.5V18.5C4.5 19.6 5.4 20.5 6.5 20.5H17.5C18.6 20.5 19.5 19.6 19.5 18.5V14.5" />
                <path d="M12 3.5V20.5" />
                <path d="M8.5 7.5C8.5 8.6 7.6 9.5 6.5 9.5C5.4 9.5 4.5 8.6 4.5 7.5C4.5 6.4 5.4 5.5 6.5 5.5C7.6 5.5 8.5 6.4 8.5 7.5Z" />
                <path d="M19.5 7.5C19.5 8.6 18.6 9.5 17.5 9.5C16.4 9.5 15.5 8.6 15.5 7.5C15.5 6.4 16.4 5.5 17.5 5.5C18.6 5.5 19.5 6.4 19.5 7.5Z" />
                <path d="M8.5 16.5C8.5 17.6 7.6 18.5 6.5 18.5C5.4 18.5 4.5 17.6 4.5 16.5C4.5 15.4 5.4 14.5 6.5 14.5C7.6 14.5 8.5 15.4 8.5 16.5Z" />
                <path d="M19.5 16.5C19.5 17.6 18.6 18.5 17.5 18.5C16.4 18.5 15.5 17.6 15.5 16.5C15.5 15.4 16.4 14.5 17.5 14.5C18.6 14.5 19.5 15.4 19.5 16.5Z" />
              </svg>
              <span className="font-medium">{Math.round(avgCaloriesPerDay)}</span>
              <span className="text-xs text-muted-foreground">Cal/Day</span>
            </div>
          </div>
        </div>

        <div className="p-4 pt-2">
          <div className="flex justify-between items-center text-sm mb-1">
            <span>Progress</span>
            <span>{completedMeals}/{totalMeals} meals completed</span>
          </div>
          <Progress value={completionPercentage} className="h-2" />

          {/* Macronutrient breakdown */}
          <div className="mt-4">
            <div className="flex justify-between items-center text-sm mb-1">
              <span>Macronutrients</span>
              <span>{totalMacros}g total</span>
            </div>
            <div className="w-full h-2 rounded-full bg-muted overflow-hidden flex">
              <div
                className="h-full bg-blue-500"
                style={{ width: `${proteinPercentage}%` }}
                title={`Protein: ${proteinValue}g (${Math.round(proteinPercentage)}%)`}
              />
              <div
                className="h-full bg-green-500"
                style={{ width: `${carbsPercentage}%` }}
                title={`Carbs: ${carbsValue}g (${Math.round(carbsPercentage)}%)`}
              />
              <div
                className="h-full bg-red-500"
                style={{ width: `${fatsPercentage}%` }}
                title={`Fats: ${fatsValue}g (${Math.round(fatsPercentage)}%)`}
              />
            </div>
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>P: {proteinValue}g ({Math.round(proteinPercentage)}%)</span>
              <span>C: {carbsValue}g ({Math.round(carbsPercentage)}%)</span>
              <span>F: {fatsValue}g ({Math.round(fatsPercentage)}%)</span>
            </div>
          </div>

          <div className="flex justify-between items-center mt-4">
            <AddToCalendarButton
              mealPlan={plan}
              onClick={handleAddToCalendar}
            />

            <Button
              variant="default"
              size="sm"
              className="gap-1"
              onClick={(e) => {
                e.stopPropagation();
                if (onGenerateShoppingList) {
                  onGenerateShoppingList(plan);
                } else {
                  toast('Shopping list generation coming soon');
                }
              }}
            >
              <ShoppingCart className="h-4 w-4" />
              Shopping List
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="gap-1"
              onClick={(e) => {
                e.stopPropagation();
                onSelect(plan);
              }}
            >
              View Details
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
