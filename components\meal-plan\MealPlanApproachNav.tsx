"use client";

import { usePathname, useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON>, <PERSON>, Sparkles } from 'lucide-react';

export function MealPlanApproachNav() {
  const pathname = usePathname();
  const router = useRouter();
  
  // Determine the current approach based on the pathname
  const getCurrentApproach = () => {
    if (pathname.includes('/EdaSpoon')) {
      return 'edaspoon';
    } else if (pathname.includes('/AI')) {
      return 'ai';
    } else if (pathname.includes('/EdaSpoon-AI')) {
      return 'hybrid';
    } else {
      return 'default';
    }
  };
  
  const currentApproach = getCurrentApproach();
  
  // Handle approach change
  const handleApproachChange = (value: string) => {
    switch (value) {
      case 'edaspoon':
        router.push('/meal-plan/combined/EdaSpoon');
        break;
      case 'ai':
        router.push('/meal-plan/combined/AI');
        break;
      case 'hybrid':
        router.push('/meal-plan/combined/EdaSpoon-AI');
        break;
      default:
        router.push('/meal-plan/combined');
        break;
    }
  };
  
  return (
    <div className="mb-6">
      <Tabs 
        value={currentApproach} 
        onValueChange={handleApproachChange}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="default" className="text-xs">
            Standard
          </TabsTrigger>
          <TabsTrigger value="edaspoon" className="text-xs">
            <Beaker className="h-3 w-3 mr-1" />
            EdaSpoon
          </TabsTrigger>
          <TabsTrigger value="ai" className="text-xs">
            <Brain className="h-3 w-3 mr-1" />
            AI
          </TabsTrigger>
          <TabsTrigger value="hybrid" className="text-xs">
            <Sparkles className="h-3 w-3 mr-1" />
            Hybrid
          </TabsTrigger>
        </TabsList>
      </Tabs>
      
      <div className="mt-2 text-xs text-muted-foreground">
        {currentApproach === 'edaspoon' && (
          <p>EdaSpoon: Uses real recipes from Edamam API and cost information from Spoonacular API.</p>
        )}
        {currentApproach === 'ai' && (
          <p>AI: Uses OpenAI to generate complete meal plans with recipes, nutritional information, and cost estimates.</p>
        )}
        {currentApproach === 'hybrid' && (
          <p>Hybrid: Combines AI-generated meal plans with real recipes and accurate cost information.</p>
        )}
        {currentApproach === 'default' && (
          <p>Standard: The default meal plan experience.</p>
        )}
      </div>
    </div>
  );
}
