"use client";

import Link from 'next/link';
import { SupabaseTest } from '@/components/debug/SupabaseTest';

export default function DebugPage() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Debug Tools</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Link href="/debug/meal-plan" className="p-4 border rounded-md hover:bg-gray-50 transition-colors">
          <h2 className="text-lg font-bold mb-2">Meal Plan Debug</h2>
          <p className="text-gray-600">Test meal plan database operations</p>
        </Link>

        <Link href="/debug/meal-plan-calendar" className="p-4 border rounded-md hover:bg-gray-50 transition-colors">
          <h2 className="text-lg font-bold mb-2">Meal Plan Calendar Debug</h2>
          <p className="text-gray-600">Test meal plan calendar assignments</p>
        </Link>

        <Link href="/debug/edamam-test" className="p-4 border rounded-md hover:bg-gray-50 transition-colors">
          <h2 className="text-lg font-bold mb-2">Edamam API Test</h2>
          <p className="text-gray-600">Test Edamam API integration</p>
        </Link>

        <Link href="/debug/auth-test" className="p-4 border rounded-md hover:bg-gray-50 transition-colors">
          <h2 className="text-lg font-bold mb-2">Authentication Test</h2>
          <p className="text-gray-600">Test Supabase authentication</p>
        </Link>
      </div>

      <h2 className="text-xl font-bold mb-4">Supabase Connection Test</h2>
      <SupabaseTest />
    </div>
  );
}
