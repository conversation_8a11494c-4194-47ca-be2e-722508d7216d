### **Frontend Architecture Specification: LeanEats MVP**

**Objective:** To outline the core technical stack, project structure, state management, API integration, and key development considerations for the LeanEats frontend application, based on the provided UI/UX Specification.

-----

#### **1. Technology Stack**

  * **Framework:** **Next.js (React)**
      * **Rationale:** Provides excellent developer experience, server-side rendering (SSR) for initial load performance and SEO (though less critical for a logged-in app, beneficial for public pages if any), file-system based routing, API routes for backend-for-frontend (BFF) patterns, and a strong community.
  * **Language:** **TypeScript**
      * **Rationale:** Enhances code quality, reduces runtime errors, improves developer productivity with type checking and intelligent auto-completion, especially crucial for large applications and team collaboration.
  * **State Management:** **Zustand** (or React Context API for simpler, localized states)
      * **Rationale:** Zustand is lightweight, flexible, and provides a simple API for global state management. It avoids boilerplate often associated with Redux, making it faster to develop with while being highly performant. React Context will be used for component-tree specific states (e.g., theming, user session within a sub-tree).
  * **Styling:** **Tailwind CSS** with **CSS Modules** for complex components
      * **Rationale:** Tailwind CSS offers rapid UI development with utility-first classes, ensuring consistency and responsiveness. CSS Modules will be used for specific, highly complex components where encapsulated styles are beneficial.
  * **API Communication:** **React Query (TanStack Query)** with **Axios**
      * **Rationale:** React Query provides powerful data fetching, caching, synchronization, and error handling capabilities out-of-the-box, drastically simplifying API interactions. Axios will be the HTTP client.
  * **Form Management:** **React Hook Form** with **Zod** for schema validation
      * **Rationale:** React Hook Form provides un-controlled components for performance and simplifies form state management and validation. Zod is a TypeScript-first schema declaration and validation library, offering robust and reusable validation logic.
  * **Icons:** **React Icons**
      * **Rationale:** A popular library providing a wide range of customizable SVG icons from various icon libraries.

-----

#### **2. Project Structure**

A clear and consistent project structure is vital for maintainability and scalability.

```
/src
├── app/                  # Next.js App Router root (pages, layouts)
│   ├── (auth)/           # Route group for authentication pages (login, signup, forgot-password)
│   │   ├── login/
│   │   │   └── page.tsx
│   │   └── signup/
│   │       └── page.tsx
│   ├── (dashboard)/      # Route group for authenticated user pages
│   │   ├── layout.tsx    # Layout for authenticated routes (sidebar, header)
│   │   ├── dashboard/
│   │   │   └── page.tsx  # Dashboard overview
│   │   ├── meal-plans/
│   │   │   ├── page.tsx  # Meal plan list/overview
│   │   │   └── [id]/     # Dynamic route for specific meal plan details
│   │   │       └── page.tsx
│   │   ├── shopping-list/
│   │   │   └── page.tsx
│   │   ├── recipes/
│   │   │   ├── page.tsx  # Recipe search/browse
│   │   │   └── [id]/     # Dynamic route for specific recipe details
│   │   │       └── page.tsx
│   │   └── settings/
│   │       ├── page.tsx  # Settings overview (redirects to default sub-setting)
│   │       ├── profile/
│   │       │   └── page.tsx
│   │       ├── preferences/
│   │       │   └── page.tsx
│   │       ├── account-security/
│   │       │   └── page.tsx
│   │       ├── notifications/
│   │       │   └── page.tsx
│   │       ├── privacy/
│   │       │   └── page.tsx
│   │       └── data-export/
│   │           └── page.tsx
│   └── layout.tsx        # Root layout (html, body, providers)
│   └── page.tsx          # Public landing page
│   └── global.css        # Tailwind CSS directives
├── components/           # Reusable UI components (pure UI, no business logic)
│   ├── ui/               # Generic UI primitives (Button, Input, Modal, Card)
│   │   └── Button.tsx
│   │   └── Input.tsx
│   │   └── Modal.tsx
│   ├── layout/           # Layout components (Header, Sidebar, Footer)
│   │   └── Header.tsx
│   │   └── Sidebar.tsx
│   ├── onboarding/       # Specific components for the onboarding wizard
│   │   └── WizardStep.tsx
│   │   └── ProgressBar.tsx
│   ├── dashboard/        # Specific components for the dashboard
│   │   └── MealPlanCard.tsx
│   │   └── QuickActionButton.tsx
│   ├── recipes/          # Specific components for recipes
│   │   └── RecipeCard.tsx
│   │   └── IngredientsList.tsx
│   └── common/           # Other common components (LoadingSpinner, ErrorMessage)
│       └── LoadingSpinner.tsx
├── hooks/                # Custom React Hooks (reusable logic)
│   ├── useAuth.ts
│   ├── useMealPlan.ts
│   └── usePreferences.ts
├── lib/                  # Utility functions, helpers, constants, types
│   ├── api.ts            # Axios instance, API client functions
│   ├── constants.ts
│   ├── utils.ts          # General utility functions
│   ├── types.ts          # Global TypeScript types and interfaces
│   ├── validation.ts     # Zod schemas for validation
│   └── calendar.ts       # Logic for calendar integration
├── context/              # React Context providers for global state (if not using Zustand for everything)
│   ├── AuthContext.tsx
│   └── PreferencesContext.tsx
├── store/                # Zustand stores for global state
│   ├── authStore.ts
│   ├── mealPlanStore.ts
│   └── shoppingListStore.ts
├── styles/               # Component-specific CSS Modules (if needed)
├── services/             # Functions interacting with external APIs (backend, Spoonacular, Edamam, OpenAI)
│   ├── authService.ts
│   ├── mealPlanService.ts
│   ├── recipeService.ts
│   ├── shoppingListService.ts
│   ├── userService.ts
│   └── notificationService.ts
├── public/               # Static assets
│   └── images/
│   └── favicon.ico
├── tests/                # Jest/React Testing Library setup
│   ├── components/
│   ├── hooks/
│   └── pages/
└── README.md
└── package.json
└── tsconfig.json
└── postcss.config.js     # For Tailwind CSS
└── tailwind.config.ts    # For Tailwind CSS
```

-----

#### **3. State Management Strategy**

  * **Global State (Zustand):**
      * **User Session:** Authentication status, user profile data (`authStore.ts`).
      * **Meal Plans:** Current active meal plan, generated plans history (`mealPlanStore.ts`).
      * **Shopping List:** Current active shopping list (`shoppingListStore.ts`).
      * **User Preferences:** Dietary, budget, cooking preferences (`preferencesStore.ts`).
      * **Notifications:** Unread count, list of notifications (`notificationStore.ts`).
  * **Server State (React Query):**
      * All data fetched from the backend (recipes, meal plans, shopping lists, user profile details) will be managed by React Query for caching, automatic re-fetching, background updates, and error handling. This separates server data from client UI state effectively.
  * **Local Component State (useState/useReducer):**
      * For ephemeral UI states (e.g., form input values before submission, modal open/close state, loading indicators specific to a component).

-----

#### **4. API Integration**

  * **Centralized API Client:** A single `axios` instance configured in `lib/api.ts` with base URL, headers (e.g., Authorization token from `authStore`), and interceptors for error handling (e.g., refreshing tokens, logging out on 401).
  * **Service Layer (`/services`):** Each major resource (auth, meal plans, recipes, shopping lists, user profile) will have its own service file. These files will encapsulate API calls and data transformation, providing clean functions for React Query hooks.
  * **React Query Hooks (`/hooks`):** Custom hooks (e.g., `useGetMealPlan`, `useUpdatePreferences`, `useGenerateMealPlan`) will wrap the service calls, providing `isLoading`, `isError`, `data`, `error` states, and mutation functions.
  * **Error Handling:**
      * **API Interceptors:** Catch network or API-specific errors.
      * **React Query's `onError` callbacks:** Handle specific errors per query/mutation, showing user-friendly toast messages (`ErrorHandling.md`).
      * **Client-side Error Boundaries:** For React rendering errors (`ErrorHandling.md`).
      * **Centralized Notification System:** A global toast notification component will be triggered by `onError` callbacks or direct service responses.

-----

#### **5. Component Design System & Reusability**

  * **Atomic Design Principles (applied loosely):**
      * **Atoms (`components/ui`):** Basic HTML elements styled (Button, Input, Checkbox, Radio, Textarea, Typography components).
      * **Molecules (`components/common`, `components/ui`):** Combinations of atoms (e.g., FormField, Card, Modal, Dropdown).
      * **Organisms (`components/dashboard`, `components/recipes`, etc.):** More complex UI sections (e.g., WeeklyMealPlanOverview, RecipeDetailCard, ShoppingListItem).
  * **Theming:** Define primary colors, typography, spacing, and border-radii within `tailwind.config.ts` for consistent branding.
  * **Responsiveness:** Primarily achieved using Tailwind's responsive utility classes (`sm:`, `md:`, `lg:`). Flexible layouts (Flexbox, Grid) will be extensively used.

-----

#### **6. Routing**

  * Next.js App Router will handle all routing.
  * Route groups `(auth)` and `(dashboard)` will manage layouts and middleware (for authentication checks).
  * Dynamic routes `[id]` will be used for specific recipe or meal plan details.

-----

#### **7. Form Management**

  * **React Hook Form:** Used for all user input forms (login, signup, preferences, profile settings, add custom item).
  * **Zod:** Define schemas for form validation, ensuring data integrity before API calls. This allows for clear error messages and type-safe form data.

-----

#### **8. Performance Considerations**

  * **Code Splitting & Lazy Loading:** Next.js handles this automatically for pages. For large components not immediately visible, `React.lazy` and `Suspense` can be used.
  * **Image Optimization:** Use Next.js `Image` component for automatic optimization, responsive images, and lazy loading.
  * **Data Caching:** React Query's caching mechanisms will minimize unnecessary API calls.
  * **SSR/SSG (Selective):** While Next.js provides SSR, for highly dynamic, user-specific data, client-side fetching with React Query will be used after initial render. Public-facing pages (like a landing page, if implemented) can leverage SSR/SSG.

-----

#### **9. Accessibility (A11y)**

  * Semantic HTML will be prioritized.
  * ARIA attributes will be used where necessary for custom UI components.
  * Keyboard navigation will be considered for interactive elements.
  * Color contrast will be checked against WCAG guidelines during styling.

-----

#### **10. Testing Strategy**

  * **Unit Tests:** Jest and React Testing Library for individual components, hooks, and utility functions (`/tests/components`, `/tests/hooks`, `/tests/lib`).
  * **Integration Tests:** React Testing Library for testing interactions between multiple components or with API mocks (`/tests/pages`).
  * **End-to-End Tests (Future):** Playwright or Cypress for full user flow testing (e.g., complete onboarding, meal plan generation to shopping list).

-----

#### **11. Deployment Considerations**

  * **Platform:** Vercel (recommended for Next.js applications due to seamless integration, automatic scaling, and CI/CD).
  * **Environment Variables:** Securely manage API keys and other sensitive configurations.
  * **Build Process:** Next.js handles the build process, generating optimized bundles.

-----
