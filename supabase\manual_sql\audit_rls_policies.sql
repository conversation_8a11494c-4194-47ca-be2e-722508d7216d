-- This script audits all RLS policies in your Supabase database
-- Run this script in the Supabase SQL Editor

-- Create a temporary table to store the results
CREATE TEMP TABLE audit_results (
    table_name TEXT,
    rls_enabled BOOLEAN,
    policy_name TEXT,
    command TEXT,
    roles TEXT[],
    using_expr TEXT,
    check_expr TEXT
);

-- Get all tables in the public schema
DO $$
DECLARE
    r RECORD;
    rls_enabled BOOLEAN;
BEGIN
    FOR r IN (
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
    ) LOOP
        -- Check if RLS is enabled
        SELECT relrowsecurity INTO rls_enabled
        FROM pg_class 
        WHERE oid = (r.table_name::regclass)::oid;
        
        -- If no policies, insert a single row with NULL policy details
        IF rls_enabled THEN
            -- Get policies for this table
            INSERT INTO audit_results
            SELECT 
                r.table_name,
                rls_enabled,
                polname::text,
                CASE
                    WHEN polcmd = 'r' THEN 'SELECT'
                    WHEN polcmd = 'a' THEN 'INSERT'
                    WHEN polcmd = 'w' THEN 'UPDATE'
                    WHEN polcmd = 'd' THEN 'DELETE'
                    WHEN polcmd = '*' THEN 'ALL'
                END,
                polroles::text[],
                pg_get_expr(polqual, polrelid, true),
                pg_get_expr(polwithcheck, polrelid, true)
            FROM pg_policy
            JOIN pg_class ON pg_class.oid = pg_policy.polrelid
            WHERE relname = r.table_name;
            
            -- If no policies found, insert a row indicating RLS is enabled but no policies
            IF NOT EXISTS (SELECT 1 FROM audit_results WHERE table_name = r.table_name) THEN
                INSERT INTO audit_results (table_name, rls_enabled, policy_name)
                VALUES (r.table_name, rls_enabled, 'NO POLICIES (restrictive)');
            END IF;
        ELSE
            -- RLS is not enabled
            INSERT INTO audit_results (table_name, rls_enabled)
            VALUES (r.table_name, rls_enabled);
        END IF;
    END LOOP;
END $$;

-- Display the results in a formatted way
SELECT 
    table_name AS "Table",
    rls_enabled AS "RLS Enabled",
    policy_name AS "Policy Name",
    command AS "Command",
    roles AS "Roles",
    using_expr AS "USING Expression",
    check_expr AS "WITH CHECK Expression"
FROM audit_results
ORDER BY table_name, policy_name;

-- Clean up
DROP TABLE audit_results;
