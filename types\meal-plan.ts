export interface Nutrition {
  calories: number;
  protein: string;
  carbs: string;
  fats: string;
}

export interface Meal {
  type: string;
  name: string;
  recipeId?: string;
  prepTime: number;
  cost: number;
  servings: number;
  image: string;
  nutrition: Nutrition;
}

export interface DayPlan {
  day: string;
  meals: Meal[];
}

export interface MealPlanSummary {
  totalCost: number;
  averageCalories: number;
  totalCalories?: number;
  totalPrepTime?: number;
  macros: {
    protein: string;
    carbs: string;
    fats: string;
  };
}

export interface MealPlanData {
  mealPlan: {
    week: DayPlan[];
    summary: MealPlanSummary;
    meals?: Meal[];
    totalCalories?: number;
    totalPrepTime?: number;
  };
}

export interface MealPlanResponse {
  mealPlan: {
    week: DayPlan[];
    summary: MealPlanSummary;
  };
}

export interface MealPlan {
  id: string;
  user_id: string;
  start_date: string;
  end_date: string;
  total_cost: number;
  meal_data: MealPlanData;
  status: string;
  created_at: string;
  updated_at: string;
  name?: string;
  description?: string;
  is_favorite?: boolean;
  is_archived?: boolean;
}