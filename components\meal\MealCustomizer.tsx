'use client';

import { useState } from 'react';
import { Meal, MealCustomization } from '@/types/mealPlanner';
import { Button } from '@/components/ui/button';

interface MealCustomizerProps {
  meal: Meal;
  onSave: (customization: MealCustomization) => void;
  onCancel: () => void;
}

export function MealCustomizer({ meal, onSave, onCancel }: MealCustomizerProps) {
  const [customization, setCustomization] = useState<MealCustomization>({
    portionSize: meal.customization?.portionSize || 1,
    servingSize: meal.customization?.servingSize || meal.originalServings,
    cookingTime: meal.customization?.cookingTime || meal.prepTime,
    ingredients: meal.customization?.ingredients || []
  });

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-medium">Customize {meal.name}</h2>
      {/* Add customization controls */}
      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={onCancel}>Cancel</Button>
        <Button onClick={() => onSave(customization)}>Save</Button>
      </div>
    </div>
  );
}