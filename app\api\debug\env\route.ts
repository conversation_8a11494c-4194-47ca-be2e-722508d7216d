import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  // Only return environment variables that start with NEXT_PUBLIC_
  const publicEnvVars: Record<string, string> = {};
  
  Object.keys(process.env).forEach(key => {
    if (key.startsWith('NEXT_PUBLIC_')) {
      publicEnvVars[key] = process.env[key] || '';
    }
  });
  
  return NextResponse.json({
    publicEnvVars
  });
}
