-- Migration: Standardize meal_plans table
-- This migration ensures the meal_plans table has a consistent structure
-- and proper RLS policies

-- First, record this migration
INSERT INTO public.schema_migrations (version, description)
VALUES ('20230804000000', 'Standardize meal_plans table')
ON CONFLICT (version) DO NOTHING;

-- Ensure the meal_plans table exists with the correct structure
CREATE TABLE IF NOT EXISTS public.meal_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_cost DOUBLE PRECISION NOT NULL,
    meal_data JSONB NOT NULL,
    status TEXT NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add foreign key constraint if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'meal_plans_user_id_fkey' 
        AND conrelid = 'public.meal_plans'::regclass
    ) THEN
        ALTER TABLE public.meal_plans
        ADD CONSTRAINT meal_plans_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END
$$;

-- Add comment to the table
COMMENT ON TABLE public.meal_plans IS 'Stores user meal plans';

-- Enable Row Level Security
ALTER TABLE public.meal_plans ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own meal plans" ON public.meal_plans;
DROP POLICY IF EXISTS "Users can insert their own meal plans" ON public.meal_plans;
DROP POLICY IF EXISTS "Users can update their own meal plans" ON public.meal_plans;
DROP POLICY IF EXISTS "Users can delete their own meal plans" ON public.meal_plans;
DROP POLICY IF EXISTS "Enable read for users based on user_id" ON public.meal_plans;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.meal_plans;
DROP POLICY IF EXISTS "Enable update for users based on user_id" ON public.meal_plans;
DROP POLICY IF EXISTS "Enable delete for users based on user_id" ON public.meal_plans;
DROP POLICY IF EXISTS "Users can create their own meal plans" ON public.meal_plans;
DROP POLICY IF EXISTS "Users can manage own meal plans" ON public.meal_plans;

-- Create standardized policies
CREATE POLICY "Users can view their own meal plans"
ON public.meal_plans
FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own meal plans"
ON public.meal_plans
FOR INSERT
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own meal plans"
ON public.meal_plans
FOR UPDATE
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete their own meal plans"
ON public.meal_plans
FOR DELETE
USING (user_id = auth.uid());

-- Grant permissions to authenticated users
GRANT ALL ON public.meal_plans TO authenticated;
