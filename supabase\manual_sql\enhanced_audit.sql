-- Enhanced audit script for Supabase database
-- This script audits RLS policies and schema information

-- Part 1: Audit RLS Policies
-- Create a temporary table to store the results
CREATE TEMP TABLE audit_results (
    table_name TEXT,
    rls_enabled BOOLEAN,
    policy_name TEXT,
    command TEXT,
    roles TEXT[],
    using_expr TEXT,
    check_expr TEXT
);

-- Get all tables in the public schema
DO $$
DECLARE
    r RECORD;
    rls_enabled BOOLEAN;
BEGIN
    FOR r IN (
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
    ) LOOP
        -- Check if RLS is enabled
        SELECT relrowsecurity INTO rls_enabled
        FROM pg_class 
        WHERE oid = (r.table_name::regclass)::oid;
        
        -- If no policies, insert a single row with NULL policy details
        IF rls_enabled THEN
            -- Get policies for this table
            INSERT INTO audit_results
            SELECT 
                r.table_name,
                rls_enabled,
                polname::text,
                CASE
                    WHEN polcmd = 'r' THEN 'SELECT'
                    WHEN polcmd = 'a' THEN 'INSERT'
                    WHEN polcmd = 'w' THEN 'UPDATE'
                    WHEN polcmd = 'd' THEN 'DELETE'
                    WHEN polcmd = '*' THEN 'ALL'
                END,
                polroles::text[],
                pg_get_expr(polqual, polrelid, true),
                pg_get_expr(polwithcheck, polrelid, true)
            FROM pg_policy
            JOIN pg_class ON pg_class.oid = pg_policy.polrelid
            WHERE relname = r.table_name;
            
            -- If no policies found, insert a row indicating RLS is enabled but no policies
            IF NOT EXISTS (SELECT 1 FROM audit_results WHERE table_name = r.table_name) THEN
                INSERT INTO audit_results (table_name, rls_enabled, policy_name)
                VALUES (r.table_name, rls_enabled, 'NO POLICIES (restrictive)');
            END IF;
        ELSE
            -- RLS is not enabled
            INSERT INTO audit_results (table_name, rls_enabled)
            VALUES (r.table_name, rls_enabled);
        END IF;
    END LOOP;
END $$;

-- Display the RLS policy results
SELECT 
    table_name AS "Table",
    rls_enabled AS "RLS Enabled",
    policy_name AS "Policy Name",
    command AS "Command",
    roles AS "Roles",
    using_expr AS "USING Expression",
    check_expr AS "WITH CHECK Expression"
FROM audit_results
ORDER BY table_name, policy_name;

-- Part 2: Audit Schema Information
-- Create a temporary table for schema information
CREATE TEMP TABLE schema_info (
    table_name TEXT,
    column_name TEXT,
    data_type TEXT,
    is_nullable TEXT,
    column_default TEXT,
    is_identity TEXT,
    is_foreign_key BOOLEAN,
    references_table TEXT,
    references_column TEXT
);

-- Get column information
INSERT INTO schema_info (table_name, column_name, data_type, is_nullable, column_default, is_identity)
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default,
    is_identity
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
ORDER BY 
    table_name, ordinal_position;

-- Update foreign key information
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (
        SELECT
            tc.table_name,
            kcu.column_name,
            ccu.table_name AS references_table,
            ccu.column_name AS references_column
        FROM
            information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_schema = 'public'
    ) LOOP
        UPDATE schema_info
        SET 
            is_foreign_key = TRUE,
            references_table = r.references_table,
            references_column = r.references_column
        WHERE 
            table_name = r.table_name
            AND column_name = r.column_name;
    END LOOP;
END $$;

-- Display the schema information
SELECT 
    table_name AS "Table",
    column_name AS "Column",
    data_type AS "Data Type",
    is_nullable AS "Nullable",
    column_default AS "Default",
    is_identity AS "Identity",
    is_foreign_key AS "Foreign Key",
    references_table AS "References Table",
    references_column AS "References Column"
FROM 
    schema_info
ORDER BY 
    table_name, column_name;

-- Part 3: Summary of Tables with RLS Issues
SELECT 
    table_name AS "Table",
    CASE 
        WHEN NOT rls_enabled THEN 'RLS not enabled'
        WHEN policy_name = 'NO POLICIES (restrictive)' THEN 'RLS enabled but no policies'
        ELSE NULL
    END AS "Issue"
FROM 
    audit_results
WHERE 
    NOT rls_enabled 
    OR policy_name = 'NO POLICIES (restrictive)'
GROUP BY 
    table_name, rls_enabled, policy_name
ORDER BY 
    table_name;

-- Clean up
DROP TABLE audit_results;
DROP TABLE schema_info;
