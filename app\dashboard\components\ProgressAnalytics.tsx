'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useEffect, useState } from "react";

export function ProgressAnalytics() {
  const [weeklyProgress, setWeeklyProgress] = useState(0);
  const [budgetUsage, setBudgetUsage] = useState(0);

  useEffect(() => {
    // TODO: Replace with actual data fetching
    setWeeklyProgress(75);
    setBudgetUsage(60);
  }, []);

  return (
    <div className="grid gap-4 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Weekly Meal Plan Progress</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Progress value={weeklyProgress} />
            <p className="text-sm text-muted-foreground">
              {weeklyProgress}% of meals planned for this week
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Budget Usage</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Progress value={budgetUsage} />
            <p className="text-sm text-muted-foreground">
              {budgetUsage}% of weekly budget used
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}


