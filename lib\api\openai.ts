"use client";

import OpenAI from 'openai';
import { MealGenerationOptions } from '@/types/new-meal-plan';
import { MealPlan } from '@/types/new-meal-plan';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true // For client-side usage
});

// Cache for OpenAI responses to reduce API calls
const responseCache = new Map<string, any>();

/**
 * Generate a meal plan using OpenAI
 */
export async function generateMealPlanWithAI(options: MealGenerationOptions): Promise<MealPlan> {
  try {
    // Create a cache key based on the options
    const cacheKey = JSON.stringify(options);

    // Check if we have a cached response
    if (responseCache.has(cacheKey)) {
      console.log('Using cached meal plan');
      return responseCache.get(cacheKey);
    }

    // Create the system prompt
    const systemPrompt = `You are a professional nutritionist and meal planning expert. Your task is to create a detailed, nutritionally balanced meal plan based on the user's preferences and requirements.

The meal plan should include:
1. A variety of recipes for each meal type (breakfast, lunch, dinner, and optional snacks)
2. Detailed ingredient lists with quantities
3. Nutritional information for each meal (calories, protein, carbs, fat)
4. Estimated cost for each meal and ingredient
5. Simple cooking instructions

Follow these guidelines:
- Create recipes that are realistic and can be prepared at home
- Ensure nutritional balance across the entire plan
- Respect all dietary restrictions and preferences
- Provide reasonable portion sizes
- Estimate costs based on average US grocery prices
- Include a variety of foods to ensure nutritional completeness
- Consider the cooking time constraints specified by the user

Format your response as a JSON object with the following structure:
{
  "mealPlan": {
    "days": [
      {
        "date": "YYYY-MM-DD",
        "meals": [
          {
            "type": "breakfast|lunch|dinner|snack",
            "name": "Recipe name",
            "ingredients": [
              {
                "name": "Ingredient name",
                "amount": number,
                "unit": "Unit of measurement",
                "cost": number
              }
            ],
            "instructions": ["Step 1", "Step 2", ...],
            "nutrition": {
              "calories": number,
              "protein": number,
              "carbs": number,
              "fat": number
            },
            "totalCost": number,
            "prepTime": number,
            "cookTime": number
          }
        ],
        "dailyNutrition": {
          "calories": number,
          "protein": number,
          "carbs": number,
          "fat": number
        },
        "dailyCost": number
      }
    ],
    "totalCost": number,
    "averageDailyCost": number
  }
}`;

    // Create the user prompt
    const userPrompt = `Generate a meal plan with the following specifications:

Duration: ${options.days} days
Meals per day: ${options.mealsPerDay}
Target calories: ${options.calories} per day
Dietary preferences: ${options.dietaryPreferences.join(', ')}
Excluded ingredients: ${options.excludeIngredients.join(', ')}
Budget level: ${options.budget}
Maximum cooking time: ${options.cookingTime}

Additional notes: ${options.additionalNotes || 'No additional notes.'}`;

    console.log('Generating meal plan with OpenAI...');

    // Call OpenAI API
    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo-1106',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.7,
      max_tokens: 4000,
      response_format: { type: 'json_object' }
    });

    // Extract the content from the response
    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error('No content in OpenAI response');
    }

    // Parse the JSON response
    let parsedResponse;
    try {
      // Try to clean up the response if it's malformed
      let cleanedContent = content;

      // Replace any unescaped quotes in strings
      cleanedContent = cleanedContent.replace(/([\{\[,]\s*"[^"]*)("[^"]*")(\s*[\}\],])/g, '$1\\"$2\\"$3');

      // Try to parse the cleaned content
      try {
        parsedResponse = JSON.parse(cleanedContent);
      } catch (cleanedError) {
        // If that fails, try the original content
        parsedResponse = JSON.parse(content);
      }
    } catch (parseError) {
      console.error('Error parsing OpenAI response:', parseError);
      console.log('Raw response content:', content);

      // Create a simple fallback response
      parsedResponse = {
        mealPlan: {
          days: []
        }
      };

      throw new Error('Failed to parse OpenAI response as JSON');
    }

    // Transform the OpenAI response to our internal format
    const mealPlan = transformOpenAIMealPlanToInternal(parsedResponse);

    // Cache the response
    responseCache.set(cacheKey, mealPlan);

    return mealPlan;
  } catch (error) {
    console.error('Error generating meal plan with OpenAI:', error);
    throw error;
  }
}

/**
 * Transform the OpenAI meal plan response to our internal format
 */
function transformOpenAIMealPlanToInternal(openAIResponse: any): MealPlan {
  const mealPlan: MealPlan = {};

  // Process each day in the meal plan
  openAIResponse.mealPlan.days.forEach((day: any) => {
    const dateStr = day.date;
    mealPlan[dateStr] = {};

    // Process each meal in the day
    day.meals.forEach((meal: any) => {
      // Create a unique ID for the meal
      const mealId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

      // Add the meal to the meal plan
      mealPlan[dateStr][meal.type] = {
        id: mealId,
        name: meal.name,
        image: '', // OpenAI doesn't provide images
        ingredients: meal.ingredients.map((ingredient: any) => ({
          name: ingredient.name,
          amount: ingredient.amount,
          unit: ingredient.unit
        })),
        instructions: meal.instructions,
        cost: meal.totalCost,
        calories: meal.nutrition.calories,
        prepTime: meal.prepTime || 0,
        cookTime: meal.cookTime || 0,
        macros: {
          protein: meal.nutrition.protein,
          carbs: meal.nutrition.carbs,
          fats: meal.nutrition.fat
        },
        nutrition: {
          protein: meal.nutrition.protein,
          carbs: meal.nutrition.carbs,
          fat: meal.nutrition.fat
        },
        status: null
      };
    });
  });

  return mealPlan;
}

/**
 * Generate a single recipe using OpenAI
 */
export async function generateRecipeWithAI(options: {
  mealType: string;
  calories: number;
  dietaryPreferences: string[];
  excludeIngredients: string[];
  cookingTime: string;
}): Promise<any> {
  try {
    // Create the system prompt
    const systemPrompt = `You are a professional chef and recipe developer. Your task is to create a detailed recipe based on the user's preferences and requirements.

The recipe should include:
1. A descriptive name
2. Detailed ingredient list with quantities
3. Nutritional information (calories, protein, carbs, fat)
4. Estimated cost for each ingredient and total cost
5. Step-by-step cooking instructions
6. Preparation and cooking time

Format your response as a JSON object with the following structure:
{
  "recipe": {
    "name": "Recipe name",
    "ingredients": [
      {
        "name": "Ingredient name",
        "amount": number,
        "unit": "Unit of measurement",
        "cost": number
      }
    ],
    "instructions": ["Step 1", "Step 2", ...],
    "nutrition": {
      "calories": number,
      "protein": number,
      "carbs": number,
      "fat": number
    },
    "totalCost": number,
    "prepTime": number,
    "cookTime": number
  }
}`;

    // Create the user prompt
    const userPrompt = `Generate a ${options.mealType} recipe with the following specifications:

Target calories: ${options.calories}
Dietary preferences: ${options.dietaryPreferences.join(', ')}
Excluded ingredients: ${options.excludeIngredients.join(', ')}
Maximum cooking time: ${options.cookingTime}`;

    // Call OpenAI API
    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo-1106',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.7,
      max_tokens: 1000,
      response_format: { type: 'json_object' }
    });

    // Extract the content from the response
    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error('No content in OpenAI response');
    }

    // Parse the JSON response
    try {
      // Try to clean up the response if it's malformed
      let cleanedContent = content;

      // Replace any unescaped quotes in strings
      cleanedContent = cleanedContent.replace(/([\{\[,]\s*"[^"]*)("[^"]*")(\s*[\}\],])/g, '$1\\"$2\\"$3');

      // Try to parse the cleaned content
      try {
        return JSON.parse(cleanedContent);
      } catch (cleanedError) {
        // If that fails, try the original content
        return JSON.parse(content);
      }
    } catch (parseError) {
      console.error('Error parsing OpenAI response:', parseError);
      console.log('Raw response content:', content);

      // Return a fallback recipe
      return {
        recipe: {
          name: 'Fallback Recipe',
          ingredients: [],
          instructions: [],
          nutrition: {
            calories: 0,
            protein: 0,
            carbs: 0,
            fat: 0
          },
          totalCost: 0,
          prepTime: 0,
          cookTime: 0
        }
      };
    }
  } catch (error) {
    console.error('Error generating recipe with OpenAI:', error);
    throw error;
  }
}

/**
 * Estimate the cost of a recipe using OpenAI
 */
export async function estimateRecipeCostWithAI(recipe: {
  name: string;
  ingredients: { name: string; amount: number; unit: string }[];
}): Promise<number> {
  try {
    // Create the system prompt
    const systemPrompt = `You are a grocery pricing expert. Your task is to estimate the cost of a recipe based on its ingredients.

Provide your estimate based on average US grocery prices.
Format your response as a JSON object with the following structure:
{
  "ingredients": [
    {
      "name": "Ingredient name",
      "amount": number,
      "unit": "Unit of measurement",
      "cost": number
    }
  ],
  "totalCost": number
}`;

    // Create the user prompt
    const userPrompt = `Estimate the cost of the following recipe:

Recipe: ${recipe.name}
Ingredients:
${recipe.ingredients.map(ing => `- ${ing.amount} ${ing.unit} ${ing.name}`).join('\n')}`;

    // Call OpenAI API
    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo-1106',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.3,
      max_tokens: 500,
      response_format: { type: 'json_object' }
    });

    // Extract the content from the response
    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error('No content in OpenAI response');
    }

    // Parse the JSON response
    try {
      // Try to clean up the response if it's malformed
      let cleanedContent = content;

      // Replace any unescaped quotes in strings
      cleanedContent = cleanedContent.replace(/([\{\[,]\s*"[^"]*)("[^"]*")(\s*[\}\],])/g, '$1\\"$2\\"$3');

      // Try to parse the cleaned content
      try {
        const parsedResponse = JSON.parse(cleanedContent);
        return parsedResponse.totalCost;
      } catch (cleanedError) {
        // If that fails, try the original content
        const parsedResponse = JSON.parse(content);
        return parsedResponse.totalCost;
      }
    } catch (parseError) {
      console.error('Error parsing OpenAI response:', parseError);
      console.log('Raw response content:', content);

      // Return a fallback cost
      return 5.0; // Default cost of $5
    }
  } catch (error) {
    console.error('Error estimating recipe cost with OpenAI:', error);
    throw error;
  }
}
