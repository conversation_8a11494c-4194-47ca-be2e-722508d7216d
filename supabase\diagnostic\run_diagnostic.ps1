# This script runs the diagnostic SQL using the Supabase CLI
# It requires the Supabase CLI to be installed and configured

# Check if Supabase CLI is installed
if (-not (Get-Command "supabase" -ErrorAction SilentlyContinue)) {
    Write-Error "Error: Supabase CLI is not installed."
    Write-Error "Please install it by following the instructions at https://supabase.com/docs/guides/cli"
    exit 1
}

# Create the results directory if it doesn't exist
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$resultsDir = Join-Path $scriptDir "results"
if (-not (Test-Path $resultsDir)) {
    New-Item -ItemType Directory -Path $resultsDir | Out-Null
}

# Get the current timestamp
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"

# Run the diagnostic SQL and save the results
Write-Host "Running database diagnostic..."
$sqlFile = Join-Path $scriptDir "check_database.sql"
$outputFile = Join-Path $resultsDir "diagnostic_results_$timestamp.txt"
supabase db execute --file $sqlFile > $outputFile

Write-Host "Diagnostic complete. Results saved to $outputFile"
