'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { StickyNote, Save, X } from 'lucide-react';
import { useMealNote } from '@/app/hooks/useMealNotes';

interface MealNotesModalProps {
  isOpen: boolean;
  onClose: () => void;
  mealPlanId: string;
  mealId: string;
  mealName: string;
}

export function MealNotesModal({
  isOpen,
  onClose,
  mealPlanId,
  mealId,
  mealName
}: MealNotesModalProps) {
  const { mealNote, saveNote, isSaving, hasNote } = useMealNote(mealPlanId, mealId);
  const [note, setNote] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (mealNote) {
      setNote(mealNote.note);
    } else {
      setNote('');
    }
    setError('');
    setSuccess('');
  }, [mealNote, isOpen]);

  const handleSave = async () => {
    if (!note.trim()) {
      setError('Please enter a note');
      return;
    }

    try {
      setError('');
      setSuccess('');
      await saveNote(note.trim());
      setSuccess('Note saved successfully!');
      setTimeout(() => {
        setSuccess('');
        onClose();
      }, 1500);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save note');
    }
  };

  const handleClose = () => {
    setNote(mealNote?.note || '');
    setError('');
    setSuccess('');
    onClose();
  };

  const hasChanges = note.trim() !== (mealNote?.note || '');

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <StickyNote className="h-5 w-5" />
            Meal Notes
          </DialogTitle>
          <DialogDescription>
            Add a note for <strong>{mealName}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label htmlFor="note">Your Note</Label>
            <Textarea
              id="note"
              value={note}
              onChange={(e) => setNote(e.target.value)}
              placeholder="Add any customizations, cooking tips, or reminders for this meal..."
              className="min-h-[120px] mt-2"
              maxLength={1000}
              disabled={isSaving}
            />
            <div className="text-sm text-gray-500 mt-1">
              {note.length}/1000 characters
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <AlertDescription className="text-green-600">
                {success}
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isSaving}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={!hasChanges || isSaving || !note.trim()}
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : hasNote ? 'Update Note' : 'Save Note'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function MealNotesButton({
  mealPlanId,
  mealId,
  mealName,
  hasNote = false,
  variant = 'ghost',
  size = 'sm'
}: {
  mealPlanId: string;
  mealId: string;
  mealName: string;
  hasNote?: boolean;
  variant?: 'ghost' | 'outline' | 'default';
  size?: 'sm' | 'md' | 'lg';
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={() => setIsModalOpen(true)}
        className="gap-2"
      >
        <StickyNote className={`h-4 w-4 ${hasNote ? 'text-blue-600' : ''}`} />
        {hasNote ? 'Edit Note' : 'Add Note'}
      </Button>

      <MealNotesModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        mealPlanId={mealPlanId}
        mealId={mealId}
        mealName={mealName}
      />
    </>
  );
}
