// Script to populate Supabase with real recipe data

require('dotenv').config();

console.log("Raw Supabase URL:", process.env.NEXT_PUBLIC_SUPABASE_URL);
console.log("Raw Supabase Key:", process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);

// Remove any extraneous quotes and trim whitespace
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL.replace(/^"|"$/g, '').trim();
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.replace(/^"|"$/g, '').trim();

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL and key must be provided as environment variables');
  process.exit(1);
}

const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(supabaseUrl, supabaseKey);

// We'll get the actual user ID from <PERSON><PERSON><PERSON> Auth in the populateRecipes function

// Define recipes to insert
const recipes = [
  {
    name: "Grilled Chicken Salad",
    description: "A healthy and delicious salad with grilled chicken breast, mixed greens, and a light vinaigrette dressing.",
    prep_time: 15,
    cook_time: 20,
    servings: 2,
    cost_per_serving: 4.50,
    image_url: "https://images.unsplash.com/photo-1527477396000-e27163b481c2",
    meal_type: "Lunch",
    difficulty: "Easy",
    is_favorite: true
  },
  {
    name: "Quinoa Buddha Bowl",
    description: "A nutritious and colorful bowl packed with quinoa, roasted vegetables, and a tahini dressing.",
    prep_time: 20,
    cook_time: 25,
    servings: 2,
    cost_per_serving: 3.75,
    image_url: "https://images.unsplash.com/photo-**********-ba9599a7e63c",
    meal_type: "Dinner",
    difficulty: "Medium",
    is_favorite: false
  },
  {
    name: "Smoothie Bowl",
    description: "A refreshing and nutritious smoothie bowl topped with fresh fruits, granola, and nuts.",
    prep_time: 10,
    cook_time: 0,
    servings: 1,
    cost_per_serving: 2.95,
    image_url: "https://images.unsplash.com/photo-1553530666-ba11a90a0868",
    meal_type: "Breakfast",
    difficulty: "Easy",
    is_favorite: true
  },
  {
    name: "Grilled Salmon with Asparagus",
    description: "Perfectly grilled salmon served with roasted asparagus and a lemon butter sauce.",
    prep_time: 15,
    cook_time: 15,
    servings: 2,
    cost_per_serving: 8.95,
    image_url: "https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2",
    meal_type: "Dinner",
    difficulty: "Medium",
    is_favorite: true
  }
];

async function populateRecipes() {
  console.log('Starting to populate recipes...');

  try {
    // Use a hardcoded user ID for now
    // In a production environment, you would get this from the authenticated user
    const hardcodedUserId = '00000000-0000-0000-0000-000000000000'; // Replace with a real user ID

    console.log('Using hardcoded user ID:', hardcodedUserId);

    // Add user_id to each recipe
    const recipesWithUserId = recipes.map(recipe => ({
      ...recipe,
      user_id: hardcodedUserId
    }));

    // Insert recipes into Supabase
    const { data, error } = await supabase
      .from('recipes')
      .insert(recipesWithUserId)
      .select();

    if (error) {
      console.error('Error inserting recipes:', error);
      process.exit(1);
    }

    console.log(`Successfully inserted ${data.length} recipes:`);
    data.forEach(recipe => {
      console.log(`- ID: ${recipe.id}, Name: ${recipe.name}`);
    });

    console.log('Recipe population complete!');
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Run the population script
populateRecipes().catch(err => {
  console.error('Unexpected error:', err);
  process.exit(1);
});
