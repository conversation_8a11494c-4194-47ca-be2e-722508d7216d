-- Create a function to delete a meal plan
-- This function bypasses R<PERSON> and can be called by the service role
CREATE OR REPLACE FUNCTION public.delete_meal_plan(plan_id UUID, user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  success BOOLEAN;
BEGIN
  -- First check if the meal plan exists and belongs to the user
  IF NOT EXISTS (
    SELECT 1 FROM meal_plans
    WHERE id = plan_id AND user_id = $2  -- Use $2 to refer to the second parameter (user_id)
  ) THEN
    RETURN FALSE;
  END IF;

  -- Delete the meal plan
  DELETE FROM meal_plans
  WHERE id = plan_id AND user_id = $2;  -- Use $2 to refer to the second parameter (user_id)

  -- Check if the deletion was successful
  GET DIAGNOSTICS success = ROW_COUNT;
  RETURN success > 0;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.delete_meal_plan(UUI<PERSON>, UUID) TO authenticated;
