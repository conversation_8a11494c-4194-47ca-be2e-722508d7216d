import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function GET() {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get current week's meal plans
    const startOfWeek = new Date();
    startOfWeek.setHours(0, 0, 0, 0);
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(endOfWeek.getDate() + 6);

    const [weeklyMeals, budgetInfo, trends] = await Promise.all([
      prisma.meal_plans.findMany({
        where: {
          user_id: userId,
          start_date: {
            gte: startOfWeek,
            lte: endOfWeek,
          },
        },
      }),
      prisma.user_preferences.findUnique({
        where: { user_id: userId },
        select: { weekly_budget: true },
      }),
      prisma.meal_plans.groupBy({
        by: ['start_date'],
        where: {
          user_id: userId,
          start_date: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
        _count: {
          id: true,
        },
      }),
    ]);

    // Calculate analytics
    const completedMeals = weeklyMeals.filter(meal => meal.status === 'completed').length;
    const totalMeals = weeklyMeals.length;
    const weeklyProgress = totalMeals ? (completedMeals / totalMeals) * 100 : 0;

    const totalSpent = weeklyMeals.reduce((sum, meal) => sum + (meal.total_cost || 0), 0);
    const budgetRemaining = (budgetInfo?.weekly_budget || 0) - totalSpent;

    // Format trend data
    const trendData = {
      labels: trends.map(t => new Date(t.start_date).toLocaleDateString()),
      datasets: [{
        label: 'Meals Planned',
        data: trends.map(t => t._count.id),
      }],
    };

    return NextResponse.json({
      weeklyProgress,
      budgetUsage: budgetRemaining,
      mealCompletionRate: weeklyProgress,
      trendData,
    });
  } catch (error) {
    console.error('Dashboard analytics error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
}