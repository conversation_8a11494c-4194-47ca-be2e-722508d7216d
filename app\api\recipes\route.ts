import { NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import type { Database } from "@/types/supabase";
import { z } from "zod";
import { recipeService, ClientType, getSupabaseClient } from "@/app/services/database-server";

// Define the schema for recipe validation
const recipeSchema = z.object({
  name: z.string().min(3, "Recipe name must be at least 3 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  prep_time: z.number().min(1, "Prep time must be at least 1 minute"),
  cook_time: z.number().min(0, "Cook time cannot be negative"),
  servings: z.number().min(1, "Servings must be at least 1"),
  cost_per_serving: z.number().min(0, "Cost cannot be negative"),
  cuisine: z.string().min(1, "Please select a cuisine"),
  difficulty: z.string().min(1, "Please select a difficulty level"),
  dietary_restrictions: z.array(z.string()).optional().default([]),
  instructions: z.array(z.string()).min(1, "At least one instruction is required"),
  ingredients: z.array(
    z.object({
      name: z.string().min(1, "Ingredient name is required"),
      amount: z.string().min(1, "Amount is required"),
      unit: z.string().optional(),
    })
  ).min(1, "At least one ingredient is required"),
  nutrition: z.object({
    calories: z.number().min(0, "Calories cannot be negative"),
    protein: z.number().min(0, "Protein cannot be negative"),
    carbs: z.number().min(0, "Carbs cannot be negative"),
    fat: z.number().min(0, "Fat cannot be negative"),
    fiber: z.number().min(0, "Fiber cannot be negative"),
  }),
  image_url: z.string().optional(),
  user_id: z.string().uuid("Invalid user ID"),
});

// GET handler to fetch recipes
export async function GET(request: Request) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();

    if (authError) {
      return NextResponse.json(
        { error: "Authentication error" },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("query") || "";
    const cuisine = searchParams.get("cuisine") || "";
    const favorites = searchParams.get("favorites") === "true";
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");

    // Use the recipe service to fetch recipes
    const { data: recipes, error } = await recipeService.getRecipes({
      userId: session?.user?.id || '',
      query: query || undefined,
      cuisine: cuisine || undefined,
      favorites: favorites || undefined,
      limit,
      offset
    });

    if (error) {
      console.error("Error fetching recipes:", error);
      return NextResponse.json(
        { error: "Failed to fetch recipes" },
        { status: 500 }
      );
    }

    return NextResponse.json(recipes);
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST handler to create a new recipe
export async function POST(request: Request) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();

    if (authError || !session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Parse and validate the request body
    const body = await request.json();

    // Ensure user_id matches the authenticated user
    body.user_id = session.user.id;

    try {
      const validatedData = recipeSchema.parse(body);

      // Use the recipe service to create the recipe
      const { data: recipe, error } = await recipeService.createRecipe(validatedData);

      if (error) {
        console.error("Error creating recipe:", error);
        return NextResponse.json(
          { error: "Failed to create recipe" },
          { status: 500 }
        );
      }

      return NextResponse.json(recipe, { status: 201 });
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Validation error", details: validationError.format() },
          { status: 400 }
        );
      }
      throw validationError;
    }
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
