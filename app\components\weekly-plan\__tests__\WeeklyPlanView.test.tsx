import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { WeeklyPlanView } from '../WeeklyPlanView';
import { mockWeeklyPlan } from '@/test/mocks/mealPlan';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

describe('WeeklyPlanView', () => {
  beforeEach(() => {
    queryClient.clear();
  });

  it('renders weekly plan correctly', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <WeeklyPlanView plan={mockWeeklyPlan} />
      </QueryClientProvider>
    );

    expect(screen.getByText(mockWeeklyPlan.days[0].day)).toBeInTheDocument();
    expect(screen.getByText(`$${mockWeeklyPlan.totalCost}`)).toBeInTheDocument();
  });

  it('expands day panel on click', async () => {
    render(
      <QueryClientProvider client={queryClient}>
        <WeeklyPlanView plan={mockWeeklyPlan} />
      </QueryClientProvider>
    );

    const dayPanel = screen.getByText(mockWeeklyPlan.days[0].day);
    fireEvent.click(dayPanel);

    await waitFor(() => {
      expect(screen.getByText(mockWeeklyPlan.days[0].meals[0].name)).toBeInTheDocument();
    });
  });

  it('shows loading skeleton when plan is undefined', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <WeeklyPlanView plan={undefined} />
      </QueryClientProvider>
    );

    expect(screen.getByTestId('weekly-plan-skeleton')).toBeInTheDocument();
  });
});