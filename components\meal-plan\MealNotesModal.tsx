"use client";

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { StickyNote } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { useMealPlanStore } from '@/lib/stores/meal-plan-store-supabase';

interface MealNotesModalProps {
  isOpen: boolean;
  onClose: () => void;
  date: string;
  mealType: string;
  currentMeal: any;
}

export function MealNotesModal({ isOpen, onClose, date, mealType, currentMeal }: MealNotesModalProps) {
  const [notes, setNotes] = useState('');
  const { updateMealNotes } = useMealPlanStore();

  // Initialize notes when the modal opens or the meal changes
  useEffect(() => {
    if (currentMeal) {
      setNotes(currentMeal.notes || '');
    }
  }, [currentMeal, isOpen]);

  // Handle save
  const handleSave = () => {
    try {
      updateMealNotes(date, mealType, notes);
      toast.success(`Updated notes for ${currentMeal.name}`);
      onClose();
    } catch (error) {
      console.error('Error updating notes:', error);
      toast.error('Failed to update notes');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <StickyNote className="h-5 w-5" />
            Meal Notes
          </DialogTitle>
        </DialogHeader>

        <div className="py-4 space-y-4">
          <div className="mb-4">
            <div className="text-sm text-muted-foreground mb-2">Meal:</div>
            <div className="p-3 border rounded-md bg-gray-50">
              <div className="font-medium">{currentMeal?.name}</div>
              <div className="text-xs text-muted-foreground">
                {format(new Date(date), 'MMMM d, yyyy')} - {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <label htmlFor="notes" className="text-sm font-medium">
                Notes
              </label>
              <span className="text-xs text-muted-foreground">
                {notes.length} / 500 characters
              </span>
            </div>
            <Textarea
              id="notes"
              placeholder="Add notes about this meal (e.g., substitutions, cooking tips, etc.)"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              maxLength={500}
              className="min-h-[150px]"
            />
          </div>

          <div className="text-xs text-muted-foreground">
            <p>Use notes to:</p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>Record ingredient substitutions</li>
              <li>Add cooking tips or modifications</li>
              <li>Note family preferences</li>
              <li>Track meal preparation details</li>
            </ul>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Notes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
