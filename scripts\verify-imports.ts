import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';

const ROOT_DIR = process.cwd();
const tsConfigPath = path.join(ROOT_DIR, 'tsconfig.json');
const tsConfig = JSON.parse(fs.readFileSync(tsConfigPath, 'utf8'));

function verifyImports() {
  // Use sync method from glob
  const files = glob.sync('**/*.{ts,tsx}', {
    ignore: ['node_modules/**', '.next/**', 'scripts/**'],
  });

  const importRegex = /from ['"]([^'"]+)['"]/g;
  const errors: string[] = [];

  files.forEach((file: string) => {
    const content = fs.readFileSync(file, 'utf8');
    let match: RegExpExecArray | null;

    while ((match = importRegex.exec(content)) !== null) {
      const importPath = match[1];
      if (importPath.startsWith('@/')) {
        const aliasPath = importPath.replace('@/', '');
        const fullPath = path.join(ROOT_DIR, aliasPath);
        
        if (!fs.existsSync(fullPath) && !fs.existsSync(`${fullPath}.ts`) && !fs.existsSync(`${fullPath}.tsx`)) {
          errors.push(`Missing file: ${importPath} in ${file}`);
        }
      }
    }
  });

  if (errors.length > 0) {
    console.error('Import verification failed:');
    errors.forEach((error: string) => console.error(error));
    process.exit(1);
  } else {
    console.log('All imports verified successfully!');
  }
}

verifyImports();

