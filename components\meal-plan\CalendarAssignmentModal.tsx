"use client";

import { useState, useEffect } from 'react';
import { format, addWeeks, startOfWeek, endOfWeek, eachDayOfInterval, isSameDay } from 'date-fns';
import { LegacyMealPlan, MealPlan } from '@/types/new-meal-plan';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight, Loader2, AlertTriangle, Calendar, Info } from 'lucide-react';
import { toast } from 'sonner';

interface CalendarAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  mealPlan: LegacyMealPlan | null;
  currentMealPlan?: MealPlan | null;
  onAssign: (weekStartDate: Date) => Promise<void>;
  isLoading: boolean;
  onCheckConflicts?: (weekStartDate: Date) => Promise<{ hasConflicts: boolean, conflictDays: string[] }>;
}

export function CalendarAssignmentModal({
  isOpen,
  onClose,
  mealPlan,
  currentMealPlan,
  onAssign,
  isLoading,
  onCheckConflicts
}: CalendarAssignmentModalProps) {
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  const [hasConflicts, setHasConflicts] = useState(false);
  const [conflictDays, setConflictDays] = useState<string[]>([]);
  const [isCheckingConflicts, setIsCheckingConflicts] = useState(false);

  if (!mealPlan) return null;

  const goToPreviousWeek = () => {
    setCurrentDate(prevDate => addWeeks(prevDate, -1));
    setHasConflicts(false);
    setConflictDays([]);
  };

  const goToNextWeek = () => {
    setCurrentDate(prevDate => addWeeks(prevDate, 1));
    setHasConflicts(false);
    setConflictDays([]);
  };

  const weekStart = startOfWeek(currentDate, { weekStartsOn: 1 });
  const weekEnd = endOfWeek(currentDate, { weekStartsOn: 1 });
  const weekDays = eachDayOfInterval({ start: weekStart, end: weekEnd });

  // Check for conflicts when the week changes
  useEffect(() => {
    const checkConflicts = async () => {
      if (!onCheckConflicts) return;

      setIsCheckingConflicts(true);
      try {
        const { hasConflicts, conflictDays } = await onCheckConflicts(weekStart);
        setHasConflicts(hasConflicts);
        setConflictDays(conflictDays);
      } catch (error) {
        console.error('Error checking conflicts:', error);
        toast.error('Failed to check for conflicts');
      } finally {
        setIsCheckingConflicts(false);
      }
    };

    checkConflicts();
  }, [weekStart, onCheckConflicts]);

  const handleAssign = async () => {
    // If there are conflicts, show a confirmation
    if (hasConflicts && !window.confirm('There are existing meals in this week. Are you sure you want to replace them?')) {
      return;
    }

    await onAssign(weekStart);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add to Calendar</DialogTitle>
          <DialogDescription>
            Select a week to add "{mealPlan.name || 'Meal Plan'}" to your calendar.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <Button variant="outline" size="icon" onClick={goToPreviousWeek} disabled={isLoading || isCheckingConflicts}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <div className="text-center">
              <div className="font-medium">
                {format(weekStart, 'MMM d')} - {format(weekEnd, 'MMM d, yyyy')}
              </div>
              <div className="text-sm text-muted-foreground">Week {format(weekStart, 'w')}</div>
            </div>
            <Button variant="outline" size="icon" onClick={goToNextWeek} disabled={isLoading || isCheckingConflicts}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          {/* Week Calendar */}
          <div className="grid grid-cols-7 gap-1 mt-4">
            {weekDays.map((day) => {
              const dayStr = format(day, 'yyyy-MM-dd');
              const hasConflict = conflictDays.includes(dayStr);

              return (
                <div
                  key={dayStr}
                  className={`text-center p-2 rounded-md ${hasConflict ? 'bg-red-50 border border-red-200' : 'bg-muted/30'}`}
                >
                  <div className="text-xs font-medium">{format(day, 'EEE')}</div>
                  <div className="text-sm">{format(day, 'd')}</div>
                  {hasConflict && (
                    <div className="mt-1">
                      <Badge variant="destructive" className="text-xs px-1 py-0">
                        <AlertTriangle className="h-2 w-2 mr-1" />
                        Conflict
                      </Badge>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {isCheckingConflicts ? (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              <span className="text-sm">Checking for conflicts...</span>
            </div>
          ) : hasConflicts ? (
            <div className="bg-red-50 border border-red-200 p-4 rounded-md">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-red-800">Conflicts Detected</p>
                  <p className="text-sm text-red-700 mt-1">
                    There are existing meals in this week. Adding this meal plan will replace them.
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-muted/50 p-4 rounded-md">
              <div className="flex items-start">
                <Info className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                <div>
                  <p className="text-sm">
                    This will add all meals from "{mealPlan.name || 'Meal Plan'}" to your calendar for the selected week.
                    No conflicts were detected for this week.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading || isCheckingConflicts}>
            Cancel
          </Button>
          <Button
            onClick={handleAssign}
            disabled={isLoading || isCheckingConflicts}
            variant={hasConflicts ? 'destructive' : 'default'}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {hasConflicts ? 'Replace Existing Meals' : 'Add to Calendar'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
