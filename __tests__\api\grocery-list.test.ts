import { createMocks } from 'node-mocks-http';
import { GET } from '@/app/api/grocery/list/route';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase';

jest.mock('@/lib/prisma');
jest.mock('@/lib/supabase');

describe('Grocery List API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('returns 401 when not authenticated', async () => {
    const { req } = createMocks({
      method: 'GET',
    });

    (createServerSupabaseClient as jest.Mock).mockReturnValue({
      auth: {
        getSession: jest.fn().mockResolvedValue({ data: { session: null } }),
      },
    });

    const response = await GET(req);
    expect(response.status).toBe(401);
  });

  it('returns grocery list for authenticated user', async () => {
    const { req } = createMocks({
      method: 'GET',
    });

    const mockUser = { id: '123', email: '<EMAIL>' };
    const mockCategories = [
      {
        id: '1',
        name: 'Fruits',
        items: [{ id: '1', name: 'Apple', checked: false }],
      },
    ];

    (createServerSupabaseClient as jest.Mock).mockReturnValue({
      auth: {
        getSession: jest.fn().mockResolvedValue({
          data: { session: { user: mockUser } },
        }),
      },
    });

    (prisma.groceryCategory.findMany as jest.Mock).mockResolvedValue(
      mockCategories
    );

    const response = await GET(req);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toEqual(mockCategories);
    expect(prisma.groceryCategory.findMany).toHaveBeenCalledWith({
      where: { userId: mockUser.id },
      include: { items: true },
    });
  });
});