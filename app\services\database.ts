import { createClient } from '@supabase/supabase-js';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/types/supabase';

// Client types
export enum ClientType {
  USER,      // Regular user client (respects RLS)
  ADMIN,     // Admin client with service role (bypasses RLS)
  SERVER,    // Server component client
  ROUTE      // Route handler client
}

// Create a singleton instance for the admin client
let adminClient: ReturnType<typeof createClient<Database>> | null = null;

/**
 * Get a Supabase client based on the specified type
 */
export function getSupabaseClient(type: ClientType = ClientType.USER) {
  switch (type) {
    case ClientType.ADMIN:
      // Use singleton pattern for admin client
      if (!adminClient) {
        adminClient = createClient<Database>(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.SUPABASE_SERVICE_ROLE_KEY!,
          {
            auth: {
              autoRefreshToken: false,
              persistSession: false
            }
          }
        );
      }
      return adminClient;

    case ClientType.SERVER:
      return createServerComponentClient<Database>({ cookies: () => cookies() });

    case ClientType.ROUTE:
      return createRouteHandlerClient<Database>({ cookies: () => cookies() });

    case ClientType.USER:
    default:
      return createClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
  }
}

/**
 * Database service for meal plans
 */
export const mealPlanService = {
  /**
   * Create a new meal plan
   */
  async createMealPlan(data: {
    userId: string;
    startDate: string;
    endDate: string;
    totalCost: number;
    mealData: any;
    status?: string;
  }) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      // Special handling for the fallback user ID
      if (data.userId === '00000000-0000-0000-0000-000000000000') {
        console.log('Using fallback user ID, skipping user existence check');
        // Create a fallback user if it doesn't exist
        try {
          const { error: insertError } = await client
            .from('users')
            .insert({
              id: data.userId,
              email: '<EMAIL>',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select()
            .single();

          if (insertError && !insertError.message.includes('duplicate key')) {
            console.error('Error creating fallback user:', insertError);
          } else {
            console.log('Fallback user created or already exists');
          }
        } catch (err) {
          console.log('Error handling fallback user, continuing anyway:', err);
        }
      } else {
        // Check if the user exists in the users table
        const { error: userError } = await client
          .from('users')
          .select('id')
          .eq('id', data.userId)
          .single();

        // If user doesn't exist, create a user record first
        if (userError && userError.code === 'PGRST116') { // PGRST116 is the error code for no rows returned
          console.log('User not found, creating user record first');

          // Get user email from auth.users
          const { data: authUser, error: authError } = await client
            .from('auth.users')
            .select('email')
            .eq('id', data.userId)
            .single();

          if (authError) {
            console.error('Error fetching auth user:', authError);
            throw new Error('User not found in auth.users');
          }

          // Create user record
          const { error: insertError } = await client
            .from('users')
            .insert({
              id: data.userId,
              email: authUser.email,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (insertError) {
            console.error('Error creating user record:', insertError);
            throw insertError;
          }
        } else if (userError) {
          throw userError;
        }
      }

      // Now create the meal plan
      const { data: result, error } = await client
        .from('meal_plans')
        .insert({
          user_id: data.userId,
          start_date: data.startDate,
          end_date: data.endDate,
          total_cost: data.totalCost,
          meal_data: data.mealData,
          status: data.status || 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      return { data: result, error: null };
    } catch (error: any) {
      console.error('Error creating meal plan:', error);

      // Try fallback method using the SQL function
      try {
        const client = getSupabaseClient(ClientType.ADMIN);

        // Check if the error is due to foreign key constraint
        if (error.code === '23503' && error.message.includes('meal_plans_user_id_fkey')) {
          console.log('Foreign key constraint violation, creating user record first');

          // Get user email from auth.users
          const { data: authUser, error: authError } = await client
            .from('auth.users')
            .select('email')
            .eq('id', data.userId)
            .single();

          if (authError) {
            console.error('Error fetching auth user:', authError);
            throw new Error('User not found in auth.users');
          }

          // Create user record
          const { error: insertError } = await client
            .from('users')
            .insert({
              id: data.userId,
              email: authUser.email,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (insertError) {
            console.error('Error creating user record:', insertError);
            throw insertError;
          }

          // Try creating the meal plan again
          const { data: retryResult, error: retryError } = await client
            .from('meal_plans')
            .insert({
              user_id: data.userId,
              start_date: data.startDate,
              end_date: data.endDate,
              total_cost: data.totalCost,
              meal_data: data.mealData,
              status: data.status || 'active',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select()
            .single();

          if (retryError) throw retryError;

          return { data: retryResult, error: null };
        }

        // If not a foreign key constraint error, try the RPC function
        const { data: result, error: funcError } = await client.rpc(
          'insert_meal_plan_bypass_rls',
          {
            p_user_id: data.userId,
            p_start_date: data.startDate,
            p_end_date: data.endDate,
            p_total_cost: data.totalCost,
            p_meal_data: data.mealData,
            p_status: data.status || 'active'
          }
        );

        if (funcError) throw funcError;

        return {
          data: { id: result },
          error: null,
          usedFallback: true
        };
      } catch (fallbackError: any) {
        console.error('Error using fallback method:', fallbackError);
        return {
          data: null,
          error: fallbackError.message || 'Failed to create meal plan'
        };
      }
    }
  },

  /**
   * Get meal plans for a user
   */
  async getMealPlans(userId: string) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { data, error } = await client
        .from('meal_plans')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching meal plans:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch meal plans'
      };
    }
  },

  /**
   * Get a meal plan by ID
   */
  async getMealPlanById(id: string) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { data, error } = await client
        .from('meal_plans')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching meal plan:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch meal plan'
      };
    }
  },

  /**
   * Update a meal plan
   */
  async updateMealPlan(id: string, updates: Partial<{
    startDate: string;
    endDate: string;
    totalCost: number;
    mealData: any;
    status: string;
  }>) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      if (updates.startDate) updateData.start_date = updates.startDate;
      if (updates.endDate) updateData.end_date = updates.endDate;
      if (updates.totalCost) updateData.total_cost = updates.totalCost;
      if (updates.mealData) updateData.meal_data = updates.mealData;
      if (updates.status) updateData.status = updates.status;

      const { data, error } = await client
        .from('meal_plans')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error updating meal plan:', error);
      return {
        data: null,
        error: error.message || 'Failed to update meal plan'
      };
    }
  },

  /**
   * Delete a meal plan
   */
  async deleteMealPlan(id: string) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { error } = await client
        .from('meal_plans')
        .delete()
        .eq('id', id);

      if (error) throw error;

      return { success: true, error: null };
    } catch (error: any) {
      console.error('Error deleting meal plan:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete meal plan'
      };
    }
  }
};

/**
 * Database service for recipes
 */
export const recipeService = {
  /**
   * Get recipes with filtering and pagination
   */
  async getRecipes(params: {
    userId: string;
    query?: string;
    cuisine?: string;
    favorites?: boolean;
    limit?: number;
    offset?: number;
  }) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      let query = client
        .from('recipes')
        .select('*')
        .eq('user_id', params.userId);

      // Apply search filter if provided
      if (params.query) {
        query = query.or(`name.ilike.%${params.query}%,description.ilike.%${params.query}%`);
      }

      // Filter by cuisine if provided
      if (params.cuisine) {
        query = query.eq('cuisine', params.cuisine);
      }

      // Filter by favorites if requested
      if (params.favorites) {
        query = query.eq('is_favorite', true);
      }

      // Apply pagination
      query = query
        .order('created_at', { ascending: false })
        .range(params.offset || 0, (params.offset || 0) + (params.limit || 50) - 1);

      const { data, error } = await query;

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching recipes:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch recipes'
      };
    }
  },

  /**
   * Create a new recipe
   */
  async createRecipe(data: any) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      // Add timestamps
      data.created_at = new Date().toISOString();
      data.updated_at = new Date().toISOString();

      const { data: recipe, error } = await client
        .from('recipes')
        .insert(data)
        .select()
        .single();

      if (error) throw error;

      return { data: recipe, error: null };
    } catch (error: any) {
      console.error('Error creating recipe:', error);
      return {
        data: null,
        error: error.message || 'Failed to create recipe'
      };
    }
  },

  /**
   * Get a recipe by ID
   */
  async getRecipeById(id: string) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { data, error } = await client
        .from('recipes')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching recipe:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch recipe'
      };
    }
  },

  /**
   * Update a recipe
   */
  async updateRecipe(id: string, updates: any) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      // Add updated timestamp
      updates.updated_at = new Date().toISOString();

      const { data, error } = await client
        .from('recipes')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error updating recipe:', error);
      return {
        data: null,
        error: error.message || 'Failed to update recipe'
      };
    }
  },

  /**
   * Delete a recipe
   */
  async deleteRecipe(id: string) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { error } = await client
        .from('recipes')
        .delete()
        .eq('id', id);

      if (error) throw error;

      return { success: true, error: null };
    } catch (error: any) {
      console.error('Error deleting recipe:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete recipe'
      };
    }
  },

  /**
   * Toggle favorite status of a recipe
   */
  async toggleFavorite(id: string, isFavorite: boolean) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { data, error } = await client
        .from('recipes')
        .update({
          is_favorite: isFavorite,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error toggling favorite status:', error);
      return {
        data: null,
        error: error.message || 'Failed to update favorite status'
      };
    }
  }
};

/**
 * Database service for grocery lists
 */
export const groceryService = {
  /**
   * Get grocery lists for a user
   */
  async getGroceryLists(userId: string) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { data, error } = await client
        .from('shopping_lists')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching grocery lists:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch grocery lists'
      };
    }
  },

  /**
   * Create a new grocery list
   */
  async createGroceryList(data: {
    userId: string;
    name: string;
  }) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { data: list, error } = await client
        .from('shopping_lists')
        .insert({
          user_id: data.userId,
          name: data.name,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      return { data: list, error: null };
    } catch (error: any) {
      console.error('Error creating grocery list:', error);
      return {
        data: null,
        error: error.message || 'Failed to create grocery list'
      };
    }
  },

  /**
   * Get a grocery list by ID
   */
  async getGroceryListById(id: string) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { data, error } = await client
        .from('shopping_lists')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching grocery list:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch grocery list'
      };
    }
  },

  /**
   * Update a grocery list
   */
  async updateGroceryList(id: string, updates: {
    name?: string;
  }) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      if (updates.name) updateData.name = updates.name;

      const { data, error } = await client
        .from('shopping_lists')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error updating grocery list:', error);
      return {
        data: null,
        error: error.message || 'Failed to update grocery list'
      };
    }
  },

  /**
   * Delete a grocery list
   */
  async deleteGroceryList(id: string) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { error } = await client
        .from('shopping_lists')
        .delete()
        .eq('id', id);

      if (error) throw error;

      return { success: true, error: null };
    } catch (error: any) {
      console.error('Error deleting grocery list:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete grocery list'
      };
    }
  },

  /**
   * Get grocery items for a list
   */
  async getGroceryItems(listId: string) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { data, error } = await client
        .from('shopping_items')
        .select('*')
        .eq('shopping_list_id', listId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching grocery items:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch grocery items'
      };
    }
  },

  /**
   * Create a new grocery item
   */
  async createGroceryItem(data: {
    listId: string;
    name: string;
    quantity: number;
    unit?: string;
    price: number;
    completed?: boolean;
  }) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { data: item, error } = await client
        .from('shopping_items')
        .insert({
          shopping_list_id: data.listId,
          name: data.name,
          quantity: data.quantity,
          unit: data.unit,
          price: data.price,
          completed: data.completed || false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      return { data: item, error: null };
    } catch (error: any) {
      console.error('Error creating grocery item:', error);
      return {
        data: null,
        error: error.message || 'Failed to create grocery item'
      };
    }
  },

  /**
   * Update a grocery item
   */
  async updateGroceryItem(id: string, updates: {
    name?: string;
    quantity?: number;
    unit?: string;
    price?: number;
    completed?: boolean;
  }) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      if (updates.name !== undefined) updateData.name = updates.name;
      if (updates.quantity !== undefined) updateData.quantity = updates.quantity;
      if (updates.unit !== undefined) updateData.unit = updates.unit;
      if (updates.price !== undefined) updateData.price = updates.price;
      if (updates.completed !== undefined) updateData.completed = updates.completed;

      const { data, error } = await client
        .from('shopping_items')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error updating grocery item:', error);
      return {
        data: null,
        error: error.message || 'Failed to update grocery item'
      };
    }
  },

  /**
   * Delete a grocery item
   */
  async deleteGroceryItem(id: string) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { error } = await client
        .from('shopping_items')
        .delete()
        .eq('id', id);

      if (error) throw error;

      return { success: true, error: null };
    } catch (error: any) {
      console.error('Error deleting grocery item:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete grocery item'
      };
    }
  }
};

/**
 * Database service for users
 */
export const userService = {
  /**
   * Get a user by ID
   */
  async getUserById(id: string) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { data, error } = await client
        .from('users')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching user:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch user'
      };
    }
  },

  /**
   * Create or update a user profile
   */
  async upsertUser(data: {
    id: string;
    email: string;
    first_name?: string;
    last_name?: string;
    avatar_url?: string;
  }) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { data: user, error } = await client
        .from('users')
        .upsert({
          id: data.id,
          email: data.email,
          first_name: data.first_name,
          last_name: data.last_name,
          avatar_url: data.avatar_url,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'id',
          ignoreDuplicates: false
        })
        .select()
        .single();

      if (error) throw error;

      return { data: user, error: null };
    } catch (error: any) {
      console.error('Error upserting user:', error);
      return {
        data: null,
        error: error.message || 'Failed to upsert user'
      };
    }
  },

  /**
   * Update a user's profile
   */
  async updateUser(id: string, updates: {
    first_name?: string;
    last_name?: string;
    avatar_url?: string;
  }) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      if (updates.first_name !== undefined) updateData.first_name = updates.first_name;
      if (updates.last_name !== undefined) updateData.last_name = updates.last_name;
      if (updates.avatar_url !== undefined) updateData.avatar_url = updates.avatar_url;

      const { data, error } = await client
        .from('users')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error updating user:', error);
      return {
        data: null,
        error: error.message || 'Failed to update user'
      };
    }
  },

  /**
   * Get user preferences
   */
  async getUserPreferences(userId: string) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      const { data, error } = await client
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is the error code for no rows returned
        throw error;
      }

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching user preferences:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch user preferences'
      };
    }
  },

  /**
   * Update user preferences
   */
  async upsertUserPreferences(data: {
    userId: string;
    dietary_preferences?: any;
    allergens?: string[];
    household_size?: number;
    weekly_budget?: number;
  }) {
    try {
      const client = getSupabaseClient(ClientType.ADMIN);

      // Check if preferences exist
      const { data: existing } = await client
        .from('user_preferences')
        .select('id')
        .eq('user_id', data.userId)
        .single();

      let result;

      if (existing) {
        // Update existing preferences
        const updateData: any = {
          updated_at: new Date().toISOString()
        };

        if (data.dietary_preferences !== undefined) updateData.dietary_preferences = data.dietary_preferences;
        if (data.allergens !== undefined) updateData.allergens = data.allergens;
        if (data.household_size !== undefined) updateData.household_size = data.household_size;
        if (data.weekly_budget !== undefined) updateData.weekly_budget = data.weekly_budget;

        result = await client
          .from('user_preferences')
          .update(updateData)
          .eq('id', existing.id)
          .select()
          .single();
      } else {
        // Insert new preferences
        result = await client
          .from('user_preferences')
          .insert({
            user_id: data.userId,
            dietary_preferences: data.dietary_preferences,
            allergens: data.allergens,
            household_size: data.household_size,
            weekly_budget: data.weekly_budget,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();
      }

      if (result.error) throw result.error;

      return { data: result.data, error: null };
    } catch (error: any) {
      console.error('Error upserting user preferences:', error);
      return {
        data: null,
        error: error.message || 'Failed to upsert user preferences'
      };
    }
  }
};

// Add more service modules as needed
