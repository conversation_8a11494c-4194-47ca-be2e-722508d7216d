CREATE TABLE IF NOT EXISTS public.pantry_items (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) NOT NULL,  -- Changed to uuid
    name text NOT NULL,
    category text NOT NULL,
    quantity text NOT NULL,
    unit text NOT NULL,
    expiry_date timestamp with time zone,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create indexes
CREATE INDEX IF NOT EXISTS pantry_items_user_id_idx ON public.pantry_items(user_id);
CREATE INDEX IF NOT EXISTS pantry_items_category_idx ON public.pantry_items(category);

-- Create RLS policies
ALTER TABLE public.pantry_items ENABLE ROW LEVEL SECURITY;

CREATE POLICY pantry_items_select_policy ON public.pantry_items
    FOR SELECT USING (auth.uid()::uuid = user_id);  -- Changed to uuid

CREATE POLICY pantry_items_insert_policy ON public.pantry_items
    FOR INSERT WITH CHECK (auth.uid()::uuid = user_id);  -- Changed to uuid

CREATE POLICY pantry_items_update_policy ON public.pantry_items
    FOR UPDATE USING (auth.uid()::uuid = user_id);  -- Changed to uuid

CREATE POLICY pantry_items_delete_policy ON public.pantry_items
    FOR DELETE USING (auth.uid()::uuid = user_id);  -- Changed to uuid

-- Create trigger for updating the updated_at timestamp
CREATE OR REPLACE FUNCTION update_pantry_items_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_pantry_items_updated_at
BEFORE UPDATE ON public.pantry_items
FOR EACH ROW
EXECUTE FUNCTION update_pantry_items_updated_at();