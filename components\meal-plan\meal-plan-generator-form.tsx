"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { Loader2, Plus, X } from "lucide-react";
import { openAIService } from "@/services/openai-service";

// Define the form schema
const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  dietaryPreferences: z.array(z.string()).min(1, "Select at least one dietary preference"),
  calorieGoal: z.number().min(1000, "Minimum 1000 calories").max(4000, "Maximum 4000 calories"),
  allergies: z.array(z.string()),
  householdSize: z.number().min(1, "Minimum 1 person").max(10, "Maximum 10 people"),
  weeklyBudget: z.number().min(50, "Minimum $50").max(500, "Maximum $500"),
  cuisinePreferences: z.array(z.string()).optional(),
  mealTypes: z.array(z.string()).min(1, "Select at least one meal type"),
  daysToGenerate: z.number().min(1, "Minimum 1 day").max(14, "Maximum 14 days"),
  notes: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

const dietaryOptions = [
  { label: "No restrictions", value: "no-restrictions" },
  { label: "Vegetarian", value: "vegetarian" },
  { label: "Vegan", value: "vegan" },
  { label: "Pescatarian", value: "pescatarian" },
  { label: "Gluten-free", value: "gluten-free" },
  { label: "Dairy-free", value: "dairy-free" },
  { label: "Keto", value: "keto" },
  { label: "Paleo", value: "paleo" },
  { label: "Low-carb", value: "low-carb" },
  { label: "High-protein", value: "high-protein" },
];

const cuisineOptions = [
  { label: "American", value: "american" },
  { label: "Italian", value: "italian" },
  { label: "Mexican", value: "mexican" },
  { label: "Asian", value: "asian" },
  { label: "Mediterranean", value: "mediterranean" },
  { label: "Indian", value: "indian" },
  { label: "French", value: "french" },
  { label: "Greek", value: "greek" },
  { label: "Middle Eastern", value: "middle-eastern" },
  { label: "Thai", value: "thai" },
  { label: "Japanese", value: "japanese" },
  { label: "Chinese", value: "chinese" },
];

const mealTypeOptions = [
  { label: "Breakfast", value: "Breakfast" },
  { label: "Lunch", value: "Lunch" },
  { label: "Dinner", value: "Dinner" },
  { label: "Snacks", value: "Snacks" },
];

const commonAllergies = [
  { label: "Peanuts", value: "peanuts" },
  { label: "Tree nuts", value: "tree-nuts" },
  { label: "Milk", value: "milk" },
  { label: "Eggs", value: "eggs" },
  { label: "Fish", value: "fish" },
  { label: "Shellfish", value: "shellfish" },
  { label: "Soy", value: "soy" },
  { label: "Wheat", value: "wheat" },
];

export function MealPlanGeneratorForm() {
  const router = useRouter();
  const [isGenerating, setIsGenerating] = useState(false);
  const [customAllergy, setCustomAllergy] = useState("");
  const [customAllergies, setCustomAllergies] = useState<string[]>([]);

  // Initialize the form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "My Meal Plan",
      dietaryPreferences: ["no-restrictions"],
      calorieGoal: 2000,
      allergies: [],
      householdSize: 1,
      weeklyBudget: 100,
      cuisinePreferences: [],
      mealTypes: ["Breakfast", "Lunch", "Dinner"],
      daysToGenerate: 7,
      notes: "",
    },
  });

  const onSubmit = async (values: FormValues) => {
    try {
      setIsGenerating(true);
      
      // Combine form allergies with custom allergies
      const allAllergies = [...values.allergies, ...customAllergies];
      
      // Call the OpenAI service to generate a meal plan
      const mealPlan = await openAIService.generateMealPlan({
        dietaryPreferences: values.dietaryPreferences,
        calorieGoal: values.calorieGoal,
        allergies: allAllergies,
        householdSize: values.householdSize,
        weeklyBudget: values.weeklyBudget,
        cuisinePreferences: values.cuisinePreferences,
        mealTypes: values.mealTypes,
        daysToGenerate: values.daysToGenerate,
      });
      
      // Save the generated meal plan
      const mealPlanId = await openAIService.saveMealPlan(mealPlan);
      
      toast.success("Meal plan generated successfully!");
      
      // Redirect to the meal plan detail page
      router.push(`/meal-plan/view?id=${mealPlanId}`);
    } catch (error) {
      console.error("Error generating meal plan:", error);
      toast.error("Failed to generate meal plan. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const addCustomAllergy = () => {
    if (customAllergy.trim() && !customAllergies.includes(customAllergy.trim())) {
      setCustomAllergies([...customAllergies, customAllergy.trim()]);
      setCustomAllergy("");
    }
  };

  const removeCustomAllergy = (allergy: string) => {
    setCustomAllergies(customAllergies.filter(a => a !== allergy));
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Provide basic information for your meal plan
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Meal Plan Name</FormLabel>
                  <FormControl>
                    <Input placeholder="My Meal Plan" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="householdSize"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Household Size</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min={1}
                        max={10}
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>Number of people to cook for</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="daysToGenerate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Days to Generate</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min={1}
                        max={14}
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>Number of days in the meal plan</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Dietary Preferences</CardTitle>
            <CardDescription>
              Select your dietary preferences and restrictions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <FormField
              control={form.control}
              name="dietaryPreferences"
              render={() => (
                <FormItem>
                  <div className="mb-4">
                    <FormLabel>Dietary Preferences</FormLabel>
                    <FormDescription>
                      Select all that apply
                    </FormDescription>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {dietaryOptions.map((option) => (
                      <FormField
                        key={option.value}
                        control={form.control}
                        name="dietaryPreferences"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={option.value}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(option.value)}
                                  onCheckedChange={(checked) => {
                                    return checked
                                      ? field.onChange([...field.value, option.value])
                                      : field.onChange(
                                          field.value?.filter(
                                            (value) => value !== option.value
                                          )
                                        );
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {option.label}
                              </FormLabel>
                            </FormItem>
                          );
                        }}
                      />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            <FormField
              control={form.control}
              name="calorieGoal"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Daily Calorie Goal: {field.value} calories</FormLabel>
                  <FormControl>
                    <Slider
                      min={1000}
                      max={4000}
                      step={50}
                      defaultValue={[field.value]}
                      onValueChange={(values) => field.onChange(values[0])}
                    />
                  </FormControl>
                  <FormDescription>
                    Adjust the slider to set your daily calorie goal
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            <FormField
              control={form.control}
              name="weeklyBudget"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Weekly Budget: ${field.value}</FormLabel>
                  <FormControl>
                    <Slider
                      min={50}
                      max={500}
                      step={10}
                      defaultValue={[field.value]}
                      onValueChange={(values) => field.onChange(values[0])}
                    />
                  </FormControl>
                  <FormDescription>
                    Adjust the slider to set your weekly grocery budget
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Meal Preferences</CardTitle>
            <CardDescription>
              Customize your meal types and cuisine preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <FormField
              control={form.control}
              name="mealTypes"
              render={() => (
                <FormItem>
                  <div className="mb-4">
                    <FormLabel>Meal Types</FormLabel>
                    <FormDescription>
                      Select the types of meals to include
                    </FormDescription>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {mealTypeOptions.map((option) => (
                      <FormField
                        key={option.value}
                        control={form.control}
                        name="mealTypes"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={option.value}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(option.value)}
                                  onCheckedChange={(checked) => {
                                    return checked
                                      ? field.onChange([...field.value, option.value])
                                      : field.onChange(
                                          field.value?.filter(
                                            (value) => value !== option.value
                                          )
                                        );
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {option.label}
                              </FormLabel>
                            </FormItem>
                          );
                        }}
                      />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            <FormField
              control={form.control}
              name="cuisinePreferences"
              render={() => (
                <FormItem>
                  <div className="mb-4">
                    <FormLabel>Cuisine Preferences (Optional)</FormLabel>
                    <FormDescription>
                      Select cuisines you prefer
                    </FormDescription>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {cuisineOptions.map((option) => (
                      <FormField
                        key={option.value}
                        control={form.control}
                        name="cuisinePreferences"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={option.value}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(option.value)}
                                  onCheckedChange={(checked) => {
                                    return checked
                                      ? field.onChange([...(field.value || []), option.value])
                                      : field.onChange(
                                          field.value?.filter(
                                            (value) => value !== option.value
                                          )
                                        );
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {option.label}
                              </FormLabel>
                            </FormItem>
                          );
                        }}
                      />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Allergies & Additional Notes</CardTitle>
            <CardDescription>
              Specify any allergies and additional notes
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <FormField
              control={form.control}
              name="allergies"
              render={() => (
                <FormItem>
                  <div className="mb-4">
                    <FormLabel>Allergies</FormLabel>
                    <FormDescription>
                      Select all that apply
                    </FormDescription>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {commonAllergies.map((option) => (
                      <FormField
                        key={option.value}
                        control={form.control}
                        name="allergies"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={option.value}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(option.value)}
                                  onCheckedChange={(checked) => {
                                    return checked
                                      ? field.onChange([...(field.value || []), option.value])
                                      : field.onChange(
                                          field.value?.filter(
                                            (value) => value !== option.value
                                          )
                                        );
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {option.label}
                              </FormLabel>
                            </FormItem>
                          );
                        }}
                      />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-2">
              <FormLabel>Custom Allergies</FormLabel>
              <div className="flex space-x-2">
                <Input
                  placeholder="Enter custom allergy"
                  value={customAllergy}
                  onChange={(e) => setCustomAllergy(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      addCustomAllergy();
                    }
                  }}
                />
                <Button type="button" onClick={addCustomAllergy}>
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2 mt-2">
                {customAllergies.map((allergy) => (
                  <Badge key={allergy} variant="secondary" className="flex items-center gap-1">
                    {allergy}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeCustomAllergy(allergy)}
                    />
                  </Badge>
                ))}
              </div>
            </div>

            <Separator />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional preferences or notes for your meal plan"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Add any additional information that might be helpful
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" onClick={() => router.back()}>
              Cancel
            </Button>
            <Button type="submit" disabled={isGenerating}>
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                "Generate Meal Plan"
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}
