'use client';

import { useState, useEffect } from 'react';
import { useSupabase } from '@/components/supabase-provider';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { format, parseISO } from 'date-fns';
import { Check, Clock, AlertCircle, Trophy, ChevronRight } from 'lucide-react';
import { mealCompletionService } from '@/services/meal-completion-service';
import Link from 'next/link';

export function MealCompletionReminder() {
  const { supabase } = useSupabase();
  const [isLoading, setIsLoading] = useState(true);
  const [todayMeals, setTodayMeals] = useState<any[]>([]);
  const [activeMealPlan, setActiveMealPlan] = useState<any>(null);
  const [completedMeals, setCompletedMeals] = useState<Record<string, boolean>>({});
  const [streak, setStreak] = useState(0);

  useEffect(() => {
    fetchTodayMeals();
  }, []);

  const fetchTodayMeals = async () => {
    try {
      setIsLoading(true);
      
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      // Get today's date in the format "YYYY-MM-DD"
      const today = new Date().toISOString().split('T')[0];
      
      // Fetch active meal plan that includes today
      const { data: mealPlans, error } = await supabase
        .from('meal_plans')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .lte('start_date', today)
        .gte('end_date', today)
        .order('created_at', { ascending: false })
        .limit(1);
      
      if (error) throw error;
      
      if (!mealPlans || mealPlans.length === 0) {
        setIsLoading(false);
        return;
      }
      
      const mealPlan = mealPlans[0];
      setActiveMealPlan(mealPlan);
      
      // Extract today's meals from the meal plan
      const todayData = mealPlan.meal_data?.mealPlan?.week?.find((day: any) => day.date === today);
      
      if (todayData && todayData.meals) {
        setTodayMeals(todayData.meals);
        
        // Fetch completed meals
        const { data: completions, error: completionsError } = await supabase
          .from('meal_completions')
          .select('*')
          .eq('user_id', user.id)
          .eq('meal_plan_id', mealPlan.id)
          .eq('date', today);
        
        if (completionsError) throw completionsError;
        
        // Create a map of completed meals
        const completedMap: Record<string, boolean> = {};
        completions?.forEach(completion => {
          completedMap[completion.meal_type] = true;
        });
        
        setCompletedMeals(completedMap);
        
        // Get streak
        if (mealPlan.id) {
          try {
            const stats = await mealCompletionService.getMealCompletionStats(mealPlan.id, mealPlan.meal_data);
            setStreak(stats.streak);
          } catch (error) {
            console.error('Error getting streak:', error);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching today\'s meals:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleMealCompletion = async (mealType: string, isCompleted: boolean) => {
    try {
      if (!activeMealPlan) return;
      
      const today = new Date().toISOString().split('T')[0];
      
      if (isCompleted) {
        // Remove completion
        await mealCompletionService.uncompleteMeal(activeMealPlan.id, today, mealType);
        
        // Update local state
        setCompletedMeals(prev => {
          const updated = { ...prev };
          delete updated[mealType];
          return updated;
        });
        
        toast.success('Meal marked as incomplete');
      } else {
        // Add completion
        await mealCompletionService.completeMeal(activeMealPlan.id, today, mealType);
        
        // Update local state
        setCompletedMeals(prev => ({
          ...prev,
          [mealType]: true
        }));
        
        toast.success('Meal marked as completed');
      }
      
      // Refresh streak
      if (activeMealPlan.id) {
        const stats = await mealCompletionService.getMealCompletionStats(activeMealPlan.id, activeMealPlan.meal_data);
        setStreak(stats.streak);
      }
    } catch (error) {
      console.error('Error toggling meal completion:', error);
      toast.error('Failed to update meal completion');
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!activeMealPlan || todayMeals.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Today's Meals</CardTitle>
          <CardDescription>No meals planned for today</CardDescription>
        </CardHeader>
        <CardContent className="text-center py-6">
          <AlertCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <p className="text-muted-foreground mb-4">You don't have any meals planned for today.</p>
          <Button asChild>
            <Link href="/meal-plan/create">Create a Meal Plan</Link>
          </Button>
        </CardContent>
      </Card>
    );
  }

  const completedCount = Object.keys(completedMeals).length;
  const totalMeals = todayMeals.length;
  const completionPercentage = totalMeals > 0 ? (completedCount / totalMeals) * 100 : 0;

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>Today's Meals</CardTitle>
            <CardDescription>
              {format(new Date(), 'EEEE, MMMM d, yyyy')}
            </CardDescription>
          </div>
          {streak > 0 && (
            <Badge className="bg-yellow-500 hover:bg-yellow-600">
              <Trophy className="h-3 w-3 mr-1" />
              {streak} day streak
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <div className="flex justify-between items-center mb-1">
            <span className="text-sm text-muted-foreground">
              {completedCount} of {totalMeals} completed
            </span>
            <span className="text-sm font-medium">{Math.round(completionPercentage)}%</span>
          </div>
          <Progress value={completionPercentage} className="h-2" />
        </div>

        <div className="space-y-2">
          {todayMeals.map((meal, index) => {
            const isCompleted = completedMeals[meal.type] || false;
            
            return (
              <div 
                key={`${meal.id || index}`}
                className={`p-3 border rounded-lg flex items-center justify-between ${
                  isCompleted ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-900' : ''
                }`}
              >
                <div className="flex items-center">
                  <Checkbox 
                    id={`meal-${meal.type}-${index}`}
                    checked={isCompleted}
                    onCheckedChange={() => handleToggleMealCompletion(meal.type, isCompleted)}
                    className="mr-3"
                  />
                  <div>
                    <Label 
                      htmlFor={`meal-${meal.type}-${index}`}
                      className={`font-medium ${isCompleted ? 'line-through text-muted-foreground' : ''}`}
                    >
                      {meal.name}
                    </Label>
                    <div className="flex items-center text-sm text-muted-foreground mt-1">
                      <Badge variant="outline" className="mr-2">{meal.type}</Badge>
                      <Clock className="h-3 w-3 mr-1" />
                      <span>{meal.prepTime} min</span>
                    </div>
                  </div>
                </div>
                
                {isCompleted && (
                  <Badge className="bg-green-500 hover:bg-green-600">
                    <Check className="h-3 w-3 mr-1" />
                    Completed
                  </Badge>
                )}
              </div>
            );
          })}
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full" asChild>
          <Link href={`/meal-plan/view?id=${activeMealPlan.id}`}>
            View Full Meal Plan
            <ChevronRight className="h-4 w-4 ml-1" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
