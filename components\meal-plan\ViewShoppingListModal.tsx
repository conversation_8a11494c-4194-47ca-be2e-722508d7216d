"use client";

import { useState, useEffect, useMemo } from 'react';
import { ShoppingItem, PantryItem, Meal, MealPlan as NewMealPlan } from '@/types/new-meal-plan';
import { MealPlan } from '@/types/meal-plan';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Search, Printer, Download, ShoppingCart, Package, Plus, AlertTriangle, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';

interface ViewShoppingListModalProps {
  isOpen: boolean;
  onClose: () => void;
  shoppingList: ShoppingItem[];
  pantryItems: PantryItem[];
  mealPlan?: MealPlan | null;
  newMealPlan?: NewMealPlan | null;
  onAddToPantry?: (items: ShoppingItem[]) => void;
  onUpdateShoppingList?: (items: ShoppingItem[]) => void;
  onGenerateShoppingList?: (mealPlan: NewMealPlan) => Promise<{ shoppingList: ShoppingItem[], pantryItems: PantryItem[] }>;
}

export function ViewShoppingListModal({
  isOpen,
  onClose,
  shoppingList,
  pantryItems,
  mealPlan,
  newMealPlan,
  onAddToPantry,
  onUpdateShoppingList,
  onGenerateShoppingList
}: ViewShoppingListModalProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [checkedItems, setCheckedItems] = useState<Record<string, boolean>>({});
  const [activeTab, setActiveTab] = useState('shopping');

  // Group shopping items by category
  const groupedItems = shoppingList.reduce<Record<string, ShoppingItem[]>>((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {});

  // Group pantry items by category
  const groupedPantryItems = pantryItems.reduce<Record<string, PantryItem[]>>((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {});

  // Filter items based on search term
  const filteredCategories = Object.keys(groupedItems).filter(category => {
    if (searchTerm === '') return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      category.toLowerCase().includes(searchLower) ||
      groupedItems[category].some(item => item.name.toLowerCase().includes(searchLower))
    );
  });

  // Filter pantry items based on search term
  const filteredPantryCategories = Object.keys(groupedPantryItems).filter(category => {
    if (searchTerm === '') return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      category.toLowerCase().includes(searchLower) ||
      groupedPantryItems[category].some(item => item.name.toLowerCase().includes(searchLower))
    );
  });

  // Calculate total cost
  const totalCost = shoppingList
    .filter(item => !item.inPantry && !checkedItems[item.id])
    .reduce((sum, item) => sum + item.cost, 0);

  // State for loading indicators
  const [isGenerating, setIsGenerating] = useState(false);
  const [isAddingToPantry, setIsAddingToPantry] = useState(false);

  // Get checked items
  const checkedItemsList = useMemo(() => {
    return shoppingList.filter(item => checkedItems[item.id]);
  }, [shoppingList, checkedItems]);

  // Handle toggling an item's checked state
  const handleToggleItem = (id: string) => {
    setCheckedItems(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // Handle adding checked items to pantry
  const handleAddToPantry = async () => {
    if (!onAddToPantry || checkedItemsList.length === 0) return;

    setIsAddingToPantry(true);
    try {
      await onAddToPantry(checkedItemsList);

      // Clear checked items
      setCheckedItems({});

      toast.success(`Added ${checkedItemsList.length} items to pantry`);
    } catch (error) {
      console.error('Error adding items to pantry:', error);
      toast.error('Failed to add items to pantry');
    } finally {
      setIsAddingToPantry(false);
    }
  };

  // Handle regenerating shopping list from meal plan
  const handleRegenerateShoppingList = async () => {
    if (!onGenerateShoppingList || !newMealPlan) return;

    setIsGenerating(true);
    try {
      await onGenerateShoppingList(newMealPlan);
      toast.success('Shopping list regenerated');
    } catch (error) {
      console.error('Error regenerating shopping list:', error);
      toast.error('Failed to regenerate shopping list');
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    // Create a CSV string
    const headers = ['Category', 'Item', 'Quantity', 'Unit', 'Cost'];
    const rows = shoppingList.map(item => [
      item.category,
      item.name,
      item.quantity,
      item.unit,
      item.cost.toFixed(2)
    ]);

    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'shopping_list.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success('Shopping list downloaded');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            Shopping List
            {mealPlan && (
              <span className="text-sm font-normal text-muted-foreground ml-2">
                for {mealPlan.name || 'Meal Plan'}
              </span>
            )}
          </DialogTitle>
          <DialogDescription>
            All the ingredients you need for your meal plan.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search items..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <Tabs defaultValue="shopping" onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="shopping" className="flex items-center gap-2">
                <ShoppingCart className="h-4 w-4" />
                <span>Shopping List</span>
              </TabsTrigger>
              <TabsTrigger value="pantry" className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                <span>Pantry</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="shopping" className="space-y-4 pt-4">
              {filteredCategories.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">No items found</p>
                </div>
              ) : (
                <>
                  {filteredCategories.map(category => (
                    <div key={category} className="space-y-2">
                      <h3 className="font-medium">{category}</h3>
                      <div className="space-y-2">
                        {groupedItems[category]
                          .filter(item => item.name.toLowerCase().includes(searchTerm.toLowerCase()) || searchTerm === '')
                          .map(item => (
                            <div key={item.id} className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <Checkbox
                                  id={item.id}
                                  checked={checkedItems[item.id] || false}
                                  onCheckedChange={() => handleToggleItem(item.id)}
                                />
                                <Label
                                  htmlFor={item.id}
                                  className={`text-sm font-normal ${checkedItems[item.id] ? 'line-through text-muted-foreground' : ''}`}
                                >
                                  {item.quantity} {item.unit} {item.name}
                                </Label>
                                {item.inPantry && (
                                  <Badge variant="outline" className="text-xs">In Pantry</Badge>
                                )}
                              </div>
                              <span className="text-sm">${item.cost.toFixed(2)}</span>
                            </div>
                          ))}
                      </div>
                    </div>
                  ))}

                  <div className="pt-4 border-t flex justify-between items-center">
                    <span className="font-medium">Total:</span>
                    <span className="font-medium">${totalCost.toFixed(2)}</span>
                  </div>
                </>
              )}
            </TabsContent>

            <TabsContent value="pantry" className="space-y-4 pt-4">
              {filteredPantryCategories.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">No pantry items found</p>
                </div>
              ) : (
                <>
                  {filteredPantryCategories.map(category => (
                    <div key={category} className="space-y-2">
                      <h3 className="font-medium">{category}</h3>
                      <div className="space-y-2">
                        {groupedPantryItems[category]
                          .filter(item => item.name.toLowerCase().includes(searchTerm.toLowerCase()) || searchTerm === '')
                          .map(item => (
                            <div key={item.id} className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <span className="text-sm">
                                  {item.quantity} {item.unit} {item.name}
                                </span>
                                {item.lowStock && (
                                  <Badge variant="destructive" className="text-xs flex items-center gap-1">
                                    <AlertTriangle className="h-3 w-3" />
                                    Low Stock
                                  </Badge>
                                )}
                              </div>

                              {onUpdateShoppingList && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-7 px-2"
                                  onClick={() => {
                                    // Create a shopping item from the pantry item
                                    const shoppingItem: ShoppingItem = {
                                      id: `shop-${item.id}`,
                                      name: item.name,
                                      quantity: item.quantity,
                                      unit: item.unit,
                                      category: item.category,
                                      cost: 0, // Default cost
                                      inPantry: false
                                    };

                                    onUpdateShoppingList([shoppingItem]);
                                    toast.success(`Added ${item.name} to shopping list`);
                                  }}
                                >
                                  <ShoppingCart className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          ))}
                      </div>
                    </div>
                  ))}
                </>
              )}
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2 sm:justify-between">
          <div className="flex gap-2 w-full sm:w-auto justify-end sm:justify-start">
            {activeTab === 'shopping' && checkedItemsList.length > 0 && onAddToPantry && (
              <Button
                variant="default"
                size="sm"
                onClick={handleAddToPantry}
                disabled={isAddingToPantry}
                className="w-full sm:w-auto"
              >
                {isAddingToPantry ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <Plus className="mr-2 h-4 w-4" />
                    Add {checkedItemsList.length} to Pantry
                  </>
                )}
              </Button>
            )}

            {newMealPlan && onGenerateShoppingList && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleRegenerateShoppingList}
                disabled={isGenerating}
                className="w-full sm:w-auto"
              >
                {isGenerating ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Regenerate List
                  </>
                )}
              </Button>
            )}
          </div>

          <div className="flex gap-2 w-full sm:w-auto justify-end">
            <Button variant="outline" size="sm" onClick={handlePrint}>
              <Printer className="mr-2 h-4 w-4" />
              Print
            </Button>
            <Button variant="outline" size="sm" onClick={handleDownload}>
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
