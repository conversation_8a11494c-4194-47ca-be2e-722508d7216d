'use client';

import { Button } from '@/components/ui/button';
import { Calendar } from 'lucide-react';

interface AddToCalendarButtonProps {
  mealPlan: any;
  onClick?: (e: React.MouseEvent) => void;
  className?: string;
}

export function AddToCalendarButton({ mealPlan, onClick, className }: AddToCalendarButtonProps) {
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      onClick(e);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      className={`flex items-center ${className || ''}`}
      onClick={handleClick}
    >
      <Calendar className="h-4 w-4 mr-2" />
      Add to Calendar
    </Button>
  );
}
