"use client";

import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { Meal, MealPlan } from "@/types/new-meal-plan";
import { toast } from "sonner";
import { sanitizeMealPlan, isMealPlanValid, estimateMealPlanSize } from "@/lib/utils/meal-plan-sanitizer";

// Define types for the service
interface MealPlanServiceTypes {
  saveMealPlan: (mealPlan: MealPlan, userId: string) => Promise<boolean>;
  getMealPlans: (userId: string) => Promise<MealPlan[]>;
  getMealPlanById: (id: string) => Promise<MealPlan | null>;
  updateMealPlan: (id: string, mealPlan: Partial<MealPlan>) => Promise<boolean>;
  deleteMealPlan: (id: string) => Promise<boolean>;
  toggleFavoriteMeal: (userId: string, meal: Meal) => Promise<boolean>;
  getFavoriteMeals: (userId: string) => Promise<Meal[]>;
  saveMealNotes: (userId: string, date: string, mealType: string, mealId: string, notes: string) => Promise<boolean>;
  getMealNotes: (userId: string, date: string, mealType: string, mealId: string) => Promise<string | null>;
  updateMealServings: (userId: string, date: string, mealType: string, mealId: string, servings: number) => Promise<boolean>;
  setMealStatus: (userId: string, date: string, mealType: string, status: 'cooked' | 'skipped' | null) => Promise<boolean>;
  moveMeal: (
    userId: string,
    sourceMeal: { day: string, mealType: string },
    targetMeal: { day: string, mealType: string }
  ) => Promise<boolean>;
  assignMealPlanToCalendar: (userId: string, mealPlanId: string, startDate: string, endDate: string) => Promise<boolean>;
  getMealPlanAssignments: (userId: string) => Promise<any[]>;
  removeMealPlanAssignment: (assignmentId: string) => Promise<boolean>;
}

// Create the meal plan service
export const mealPlanService: MealPlanServiceTypes = {
  // Save a meal plan to the database
  saveMealPlan: async (mealPlan: MealPlan, userId: string): Promise<boolean> => {
    try {
      console.log('saveMealPlan called with userId:', userId);
      console.log('Meal plan data:', JSON.stringify(mealPlan).substring(0, 200) + '...');

      const supabase = createClientComponentClient();

      // Verify authentication
      const { data: authData } = await supabase.auth.getUser();
      console.log('Current authenticated user:', authData.user?.id);

      if (!authData.user) {
        console.error('No authenticated user found');
        toast.error('You must be logged in to save a meal plan');
        return false;
      }

      if (authData.user.id !== userId) {
        console.warn('User ID mismatch:', { authUserId: authData.user.id, providedUserId: userId });
        // Use the authenticated user ID instead
        userId = authData.user.id;
      }

      // Sanitize the meal plan to ensure it can be safely stored
      console.log('Sanitizing meal plan data...');
      const sanitizedMealPlan = sanitizeMealPlan(mealPlan);

      // Check if the meal plan is valid
      if (!isMealPlanValid(sanitizedMealPlan)) {
        console.error('Invalid meal plan data');
        toast.error('Invalid meal plan data');
        return false;
      }

      // Estimate the size of the meal plan
      const mealPlanSize = estimateMealPlanSize(sanitizedMealPlan);
      console.log(`Estimated meal plan size: ${mealPlanSize} bytes`);

      // Check if the meal plan is too large (Supabase JSONB limit is 1GB, but we'll be conservative)
      if (mealPlanSize > 10 * 1024 * 1024) { // 10MB
        console.error('Meal plan data is too large');
        toast.error('Meal plan data is too large');
        return false;
      }

      // Convert the meal plan to a format suitable for storage
      const mealPlanData = {
        user_id: userId,
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        total_cost: calculateTotalCost(sanitizedMealPlan),
        meal_data: sanitizedMealPlan,
        status: 'active'
      };

      console.log('Saving meal plan with data:', {
        user_id: mealPlanData.user_id,
        start_date: mealPlanData.start_date,
        end_date: mealPlanData.end_date,
        total_cost: mealPlanData.total_cost,
        status: mealPlanData.status
      });

      // Use select() to return the inserted data
      const { data, error } = await supabase
        .from('meal_plans')
        .insert(mealPlanData)
        .select();

      if (error) {
        console.error('Error saving meal plan:', error);
        toast.error(`Failed to save meal plan: ${error.message}`);
        return false;
      }

      console.log('Meal plan saved successfully:', data);
      toast.success('Meal plan saved successfully');
      return true;
    } catch (error) {
      console.error('Error in saveMealPlan:', error);
      toast.error(`An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    }
  },

  // Get all meal plans for a user
  getMealPlans: async (userId: string): Promise<MealPlan[]> => {
    try {
      console.log('getMealPlans called with userId:', userId);
      const supabase = createClientComponentClient();

      // Verify authentication
      const { data: authData } = await supabase.auth.getUser();
      console.log('Current authenticated user:', authData.user?.id);

      if (!authData.user) {
        console.error('No authenticated user found');
        toast.error('You must be logged in to fetch meal plans');
        return [];
      }

      if (authData.user.id !== userId) {
        console.warn('User ID mismatch:', { authUserId: authData.user.id, providedUserId: userId });
        // Use the authenticated user ID instead
        userId = authData.user.id;
      }

      console.log('Fetching meal plans for user ID:', userId);
      const { data, error } = await supabase
        .from('meal_plans')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching meal plans:', error);
        toast.error(`Failed to fetch meal plans: ${error.message}`);
        return [];
      }

      console.log(`Meal plans fetched successfully: ${data.length} plans found`);
      if (data.length > 0) {
        console.log('First meal plan:', {
          id: data[0].id,
          user_id: data[0].user_id,
          created_at: data[0].created_at,
          status: data[0].status,
          meal_data_keys: Object.keys(data[0].meal_data || {})
        });
      } else {
        console.log('No meal plans found');
      }

      // Extract the meal_data from each record
      return data.map(record => record.meal_data);
    } catch (error) {
      console.error('Error in getMealPlans:', error);
      toast.error(`An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    }
  },

  // Get a specific meal plan by ID
  getMealPlanById: async (id: string): Promise<MealPlan | null> => {
    try {
      const supabase = createClientComponentClient();

      const { data, error } = await supabase
        .from('meal_plans')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching meal plan:', error);
        toast.error('Failed to fetch meal plan');
        return null;
      }

      return data.meal_data;
    } catch (error) {
      console.error('Error in getMealPlanById:', error);
      toast.error('An unexpected error occurred');
      return null;
    }
  },

  // Update a meal plan
  updateMealPlan: async (id: string, mealPlan: Partial<MealPlan>): Promise<boolean> => {
    try {
      const supabase = createClientComponentClient();

      const { error } = await supabase
        .from('meal_plans')
        .update({
          meal_data: mealPlan,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        console.error('Error updating meal plan:', error);
        toast.error('Failed to update meal plan');
        return false;
      }

      toast.success('Meal plan updated successfully');
      return true;
    } catch (error) {
      console.error('Error in updateMealPlan:', error);
      toast.error('An unexpected error occurred');
      return false;
    }
  },

  // Delete a meal plan
  deleteMealPlan: async (id: string): Promise<boolean> => {
    try {
      const supabase = createClientComponentClient();

      // Instead of actually deleting, we'll set the status to 'deleted'
      const { error } = await supabase
        .from('meal_plans')
        .update({
          status: 'deleted',
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        console.error('Error deleting meal plan:', error);
        toast.error('Failed to delete meal plan');
        return false;
      }

      toast.success('Meal plan deleted successfully');
      return true;
    } catch (error) {
      console.error('Error in deleteMealPlan:', error);
      toast.error('An unexpected error occurred');
      return false;
    }
  },

  // Toggle a meal as favorite
  toggleFavoriteMeal: async (userId: string, meal: Meal): Promise<boolean> => {
    try {
      const supabase = createClientComponentClient();

      // Check if the meal is already a favorite
      const { data, error: fetchError } = await supabase
        .from('favorite_meals')
        .select('*')
        .eq('user_id', userId)
        .eq('meal_id', meal.id)
        .maybeSingle();

      if (fetchError) {
        console.error('Error checking favorite status:', fetchError);
        toast.error('Failed to check favorite status');
        return false;
      }

      if (data) {
        // Meal is already a favorite, so remove it
        const { error: deleteError } = await supabase
          .from('favorite_meals')
          .delete()
          .eq('id', data.id);

        if (deleteError) {
          console.error('Error removing favorite:', deleteError);
          toast.error('Failed to remove from favorites');
          return false;
        }

        toast.success('Removed from favorites');
        return true;
      } else {
        // Meal is not a favorite, so add it
        const { error: insertError } = await supabase
          .from('favorite_meals')
          .insert({
            user_id: userId,
            meal_id: meal.id,
            meal_data: meal
          });

        if (insertError) {
          console.error('Error adding favorite:', insertError);
          toast.error('Failed to add to favorites');
          return false;
        }

        toast.success('Added to favorites');
        return true;
      }
    } catch (error) {
      console.error('Error in toggleFavoriteMeal:', error);
      toast.error('An unexpected error occurred');
      return false;
    }
  },

  // Get all favorite meals for a user
  getFavoriteMeals: async (userId: string): Promise<Meal[]> => {
    try {
      const supabase = createClientComponentClient();

      const { data, error } = await supabase
        .from('favorite_meals')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching favorite meals:', error);
        toast.error('Failed to fetch favorite meals');
        return [];
      }

      // Extract the meal_data from each record
      return data.map(record => record.meal_data);
    } catch (error) {
      console.error('Error in getFavoriteMeals:', error);
      toast.error('An unexpected error occurred');
      return [];
    }
  },

  // Save notes for a meal
  saveMealNotes: async (userId: string, date: string, mealType: string, mealId: string, notes: string): Promise<boolean> => {
    try {
      const supabase = createClientComponentClient();

      // Check if notes already exist for this meal
      const { data, error: fetchError } = await supabase
        .from('meal_notes')
        .select('*')
        .eq('user_id', userId)
        .eq('date', date)
        .eq('meal_type', mealType)
        .eq('meal_id', mealId)
        .maybeSingle();

      if (fetchError) {
        console.error('Error checking meal notes:', fetchError);
        toast.error('Failed to check meal notes');
        return false;
      }

      if (data) {
        // Notes already exist, so update them
        const { error: updateError } = await supabase
          .from('meal_notes')
          .update({ notes })
          .eq('id', data.id);

        if (updateError) {
          console.error('Error updating meal notes:', updateError);
          toast.error('Failed to update meal notes');
          return false;
        }
      } else {
        // Notes don't exist, so create them
        const { error: insertError } = await supabase
          .from('meal_notes')
          .insert({
            user_id: userId,
            date,
            meal_type: mealType,
            meal_id: mealId,
            notes
          });

        if (insertError) {
          console.error('Error creating meal notes:', insertError);
          toast.error('Failed to create meal notes');
          return false;
        }
      }

      toast.success('Meal notes saved successfully');
      return true;
    } catch (error) {
      console.error('Error in saveMealNotes:', error);
      toast.error('An unexpected error occurred');
      return false;
    }
  },

  // Get notes for a meal
  getMealNotes: async (userId: string, date: string, mealType: string, mealId: string): Promise<string | null> => {
    try {
      const supabase = createClientComponentClient();

      const { data, error } = await supabase
        .from('meal_notes')
        .select('notes')
        .eq('user_id', userId)
        .eq('date', date)
        .eq('meal_type', mealType)
        .eq('meal_id', mealId)
        .maybeSingle();

      if (error) {
        console.error('Error fetching meal notes:', error);
        toast.error('Failed to fetch meal notes');
        return null;
      }

      return data?.notes || null;
    } catch (error) {
      console.error('Error in getMealNotes:', error);
      toast.error('An unexpected error occurred');
      return null;
    }
  },

  // Update meal servings
  updateMealServings: async (userId: string, date: string, mealType: string, mealId: string, servings: number): Promise<boolean> => {
    try {
      const supabase = createClientComponentClient();

      // First, get the current meal plan
      const { data: mealPlanData, error: fetchError } = await supabase
        .from('meal_plans')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .maybeSingle();

      if (fetchError) {
        console.error('Error fetching meal plan:', fetchError);
        toast.error('Failed to fetch meal plan');
        return false;
      }

      if (!mealPlanData) {
        toast.error('No active meal plan found');
        return false;
      }

      // Update the servings in the meal plan data
      const mealPlan = mealPlanData.meal_data;
      if (mealPlan[date] && mealPlan[date][mealType]) {
        const meal = mealPlan[date][mealType];
        const originalServings = meal.servings || 1;
        const scaleFactor = servings / originalServings;

        // Update servings
        meal.servings = servings;

        // Scale ingredients quantities
        if (meal.ingredients && Array.isArray(meal.ingredients)) {
          meal.ingredients = meal.ingredients.map(ingredient => {
            const newIngredient = { ...ingredient };
            // Only scale numeric quantities
            if (!isNaN(parseFloat(newIngredient.quantity))) {
              const scaledAmount = parseFloat(newIngredient.quantity) * scaleFactor;
              newIngredient.quantity = scaledAmount.toFixed(2);
            }
            return newIngredient;
          });
        }

        // Scale nutritional values and cost
        if (meal.nutrition) {
          meal.nutrition = {
            protein: Math.round(meal.nutrition.protein * scaleFactor),
            carbs: Math.round(meal.nutrition.carbs * scaleFactor),
            fat: Math.round(meal.nutrition.fat * scaleFactor)
          };
        }

        meal.calories = Math.round(meal.calories * scaleFactor);
        meal.cost = parseFloat((meal.cost * scaleFactor).toFixed(2));

        // Update the meal plan in the database
        const { error: updateError } = await supabase
          .from('meal_plans')
          .update({
            meal_data: mealPlan,
            updated_at: new Date().toISOString()
          })
          .eq('id', mealPlanData.id);

        if (updateError) {
          console.error('Error updating meal servings:', updateError);
          toast.error('Failed to update meal servings');
          return false;
        }

        toast.success('Meal servings updated successfully');
        return true;
      } else {
        toast.error('Meal not found in the plan');
        return false;
      }
    } catch (error) {
      console.error('Error in updateMealServings:', error);
      toast.error('An unexpected error occurred');
      return false;
    }
  },

  // Set meal status (cooked, skipped, null)
  setMealStatus: async (userId: string, date: string, mealType: string, status: 'cooked' | 'skipped' | null): Promise<boolean> => {
    try {
      const supabase = createClientComponentClient();

      // First, get the current meal plan
      const { data: mealPlanData, error: fetchError } = await supabase
        .from('meal_plans')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .maybeSingle();

      if (fetchError) {
        console.error('Error fetching meal plan:', fetchError);
        toast.error('Failed to fetch meal plan');
        return false;
      }

      if (!mealPlanData) {
        toast.error('No active meal plan found');
        return false;
      }

      // Update the status in the meal plan data
      const mealPlan = mealPlanData.meal_data;
      if (mealPlan[date] && mealPlan[date][mealType]) {
        mealPlan[date][mealType].status = status;

        // Update the meal plan in the database
        const { error: updateError } = await supabase
          .from('meal_plans')
          .update({
            meal_data: mealPlan,
            updated_at: new Date().toISOString()
          })
          .eq('id', mealPlanData.id);

        if (updateError) {
          console.error('Error updating meal status:', updateError);
          toast.error('Failed to update meal status');
          return false;
        }

        // If the status is 'cooked' or 'skipped', also record it in the meal_completions table
        if (status) {
          const { error: completionError } = await supabase
            .from('meal_completions')
            .upsert({
              user_id: userId,
              meal_plan_id: mealPlanData.id,
              date,
              meal_type: mealType,
              status,
              completed_at: new Date().toISOString()
            }, {
              onConflict: 'user_id,meal_plan_id,date,meal_type'
            });

          if (completionError) {
            console.error('Error recording meal completion:', completionError);
            // Don't return false here, as the main update was successful
          }
        }

        toast.success(`Meal marked as ${status || 'planned'}`);
        return true;
      } else {
        toast.error('Meal not found in the plan');
        return false;
      }
    } catch (error) {
      console.error('Error in setMealStatus:', error);
      toast.error('An unexpected error occurred');
      return false;
    }
  },

  // Move a meal from one day/type to another
  moveMeal: async (
    userId: string,
    sourceMeal: { day: string, mealType: string },
    targetMeal: { day: string, mealType: string }
  ): Promise<boolean> => {
    try {
      const supabase = createClientComponentClient();

      // First, get the current meal plan
      const { data: mealPlanData, error: fetchError } = await supabase
        .from('meal_plans')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .maybeSingle();

      if (fetchError) {
        console.error('Error fetching meal plan:', fetchError);
        toast.error('Failed to fetch meal plan');
        return false;
      }

      if (!mealPlanData) {
        toast.error('No active meal plan found');
        return false;
      }

      // Move the meal in the meal plan data
      const mealPlan = mealPlanData.meal_data;
      const { day: sourceDay, mealType: sourceMealType } = sourceMeal;
      const { day: targetDay, mealType: targetMealType } = targetMeal;

      if (mealPlan[sourceDay] && mealPlan[sourceDay][sourceMealType]) {
        const meal = mealPlan[sourceDay][sourceMealType];

        // Initialize target day if it doesn't exist
        if (!mealPlan[targetDay]) {
          mealPlan[targetDay] = {};
        }

        // Store the target meal if it exists (for swapping)
        const targetMealExists = !!mealPlan[targetDay][targetMealType];
        const targetMeal = targetMealExists ? { ...mealPlan[targetDay][targetMealType] } : null;

        // Move the source meal to the target
        mealPlan[targetDay][targetMealType] = meal;

        // If swapping, move the target meal to the source
        if (targetMealExists) {
          mealPlan[sourceDay][sourceMealType] = targetMeal;
        } else {
          // Otherwise, remove the source meal
          delete mealPlan[sourceDay][sourceMealType];

          // Clean up empty days
          if (Object.keys(mealPlan[sourceDay]).length === 0) {
            delete mealPlan[sourceDay];
          }
        }

        // Update the meal plan in the database
        const { error: updateError } = await supabase
          .from('meal_plans')
          .update({
            meal_data: mealPlan,
            updated_at: new Date().toISOString()
          })
          .eq('id', mealPlanData.id);

        if (updateError) {
          console.error('Error moving meal:', updateError);
          toast.error('Failed to move meal');
          return false;
        }

        toast.success(targetMealExists ? 'Meals swapped successfully' : 'Meal moved successfully');
        return true;
      } else {
        toast.error('Source meal not found in the plan');
        return false;
      }
    } catch (error) {
      console.error('Error in moveMeal:', error);
      toast.error('An unexpected error occurred');
      return false;
    }
  },

  // Assign a meal plan to the calendar
  assignMealPlanToCalendar: async (userId: string, mealPlanId: string, startDate: string, endDate: string): Promise<boolean> => {
    try {
      const supabase = createClientComponentClient();

      // Check for existing assignments in the date range
      const { data: existingAssignments, error: fetchError } = await supabase
        .from('meal_plan_assignments')
        .select('*')
        .eq('user_id', userId)
        .or(`start_date.lte.${endDate},end_date.gte.${startDate}`);

      if (fetchError) {
        console.error('Error checking existing assignments:', fetchError);
        toast.error('Failed to check existing assignments');
        return false;
      }

      if (existingAssignments && existingAssignments.length > 0) {
        toast.error('There are already meal plans assigned to this date range');
        return false;
      }

      // Create the assignment
      const { error: insertError } = await supabase
        .from('meal_plan_assignments')
        .insert({
          user_id: userId,
          meal_plan_id: mealPlanId,
          start_date: startDate,
          end_date: endDate
        });

      if (insertError) {
        console.error('Error assigning meal plan to calendar:', insertError);
        toast.error('Failed to assign meal plan to calendar');
        return false;
      }

      toast.success('Meal plan assigned to calendar successfully');
      return true;
    } catch (error) {
      console.error('Error in assignMealPlanToCalendar:', error);
      toast.error('An unexpected error occurred');
      return false;
    }
  },

  // Get all meal plan assignments for a user
  getMealPlanAssignments: async (userId: string): Promise<any[]> => {
    try {
      const supabase = createClientComponentClient();

      const { data, error } = await supabase
        .from('meal_plan_assignments')
        .select(`
          *,
          meal_plans:meal_plan_id (
            meal_data
          )
        `)
        .eq('user_id', userId)
        .order('start_date', { ascending: true });

      if (error) {
        console.error('Error fetching meal plan assignments:', error);
        toast.error('Failed to fetch meal plan assignments');
        return [];
      }

      return data;
    } catch (error) {
      console.error('Error in getMealPlanAssignments:', error);
      toast.error('An unexpected error occurred');
      return [];
    }
  },

  // Remove a meal plan assignment
  removeMealPlanAssignment: async (assignmentId: string): Promise<boolean> => {
    try {
      const supabase = createClientComponentClient();

      const { error } = await supabase
        .from('meal_plan_assignments')
        .delete()
        .eq('id', assignmentId);

      if (error) {
        console.error('Error removing meal plan assignment:', error);
        toast.error('Failed to remove meal plan assignment');
        return false;
      }

      toast.success('Meal plan assignment removed successfully');
      return true;
    } catch (error) {
      console.error('Error in removeMealPlanAssignment:', error);
      toast.error('An unexpected error occurred');
      return false;
    }
  }
};

// Helper function to calculate the total cost of a meal plan
function calculateTotalCost(mealPlan: MealPlan): number {
  let totalCost = 0;

  // Iterate through each day in the meal plan
  Object.values(mealPlan).forEach(day => {
    // Iterate through each meal type (breakfast, lunch, dinner)
    Object.values(day).forEach(meal => {
      if (meal && meal.cost) {
        totalCost += meal.cost;
      }
    });
  });

  return parseFloat(totalCost.toFixed(2));
}
