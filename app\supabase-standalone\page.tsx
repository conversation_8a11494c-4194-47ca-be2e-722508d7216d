'use client';

import React, { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export default function StandaloneTestPage() {
  const [supabase, setSupabase] = useState<any>(null);
  const [user, setUser] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    console.log('StandaloneTestPage mounted');
    
    // Create a Supabase client
    try {
      const client = createClientComponentClient();
      console.log('Supabase client created successfully');
      setSupabase(client);
      
      // Get the current user
      const getUser = async () => {
        try {
          const { data, error } = await client.auth.getUser();
          
          if (error) {
            throw error;
          }
          
          setUser(data.user);
        } catch (err: any) {
          console.error('Error getting user:', err);
          setError(err.message || 'Failed to get user');
        } finally {
          setLoading(false);
        }
      };
      
      getUser();
    } catch (err: any) {
      console.error('Error creating Supabase client:', err);
      setError(err.message || 'Failed to create Supabase client');
      setLoading(false);
    }
  }, []);
  
  return (
    <div className="container max-w-4xl py-8">
      <h1 className="text-2xl font-bold mb-6">Standalone Supabase Test</h1>
      
      {loading ? (
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-md text-blue-700">
          Loading...
        </div>
      ) : error ? (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md text-red-700">
          Error: {error}
        </div>
      ) : (
        <div className="p-4 bg-green-50 border border-green-200 rounded-md text-green-700">
          <p><strong>Supabase client:</strong> {supabase ? 'Created successfully' : 'Not created'}</p>
          <p><strong>User:</strong> {user ? user.email || user.id : 'Not logged in'}</p>
        </div>
      )}
      
      <div className="mt-8 p-4 bg-gray-50 border border-gray-200 rounded-md">
        <h2 className="text-lg font-semibold mb-2">Debug Information</h2>
        <pre className="text-xs overflow-auto p-2 bg-gray-100 rounded">
          {JSON.stringify({ 
            supabaseExists: !!supabase,
            userExists: !!user,
            error,
            loading
          }, null, 2)}
        </pre>
      </div>
    </div>
  );
}
