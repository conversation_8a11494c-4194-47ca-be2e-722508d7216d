import { mealPlanService } from '@/app/services/meal-plan-service';
import { getSupabaseClient, ClientType } from '@/app/services/database-client';

// Mock the getSupabaseClient function
jest.mock('@/app/services/database-client', () => ({
  getSupabaseClient: jest.fn(),
  ClientType: {
    USER: 'USER',
    ADMIN: 'ADMIN'
  }
}));

describe('mealPlanService', () => {
  // Mock Supabase client
  const mockSupabaseClient = {
    auth: {
      getUser: jest.fn()
    },
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabaseClient);
  });

  describe('getMealPlans', () => {
    it('should handle RLS permission denied errors correctly', async () => {
      // Mock the auth.getUser to return a valid user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'test-user-id' } },
        error: null
      });

      // Mock the query to return a permission denied error
      mockSupabaseClient.from.mockImplementation(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: null,
          error: {
            message: 'permission denied for table meal_plans',
            code: '42501'
          }
        })
      }));

      // Call the function
      const result = await mealPlanService.getMealPlans('test-user-id');

      // Verify the result
      expect(result.data).toEqual([]);
      expect(result.error).toContain('permission denied for table meal_plans');
      expect(result.error).toContain('Row Level Security (RLS) policies');
    });

    it('should return empty array with no error when no meal plans are found', async () => {
      // Mock the auth.getUser to return a valid user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'test-user-id' } },
        error: null
      });

      // Mock the query to return an empty array
      mockSupabaseClient.from.mockImplementation(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: [],
          error: null
        })
      }));

      // Call the function
      const result = await mealPlanService.getMealPlans('test-user-id');

      // Verify the result
      expect(result.data).toEqual([]);
      expect(result.error).toBeNull();
    });

    it('should return meal plans when query is successful', async () => {
      // Mock the auth.getUser to return a valid user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'test-user-id' } },
        error: null
      });

      // Mock meal plans data
      const mockMealPlans = [
        {
          id: '1',
          user_id: 'test-user-id',
          name: 'Test Meal Plan',
          status: 'active',
          start_date: '2023-01-01',
          end_date: '2023-01-07',
          total_cost: 100,
          meal_data: {},
          created_at: '2023-01-01',
          updated_at: '2023-01-01'
        }
      ];

      // Mock the query to return meal plans
      mockSupabaseClient.from.mockImplementation(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockMealPlans,
          error: null
        })
      }));

      // Call the function
      const result = await mealPlanService.getMealPlans('test-user-id');

      // Verify the result
      expect(result.data).toEqual(mockMealPlans);
      expect(result.error).toBeNull();
    });
  });
});
