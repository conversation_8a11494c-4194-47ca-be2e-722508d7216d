import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const createMealNoteSchema = z.object({
  meal_plan_id: z.string().uuid('Invalid meal plan ID'),
  meal_id: z.string().min(1, 'Meal ID is required'),
  note: z.string().min(1, 'Note cannot be empty').max(1000, 'Note is too long'),
});

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get current user
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const mealPlanId = searchParams.get('meal_plan_id');

    let query = supabase
      .from('meal_notes')
      .select('*')
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false });

    if (mealPlanId) {
      query = query.eq('meal_plan_id', mealPlanId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching meal notes:', error);
      return NextResponse.json(
        { error: 'Failed to fetch meal notes' },
        { status: 500 }
      );
    }

    return NextResponse.json({ data: data || [] });

  } catch (error) {
    console.error('Meal notes GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get current user
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { meal_plan_id, meal_id, note } = createMealNoteSchema.parse(body);

    // Check if meal plan exists and belongs to user
    const { data: mealPlan, error: mealPlanError } = await supabase
      .from('meal_plans')
      .select('id')
      .eq('id', meal_plan_id)
      .eq('user_id', session.user.id)
      .single();

    if (mealPlanError || !mealPlan) {
      return NextResponse.json(
        { error: 'Meal plan not found' },
        { status: 404 }
      );
    }

    // Check if note already exists for this meal
    const { data: existingNote, error: existingError } = await supabase
      .from('meal_notes')
      .select('id')
      .eq('user_id', session.user.id)
      .eq('meal_plan_id', meal_plan_id)
      .eq('meal_id', meal_id)
      .single();

    if (existingNote) {
      // Update existing note
      const { data, error } = await supabase
        .from('meal_notes')
        .update({
          note,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingNote.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating meal note:', error);
        return NextResponse.json(
          { error: 'Failed to update meal note' },
          { status: 500 }
        );
      }

      return NextResponse.json({ data, message: 'Meal note updated successfully' });
    } else {
      // Create new note
      const { data, error } = await supabase
        .from('meal_notes')
        .insert({
          user_id: session.user.id,
          meal_plan_id,
          meal_id,
          note,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating meal note:', error);
        return NextResponse.json(
          { error: 'Failed to create meal note' },
          { status: 500 }
        );
      }

      return NextResponse.json({ data, message: 'Meal note created successfully' });
    }

  } catch (error) {
    console.error('Meal notes POST error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
