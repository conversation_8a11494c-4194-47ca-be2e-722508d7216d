'use client';

import { useMealPlanner } from '../app/context/MealPlannerContext';
import { Skeleton } from './ui/skeleton';
import type { GroceryCategory } from '@/types/mealPlanner';

export function GroceryList() {
  const { groceryList: { categories, isLoading, error }, toggleGroceryItem } = useMealPlanner();

  if (isLoading) {
    return <Skeleton className="h-[200px] w-full" />;
  }

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  return (
    <div className="space-y-4">
      {categories.map((category: GroceryCategory) => (
        <div key={category.id}>
          <h3 className="font-medium">{category.name}</h3>
          <div className="space-y-2">
            {category.items.map((item) => (
              <div key={item.id} className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={item.checked}
                  onChange={(e) => toggleGroceryItem(category.id, item.id, e.target.checked)}
                />
                <span>{item.name}</span>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}



