import { Meal, DayPlan, GroceryList } from './mealPlanner';

export interface ApiResponse<T> {
  data?: T;
  error?: string;
}

export interface TodaysMealsResponse extends ApiResponse<{
  meals: Meal[];
}> {}

export interface WeeklyPlanResponse extends ApiResponse<{
  weeklyPlan: DayPlan[];
}> {}

export interface AlternativeMealsResponse extends ApiResponse<{
  alternatives: Meal[];
}> {}

export interface GroceryListResponse extends ApiResponse<{
  groceryList: GroceryList;
}> {}