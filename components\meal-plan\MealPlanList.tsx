"use client";

import { useState } from 'react';
import { <PERSON>al<PERSON><PERSON>, Meal, LegacyMealPlan } from '@/types/new-meal-plan';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Calendar, 
  Search, 
  ShoppingCart, 
  Trash2, 
  Eye, 
  Clock, 
  DollarSign 
} from 'lucide-react';
import { format, parseISO } from 'date-fns';

interface MealPlanListProps {
  mealPlans: LegacyMealPlan[];
  onViewPlan: (plan: LegacyMealPlan) => void;
  onDeletePlan: (planId: string) => void;
  onAddToCalendar: (plan: LegacyMealPlan) => void;
  onViewShoppingList: (plan: LegacyMealPlan) => void;
}

export function MealPlanList({
  mealPlans,
  onViewPlan,
  onDeletePlan,
  onAddToCalendar,
  onViewShoppingList
}: MealPlanListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  
  const filteredPlans = mealPlans.filter(plan => {
    const searchLower = searchTerm.toLowerCase();
    const nameMatch = plan.name?.toLowerCase().includes(searchLower);
    const descMatch = plan.description?.toLowerCase().includes(searchLower);
    return !searchTerm || nameMatch || descMatch;
  });
  
  return (
    <div className="space-y-6">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Search meal plans..."
          className="pl-10"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
      
      {filteredPlans.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-muted-foreground">No meal plans found</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPlans.map((plan) => (
            <Card key={plan.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{plan.name || 'Meal Plan'}</CardTitle>
                    <CardDescription>
                      {format(parseISO(plan.start_date), 'MMM d')} - {format(parseISO(plan.end_date), 'MMM d, yyyy')}
                    </CardDescription>
                  </div>
                  <Badge variant={plan.status === 'active' ? 'default' : 'outline'}>
                    {plan.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                  <div className="flex items-center">
                    <Clock className="mr-1 h-4 w-4" />
                    <span>7 days</span>
                  </div>
                  <div className="flex items-center">
                    <DollarSign className="mr-1 h-4 w-4" />
                    <span>${plan.total_cost?.toFixed(2) || '0.00'}</span>
                  </div>
                </div>
                <p className="text-sm line-clamp-2">
                  {plan.description || 'A weekly meal plan with balanced nutrition and delicious recipes.'}
                </p>
              </CardContent>
              <CardFooter className="flex justify-between pt-2">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => onViewPlan(plan)}
                  className="flex items-center gap-1"
                >
                  <Eye className="h-4 w-4" />
                  <span>View</span>
                </Button>
                <div className="flex gap-2">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => onViewShoppingList(plan)}
                    className="flex items-center gap-1"
                  >
                    <ShoppingCart className="h-4 w-4" />
                    <span className="sr-only sm:not-sr-only">Shopping List</span>
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => onAddToCalendar(plan)}
                    className="flex items-center gap-1"
                  >
                    <Calendar className="h-4 w-4" />
                    <span className="sr-only sm:not-sr-only">Add to Calendar</span>
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => onDeletePlan(plan.id)}
                    className="flex items-center gap-1 text-red-500 hover:text-red-600 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                    <span className="sr-only sm:not-sr-only">Delete</span>
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
