-- Create shopping_lists table
CREATE TABLE IF NOT EXISTS shopping_lists (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  meal_plan_id UUID REFERENCES meal_plans(id) ON DELETE SET NULL,
  name TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create shopping_items table
CREATE TABLE IF NOT EXISTS shopping_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  shopping_list_id UUID NOT NULL REFERENCES shopping_lists(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  quantity TEXT NOT NULL DEFAULT '1',
  unit TEXT NOT NULL DEFAULT 'item',
  category TEXT NOT NULL DEFAULT 'Other',
  checked BOOLEAN NOT NULL DEFAULT false,
  in_pantry BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE shopping_lists ENABLE ROW LEVEL SECURITY;
ALTER TABLE shopping_items ENABLE ROW LEVEL SECURITY;

-- Policy for selecting shopping lists (users can only see their own)
CREATE POLICY "Users can view their own shopping lists"
  ON shopping_lists
  FOR SELECT
  USING (user_id::text = auth.uid()::text);

-- Policy for inserting shopping lists
CREATE POLICY "Users can insert their own shopping lists"
  ON shopping_lists
  FOR INSERT
  WITH CHECK (user_id::text = auth.uid()::text);

-- Policy for updating shopping lists
CREATE POLICY "Users can update their own shopping lists"
  ON shopping_lists
  FOR UPDATE
  USING (user_id::text = auth.uid()::text);

-- Policy for deleting shopping lists
CREATE POLICY "Users can delete their own shopping lists"
  ON shopping_lists
  FOR DELETE
  USING (user_id::text = auth.uid()::text);

-- Policy for selecting shopping items (users can only see items from their own lists)
CREATE POLICY "Users can view their own shopping items"
  ON shopping_items
  FOR SELECT
  USING (shopping_list_id IN (SELECT id FROM shopping_lists WHERE user_id::text = auth.uid()::text));

-- Policy for inserting shopping items
CREATE POLICY "Users can insert their own shopping items"
  ON shopping_items
  FOR INSERT
  WITH CHECK (shopping_list_id IN (SELECT id FROM shopping_lists WHERE user_id::text = auth.uid()::text));

-- Policy for updating shopping items
CREATE POLICY "Users can update their own shopping items"
  ON shopping_items
  FOR UPDATE
  USING (shopping_list_id IN (SELECT id FROM shopping_lists WHERE user_id::text = auth.uid()::text));

-- Policy for deleting shopping items
CREATE POLICY "Users can delete their own shopping items"
  ON shopping_items
  FOR DELETE
  USING (shopping_list_id IN (SELECT id FROM shopping_lists WHERE user_id::text = auth.uid()::text));

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS shopping_lists_user_id_idx ON shopping_lists(user_id);
CREATE INDEX IF NOT EXISTS shopping_lists_meal_plan_id_idx ON shopping_lists(meal_plan_id);
CREATE INDEX IF NOT EXISTS shopping_items_shopping_list_id_idx ON shopping_items(shopping_list_id);
CREATE INDEX IF NOT EXISTS shopping_items_category_idx ON shopping_items(category);

-- Create trigger for updating the updated_at timestamp
CREATE OR REPLACE FUNCTION update_shopping_lists_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_shopping_lists_updated_at
BEFORE UPDATE ON shopping_lists
FOR EACH ROW
EXECUTE FUNCTION update_shopping_lists_updated_at();

CREATE OR REPLACE FUNCTION update_shopping_items_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_shopping_items_updated_at
BEFORE UPDATE ON shopping_items
FOR EACH ROW
EXECUTE FUNCTION update_shopping_items_updated_at();

-- Grant permissions to authenticated users
GRANT ALL ON shopping_lists TO authenticated;
GRANT ALL ON shopping_items TO authenticated;
