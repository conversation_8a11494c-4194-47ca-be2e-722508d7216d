import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { UserPreferences, UpdateUserPreferencesRequest } from '@/types/database-extended';

export class UserPreferencesService {
  private supabase = createClientComponentClient();

  async getUserPreferences(userId: string): Promise<{ data: UserPreferences | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
        console.error('Error fetching user preferences:', error);
        return { data: null, error: error.message };
      }

      return { data: data || null, error: null };
    } catch (error) {
      console.error('Unexpected error fetching user preferences:', error);
      return { data: null, error: 'Failed to fetch user preferences' };
    }
  }

  async createUserPreferences(userId: string, preferences: Partial<UpdateUserPreferencesRequest> = {}): Promise<{ data: UserPreferences | null; error: string | null }> {
    try {
      const defaultPreferences = {
        user_id: userId,
        cooking_skill_level: 'intermediate' as const,
        max_cooking_time: 30,
        preferred_cuisines: [],
        excluded_ingredients: [],
        notification_preferences: {
          email: true,
          push: false,
          meal_reminders: true
        },
        privacy_settings: {
          data_sharing: false,
          analytics: true
        },
        units_preference: 'metric' as const,
        currency_preference: 'USD',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        ...preferences
      };

      const { data, error } = await this.supabase
        .from('user_preferences')
        .insert(defaultPreferences)
        .select()
        .single();

      if (error) {
        console.error('Error creating user preferences:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Unexpected error creating user preferences:', error);
      return { data: null, error: 'Failed to create user preferences' };
    }
  }

  async updateUserPreferences(userId: string, updates: UpdateUserPreferencesRequest): Promise<{ data: UserPreferences | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('user_preferences')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        console.error('Error updating user preferences:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Unexpected error updating user preferences:', error);
      return { data: null, error: 'Failed to update user preferences' };
    }
  }

  async upsertUserPreferences(userId: string, preferences: UpdateUserPreferencesRequest): Promise<{ data: UserPreferences | null; error: string | null }> {
    try {
      // First, try to get existing preferences
      const { data: existingPreferences } = await this.getUserPreferences(userId);

      if (existingPreferences) {
        // Update existing preferences
        return await this.updateUserPreferences(userId, preferences);
      } else {
        // Create new preferences
        return await this.createUserPreferences(userId, preferences);
      }
    } catch (error) {
      console.error('Unexpected error upserting user preferences:', error);
      return { data: null, error: 'Failed to save user preferences' };
    }
  }

  async getDietaryRestrictions(userId: string): Promise<{ data: string[] | null; error: string | null }> {
    try {
      const { data, error } = await this.getUserPreferences(userId);
      
      if (error) {
        return { data: null, error };
      }

      // Also get dietary restrictions from the main users table
      const { data: userData, error: userError } = await this.supabase
        .from('users')
        .select('dietary_restrictions')
        .eq('id', userId)
        .single();

      if (userError) {
        console.error('Error fetching user dietary restrictions:', userError);
        return { data: data?.excluded_ingredients || [], error: null };
      }

      // Combine both sources
      const combinedRestrictions = [
        ...(data?.excluded_ingredients || []),
        ...(userData?.dietary_restrictions || [])
      ];

      // Remove duplicates
      const uniqueRestrictions = [...new Set(combinedRestrictions)];

      return { data: uniqueRestrictions, error: null };
    } catch (error) {
      console.error('Unexpected error fetching dietary restrictions:', error);
      return { data: null, error: 'Failed to fetch dietary restrictions' };
    }
  }

  async getGenerationPreferences(userId: string): Promise<{ 
    data: {
      cooking_skill_level: string;
      max_cooking_time: number;
      preferred_cuisines: string[];
      excluded_ingredients: string[];
      weekly_budget?: number;
      household_size?: number;
    } | null; 
    error: string | null 
  }> {
    try {
      // Get preferences from user_preferences table
      const { data: preferences, error: prefError } = await this.getUserPreferences(userId);
      
      // Get basic info from users table
      const { data: userData, error: userError } = await this.supabase
        .from('users')
        .select('weekly_budget, household_size, dietary_restrictions')
        .eq('id', userId)
        .single();

      if (userError) {
        console.error('Error fetching user data:', userError);
      }

      const generationPrefs = {
        cooking_skill_level: preferences?.cooking_skill_level || 'intermediate',
        max_cooking_time: preferences?.max_cooking_time || 30,
        preferred_cuisines: preferences?.preferred_cuisines || [],
        excluded_ingredients: [
          ...(preferences?.excluded_ingredients || []),
          ...(userData?.dietary_restrictions || [])
        ],
        weekly_budget: userData?.weekly_budget,
        household_size: userData?.household_size || 1
      };

      return { data: generationPrefs, error: null };
    } catch (error) {
      console.error('Unexpected error fetching generation preferences:', error);
      return { data: null, error: 'Failed to fetch generation preferences' };
    }
  }
}

export const userPreferencesService = new UserPreferencesService();
