"use client";

import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, ArrowDown, <PERSON>U<PERSON>, Minus } from 'lucide-react';
import { Meal } from '@/types/new-meal-plan';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';

interface NutritionComparisonProps {
  currentMeal: Meal;
  newMeal: Meal;
}

export function NutritionComparison({ currentMeal, newMeal }: NutritionComparisonProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'detailed'>('overview');

  // Calculate differences
  const caloriesDiff = newMeal.calories - currentMeal.calories;
  const proteinDiff = (newMeal.nutrition?.protein || 0) - (currentMeal.nutrition?.protein || 0);
  const carbsDiff = (newMeal.nutrition?.carbs || 0) - (currentMeal.nutrition?.carbs || 0);
  const fatDiff = (newMeal.nutrition?.fat || 0) - (currentMeal.nutrition?.fat || 0);
  const costDiff = newMeal.cost - currentMeal.cost;
  const prepTimeDiff = newMeal.prepTime - currentMeal.prepTime;

  // Calculate percentage differences
  const caloriesPercent = Math.round((caloriesDiff / currentMeal.calories) * 100);
  const proteinPercent = currentMeal.nutrition?.protein 
    ? Math.round((proteinDiff / currentMeal.nutrition.protein) * 100) 
    : 0;
  const carbsPercent = currentMeal.nutrition?.carbs 
    ? Math.round((carbsDiff / currentMeal.nutrition.carbs) * 100) 
    : 0;
  const fatPercent = currentMeal.nutrition?.fat 
    ? Math.round((fatDiff / currentMeal.nutrition.fat) * 100) 
    : 0;
  const costPercent = Math.round((costDiff / currentMeal.cost) * 100);
  const prepTimePercent = Math.round((prepTimeDiff / currentMeal.prepTime) * 100);

  // Helper function to get arrow icon based on difference
  const getDiffIcon = (diff: number) => {
    if (diff > 0) return <ArrowUp className="h-3 w-3 text-red-500" />;
    if (diff < 0) return <ArrowDown className="h-3 w-3 text-green-500" />;
    return <Minus className="h-3 w-3 text-gray-500" />;
  };

  // Helper function to get color based on difference
  const getDiffColor = (diff: number, isGoodWhenLower = true) => {
    if (diff === 0) return 'text-gray-500';
    return (diff > 0 && isGoodWhenLower) || (diff < 0 && !isGoodWhenLower) 
      ? 'text-red-500' 
      : 'text-green-500';
  };

  // Helper function to format percentage
  const formatPercent = (percent: number) => {
    if (percent === 0) return '0%';
    return `${percent > 0 ? '+' : ''}${percent}%`;
  };

  // Calculate max values for progress bars
  const maxCalories = Math.max(currentMeal.calories, newMeal.calories);
  const maxProtein = Math.max(
    currentMeal.nutrition?.protein || 0, 
    newMeal.nutrition?.protein || 0
  );
  const maxCarbs = Math.max(
    currentMeal.nutrition?.carbs || 0, 
    newMeal.nutrition?.carbs || 0
  );
  const maxFat = Math.max(
    currentMeal.nutrition?.fat || 0, 
    newMeal.nutrition?.fat || 0
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">Nutrition Comparison</h3>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          <TabsList className="h-8">
            <TabsTrigger value="overview" className="text-xs px-3 py-1">Overview</TabsTrigger>
            <TabsTrigger value="detailed" className="text-xs px-3 py-1">Detailed</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <TabsContent value="overview" className="mt-0">
        <div className="grid grid-cols-3 gap-2">
          <div className="p-3 border rounded-md bg-gray-50">
            <div className="text-xs text-muted-foreground mb-1">Calories</div>
            <div className="flex items-center justify-between">
              <div className="font-medium">{newMeal.calories}</div>
              <div className="flex items-center text-xs">
                {getDiffIcon(caloriesDiff)}
                <span className={getDiffColor(caloriesDiff)}>
                  {formatPercent(caloriesPercent)}
                </span>
              </div>
            </div>
          </div>
          <div className="p-3 border rounded-md bg-gray-50">
            <div className="text-xs text-muted-foreground mb-1">Cost</div>
            <div className="flex items-center justify-between">
              <div className="font-medium">${newMeal.cost.toFixed(2)}</div>
              <div className="flex items-center text-xs">
                {getDiffIcon(costDiff)}
                <span className={getDiffColor(costDiff)}>
                  {formatPercent(costPercent)}
                </span>
              </div>
            </div>
          </div>
          <div className="p-3 border rounded-md bg-gray-50">
            <div className="text-xs text-muted-foreground mb-1">Prep Time</div>
            <div className="flex items-center justify-between">
              <div className="font-medium">{newMeal.prepTime} min</div>
              <div className="flex items-center text-xs">
                {getDiffIcon(prepTimeDiff)}
                <span className={getDiffColor(prepTimeDiff)}>
                  {formatPercent(prepTimePercent)}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-4 space-y-3">
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-xs font-medium">Protein</span>
              <div className="flex items-center text-xs">
                <span>{newMeal.nutrition?.protein || 0}g</span>
                <span className="mx-1 text-muted-foreground">vs</span>
                <span>{currentMeal.nutrition?.protein || 0}g</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Progress 
                value={(newMeal.nutrition?.protein || 0) / maxProtein * 100} 
                className="h-2 bg-blue-100"
              />
              <Badge variant="outline" className="text-xs">
                {formatPercent(proteinPercent)}
              </Badge>
            </div>
          </div>

          <div>
            <div className="flex justify-between mb-1">
              <span className="text-xs font-medium">Carbs</span>
              <div className="flex items-center text-xs">
                <span>{newMeal.nutrition?.carbs || 0}g</span>
                <span className="mx-1 text-muted-foreground">vs</span>
                <span>{currentMeal.nutrition?.carbs || 0}g</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Progress 
                value={(newMeal.nutrition?.carbs || 0) / maxCarbs * 100} 
                className="h-2 bg-green-100"
              />
              <Badge variant="outline" className="text-xs">
                {formatPercent(carbsPercent)}
              </Badge>
            </div>
          </div>

          <div>
            <div className="flex justify-between mb-1">
              <span className="text-xs font-medium">Fat</span>
              <div className="flex items-center text-xs">
                <span>{newMeal.nutrition?.fat || 0}g</span>
                <span className="mx-1 text-muted-foreground">vs</span>
                <span>{currentMeal.nutrition?.fat || 0}g</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Progress 
                value={(newMeal.nutrition?.fat || 0) / maxFat * 100} 
                className="h-2 bg-yellow-100"
              />
              <Badge variant="outline" className="text-xs">
                {formatPercent(fatPercent)}
              </Badge>
            </div>
          </div>
        </div>
      </TabsContent>

      <TabsContent value="detailed" className="mt-0">
        <div className="border rounded-md overflow-hidden">
          <table className="w-full text-sm">
            <thead className="bg-muted">
              <tr>
                <th className="text-left p-2 font-medium">Nutrient</th>
                <th className="text-right p-2 font-medium">Current</th>
                <th className="text-center p-2 font-medium"></th>
                <th className="text-right p-2 font-medium">New</th>
                <th className="text-right p-2 font-medium">Diff</th>
              </tr>
            </thead>
            <tbody className="divide-y">
              <tr>
                <td className="p-2">Calories</td>
                <td className="text-right p-2">{currentMeal.calories}</td>
                <td className="text-center p-2">
                  <ArrowRight className="h-4 w-4 inline text-muted-foreground" />
                </td>
                <td className="text-right p-2">{newMeal.calories}</td>
                <td className={`text-right p-2 ${getDiffColor(caloriesDiff)}`}>
                  {caloriesDiff > 0 ? '+' : ''}{caloriesDiff} ({formatPercent(caloriesPercent)})
                </td>
              </tr>
              <tr>
                <td className="p-2">Protein</td>
                <td className="text-right p-2">{currentMeal.nutrition?.protein || 0}g</td>
                <td className="text-center p-2">
                  <ArrowRight className="h-4 w-4 inline text-muted-foreground" />
                </td>
                <td className="text-right p-2">{newMeal.nutrition?.protein || 0}g</td>
                <td className={`text-right p-2 ${getDiffColor(proteinDiff, false)}`}>
                  {proteinDiff > 0 ? '+' : ''}{proteinDiff}g ({formatPercent(proteinPercent)})
                </td>
              </tr>
              <tr>
                <td className="p-2">Carbs</td>
                <td className="text-right p-2">{currentMeal.nutrition?.carbs || 0}g</td>
                <td className="text-center p-2">
                  <ArrowRight className="h-4 w-4 inline text-muted-foreground" />
                </td>
                <td className="text-right p-2">{newMeal.nutrition?.carbs || 0}g</td>
                <td className={`text-right p-2 ${getDiffColor(carbsDiff)}`}>
                  {carbsDiff > 0 ? '+' : ''}{carbsDiff}g ({formatPercent(carbsPercent)})
                </td>
              </tr>
              <tr>
                <td className="p-2">Fat</td>
                <td className="text-right p-2">{currentMeal.nutrition?.fat || 0}g</td>
                <td className="text-center p-2">
                  <ArrowRight className="h-4 w-4 inline text-muted-foreground" />
                </td>
                <td className="text-right p-2">{newMeal.nutrition?.fat || 0}g</td>
                <td className={`text-right p-2 ${getDiffColor(fatDiff)}`}>
                  {fatDiff > 0 ? '+' : ''}{fatDiff}g ({formatPercent(fatPercent)})
                </td>
              </tr>
              <tr>
                <td className="p-2">Cost</td>
                <td className="text-right p-2">${currentMeal.cost.toFixed(2)}</td>
                <td className="text-center p-2">
                  <ArrowRight className="h-4 w-4 inline text-muted-foreground" />
                </td>
                <td className="text-right p-2">${newMeal.cost.toFixed(2)}</td>
                <td className={`text-right p-2 ${getDiffColor(costDiff)}`}>
                  {costDiff > 0 ? '+' : ''}${costDiff.toFixed(2)} ({formatPercent(costPercent)})
                </td>
              </tr>
              <tr>
                <td className="p-2">Prep Time</td>
                <td className="text-right p-2">{currentMeal.prepTime} min</td>
                <td className="text-center p-2">
                  <ArrowRight className="h-4 w-4 inline text-muted-foreground" />
                </td>
                <td className="text-right p-2">{newMeal.prepTime} min</td>
                <td className={`text-right p-2 ${getDiffColor(prepTimeDiff)}`}>
                  {prepTimeDiff > 0 ? '+' : ''}{prepTimeDiff} min ({formatPercent(prepTimePercent)})
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </TabsContent>
    </div>
  );
}
