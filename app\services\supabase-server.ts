import { createClient } from '@supabase/supabase-js';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/types/supabase';

/**
 * Create a server-side Supabase client
 * This is for use in API routes and server components
 */
export function createServerClient() {
  // Use the service role key to bypass RLS
  return createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

/**
 * Create a server component Supabase client
 */
export function createServerComponentSupabaseClient() {
  return createServerComponentClient<Database>({ cookies: () => cookies() });
}

/**
 * Create a route handler Supabase client
 */
export function createRouteHandlerSupabaseClient() {
  return createRouteHandlerClient<Database>({ cookies: () => cookies() });
}
