"use client";

import { create } from 'zustand';
import { Meal, MealPlan } from '@/types/new-meal-plan';
import { saveMealPlan } from '@/lib/meal-plan-utils';
import { toast } from 'sonner';

interface MealPlanState {
  mealPlan: MealPlan | null;
  isLoading: boolean;
  userId: string | null;
  favorites: Meal[];
  setMealPlan: (mealPlan: MealPlan) => void;
  setIsLoading: (isLoading: boolean) => void;
  setUserId: (userId: string) => void;
  addMeal: (day: string, mealType: string, meal: Meal) => void;
  removeMeal: (day: string, mealType: string) => void;
  setMealStatus: (day: string, mealType: string, status: 'cooked' | 'skipped' | null) => void;
  updateMealServings: (day: string, mealType: string, servings: number) => void;
  updateMealNotes: (day: string, mealType: string, notes: string) => void;
  toggleFavorite: (day: string, mealType: string) => void;
  getFavorites: () => Meal[];
  moveMeal: (sourceMeal: { day: string, mealType: string }, targetMeal: { day: string, mealType: string }) => void;
  saveMealPlanToDatabase: () => Promise<boolean>;
}

export const useMealPlanStore = create<MealPlanState>((set, get) => ({
  mealPlan: null,
  isLoading: true,
  userId: null,
  favorites: [],
  setMealPlan: (mealPlan) => set({ mealPlan }),
  setIsLoading: (isLoading) => set({ isLoading }),
  setUserId: (userId) => set({ userId }),
  addMeal: (day, mealType, meal) => {
    set((state) => {
      const newMealPlan = { ...state.mealPlan } || {};

      if (!newMealPlan[day]) {
        newMealPlan[day] = {};
      }

      newMealPlan[day][mealType] = meal;

      // Auto-save to database after adding a meal
      setTimeout(() => {
        get().saveMealPlanToDatabase();
      }, 500);

      return { mealPlan: newMealPlan };
    });
  },
  removeMeal: (day, mealType) => {
    set((state) => {
      if (!state.mealPlan || !state.mealPlan[day]) {
        return state;
      }

      const newMealPlan = { ...state.mealPlan };
      const newDay = { ...newMealPlan[day] };

      delete newDay[mealType];
      newMealPlan[day] = newDay;

      // If the day has no meals left, remove the day
      if (Object.keys(newDay).length === 0) {
        delete newMealPlan[day];
      }

      // Auto-save to database after removing a meal
      setTimeout(() => {
        get().saveMealPlanToDatabase();
      }, 500);

      return { mealPlan: newMealPlan };
    });
  },
  setMealStatus: (day, mealType, status) => {
    set((state) => {
      if (!state.mealPlan || !state.mealPlan[day] || !state.mealPlan[day][mealType]) {
        return state;
      }

      const newMealPlan = { ...state.mealPlan };
      const meal = { ...newMealPlan[day][mealType] };

      meal.status = status;
      newMealPlan[day][mealType] = meal;

      // Auto-save to database after changing meal status
      setTimeout(() => {
        get().saveMealPlanToDatabase();
      }, 500);

      return { mealPlan: newMealPlan };
    });
  },
  updateMealServings: (day, mealType, servings) => {
    set((state) => {
      if (!state.mealPlan || !state.mealPlan[day] || !state.mealPlan[day][mealType]) {
        return state;
      }

      const newMealPlan = { ...state.mealPlan };
      const meal = { ...newMealPlan[day][mealType] };
      const originalServings = meal.servings || 1;
      const scaleFactor = servings / originalServings;

      // Update servings
      meal.servings = servings;

      // Scale ingredients quantities
      if (meal.ingredients && Array.isArray(meal.ingredients)) {
        meal.ingredients = meal.ingredients.map(ingredient => {
          const newIngredient = { ...ingredient };
          // Only scale numeric quantities
          if (!isNaN(parseFloat(newIngredient.quantity))) {
            const scaledAmount = parseFloat(newIngredient.quantity) * scaleFactor;
            newIngredient.quantity = scaledAmount.toFixed(2);
          }
          return newIngredient;
        });
      }

      // Scale nutritional values and cost
      if (meal.nutrition) {
        meal.nutrition = {
          protein: Math.round(meal.nutrition.protein * scaleFactor),
          carbs: Math.round(meal.nutrition.carbs * scaleFactor),
          fat: Math.round(meal.nutrition.fat * scaleFactor)
        };
      }

      meal.calories = Math.round(meal.calories * scaleFactor);
      meal.cost = parseFloat((meal.cost * scaleFactor).toFixed(2));

      newMealPlan[day][mealType] = meal;

      // Auto-save to database after updating servings
      setTimeout(() => {
        get().saveMealPlanToDatabase();
      }, 500);

      return { mealPlan: newMealPlan };
    });
  },

  updateMealNotes: (day, mealType, notes) => {
    set((state) => {
      if (!state.mealPlan || !state.mealPlan[day] || !state.mealPlan[day][mealType]) {
        return state;
      }

      const newMealPlan = { ...state.mealPlan };
      const meal = { ...newMealPlan[day][mealType] };

      // Update notes
      meal.notes = notes;

      newMealPlan[day][mealType] = meal;

      // Auto-save to database after updating notes
      setTimeout(() => {
        get().saveMealPlanToDatabase();
      }, 500);

      return { mealPlan: newMealPlan };
    });
  },

  toggleFavorite: (day, mealType) => {
    set((state) => {
      if (!state.mealPlan || !state.mealPlan[day] || !state.mealPlan[day][mealType]) {
        return state;
      }

      const newMealPlan = { ...state.mealPlan };
      const meal = { ...newMealPlan[day][mealType] };

      // Toggle favorite status
      meal.favorite = !meal.favorite;

      // Update the meal in the meal plan
      newMealPlan[day][mealType] = meal;

      // Update favorites list
      let newFavorites = [...state.favorites];

      if (meal.favorite) {
        // Add to favorites if not already there
        if (!newFavorites.some(fav => fav.id === meal.id)) {
          newFavorites.push(meal);
        }
      } else {
        // Remove from favorites
        newFavorites = newFavorites.filter(fav => fav.id !== meal.id);
      }

      // Auto-save to database after toggling favorite
      setTimeout(() => {
        get().saveMealPlanToDatabase();
      }, 500);

      // Save favorites to local storage
      try {
        localStorage.setItem('mealFavorites', JSON.stringify(newFavorites));
      } catch (error) {
        console.error('Error saving favorites to local storage:', error);
      }

      return {
        mealPlan: newMealPlan,
        favorites: newFavorites
      };
    });
  },

  getFavorites: () => {
    const { favorites } = get();

    // If we already have favorites in state, return them
    if (favorites.length > 0) {
      return favorites;
    }

    // Otherwise, try to load from local storage
    try {
      const storedFavorites = localStorage.getItem('mealFavorites');
      if (storedFavorites) {
        const parsedFavorites = JSON.parse(storedFavorites);
        set({ favorites: parsedFavorites });
        return parsedFavorites;
      }
    } catch (error) {
      console.error('Error loading favorites from local storage:', error);
    }

    return [];
  },
  moveMeal: (sourceMeal, targetMeal) => {
    set((state) => {
      if (!state.mealPlan || !state.mealPlan[sourceMeal.day] || !state.mealPlan[sourceMeal.day][sourceMeal.mealType]) {
        return state;
      }

      const newMealPlan = { ...state.mealPlan };
      const meal = { ...newMealPlan[sourceMeal.day][sourceMeal.mealType] };

      // Add to target
      if (!newMealPlan[targetMeal.day]) {
        newMealPlan[targetMeal.day] = {};
      }
      newMealPlan[targetMeal.day][targetMeal.mealType] = meal;

      // Remove from source
      delete newMealPlan[sourceMeal.day][sourceMeal.mealType];

      // If the source day has no meals left, remove the day
      if (Object.keys(newMealPlan[sourceMeal.day]).length === 0) {
        delete newMealPlan[sourceMeal.day];
      }

      // Auto-save to database after moving a meal
      setTimeout(() => {
        get().saveMealPlanToDatabase();
      }, 500);

      return { mealPlan: newMealPlan };
    });
  },

  saveMealPlanToDatabase: async () => {
    const { mealPlan, userId } = get();

    if (!mealPlan) {
      toast.error('No meal plan to save');
      return false;
    }

    if (!userId) {
      toast.error('User ID is required to save meal plan');
      return false;
    }

    try {
      // Show loading toast
      toast('Saving meal plan...', { duration: 2000 });

      // Save to database
      const success = await saveMealPlan(mealPlan, userId);

      if (success) {
        toast.success('Meal plan saved successfully');
        return true;
      } else {
        toast.error('Failed to save meal plan');
        return false;
      }
    } catch (error) {
      console.error('Error saving meal plan:', error);
      toast.error('An error occurred while saving the meal plan');
      return false;
    }
  },
}));
