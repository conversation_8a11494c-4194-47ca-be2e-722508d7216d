"use client";

import { Meal, MealPlan, MealGenerationOptions, ShoppingItem, PantryItem, LegacyMealPlan } from "@/types/new-meal-plan";
import { format, addDays, startOfWeek } from "date-fns";

// Mock data for development
const mockMealPlan: MealPlan = {
  "2025-05-04": {
    "breakfast": {
      id: "b1",
      name: "Greek Yogurt Parfait",
      ingredients: [
        { name: "Greek yogurt", quantity: "1", unit: "cup" },
        { name: "Mixed berries", quantity: "1/2", unit: "cup" },
        { name: "Granola", quantity: "1/4", unit: "cup" },
        { name: "Honey", quantity: "1", unit: "tbsp" }
      ],
      instructions: [
        "Layer yogurt, berries, and granola in a glass",
        "Drizzle with honey",
        "Serve immediately"
      ],
      prepTime: 5,
      calories: 320,
      cost: 2.75,
      servings: 1,
      nutrition: {
        protein: 18,
        carbs: 45,
        fat: 8
      }
    },
    "lunch": {
      id: "l1",
      name: "Mediterranean Chickpea Salad",
      ingredients: [
        { name: "Chickpeas", quantity: "1", unit: "can" },
        { name: "Cucumber", quantity: "1/2", unit: "cup" },
        { name: "Cherry tomatoes", quantity: "1/2", unit: "cup" },
        { name: "Red onion", quantity: "1/4", unit: "cup" },
        { name: "Feta cheese", quantity: "1/4", unit: "cup" },
        { name: "Olive oil", quantity: "1", unit: "tbsp" },
        { name: "Lemon juice", quantity: "1", unit: "tbsp" }
      ],
      instructions: [
        "Rinse and drain chickpeas",
        "Chop cucumber, tomatoes, and red onion",
        "Combine all ingredients in a bowl",
        "Drizzle with olive oil and lemon juice",
        "Season with salt and pepper to taste"
      ],
      prepTime: 15,
      calories: 420,
      cost: 3.50,
      servings: 2,
      nutrition: {
        protein: 15,
        carbs: 52,
        fat: 18
      }
    }
  },
  "2025-05-05": {
    "breakfast": {
      id: "b2",
      name: "Avocado Toast",
      ingredients: [
        { name: "Whole grain bread", quantity: "2", unit: "slices" },
        { name: "Avocado", quantity: "1", unit: "" },
        { name: "Cherry tomatoes", quantity: "4", unit: "" },
        { name: "Eggs", quantity: "2", unit: "" },
        { name: "Salt and pepper", quantity: "", unit: "to taste" }
      ],
      instructions: [
        "Toast bread until golden and crispy",
        "Mash avocado and spread on toast",
        "Top with sliced tomatoes",
        "Fry eggs sunny side up",
        "Place eggs on top of toast",
        "Season with salt and pepper"
      ],
      prepTime: 10,
      calories: 450,
      cost: 3.25,
      servings: 1,
      nutrition: {
        protein: 20,
        carbs: 35,
        fat: 28
      },
      status: "cooked"
    },
    "dinner": {
      id: "d1",
      name: "One-Pot Pasta Primavera",
      ingredients: [
        { name: "Pasta", quantity: "8", unit: "oz" },
        { name: "Broccoli florets", quantity: "1", unit: "cup" },
        { name: "Bell pepper", quantity: "1", unit: "" },
        { name: "Cherry tomatoes", quantity: "1", unit: "cup" },
        { name: "Garlic", quantity: "2", unit: "cloves" },
        { name: "Olive oil", quantity: "2", unit: "tbsp" },
        { name: "Parmesan cheese", quantity: "1/4", unit: "cup" }
      ],
      instructions: [
        "Boil pasta according to package instructions",
        "In a large skillet, heat olive oil over medium heat",
        "Add garlic and sauté until fragrant",
        "Add vegetables and cook until tender",
        "Drain pasta and add to skillet",
        "Toss to combine and top with parmesan cheese"
      ],
      prepTime: 25,
      calories: 580,
      cost: 4.75,
      servings: 2,
      nutrition: {
        protein: 22,
        carbs: 78,
        fat: 20
      },
      status: "skipped"
    }
  }
};

// Convert legacy meal plan format to new format
export const convertLegacyMealPlan = (legacyPlan: LegacyMealPlan): MealPlan => {
  const newMealPlan: MealPlan = {};

  try {
    // Check if meal_data has the expected structure
    if (legacyPlan.meal_data?.mealPlan?.week) {
      const week = legacyPlan.meal_data.mealPlan.week;

      // Process each day in the week
      week.forEach(dayPlan => {
        const dayStr = dayPlan.day;
        newMealPlan[dayStr] = {};

        // Process each meal in the day
        dayPlan.meals.forEach(meal => {
          const newMeal: Meal = {
            id: meal.recipeId || `meal-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            name: meal.name,
            ingredients: [], // Legacy format might not have ingredients
            instructions: [], // Legacy format might not have instructions
            prepTime: meal.prepTime || 0,
            calories: meal.nutrition?.calories || 0,
            cost: meal.cost || 0,
            servings: meal.servings || 1,
            nutrition: {
              protein: parseInt(meal.nutrition?.protein || '0'),
              carbs: parseInt(meal.nutrition?.carbs || '0'),
              fat: parseInt(meal.nutrition?.fats || '0')
            },
            image: meal.image || ''
          };

          newMealPlan[dayStr][meal.type] = newMeal;
        });
      });
    }
  } catch (error) {
    console.error('Error converting legacy meal plan:', error);
    // Return empty meal plan on error
    return {};
  }

  return newMealPlan;
};

// This function would normally fetch data from the API/database
export const getMealPlan = async (): Promise<MealPlan> => {
  // In a real implementation, this would fetch from the API
  // For now, we'll use mock data
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockMealPlan);
    }, 1000);
  });
};

// This function would normally call the OpenAI API to generate a meal plan
export const generateMealPlan = async (options: MealGenerationOptions): Promise<MealPlan> => {
  console.log("Generating meal plan with options:", options);

  // In a real implementation, this would call the OpenAI API
  // For now, we'll use mock data
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockMealPlan);
    }, 2000);
  });
};

// This function would normally call the OpenAI API to generate a single meal
export const generateMeal = async ({ mealType }: { mealType: string }): Promise<Meal> => {
  console.log("Generating meal for type:", mealType);

  // In a real implementation, this would call the OpenAI API
  // For now, we'll return a mock meal
  return new Promise((resolve) => {
    setTimeout(() => {
      const meals = [
        {
          id: `generated-${Date.now()}`,
          name: "Spinach & Feta Omelette",
          ingredients: [
            { name: "Eggs", quantity: "2", unit: "large" },
            { name: "Spinach", quantity: "1", unit: "cup" },
            { name: "Feta cheese", quantity: "2", unit: "tbsp" },
            { name: "Olive oil", quantity: "1", unit: "tsp" },
            { name: "Salt and pepper", quantity: "", unit: "to taste" }
          ],
          instructions: [
            "Whisk eggs in a bowl",
            "Heat oil in a non-stick pan over medium heat",
            "Add spinach and cook until wilted",
            "Pour eggs over spinach",
            "Sprinkle feta on top",
            "Cook until eggs are set",
            "Fold in half and serve"
          ],
          prepTime: 10,
          calories: 280,
          cost: 2.30,
          servings: 1,
          nutrition: {
            protein: 19,
            carbs: 3,
            fat: 22
          }
        },
        {
          id: `generated-${Date.now()}`,
          name: "Quinoa Buddha Bowl",
          ingredients: [
            { name: "Quinoa", quantity: "1/2", unit: "cup" },
            { name: "Sweet potato", quantity: "1/2", unit: "cup" },
            { name: "Chickpeas", quantity: "1/2", unit: "cup" },
            { name: "Avocado", quantity: "1/4", unit: "" },
            { name: "Kale", quantity: "1", unit: "cup" },
            { name: "Tahini", quantity: "1", unit: "tbsp" },
            { name: "Lemon juice", quantity: "1", unit: "tsp" }
          ],
          instructions: [
            "Cook quinoa according to package instructions",
            "Roast sweet potato and chickpeas at 400°F for 20 minutes",
            "Massage kale with a bit of olive oil and salt",
            "Arrange all ingredients in a bowl",
            "Mix tahini and lemon juice for dressing",
            "Drizzle dressing over the bowl"
          ],
          prepTime: 30,
          calories: 450,
          cost: 4.15,
          servings: 1,
          nutrition: {
            protein: 14,
            carbs: 65,
            fat: 18
          }
        },
        {
          id: `generated-${Date.now()}`,
          name: "Herb-Roasted Chicken Thighs",
          ingredients: [
            { name: "Chicken thighs", quantity: "4", unit: "" },
            { name: "Garlic", quantity: "3", unit: "cloves" },
            { name: "Fresh rosemary", quantity: "1", unit: "tbsp" },
            { name: "Fresh thyme", quantity: "1", unit: "tbsp" },
            { name: "Olive oil", quantity: "2", unit: "tbsp" },
            { name: "Lemon", quantity: "1", unit: "" },
            { name: "Salt and pepper", quantity: "", unit: "to taste" }
          ],
          instructions: [
            "Preheat oven to 425°F",
            "Mix herbs, garlic, oil, lemon juice in a bowl",
            "Season chicken with salt and pepper",
            "Brush herb mixture onto chicken thighs",
            "Roast for 25-30 minutes until internal temp reaches 165°F",
            "Let rest for 5 minutes before serving"
          ],
          prepTime: 40,
          calories: 520,
          cost: 5.80,
          servings: 2,
          nutrition: {
            protein: 45,
            carbs: 5,
            fat: 35
          }
        }
      ];

      // Return a random meal from the list
      resolve(meals[Math.floor(Math.random() * meals.length)]);
    }, 1500);
  });
};

// Function to add a custom meal
export const addCustomMeal = (meal: Meal): Meal => {
  return {
    ...meal,
    id: `custom-${Date.now()}`
  };
};

// Generate a shopping list from a meal plan
export const generateShoppingList = async (mealPlan?: MealPlan): Promise<{ shoppingList: ShoppingItem[], pantryItems: PantryItem[] }> => {
  // If a meal plan is provided, generate a shopping list based on its ingredients
  if (mealPlan) {
    try {
      console.log('Generating shopping list from meal plan:', mealPlan);

      // Create a map to track ingredients and their quantities
      const ingredientMap = new Map<string, {
        name: string;
        quantity: number;
        unit: string;
        category: string;
      }>();

      // Process each day and meal in the meal plan
      Object.values(mealPlan).forEach(dayMeals => {
        Object.values(dayMeals).forEach(meal => {
          // Process each ingredient in the meal
          meal.ingredients?.forEach(ingredient => {
            const key = ingredient.name.toLowerCase();
            const currentQuantity = parseFloat(ingredient.quantity) || 1;

            if (ingredientMap.has(key)) {
              // Update existing ingredient
              const existing = ingredientMap.get(key)!;
              existing.quantity += currentQuantity;
            } else {
              // Add new ingredient
              ingredientMap.set(key, {
                name: ingredient.name,
                quantity: currentQuantity,
                unit: ingredient.unit || '',
                // Categorize ingredients (simplified version)
                category: categorizeIngredient(ingredient.name)
              });
            }
          });
        });
      });

      // Convert the map to a shopping list
      const shoppingList: ShoppingItem[] = Array.from(ingredientMap.entries()).map(([key, item], index) => ({
        id: `s${index + 1}`,
        name: item.name,
        quantity: item.quantity.toString(),
        unit: item.unit,
        category: item.category,
        cost: estimateIngredientCost(item.name, item.quantity),
        inPantry: checkIfInPantry(item.name)
      }));

      // Get pantry items (using mock data for now)
      const pantryItems: PantryItem[] = [
        { id: 'p1', name: 'Olive oil', quantity: '1/2', unit: 'bottle', category: 'Oils', lowStock: false },
        { id: 'p2', name: 'Granola', quantity: '1/4', unit: 'box', category: 'Breakfast', lowStock: true },
        { id: 'p3', name: 'Honey', quantity: '3/4', unit: 'bottle', category: 'Condiments', lowStock: false },
        { id: 'p4', name: 'Pasta', quantity: '2', unit: 'boxes', category: 'Pasta & Rice', lowStock: false },
        { id: 'p5', name: 'Garlic', quantity: '1/2', unit: 'head', category: 'Produce', lowStock: true },
        { id: 'p6', name: 'Salt', quantity: '1', unit: 'container', category: 'Spices', lowStock: false },
        { id: 'p7', name: 'Pepper', quantity: '1', unit: 'container', category: 'Spices', lowStock: false },
        { id: 'p8', name: 'Rice', quantity: '2', unit: 'lbs', category: 'Pasta & Rice', lowStock: false }
      ];

      // Mark items that are in the pantry
      shoppingList.forEach(item => {
        const pantryItem = pantryItems.find(p => p.name.toLowerCase() === item.name.toLowerCase());
        if (pantryItem) {
          item.inPantry = true;
        }
      });

      return { shoppingList, pantryItems };
    } catch (error) {
      console.error('Error generating shopping list:', error);
      // Fall back to mock data if there's an error
    }
  }

  // If no meal plan is provided or there was an error, return mock data
  return new Promise((resolve) => {
    setTimeout(() => {
      const shoppingList: ShoppingItem[] = [
        { id: 's1', name: 'Greek yogurt', quantity: '2', unit: 'cups', category: 'Dairy', cost: 3.50, inPantry: false },
        { id: 's2', name: 'Mixed berries', quantity: '1', unit: 'bag', category: 'Produce', cost: 4.99, inPantry: false },
        { id: 's3', name: 'Granola', quantity: '1', unit: 'box', category: 'Breakfast', cost: 3.99, inPantry: true },
        { id: 's4', name: 'Honey', quantity: '1', unit: 'bottle', category: 'Condiments', cost: 5.49, inPantry: true },
        { id: 's5', name: 'Chickpeas', quantity: '2', unit: 'cans', category: 'Canned Goods', cost: 1.98, inPantry: false },
        { id: 's6', name: 'Cucumber', quantity: '1', unit: '', category: 'Produce', cost: 0.79, inPantry: false },
        { id: 's7', name: 'Cherry tomatoes', quantity: '1', unit: 'pint', category: 'Produce', cost: 3.49, inPantry: false },
        { id: 's8', name: 'Red onion', quantity: '1', unit: '', category: 'Produce', cost: 0.89, inPantry: false },
        { id: 's9', name: 'Feta cheese', quantity: '1', unit: 'package', category: 'Dairy', cost: 4.29, inPantry: false },
        { id: 's10', name: 'Olive oil', quantity: '1', unit: 'bottle', category: 'Oils', cost: 8.99, inPantry: true },
        { id: 's11', name: 'Whole grain bread', quantity: '1', unit: 'loaf', category: 'Bakery', cost: 3.99, inPantry: false },
        { id: 's12', name: 'Avocado', quantity: '2', unit: '', category: 'Produce', cost: 2.50, inPantry: false },
        { id: 's13', name: 'Eggs', quantity: '1', unit: 'dozen', category: 'Dairy', cost: 3.99, inPantry: false },
        { id: 's14', name: 'Pasta', quantity: '1', unit: 'box', category: 'Pasta & Rice', cost: 1.99, inPantry: true },
        { id: 's15', name: 'Broccoli', quantity: '1', unit: 'head', category: 'Produce', cost: 1.99, inPantry: false },
        { id: 's16', name: 'Bell pepper', quantity: '2', unit: '', category: 'Produce', cost: 2.98, inPantry: false },
        { id: 's17', name: 'Garlic', quantity: '1', unit: 'head', category: 'Produce', cost: 0.79, inPantry: true },
        { id: 's18', name: 'Parmesan cheese', quantity: '1', unit: 'wedge', category: 'Dairy', cost: 5.99, inPantry: false }
      ];

      const pantryItems: PantryItem[] = [
        { id: 'p1', name: 'Olive oil', quantity: '1/2', unit: 'bottle', category: 'Oils', lowStock: false },
        { id: 'p2', name: 'Granola', quantity: '1/4', unit: 'box', category: 'Breakfast', lowStock: true },
        { id: 'p3', name: 'Honey', quantity: '3/4', unit: 'bottle', category: 'Condiments', lowStock: false },
        { id: 'p4', name: 'Pasta', quantity: '2', unit: 'boxes', category: 'Pasta & Rice', lowStock: false },
        { id: 'p5', name: 'Garlic', quantity: '1/2', unit: 'head', category: 'Produce', lowStock: true },
        { id: 'p6', name: 'Salt', quantity: '1', unit: 'container', category: 'Spices', lowStock: false },
        { id: 'p7', name: 'Pepper', quantity: '1', unit: 'container', category: 'Spices', lowStock: false },
        { id: 'p8', name: 'Rice', quantity: '2', unit: 'lbs', category: 'Pasta & Rice', lowStock: false }
      ];

      resolve({ shoppingList, pantryItems });
    }, 1500);
  });
};

// Helper function to categorize ingredients
const categorizeIngredient = (name: string): string => {
  name = name.toLowerCase();

  if (name.includes('milk') || name.includes('cheese') || name.includes('yogurt') || name.includes('cream') || name.includes('butter')) {
    return 'Dairy';
  } else if (name.includes('apple') || name.includes('banana') || name.includes('berry') || name.includes('fruit')) {
    return 'Fruits';
  } else if (name.includes('lettuce') || name.includes('spinach') || name.includes('kale') || name.includes('carrot') ||
             name.includes('onion') || name.includes('potato') || name.includes('tomato') || name.includes('cucumber')) {
    return 'Produce';
  } else if (name.includes('chicken') || name.includes('beef') || name.includes('pork') || name.includes('fish') || name.includes('meat')) {
    return 'Meat & Seafood';
  } else if (name.includes('rice') || name.includes('pasta') || name.includes('noodle')) {
    return 'Pasta & Rice';
  } else if (name.includes('bread') || name.includes('bagel') || name.includes('muffin')) {
    return 'Bakery';
  } else if (name.includes('oil') || name.includes('vinegar')) {
    return 'Oils';
  } else if (name.includes('salt') || name.includes('pepper') || name.includes('spice') || name.includes('herb')) {
    return 'Spices';
  } else if (name.includes('sugar') || name.includes('flour') || name.includes('baking')) {
    return 'Baking';
  } else if (name.includes('can') || name.includes('bean') || name.includes('soup')) {
    return 'Canned Goods';
  } else {
    return 'Other';
  }
};

// Helper function to estimate ingredient cost
const estimateIngredientCost = (name: string, quantity: number): number => {
  name = name.toLowerCase();
  let basePrice = 2.0; // Default base price

  // Adjust base price based on ingredient type
  if (name.includes('meat') || name.includes('fish') || name.includes('chicken') || name.includes('beef')) {
    basePrice = 8.0;
  } else if (name.includes('cheese') || name.includes('dairy')) {
    basePrice = 4.0;
  } else if (name.includes('vegetable') || name.includes('fruit')) {
    basePrice = 3.0;
  } else if (name.includes('spice') || name.includes('herb')) {
    basePrice = 5.0;
  }

  // Adjust for quantity (simplified)
  return Math.round((basePrice * Math.sqrt(quantity)) * 100) / 100;
};

// Helper function to check if an item is in the pantry
const checkIfInPantry = (name: string): boolean => {
  // Mock pantry items (in a real app, this would check against the user's actual pantry)
  const pantryItems = ['olive oil', 'salt', 'pepper', 'flour', 'sugar', 'rice', 'pasta', 'garlic', 'onion'];
  return pantryItems.some(item => name.toLowerCase().includes(item));
};

// Save meal plan to the database
export const saveMealPlan = async (mealPlan: MealPlan, userId: string): Promise<boolean> => {
  try {
    // In a real implementation, this would save to the database
    // For now, we'll just simulate a successful save
    console.log('Saving meal plan to database for user:', userId);
    console.log('Meal plan data:', mealPlan);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Return success
    return true;
  } catch (error) {
    console.error('Error saving meal plan:', error);
    return false;
  }
};

// Helper function to get the current week days
export const getCurrentWeekDays = (currentDate: Date = new Date()) => {
  const startOfCurrentWeek = startOfWeek(currentDate, { weekStartsOn: 1 });

  return Array.from({ length: 7 }).map((_, i) => {
    const date = addDays(startOfCurrentWeek, i);
    const dayStr = format(date, "yyyy-MM-dd");
    return {
      date,
      dayStr,
      dayName: format(date, "EEE"),
      dayOfMonth: format(date, "d")
    };
  });
};
