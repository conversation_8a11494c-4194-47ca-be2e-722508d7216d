import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MealPlannerProvider } from '../../app/context/MealPlannerContext';
import { GroceryList } from '@/components/GroceryList';
import { groceryListService } from '@/services/groceryListService';
import type { GroceryCategory } from '@/types/mealPlanner';

// Mock the service
jest.mock('@/services/groceryListService');

const mockGroceryList = {
  categories: [
    {
      id: '1',
      name: 'Fruits',
      items: [
        { id: '1', name: 'Apple', checked: false, quantity: 1, unit: 'piece' },
        { id: '2', name: '<PERSON><PERSON>', checked: true, quantity: 2, unit: 'piece' },
      ],
    },
  ] as GroceryCategory[],
};

describe('GroceryList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (groceryListService.getGroceryList as jest.Mock).mockResolvedValue(
      mockGroceryList.categories
    );
  });

  it('renders loading state initially', () => {
    render(
      <MealPlannerProvider>
        <GroceryList />
      </MealPlannerProvider>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('renders grocery items after loading', async () => {
    render(
      <MealPlannerProvider>
        <GroceryList />
      </MealPlannerProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Fruits')).toBeInTheDocument();
      expect(screen.getByText('Apple')).toBeInTheDocument();
      expect(screen.getByText('Banana')).toBeInTheDocument();
    });
  });
});

