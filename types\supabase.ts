import { CookieOptions } from '@supabase/auth-helpers-shared'

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          weekly_budget: number
          household_size: number
          dietary_restrictions: string[]
          created_at: string
          updated_at: string
        }
        Insert: Omit<Tables['users']['Row'], 'created_at' | 'updated_at'>
        Update: Partial<Tables['users']['Insert']>
      },
      meal_plans: {
        Row: {
          id: string
          user_id: string
          start_date: string
          end_date: string
          total_cost: number
          status: string
          meal_data: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: Omit<Tables['meal_plans']['Row'], 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Tables['meal_plans']['Insert']>
      },
      recipes: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string | null
          instructions: string[]
          prep_time: number
          cook_time: number
          servings: number
          cost_per_serving: number
          image_url: string | null
          cuisine: string | null
          dietary_restrictions: string[] | null
          is_favorite: boolean
          created_at: string
          updated_at: string
        }
        Insert: Omit<Tables['recipes']['Row'], 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Tables['recipes']['Insert']>
      },
      shopping_lists: {
        Row: {
          id: string
          user_id: string
          meal_plan_id: string | null
          items: Json
          created_at: string
          updated_at: string
        }
        Insert: Omit<Tables['shopping_lists']['Row'], 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Tables['shopping_lists']['Insert']>
      },
      pantry_items: {
        Row: {
          id: string
          user_id: string
          name: string
          category: string
          quantity: string
          unit: string
          expiry_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: Omit<Tables['pantry_items']['Row'], 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Tables['pantry_items']['Insert']>
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]
