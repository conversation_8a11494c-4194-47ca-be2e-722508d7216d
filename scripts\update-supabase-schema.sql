-- Update Supabase Schema for Recipes Table

-- First, check if the recipes table exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'recipes') THEN
        -- Create the recipes table if it doesn't exist
        CREATE TABLE public.recipes (
            id SERIAL PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT,
            prep_time INTEGER,
            cook_time INTEGER,
            servings INTEGER,
            cost_per_serving DECIMAL(10, 2),
            image_url TEXT,
            is_favorite BOOLEAN DEFAULT false,
            user_id UUID REFERENCES auth.users(id),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            difficulty TEXT,
            meal_type TEXT,
            instructions TEXT[] DEFAULT '{}'::TEXT[],
            ingredients JSONB DEFAULT '[]'::JSONB,
            nutrition JSONB DEFAULT '{}'::JSONB,
            dietary_restrictions TEXT[] DEFAULT '{}'::TEXT[]
        );

        -- Add RLS policies
        ALTER TABLE public.recipes ENABLE ROW LEVEL SECURITY;
        
        -- Create policy to allow users to see only their own recipes
        CREATE POLICY "Users can view their own recipes" 
            ON public.recipes 
            FOR SELECT 
            USING (auth.uid() = user_id);
            
        -- Create policy to allow users to insert their own recipes
        CREATE POLICY "Users can insert their own recipes" 
            ON public.recipes 
            FOR INSERT 
            WITH CHECK (auth.uid() = user_id);
            
        -- Create policy to allow users to update their own recipes
        CREATE POLICY "Users can update their own recipes" 
            ON public.recipes 
            FOR UPDATE 
            USING (auth.uid() = user_id);
            
        -- Create policy to allow users to delete their own recipes
        CREATE POLICY "Users can delete their own recipes" 
            ON public.recipes 
            FOR DELETE 
            USING (auth.uid() = user_id);
    ELSE
        -- If the table exists, add any missing columns
        
        -- Check and add ingredients column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'recipes' AND column_name = 'ingredients') THEN
            ALTER TABLE public.recipes ADD COLUMN ingredients JSONB DEFAULT '[]'::JSONB;
        END IF;
        
        -- Check and add nutrition column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'recipes' AND column_name = 'nutrition') THEN
            ALTER TABLE public.recipes ADD COLUMN nutrition JSONB DEFAULT '{}'::JSONB;
        END IF;
        
        -- Check and add instructions column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'recipes' AND column_name = 'instructions') THEN
            ALTER TABLE public.recipes ADD COLUMN instructions TEXT[] DEFAULT '{}'::TEXT[];
        END IF;
        
        -- Check and add dietary_restrictions column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'recipes' AND column_name = 'dietary_restrictions') THEN
            ALTER TABLE public.recipes ADD COLUMN dietary_restrictions TEXT[] DEFAULT '{}'::TEXT[];
        END IF;
        
        -- Check and add difficulty column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'recipes' AND column_name = 'difficulty') THEN
            ALTER TABLE public.recipes ADD COLUMN difficulty TEXT;
        END IF;
        
        -- Check and add meal_type column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'recipes' AND column_name = 'meal_type') THEN
            ALTER TABLE public.recipes ADD COLUMN meal_type TEXT;
        END IF;
    END IF;
END
$$;

-- Create or update shopping_lists table
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'shopping_lists') THEN
        -- Create the shopping_lists table if it doesn't exist
        CREATE TABLE public.shopping_lists (
            id SERIAL PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id),
            meal_plan_id INTEGER,
            items JSONB DEFAULT '[]'::JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );

        -- Add RLS policies
        ALTER TABLE public.shopping_lists ENABLE ROW LEVEL SECURITY;
        
        -- Create policy to allow users to see only their own shopping lists
        CREATE POLICY "Users can view their own shopping lists" 
            ON public.shopping_lists 
            FOR SELECT 
            USING (auth.uid() = user_id);
            
        -- Create policy to allow users to insert their own shopping lists
        CREATE POLICY "Users can insert their own shopping lists" 
            ON public.shopping_lists 
            FOR INSERT 
            WITH CHECK (auth.uid() = user_id);
            
        -- Create policy to allow users to update their own shopping lists
        CREATE POLICY "Users can update their own shopping lists" 
            ON public.shopping_lists 
            FOR UPDATE 
            USING (auth.uid() = user_id);
            
        -- Create policy to allow users to delete their own shopping lists
        CREATE POLICY "Users can delete their own shopping lists" 
            ON public.shopping_lists 
            FOR DELETE 
            USING (auth.uid() = user_id);
    ELSE
        -- If the table exists, add any missing columns
        
        -- Check and add items column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'shopping_lists' AND column_name = 'items') THEN
            ALTER TABLE public.shopping_lists ADD COLUMN items JSONB DEFAULT '[]'::JSONB;
        END IF;
    END IF;
END
$$;

-- Create or update meal_plans table
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'meal_plans') THEN
        -- Create the meal_plans table if it doesn't exist
        CREATE TABLE public.meal_plans (
            id SERIAL PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id),
            name TEXT,
            start_date DATE,
            end_date DATE,
            meals JSONB DEFAULT '[]'::JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );

        -- Add RLS policies
        ALTER TABLE public.meal_plans ENABLE ROW LEVEL SECURITY;
        
        -- Create policy to allow users to see only their own meal plans
        CREATE POLICY "Users can view their own meal plans" 
            ON public.meal_plans 
            FOR SELECT 
            USING (auth.uid() = user_id);
            
        -- Create policy to allow users to insert their own meal plans
        CREATE POLICY "Users can insert their own meal plans" 
            ON public.meal_plans 
            FOR INSERT 
            WITH CHECK (auth.uid() = user_id);
            
        -- Create policy to allow users to update their own meal plans
        CREATE POLICY "Users can update their own meal plans" 
            ON public.meal_plans 
            FOR UPDATE 
            USING (auth.uid() = user_id);
            
        -- Create policy to allow users to delete their own meal plans
        CREATE POLICY "Users can delete their own meal plans" 
            ON public.meal_plans 
            FOR DELETE 
            USING (auth.uid() = user_id);
    END IF;
END
$$;

-- Add indexes for better performance
DO $$
BEGIN
    -- Add index on user_id for recipes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_recipes_user_id') THEN
        CREATE INDEX idx_recipes_user_id ON public.recipes(user_id);
    END IF;
    
    -- Add index on user_id for shopping_lists
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_shopping_lists_user_id') THEN
        CREATE INDEX idx_shopping_lists_user_id ON public.shopping_lists(user_id);
    END IF;
    
    -- Add index on user_id for meal_plans
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_meal_plans_user_id') THEN
        CREATE INDEX idx_meal_plans_user_id ON public.meal_plans(user_id);
    END IF;
END
$$;
