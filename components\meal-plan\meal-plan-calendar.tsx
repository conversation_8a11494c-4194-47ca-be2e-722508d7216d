'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, isSameDay, addMonths, subMonths } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ScrollArea } from '@/components/ui/scroll-area';

interface MealPlan {
  id: string;
  user_id: string;
  start_date: string;
  end_date: string;
  total_cost: number;
  meal_data: any;
  status: string;
  created_at: string;
  updated_at: string;
  name?: string;
  description?: string;
}

interface MealPlanCalendarProps {
  mealPlans: MealPlan[];
  onSelectDate: (date: Date) => void;
  onSelectPlan: (plan: MealPlan) => void;
}

export function MealPlanCalendar({ mealPlans, onSelectDate, onSelectPlan }: MealPlanCalendarProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [weekDates, setWeekDates] = useState<Date[]>([]);
  const [plansForWeek, setPlansForWeek] = useState<Record<string, MealPlan[]>>({});

  // Update week dates when selected date changes
  useEffect(() => {
    const start = startOfWeek(selectedDate, { weekStartsOn: 1 }); // Start on Monday
    const end = endOfWeek(selectedDate, { weekStartsOn: 1 }); // End on Sunday
    const days = eachDayOfInterval({ start, end });
    setWeekDates(days);
  }, [selectedDate]);

  // Filter plans for the current week
  useEffect(() => {
    const plansByDate: Record<string, MealPlan[]> = {};
    
    weekDates.forEach(date => {
      const dateStr = format(date, 'yyyy-MM-dd');
      plansByDate[dateStr] = mealPlans.filter(plan => {
        const planStart = new Date(plan.start_date);
        const planEnd = new Date(plan.end_date);
        return date >= planStart && date <= planEnd;
      });
    });
    
    setPlansForWeek(plansByDate);
  }, [weekDates, mealPlans]);

  // Handle date selection
  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      onSelectDate(date);
    }
  };

  // Navigate to previous month
  const prevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  // Navigate to next month
  const nextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  // Get plans for a specific date
  const getPlansForDate = (date: Date): MealPlan[] => {
    const dateStr = format(date, 'yyyy-MM-dd');
    return plansForWeek[dateStr] || [];
  };

  // Render meal plan indicators for calendar
  const renderMealPlanIndicator = (date: Date) => {
    const plans = mealPlans.filter(plan => {
      const planStart = new Date(plan.start_date);
      const planEnd = new Date(plan.end_date);
      return date >= planStart && date <= planEnd;
    });

    if (plans.length === 0) return null;

    return (
      <div className="flex justify-center mt-1">
        {plans.length > 3 ? (
          <Badge variant="outline" className="text-xs">
            {plans.length} plans
          </Badge>
        ) : (
          <div className="flex gap-1 justify-center">
            {plans.map((_, i) => (
              <div 
                key={i} 
                className="h-1.5 w-1.5 rounded-full bg-primary"
              />
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
        <Card className="md:col-span-3">
          <CardContent className="p-4">
            <div className="flex justify-between items-center mb-4">
              <Button variant="outline" size="icon" onClick={prevMonth}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h2 className="text-lg font-medium">
                {format(currentMonth, 'MMMM yyyy')}
              </h2>
              <Button variant="outline" size="icon" onClick={nextMonth}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={handleDateSelect}
              month={currentMonth}
              className="rounded-md border"
              components={{
                DayContent: ({ date }) => (
                  <div className="w-full h-full flex flex-col justify-center items-center">
                    <span>{format(date, 'd')}</span>
                    {renderMealPlanIndicator(date)}
                  </div>
                ),
              }}
            />
          </CardContent>
        </Card>

        <Card className="md:col-span-4">
          <CardContent className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium">
                Week of {format(weekDates[0], 'MMM d')} - {format(weekDates[6], 'MMM d, yyyy')}
              </h2>
              <Button variant="outline" size="sm" onClick={() => setSelectedDate(new Date())}>
                Today
              </Button>
            </div>

            <div className="space-y-4">
              {weekDates.map((date) => {
                const dateStr = format(date, 'yyyy-MM-dd');
                const plans = plansForWeek[dateStr] || [];
                const isToday = isSameDay(date, new Date());

                return (
                  <div key={dateStr} className="border rounded-lg overflow-hidden">
                    <div 
                      className={`px-4 py-2 font-medium ${
                        isToday ? 'bg-primary text-primary-foreground' : 'bg-muted'
                      }`}
                    >
                      {format(date, 'EEEE, MMMM d')}
                    </div>
                    
                    <div className="p-2">
                      {plans.length > 0 ? (
                        <ScrollArea className="h-[120px]">
                          <div className="space-y-2 p-2">
                            {plans.map((plan) => (
                              <div 
                                key={plan.id}
                                className="p-2 border rounded-md cursor-pointer hover:bg-muted/50 transition-colors"
                                onClick={() => onSelectPlan(plan)}
                              >
                                <div className="font-medium">
                                  {plan.name || `Meal Plan (${format(new Date(plan.start_date), 'MMM d')})`}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  ${plan.total_cost.toFixed(2)} • {plan.meal_data?.mealPlan?.meals?.length || 0} meals
                                </div>
                              </div>
                            ))}
                          </div>
                        </ScrollArea>
                      ) : (
                        <div className="flex flex-col items-center justify-center h-[120px] text-muted-foreground">
                          <p className="mb-2">No meal plans for this day</p>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-8">
                                  <Plus className="h-4 w-4 mr-1" />
                                  Add Plan
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Create a new meal plan starting on this date</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
