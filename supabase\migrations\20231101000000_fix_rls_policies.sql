-- Fix RLS policies for meal_plans table
-- First, drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own meal plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can insert their own meal plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can update their own meal plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can delete their own meal plans" ON meal_plans;

-- Enable RLS on meal_plans table
ALTER TABLE meal_plans ENABLE ROW LEVEL SECURITY;

-- Create policies for meal_plans table
CREATE POLICY "Users can view their own meal plans"
ON meal_plans
FOR SELECT
USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own meal plans"
ON meal_plans
FOR INSERT
WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own meal plans"
ON meal_plans
FOR UPDATE
USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own meal plans"
ON meal_plans
FOR DELETE
USING (auth.uid()::text = user_id);

-- Fix RLS policies for meals table
DROP POLICY IF EXISTS "Users can view their own meals" ON meals;
DROP POLICY IF EXISTS "Users can insert their own meals" ON meals;
DROP POLICY IF EXISTS "Users can update their own meals" ON meals;
DROP POLICY IF EXISTS "Users can delete their own meals" ON meals;

-- Enable RLS on meals table
ALTER TABLE meals ENABLE ROW LEVEL SECURITY;

-- Create policies for meals table
CREATE POLICY "Users can view their own meals"
ON meals
FOR SELECT
USING (EXISTS (
  SELECT 1 FROM meal_plans mp
  WHERE mp.id = meal_plan_id AND mp.user_id = auth.uid()::text
));

CREATE POLICY "Users can insert their own meals"
ON meals
FOR INSERT
WITH CHECK (EXISTS (
  SELECT 1 FROM meal_plans mp
  WHERE mp.id = meal_plan_id AND mp.user_id = auth.uid()::text
));

CREATE POLICY "Users can update their own meals"
ON meals
FOR UPDATE
USING (EXISTS (
  SELECT 1 FROM meal_plans mp
  WHERE mp.id = meal_plan_id AND mp.user_id = auth.uid()::text
));

CREATE POLICY "Users can delete their own meals"
ON meals
FOR DELETE
USING (EXISTS (
  SELECT 1 FROM meal_plans mp
  WHERE mp.id = meal_plan_id AND mp.user_id = auth.uid()::text
));

-- Fix RLS policies for shopping_lists table
DROP POLICY IF EXISTS "Users can view their own shopping lists" ON shopping_lists;
DROP POLICY IF EXISTS "Users can insert their own shopping lists" ON shopping_lists;
DROP POLICY IF EXISTS "Users can update their own shopping lists" ON shopping_lists;
DROP POLICY IF EXISTS "Users can delete their own shopping lists" ON shopping_lists;

-- Enable RLS on shopping_lists table
ALTER TABLE shopping_lists ENABLE ROW LEVEL SECURITY;

-- Create policies for shopping_lists table
CREATE POLICY "Users can view their own shopping lists"
ON shopping_lists
FOR SELECT
USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own shopping lists"
ON shopping_lists
FOR INSERT
WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own shopping lists"
ON shopping_lists
FOR UPDATE
USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own shopping lists"
ON shopping_lists
FOR DELETE
USING (auth.uid()::text = user_id);

-- Fix RLS policies for shopping_items table
DROP POLICY IF EXISTS "Users can view their own shopping items" ON shopping_items;
DROP POLICY IF EXISTS "Users can insert their own shopping items" ON shopping_items;
DROP POLICY IF EXISTS "Users can update their own shopping items" ON shopping_items;
DROP POLICY IF EXISTS "Users can delete their own shopping items" ON shopping_items;

-- Enable RLS on shopping_items table
ALTER TABLE shopping_items ENABLE ROW LEVEL SECURITY;

-- Create policies for shopping_items table
CREATE POLICY "Users can view their own shopping items"
ON shopping_items
FOR SELECT
USING (EXISTS (
  SELECT 1 FROM shopping_lists sl
  WHERE sl.id = shopping_list_id AND sl.user_id = auth.uid()::text
));

CREATE POLICY "Users can insert their own shopping items"
ON shopping_items
FOR INSERT
WITH CHECK (EXISTS (
  SELECT 1 FROM shopping_lists sl
  WHERE sl.id = shopping_list_id AND sl.user_id = auth.uid()::text
));

CREATE POLICY "Users can update their own shopping items"
ON shopping_items
FOR UPDATE
USING (EXISTS (
  SELECT 1 FROM shopping_lists sl
  WHERE sl.id = shopping_list_id AND sl.user_id = auth.uid()::text
));

CREATE POLICY "Users can delete their own shopping items"
ON shopping_items
FOR DELETE
USING (EXISTS (
  SELECT 1 FROM shopping_lists sl
  WHERE sl.id = shopping_list_id AND sl.user_id = auth.uid()::text
));
