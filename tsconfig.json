{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./*", "./app/*", "./src/*"], "@/components/*": ["./components/*"], "@/lib/*": ["./lib/*"], "@/types/*": ["./types/*", "./app/types/*"], "@/context/*": ["./context/*", "./app/context/*"]}, "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}