import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/types/supabase';

export async function createSupabaseServerClient() {
  try {
    const cookieStore = cookies();
    
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      throw new Error('Missing Supabase environment variables');
    }

    return createRouteHandlerClient<Database>({
      cookies: () => cookieStore,
    });
  } catch (error) {
    console.error('Failed to create Supabase client:', error);
    throw error;
  }
}




