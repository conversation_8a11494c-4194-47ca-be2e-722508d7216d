'use server';

import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

/**
 * Server action to fix RLS policies for the meal_plans table
 * This can be called from a client component
 */
export async function fixMealPlansRlsPolicies() {
  try {
    // Create a Supabase admin client with service role
    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!, // Service role key - only available server-side
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Execute the SQL to fix RLS policies
    const { error } = await supabase.rpc('exec_sql', {
      sql_string: `
        -- Drop existing policies
        DROP POLICY IF EXISTS "Users can view their own meal plans" ON meal_plans;
        DROP POLICY IF EXISTS "Users can insert their own meal plans" ON meal_plans;
        DROP POLICY IF EXISTS "Users can update their own meal plans" ON meal_plans;
        DROP POLICY IF EXISTS "Users can delete their own meal plans" ON meal_plans;
        DROP POLICY IF EXISTS "Users can view their own meal_plans" ON meal_plans;
        DROP POLICY IF EXISTS "Users can insert their own meal_plans" ON meal_plans;
        DROP POLICY IF EXISTS "Users can update their own meal_plans" ON meal_plans;
        DROP POLICY IF EXISTS "Users can delete their own meal_plans" ON meal_plans;

        -- Enable RLS on meal_plans table
        ALTER TABLE meal_plans ENABLE ROW LEVEL SECURITY;

        -- Create policies for meal_plans table with proper type casting
        CREATE POLICY "Users can view their own meal plans"
        ON meal_plans
        FOR SELECT
        USING (user_id::text = auth.uid()::text);

        CREATE POLICY "Users can insert their own meal plans"
        ON meal_plans
        FOR INSERT
        WITH CHECK (user_id::text = auth.uid()::text);

        CREATE POLICY "Users can update their own meal plans"
        ON meal_plans
        FOR UPDATE
        USING (user_id::text = auth.uid()::text);

        CREATE POLICY "Users can delete their own meal plans"
        ON meal_plans
        FOR DELETE
        USING (user_id::text = auth.uid()::text);

        -- Grant permissions to authenticated users
        GRANT ALL ON meal_plans TO authenticated;
      `
    });

    if (error) {
      console.error('Error fixing RLS policies:', error);
      return { success: false, error: error.message };
    }

    return { success: true, message: 'RLS policies fixed successfully' };
  } catch (error) {
    console.error('Error in fixMealPlansRlsPolicies:', error);
    return { success: false, error: String(error) };
  }
}
