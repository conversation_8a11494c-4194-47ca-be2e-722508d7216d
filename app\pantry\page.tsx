"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSupabase } from "@/components/supabase-provider";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Search,
  Plus,
  Trash2,
  ShoppingCart,
  Package,
  RefreshCw,
  Filter,
  Check,
  ChevronDown,
  ChevronUp
} from "lucide-react";

interface PantryItem {
  id: string;
  name: string;
  category: string;
  quantity: string;
  unit: string;
  expiry_date?: string;
  created_at: string;
}

interface Category {
  name: string;
  items: PantryItem[];
}

export default function PantryPage() {
  const router = useRouter();
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const [isLoading, setIsLoading] = useState(true);
  const [pantryItems, setPantryItems] = useState<PantryItem[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [newItem, setNewItem] = useState({
    name: "",
    category: "Other",
    quantity: "1",
    unit: "item",
    expiry_date: ""
  });
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});

  // Predefined categories
  const predefinedCategories = [
    "Fruits",
    "Vegetables",
    "Meat & Seafood",
    "Dairy",
    "Grains & Bread",
    "Condiments & Spices",
    "Canned Goods",
    "Snacks",
    "Beverages",
    "Frozen Foods",
    "Baking",
    "Other"
  ];

  useEffect(() => {
    // Don't fetch if Supabase is still loading or not available
    if (isSupabaseLoading || !supabase) {
      return;
    }

    fetchPantryItems();
  }, [supabase, isSupabaseLoading]);

  const fetchPantryItems = async () => {
    try {
      setIsLoading(true);

      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        router.push('/login');
        return;
      }

      const { data, error } = await supabase
        .from('pantry_items')
        .select('*')
        .eq('user_id', user.id)
        .order('name');

      if (error) throw error;

      if (data) {
        setPantryItems(data);
        processItemsIntoCategories(data);
      } else {
        // Create mock data for demonstration
        createMockData();
      }
    } catch (error) {
      console.error('Error fetching pantry items:', error);
      toast.error('Failed to load pantry items');

      // Create mock data if there was an error
      createMockData();
    } finally {
      setIsLoading(false);
    }
  };

  const createMockData = () => {
    const mockItems: PantryItem[] = [
      { id: "1", name: "Olive Oil", category: "Condiments & Spices", quantity: "1", unit: "bottle", created_at: new Date().toISOString() },
      { id: "2", name: "Rice", category: "Grains & Bread", quantity: "2", unit: "kg", created_at: new Date().toISOString() },
      { id: "3", name: "Chicken Breast", category: "Meat & Seafood", quantity: "500", unit: "g", created_at: new Date().toISOString() },
      { id: "4", name: "Milk", category: "Dairy", quantity: "1", unit: "liter", created_at: new Date().toISOString() },
      { id: "5", name: "Apples", category: "Fruits", quantity: "6", unit: "", created_at: new Date().toISOString() },
      { id: "6", name: "Spinach", category: "Vegetables", quantity: "1", unit: "bunch", created_at: new Date().toISOString() },
      { id: "7", name: "Pasta", category: "Grains & Bread", quantity: "2", unit: "boxes", created_at: new Date().toISOString() },
      { id: "8", name: "Canned Tomatoes", category: "Canned Goods", quantity: "3", unit: "cans", created_at: new Date().toISOString() },
      { id: "9", name: "Greek Yogurt", category: "Dairy", quantity: "500", unit: "g", created_at: new Date().toISOString() },
      { id: "10", name: "Black Pepper", category: "Condiments & Spices", quantity: "1", unit: "bottle", created_at: new Date().toISOString() },
    ];

    setPantryItems(mockItems);
    processItemsIntoCategories(mockItems);
  };

  const processItemsIntoCategories = (items: PantryItem[]) => {
    // Filter items based on search query
    const filteredItems = items.filter(item =>
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.category.toLowerCase().includes(searchQuery.toLowerCase())
    );

    // Group items by category
    const categoryGroups = filteredItems.reduce((groups: Record<string, PantryItem[]>, item) => {
      const category = item.category || "Other";
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(item);
      return groups;
    }, {});

    // Convert to array of categories
    const categoriesArray = Object.keys(categoryGroups).map(categoryName => ({
      name: categoryName,
      items: categoryGroups[categoryName].sort((a, b) => a.name.localeCompare(b.name))
    }));

    // Sort categories
    categoriesArray.sort((a, b) => {
      // Put "Other" at the end
      if (a.name === "Other") return 1;
      if (b.name === "Other") return -1;
      return a.name.localeCompare(b.name);
    });

    setCategories(categoriesArray);

    // Initialize expanded state for categories
    const expanded: Record<string, boolean> = {};
    categoriesArray.forEach(category => {
      // If we already have a state for this category, keep it, otherwise default to expanded
      expanded[category.name] = expandedCategories[category.name] !== undefined
        ? expandedCategories[category.name]
        : true;
    });
    setExpandedCategories(expanded);
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    processItemsIntoCategories(pantryItems);
  };

  const handleAddItem = async () => {
    if (!newItem.name.trim()) {
      toast.error("Please enter an item name");
      return;
    }

    try {
      setIsLoading(true);

      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        router.push('/login');
        return;
      }

      const newPantryItem: Omit<PantryItem, 'id' | 'created_at'> & { user_id: string } = {
        name: newItem.name,
        category: newItem.category,
        quantity: newItem.quantity,
        unit: newItem.unit,
        user_id: user.id,
        ...(newItem.expiry_date ? { expiry_date: newItem.expiry_date } : {})
      };

      const { data, error } = await supabase
        .from('pantry_items')
        .insert(newPantryItem)
        .select()
        .single();

      if (error) throw error;

      if (data) {
        setPantryItems([...pantryItems, data]);
        processItemsIntoCategories([...pantryItems, data]);
        toast.success("Item added to pantry");

        // Reset form
        setNewItem({
          name: "",
          category: "Other",
          quantity: "1",
          unit: "item",
          expiry_date: ""
        });
      }
    } catch (error) {
      console.error('Error adding pantry item:', error);
      toast.error('Failed to add item to pantry');

      // Add item locally for demonstration
      const mockItem: PantryItem = {
        id: `item_${Date.now()}`,
        name: newItem.name,
        category: newItem.category,
        quantity: newItem.quantity,
        unit: newItem.unit,
        ...(newItem.expiry_date ? { expiry_date: newItem.expiry_date } : {}),
        created_at: new Date().toISOString()
      };

      setPantryItems([...pantryItems, mockItem]);
      processItemsIntoCategories([...pantryItems, mockItem]);

      // Reset form
      setNewItem({
        name: "",
        category: "Other",
        quantity: "1",
        unit: "item",
        expiry_date: ""
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteItem = async (itemId: string) => {
    try {
      const { error } = await supabase
        .from('pantry_items')
        .delete()
        .eq('id', itemId);

      if (error) throw error;

      const updatedItems = pantryItems.filter(item => item.id !== itemId);
      setPantryItems(updatedItems);
      processItemsIntoCategories(updatedItems);
      toast.success("Item removed from pantry");
    } catch (error) {
      console.error('Error deleting pantry item:', error);
      toast.error('Failed to remove item from pantry');

      // Remove item locally for demonstration
      const updatedItems = pantryItems.filter(item => item.id !== itemId);
      setPantryItems(updatedItems);
      processItemsIntoCategories(updatedItems);
    }
  };

  const handleUpdateQuantity = async (itemId: string, newQuantity: string) => {
    try {
      const { error } = await supabase
        .from('pantry_items')
        .update({ quantity: newQuantity })
        .eq('id', itemId);

      if (error) throw error;

      const updatedItems = pantryItems.map(item =>
        item.id === itemId ? { ...item, quantity: newQuantity } : item
      );
      setPantryItems(updatedItems);
      processItemsIntoCategories(updatedItems);
    } catch (error) {
      console.error('Error updating pantry item:', error);
      toast.error('Failed to update item quantity');

      // Update item locally for demonstration
      const updatedItems = pantryItems.map(item =>
        item.id === itemId ? { ...item, quantity: newQuantity } : item
      );
      setPantryItems(updatedItems);
      processItemsIntoCategories(updatedItems);
    }
  };

  const toggleCategoryExpansion = (categoryName: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryName]: !prev[categoryName]
    }));
  };

  const handleGenerateShoppingList = () => {
    router.push('/shopping-list?from=pantry');
  };

  if (isLoading || isSupabaseLoading) {
    return (
      <div className="container max-w-4xl py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Pantry Management</h1>
        <Button onClick={handleGenerateShoppingList}>
          <ShoppingCart className="h-4 w-4 mr-2" />
          Shopping List
        </Button>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <TabsList>
            <TabsTrigger value="all">All Items</TabsTrigger>
            <TabsTrigger value="add">Add Item</TabsTrigger>
          </TabsList>

          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search pantry..."
              className="w-[200px] pl-8"
              value={searchQuery}
              onChange={handleSearch}
            />
          </div>
        </div>

        <TabsContent value="all" className="space-y-4">
          {categories.length === 0 ? (
            <Card className="text-center py-8">
              <CardContent>
                <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h2 className="text-xl font-semibold mb-2">Your Pantry is Empty</h2>
                <p className="text-muted-foreground max-w-md mx-auto mb-6">
                  Add items to your pantry to keep track of what you have on hand.
                </p>
                <Button onClick={() => setActiveTab("add")}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Your First Item
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {categories.map((category) => (
                <Card key={category.name} className="overflow-hidden">
                  <CardHeader
                    className="py-3 cursor-pointer"
                    onClick={() => toggleCategoryExpansion(category.name)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <CardTitle className="text-base">{category.name}</CardTitle>
                        <Badge variant="outline" className="ml-2">
                          {category.items.length}
                        </Badge>
                      </div>
                      {expandedCategories[category.name] ? (
                        <ChevronUp className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      )}
                    </div>
                  </CardHeader>

                  {expandedCategories[category.name] && (
                    <CardContent className="py-0">
                      <div className="space-y-2">
                        {category.items.map((item) => (
                          <div key={item.id} className="flex items-center justify-between py-2">
                            <div className="flex-1">
                              <p className="font-medium">{item.name}</p>
                              <div className="flex items-center text-sm text-muted-foreground">
                                <span>
                                  {item.quantity} {item.unit}
                                </span>
                                {item.expiry_date && (
                                  <span className="ml-2">
                                    • Expires: {new Date(item.expiry_date).toLocaleDateString()}
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className="flex items-center border rounded-md">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8"
                                  onClick={() => {
                                    const currentQuantity = parseInt(item.quantity) || 0;
                                    if (currentQuantity > 1) {
                                      handleUpdateQuantity(item.id, (currentQuantity - 1).toString());
                                    }
                                  }}
                                >
                                  <span className="text-lg">-</span>
                                </Button>
                                <span className="w-8 text-center">{item.quantity}</span>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8"
                                  onClick={() => {
                                    const currentQuantity = parseInt(item.quantity) || 0;
                                    handleUpdateQuantity(item.id, (currentQuantity + 1).toString());
                                  }}
                                >
                                  <span className="text-lg">+</span>
                                </Button>
                              </div>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-destructive"
                                onClick={() => handleDeleteItem(item.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  )}
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="add">
          <Card>
            <CardHeader>
              <CardTitle>Add New Item</CardTitle>
              <CardDescription>
                Add items to your pantry to keep track of what you have on hand.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <Label htmlFor="item-name">Item Name</Label>
                  <Input
                    id="item-name"
                    value={newItem.name}
                    onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                    placeholder="Enter item name"
                  />
                </div>
                <div>
                  <Label htmlFor="item-category">Category</Label>
                  <Input
                    id="item-category"
                    value={newItem.category}
                    onChange={(e) => setNewItem({ ...newItem, category: e.target.value })}
                    placeholder="Category"
                    list="categories"
                  />
                  <datalist id="categories">
                    {predefinedCategories.map(category => (
                      <option key={category} value={category} />
                    ))}
                  </datalist>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                  <Label htmlFor="item-quantity">Quantity</Label>
                  <Input
                    id="item-quantity"
                    value={newItem.quantity}
                    onChange={(e) => setNewItem({ ...newItem, quantity: e.target.value })}
                    type="text"
                  />
                </div>
                <div>
                  <Label htmlFor="item-unit">Unit</Label>
                  <Input
                    id="item-unit"
                    value={newItem.unit}
                    onChange={(e) => setNewItem({ ...newItem, unit: e.target.value })}
                    placeholder="e.g., lbs, oz, etc."
                  />
                </div>
                <div>
                  <Label htmlFor="item-expiry">Expiry Date (Optional)</Label>
                  <Input
                    id="item-expiry"
                    type="date"
                    value={newItem.expiry_date}
                    onChange={(e) => setNewItem({ ...newItem, expiry_date: e.target.value })}
                  />
                </div>
              </div>

              <Button
                className="w-full"
                onClick={handleAddItem}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add to Pantry
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
