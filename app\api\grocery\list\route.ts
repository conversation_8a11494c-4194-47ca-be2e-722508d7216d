import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/types/supabase';
import { groceryService, ClientType, getSupabaseClient } from '@/app/services/database-server';

export async function GET() {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Use the grocery service to fetch grocery lists
    const { data: groceryLists, error } = await groceryService.getGroceryLists(session.user.id);

    if (error) {
      console.error('Error fetching grocery lists:', error);
      return NextResponse.json({ error: 'Failed to fetch grocery lists' }, { status: 500 });
    }

    return NextResponse.json(groceryLists);
  } catch (error) {
    console.error('Error fetching grocery lists:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse the request body
    const body = await request.json();

    // Validate the request body
    if (!body.name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    // Use the grocery service to create a new grocery list
    const { data: groceryList, error } = await groceryService.createGroceryList({
      userId: session.user.id,
      name: body.name
    });

    if (error) {
      console.error('Error creating grocery list:', error);
      return NextResponse.json({ error: 'Failed to create grocery list' }, { status: 500 });
    }

    return NextResponse.json(groceryList, { status: 201 });
  } catch (error) {
    console.error('Error creating grocery list:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
