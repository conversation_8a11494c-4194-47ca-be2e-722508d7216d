"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Utensils, Clock, DollarSign, Check, RefreshCw, Loader2 } from "lucide-react";
import { useSupabase } from "@/components/supabase-provider";
import { toast } from "sonner";
import { recipeService } from "@/app/services/database-client";

interface MealSwapModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentMeal: any;
  onSwap: (newMeal: any) => void;
}

export function MealSwapModal({ isOpen, onClose, currentMeal, onSwap }: MealSwapModalProps) {
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const [alternatives, setAlternatives] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedMeal, setSelectedMeal] = useState<any | null>(null);
  const [fallbackMode, setFallbackMode] = useState(false);

  // Reset selected meal when modal opens with a new current meal
  useEffect(() => {
    if (isOpen && supabase && !isSupabaseLoading) {
      setSelectedMeal(null);
      // Auto-generate alternatives when modal opens
      generateAlternatives();
    }
  }, [isOpen, currentMeal, supabase, isSupabaseLoading]);

  // Function to generate alternatives
  const generateAlternatives = async () => {
    setIsLoading(true);
    setFallbackMode(false);

    try {
      // Get the current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        console.error('Authentication error:', authError);
        throw new Error('Authentication required');
      }

      // Use the recipe service to fetch recipes
      const { data: recipes, error: recipesError } = await recipeService.getRecipes({
        userId: user.id,
        limit: 5
      });

      if (recipesError) {
        console.error('Error fetching recipes:', recipesError);
        throw new Error(recipesError);
      }

      if (recipes && recipes.length > 0) {
        // Transform recipes to match the expected format
        const alternatives = recipes.map(recipe => ({
          id: recipe.id,
          recipeId: recipe.id,
          name: recipe.name,
          type: currentMeal.type,
          prepTime: recipe.prep_time,
          cost: recipe.cost_per_serving || 5.00,
          nutrition: recipe.nutrition || {
            calories: 350,
            protein: 20,
            carbs: 30,
            fat: 15
          },
          image: recipe.image
        }));

        setAlternatives(alternatives);
      } else {
        // If no recipes found, use fallback data
        setFallbackMode(true);
        generateFallbackAlternatives();
      }
    } catch (error) {
      console.error("Error generating alternatives:", error);
      toast.error("Failed to fetch alternative meals");
      // Use fallback data if there's an error
      setFallbackMode(true);
      generateFallbackAlternatives();
    } finally {
      setIsLoading(false);
    }
  };

  // Fallback function to generate mock alternatives
  const generateFallbackAlternatives = () => {
    const mockAlternatives = [
      {
        id: "alt1",
        recipeId: "alt1",
        name: "Quinoa Bowl with Roasted Vegetables",
        type: currentMeal.type,
        prepTime: 25,
        cost: 4.50,
        nutrition: {
          calories: currentMeal.nutrition?.calories ? currentMeal.nutrition.calories - 50 : 350,
          protein: 22,
          carbs: 45,
          fat: 15
        },
        image: null
      },
      {
        id: "alt2",
        recipeId: "alt2",
        name: "Mediterranean Chickpea Salad",
        type: currentMeal.type,
        prepTime: 15,
        cost: 3.75,
        nutrition: {
          calories: currentMeal.nutrition?.calories ? currentMeal.nutrition.calories - 100 : 300,
          protein: 18,
          carbs: 35,
          fat: 12
        },
        image: null
      },
      {
        id: "alt3",
        recipeId: "alt3",
        name: "Lentil and Vegetable Soup",
        type: currentMeal.type,
        prepTime: 30,
        cost: 3.25,
        nutrition: {
          calories: currentMeal.nutrition?.calories ? currentMeal.nutrition.calories - 75 : 325,
          protein: 20,
          carbs: 40,
          fat: 10
        },
        image: null
      }
    ];

    setAlternatives(mockAlternatives);
  };

  // Function to handle swap confirmation
  const handleSwapConfirm = () => {
    if (selectedMeal) {
      onSwap(selectedMeal);
      onClose();
    }
  };

  // Determine meal type color
  const getTypeColor = (type: string) => {
    if (type?.toLowerCase().includes('breakfast')) return "bg-orange-500";
    if (type?.toLowerCase().includes('lunch')) return "bg-cyan-600";
    if (type?.toLowerCase().includes('dinner')) return "bg-indigo-600";
    if (type?.toLowerCase().includes('snack')) return "bg-amber-500";
    return "bg-gray-500";
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Swap Meal</DialogTitle>
          <DialogDescription>
            Find alternative meals that match your dietary preferences.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <h3 className="text-sm font-medium mb-2">Current Meal</h3>
          <Card className="mb-6">
            <div className="relative h-12 w-full">
              <div className={`absolute top-0 left-0 w-2 h-full ${getTypeColor(currentMeal.type)}`} />
              <Badge className={`absolute top-2 right-2 ${getTypeColor(currentMeal.type)}`}>
                {currentMeal.type}
              </Badge>
            </div>
            <CardHeader className="py-3 px-4">
              <CardTitle className="text-base">{currentMeal.name}</CardTitle>
            </CardHeader>
            <CardContent className="py-0 px-4">
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center">
                  <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
                  <span>{currentMeal.prepTime || currentMeal.prep_time || 30} min</span>
                </div>
                <div className="flex items-center">
                  <DollarSign className="h-3 w-3 mr-1 text-muted-foreground" />
                  <span>${currentMeal.cost?.toFixed(2) || currentMeal.cost_per_serving?.toFixed(2) || '0.00'}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-muted-foreground mr-1">Calories:</span>
                  <span>{currentMeal.nutrition?.calories || 'N/A'} kcal</span>
                </div>
                <div className="flex items-center">
                  <span className="text-muted-foreground mr-1">Protein:</span>
                  <span>{currentMeal.nutrition?.protein || 'N/A'}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {alternatives.length === 0 ? (
            <div className="text-center py-8">
              <Utensils className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Alternatives Yet</h3>
              <p className="text-muted-foreground mb-4">
                Generate alternatives based on your dietary preferences.
              </p>
              <Button
                onClick={generateAlternatives}
                disabled={isLoading}
                className="mx-auto"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Generate Alternatives
                  </>
                )}
              </Button>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-sm font-medium">Alternative Meals</h3>
                  {fallbackMode && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Using sample alternatives. Add more recipes to see real alternatives.
                    </p>
                  )}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={generateAlternatives}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="h-4 w-4" />
                  )}
                  <span className="ml-2">Refresh</span>
                </Button>
              </div>
              <div className="space-y-4">
                {alternatives.map((meal) => (
                  <Card
                    key={meal.id}
                    className={`overflow-hidden cursor-pointer transition-all ${selectedMeal?.id === meal.id ? 'ring-2 ring-primary' : 'hover:bg-accent/50'}`}
                    onClick={() => setSelectedMeal(meal)}
                  >
                    <div className="flex items-start">
                      <div className={`w-2 h-full ${getTypeColor(meal.type)}`} />
                      <div className="flex-1 p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium text-sm">{meal.name}</h4>
                            <div className="grid grid-cols-2 gap-x-4 gap-y-1 mt-2 text-xs">
                              <div className="flex items-center">
                                <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
                                <span>{meal.prepTime || 30} min</span>
                              </div>
                              <div className="flex items-center">
                                <DollarSign className="h-3 w-3 mr-1 text-muted-foreground" />
                                <span>${typeof meal.cost === 'number' ? meal.cost.toFixed(2) : '0.00'}</span>
                              </div>
                              <div className="flex items-center">
                                <span className="text-muted-foreground mr-1">Calories:</span>
                                <span>{meal.nutrition?.calories || 'N/A'} kcal</span>
                              </div>
                              <div className="flex items-center">
                                <span className="text-muted-foreground mr-1">Protein:</span>
                                <span>{meal.nutrition?.protein || 'N/A'}{typeof meal.nutrition?.protein === 'number' ? 'g' : ''}</span>
                              </div>
                            </div>
                          </div>
                          {selectedMeal?.id === meal.id && (
                            <div className="h-6 w-6 rounded-full bg-primary flex items-center justify-center">
                              <Check className="h-4 w-4 text-primary-foreground" />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button
            onClick={handleSwapConfirm}
            disabled={!selectedMeal}
          >
            Swap Meal
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
