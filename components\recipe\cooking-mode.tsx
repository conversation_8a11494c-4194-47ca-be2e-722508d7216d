'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ChevronLeft, ChevronRight, X, Clock, Volume2, VolumeX } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

interface CookingModeProps {
  instructions: string[];
  prepTime: number;
  cookTime: number;
  onClose: () => void;
}

export function CookingMode({
  instructions,
  prepTime,
  cookTime,
  onClose
}: CookingModeProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [timer, setTimer] = useState<number | null>(null);
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [isSpeechEnabled, setIsSpeechEnabled] = useState(false);
  
  // Calculate progress percentage
  const progress = ((currentStep + 1) / instructions.length) * 100;
  
  // Navigate to previous step
  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
      // Cancel any active timer when changing steps
      if (timer) {
        clearInterval(timer);
        setTimer(null);
        setTimeRemaining(null);
      }
    }
  };
  
  // Navigate to next step
  const nextStep = () => {
    if (currentStep < instructions.length - 1) {
      setCurrentStep(prev => prev + 1);
      // Cancel any active timer when changing steps
      if (timer) {
        clearInterval(timer);
        setTimer(null);
        setTimeRemaining(null);
      }
    }
  };
  
  // Start a timer for the current step
  const startTimer = (minutes: number) => {
    // Convert minutes to seconds
    const seconds = minutes * 60;
    setTimeRemaining(seconds);
    
    // Clear any existing timer
    if (timer) {
      clearInterval(timer);
    }
    
    // Create a new timer
    const newTimer = window.setInterval(() => {
      setTimeRemaining(prev => {
        if (prev === null || prev <= 1) {
          clearInterval(newTimer);
          // Play a sound when timer completes
          const audio = new Audio('/sounds/timer-complete.mp3');
          audio.play().catch(e => console.log('Audio play failed:', e));
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    
    setTimer(newTimer);
  };
  
  // Format seconds as MM:SS
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Toggle speech synthesis
  const toggleSpeech = () => {
    setIsSpeechEnabled(prev => !prev);
  };
  
  // Read the current step aloud when it changes or speech is enabled
  useEffect(() => {
    if (isSpeechEnabled && 'speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(instructions[currentStep]);
      speechSynthesis.speak(utterance);
    }
    
    // Cleanup function to cancel speech when component unmounts
    return () => {
      if ('speechSynthesis' in window) {
        speechSynthesis.cancel();
      }
    };
  }, [currentStep, isSpeechEnabled, instructions]);
  
  // Cleanup timer when component unmounts
  useEffect(() => {
    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [timer]);
  
  // Extract timer values from instruction text
  const extractTimerValue = (instruction: string): number | null => {
    // Look for patterns like "cook for 5 minutes" or "bake for 10-15 minutes"
    const timePattern = /(\d+)(?:-\d+)?\s*(?:minute|min)/i;
    const match = instruction.match(timePattern);
    return match ? parseInt(match[1], 10) : null;
  };
  
  // Check if the current instruction has a timer value
  const currentTimerValue = extractTimerValue(instructions[currentStep]);
  
  return (
    <div className="fixed inset-0 bg-background z-50 flex flex-col">
      <div className="flex items-center justify-between p-4 border-b">
        <h2 className="text-xl font-bold">Cooking Mode</h2>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSpeech}
            title={isSpeechEnabled ? "Disable voice instructions" : "Enable voice instructions"}
          >
            {isSpeechEnabled ? <Volume2 className="h-5 w-5" /> : <VolumeX className="h-5 w-5" />}
          </Button>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-5 w-5" />
          </Button>
        </div>
      </div>
      
      <div className="flex-1 overflow-auto p-4">
        <Progress value={progress} className="mb-6" />
        
        <div className="text-sm text-muted-foreground mb-2">
          Step {currentStep + 1} of {instructions.length}
        </div>
        
        <Card className="mb-6">
          <CardContent className="p-6">
            <p className="text-xl">{instructions[currentStep]}</p>
            
            {timeRemaining !== null && (
              <div className="mt-4 p-3 bg-primary/10 rounded-md flex items-center">
                <Clock className="h-5 w-5 mr-2 text-primary" />
                <span className="font-mono text-lg">{formatTime(timeRemaining)}</span>
              </div>
            )}
            
            {currentTimerValue && timeRemaining === null && (
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => startTimer(currentTimerValue)}
              >
                <Clock className="h-4 w-4 mr-2" />
                Start {currentTimerValue} minute timer
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
      
      <div className="p-4 border-t flex justify-between">
        <Button
          variant="outline"
          onClick={prevStep}
          disabled={currentStep === 0}
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        
        <Button
          variant={currentStep === instructions.length - 1 ? "default" : "primary"}
          onClick={currentStep === instructions.length - 1 ? onClose : nextStep}
        >
          {currentStep === instructions.length - 1 ? (
            "Finish"
          ) : (
            <>
              Next
              <ChevronRight className="h-4 w-4 ml-2" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
