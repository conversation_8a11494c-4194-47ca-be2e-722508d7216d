"use client";

import { useEffect, useState } from 'react';
import { useMealPlanner } from '@/app/context/MealPlannerContext';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Bell, ChevronDown, Heart, Plus, Star, ArrowLeftRight } from 'lucide-react';
import { toast } from "sonner";
import type { Meal, DayPlan } from '@/app/types/mealPlanner';
import { MealCustomizer } from '@/components/meal/MealCustomizer';
import { UserNav } from "./UserNav";

export default function MealPlannerDashboard() {
  const {
    todaysMeals,
    weeklyPlan,
    alternativeMeals,
    isLoading,
    error,
    loadTodaysMeals,
    loadWeeklyPlan,
    loadAlternativeMeals,
    swapMeal,
    toggleFavorite,
    groceryList: { categories, isLoading: isGroceryListLoading, error: groceryListError },
    loadGroceryList,
    toggleGroceryItem,
    customizeMeal,
  } = useMealPlanner();

  const [selectedMeal, setSelectedMeal] = useState<Meal | null>(null);
  const [showMealDetail, setShowMealDetail] = useState(false);
  const [showSwapModal, setShowSwapModal] = useState(false);
  const [notifications, setNotifications] = useState(2); // Example notification count
  const [showCustomizer, setShowCustomizer] = useState(false);

  // Budget progress data
  const budgetData = {
    spent: 2000,
    total: 3000,
    progress: (2000 / 3000) * 100
  };

  useEffect(() => {
    const loadData = async () => {
      try {
        await loadTodaysMeals();
        await loadWeeklyPlan();
        await loadGroceryList();
      } catch (error) {
        console.error('[Component Error] Loading data:', error);
        toast.error('Failed to load meal plan data');
      }
    };

    loadData();
  }, [loadTodaysMeals, loadWeeklyPlan, loadGroceryList]);

  const handleSwapMeal = async (oldMealId: string) => {
    try {
      if (selectedMeal) {
        await loadAlternativeMeals(selectedMeal.type, oldMealId);
        setShowSwapModal(true);
      }
    } catch (error) {
      console.error('Failed to load alternative meals:', error);
      toast.error('Failed to load alternative meals');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#4CAF50]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500">Error: {error}</div>
      </div>
    );
  }

  const mealActionButtons = (meal: Meal) => (
    <div className="flex items-center gap-2 mt-1">
      <Button
        size="sm"
        variant="outline"
        onClick={(e) => {
          e.stopPropagation();
          setSelectedMeal(meal);
          setShowCustomizer(true);
        }}
      >
        Customize
      </Button>
      <Button
        size="sm"
        variant="outline"
        onClick={() => handleSwapMeal(meal.id)}
      >
        <ArrowLeftRight className="h-4 w-4 mr-1" />
        Swap
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={() => toggleFavorite(meal)}
      >
        <Heart
          className={`h-5 w-5 ${
            meal.isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-400'
          }`}
        />
      </Button>
    </div>
  );

  return (
    <div className="container mx-auto p-4">
      {/* Header with notifications */}
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Meal Planner Dashboard</h1>
        <div className="flex items-center gap-4">
          <Button variant="outline" className="relative">
            <Bell className="h-5 w-5" />
            {notifications > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 text-xs flex items-center justify-center">
                {notifications}
              </span>
            )}
          </Button>
          <UserNav />
          <Button variant="default">Generate New Plan</Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Today's Meals Section */}
        <div className="md:col-span-2">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>Today's Meals</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                {todaysMeals.map((meal) => (
                  <div
                    key={meal.id}
                    className="group relative rounded-lg border p-4 hover:shadow-lg transition-shadow cursor-pointer"
                    onClick={() => {
                      setSelectedMeal(meal);
                      setShowMealDetail(true);
                    }}
                  >
                    <img
                      src={meal.image}
                      alt={meal.name}
                      className="w-full h-32 object-cover rounded-md"
                    />
                    <div className="mt-2">
                      <h3 className="font-semibold">{meal.name}</h3>
                      <p className="text-sm text-muted-foreground">{meal.type}</p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-sm">{meal.calories} cal</span>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleFavorite(meal);
                          }}
                          className="opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <Heart
                            className={`h-5 w-5 ${meal.isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-400'}`}
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Budget Progress */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>Budget Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Progress value={budgetData.progress} className="h-2" />
              <div className="flex justify-between text-sm">
                <span>Spent: ₹{budgetData.spent}</span>
                <span>Total: ₹{budgetData.total}</span>
              </div>
              <div className={`text-sm ${budgetData.progress > 80 ? 'text-red-500' : 'text-green-500'}`}>
                {budgetData.progress > 80 ? 'Near budget limit!' : 'Within budget'}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Weekly Meal Plan */}
        <div className="md:col-span-3">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>Weekly Meal Plan</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="w-full">
                <div className="flex space-x-4 p-4">
                  {weeklyPlan.map((day) => (
                    <div key={day.date} className="w-[280px] shrink-0">
                      <h3 className="font-semibold mb-2">{day.date}</h3>
                      <div className="space-y-3">
                        {day.meals.map((meal) => (
                          <div key={meal.id} className="relative rounded-lg border p-3">
                            <div className="flex gap-3">
                              <img
                                src={meal.image}
                                alt={meal.name}
                                className="w-20 h-20 object-cover rounded"
                              />
                              <div className="flex-1">
                                <h4 className="font-medium">{meal.name}</h4>
                                <p className="text-sm text-muted-foreground">{meal.type}</p>
                                <div className="flex items-center gap-2 mt-1">
                                  {mealActionButtons(meal)}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        {/* Grocery List Preview */}
        <div className="md:col-span-2">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>Grocery List</CardTitle>
            </CardHeader>
            <CardContent>
              {isGroceryListLoading ? (
                <div className="flex justify-center p-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : groceryListError ? (
                <div className="text-red-500 p-4">{groceryListError}</div>
              ) : (
                <div className="space-y-4">
                  {categories.map((category) => (
                    <Collapsible key={category.id}>
                      <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-accent rounded-md">
                        <span className="font-medium">
                          {category.name} ({category.items.length})
                        </span>
                        <ChevronDown className="h-4 w-4" />
                      </CollapsibleTrigger>
                      <CollapsibleContent className="space-y-2 mt-2">
                        {category.items.map((item) => (
                          <div
                            key={item.id}
                            className="flex items-center gap-2 px-4 py-2 hover:bg-accent/50 rounded-md"
                          >
                            <input
                              type="checkbox"
                              checked={item.checked}
                              onChange={() => toggleGroceryItem(category.id, item.id, !item.checked)}
                              className="rounded"
                            />
                            <span className={item.checked ? 'line-through text-muted-foreground' : ''}>
                              {item.name}
                            </span>
                          </div>
                        ))}
                      </CollapsibleContent>
                    </Collapsible>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <Plus className="h-4 w-4 mr-2" />
                Add Custom Meal
              </Button>
              <Button variant="outline" className="w-full justify-start">
                Adjust Preferences
              </Button>
              <Button variant="outline" className="w-full justify-start">
                View Past Plans
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Meal Detail Modal */}
      <Dialog open={showMealDetail} onOpenChange={setShowMealDetail}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedMeal?.name}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {selectedMeal && (
              <>
                <img
                  src={selectedMeal.image}
                  alt={selectedMeal.name}
                  className="w-full h-48 object-cover rounded-lg"
                />
                <div className="space-y-2">
                  <p>Calories: {selectedMeal.calories}</p>
                  <p>Prep Time: {selectedMeal.prepTime} minutes</p>
                  <div className="flex items-center gap-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`h-5 w-5 ${
                          star <= (selectedMeal.rating || 0)
                            ? 'fill-yellow-400 text-yellow-400'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Swap Meal Modal */}
      <Dialog open={showSwapModal} onOpenChange={setShowSwapModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Choose Alternative Meal</DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-4">
            {alternativeMeals.map((meal) => (
              <Card key={meal.id} className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <img
                    src={meal.image}
                    alt={meal.name}
                    className="w-full h-32 object-cover rounded mb-2"
                  />
                  <h4 className="font-medium">{meal.name}</h4>
                  <p className="text-sm text-muted-foreground">{meal.calories} calories</p>
                  <Button
                    className="w-full mt-2"
                    onClick={() => {
                      if (selectedMeal) {
                        swapMeal(selectedMeal.id, meal.id);
                        setShowSwapModal(false);
                      }
                    }}
                  >
                    Select
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </DialogContent>
      </Dialog>

      {showCustomizer && selectedMeal && (
        <MealCustomizer
          meal={selectedMeal}
          isOpen={showCustomizer}
          onClose={() => setShowCustomizer(false)}
          onSave={(updatedMeal) => {
            customizeMeal(updatedMeal.id, updatedMeal.customization!);
          }}
        />
      )}
    </div>
  );
}

// Mock data with more comprehensive items
const todaysMeals = [
  {
    id: "1",
    type: "Breakfast",
    name: "Avocado Toast",
    image: "https://images.unsplash.com/photo-1528505086635-4c69d5f10908",
    calories: 350,
    prepTime: 15,
    rating: 4,
    isFavorite: true
  },
  {
    id: "2",
    type: "Lunch",
    name: "Quinoa Buddha Bowl",
    image: "https://images.unsplash.com/photo-1546069901-ba9599a7e63c",
    calories: 450,
    prepTime: 25,
    rating: 5,
    isFavorite: false
  },
  {
    id: "3",
    type: "Dinner",
    name: "Grilled Salmon",
    image: "https://images.unsplash.com/photo-1467003909585-2f8a72700288",
    calories: 550,
    prepTime: 30,
    rating: 4,
    isFavorite: true
  },
  {
    id: "4",
    type: "Snack",
    name: "Greek Yogurt Parfait",
    image: "https://images.unsplash.com/photo-1511690656952-34342bb7c2f2",
    calories: 200,
    prepTime: 5,
    rating: 3,
    isFavorite: false
  }
];

const weeklyPlan = [
  {
    date: "Monday",
    meals: [
      {
        id: "m1",
        type: "Breakfast",
        name: "Overnight Oats",
        image: "https://images.unsplash.com/photo-1517673132405-a56a62b18caf",
        calories: 300,
        prepTime: 10
      },
      {
        id: "m2",
        type: "Lunch",
        name: "Mediterranean Salad",
        image: "https://images.unsplash.com/photo-1540420773420-3366772f4999",
        calories: 400,
        prepTime: 15
      },
      {
        id: "m3",
        type: "Dinner",
        name: "Chicken Stir-Fry",
        image: "https://images.unsplash.com/photo-1512058564366-18510be2db19",
        calories: 550,
        prepTime: 30
      }
    ]
  },
  {
    date: "Tuesday",
    meals: [
      {
        id: "t1",
        type: "Breakfast",
        name: "Smoothie Bowl",
        image: "https://images.unsplash.com/photo-1511690743698-d9d85f2fbf38",
        calories: 320,
        prepTime: 12
      },
      {
        id: "t2",
        type: "Lunch",
        name: "Poke Bowl",
        image: "https://images.unsplash.com/photo-1546069901-ba9599a7e63c",
        calories: 450,
        prepTime: 20
      },
      {
        id: "t3",
        type: "Dinner",
        name: "Vegetable Lasagna",
        image: "https://images.unsplash.com/photo-1574894709920-11b28e7367e3",
        calories: 600,
        prepTime: 45
      }
    ]
  }
  // Add more days as needed
];

const alternativeMeals = [
  {
    id: "alt1",
    name: "Quinoa Bowl",
    image: "https://images.unsplash.com/photo-1546069901-ba9599a7e63c",
    calories: 400,
    prepTime: 20
  },
  {
    id: "alt2",
    name: "Chicken Caesar Salad",
    image: "https://images.unsplash.com/photo-1550304943-4f24f54ddde9",
    calories: 380,
    prepTime: 15
  },
  {
    id: "alt3",
    name: "Veggie Wrap",
    image: "https://images.unsplash.com/photo-1528735602780-2552fd46c7af",
    calories: 320,
    prepTime: 10
  },
  {
    id: "alt4",
    name: "Mushroom Risotto",
    image: "https://images.unsplash.com/photo-1476124369491-e7addf5db371",
    calories: 550,
    prepTime: 35
  }
];











