interface DailySummaryProps {
  calories: number;
  cost: number;
  macros: {
    protein: number;
    carbs: number;
    fats: number;
  };
}

export function DailySummary({ calories, cost, macros }: DailySummaryProps) {
  return (
    <div className="border-t pt-4 mt-4">
      <div className="flex justify-between items-center">
        <div>
          <p className="text-sm font-medium">Daily Summary</p>
          <p className="text-sm text-muted-foreground">
            {calories} kcal • ${cost}
          </p>
        </div>
        <div className="text-sm text-muted-foreground">
          <p>Protein: {macros.protein}g</p>
          <p>Carbs: {macros.carbs}g</p>
          <p>Fats: {macros.fats}g</p>
        </div>
      </div>
    </div>
  );
}