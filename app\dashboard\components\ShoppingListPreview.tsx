'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { useMealPlanner } from "@/app/context/MealPlannerContext";
import { useEffect } from "react";

export function ShoppingListPreview() {
  const { groceryList, loadGroceryList } = useMealPlanner();

  useEffect(() => {
    loadGroceryList();
  }, [loadGroceryList]);

  if (groceryList.isLoading) {
    return <div>Loading shopping list...</div>;
  }

  if (groceryList.error) {
    return <div>Error loading shopping list</div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Shopping List</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {groceryList.categories.slice(0, 3).map((category) => (
            <div key={category.id}>
              <h3 className="font-medium mb-2">{category.name}</h3>
              <div className="space-y-2">
                {category.items.slice(0, 3).map((item) => (
                  <div key={item.id} className="flex items-center space-x-2">
                    <Checkbox id={item.id} />
                    <label htmlFor={item.id}>{item.name}</label>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}



