# Supabase Directory

This directory contains all Supabase-related files for the LeanEats application.

## Directory Structure

- `migrations/`: Database migration scripts
- `manual_sql/`: SQL scripts for manual execution
- `functions/`: SQL functions and procedures
- `seed/`: Seed data for development and testing

## Migrations

The `migrations/` directory contains SQL scripts that modify the database schema. Each migration script is named with a timestamp prefix to ensure they are applied in the correct order.

### Creating a New Migration

1. Use the `create_migration.js` script to generate a new migration file:

```bash
node scripts/create_migration.js "Description of migration"
```

2. Edit the generated migration file to add your SQL statements.

3. Run the migration using the `run_migrations.js` script:

```bash
node scripts/run_migrations.js
```

### Migration Template

A template for migrations is available at `migrations/TEMPLATE_MIGRATION.sql`. Use this template as a starting point for new migrations.

## Manual SQL

The `manual_sql/` directory contains SQL scripts that are meant to be run manually in the Supabase SQL Editor. These scripts are typically used for one-time operations or for operations that are not part of the regular migration process.

## Functions

The `functions/` directory contains SQL functions and procedures that are used by the application. These functions are typically created as part of migrations, but they are also stored separately for reference.

## Seed Data

The `seed/` directory contains SQL scripts that populate the database with seed data for development and testing. These scripts are typically run manually after setting up a new development environment.

## Documentation

For comprehensive documentation of the Supabase database, see:

- [Supabase Documentation](../docs/SUPABASE_DOCUMENTATION.md): Complete reference for all Supabase-related components
- [Supabase Checklist](../docs/SUPABASE_CHECKLIST.md): Checklist for making changes to the Supabase database
