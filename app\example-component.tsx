'use client';

import { useSupabase } from '@/components/supabase-provider';
import { useEffect, useState } from 'react';

export default function ExampleComponent() {
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Only proceed if Supabase is loaded and available
    if (isSupabaseLoading || !supabase) {
      return;
    }

    console.log('Supabase client initialized and ready');
    setIsLoading(false);
  }, [supabase, isSupabaseLoading]);

  const handleSomeAction = async () => {
    if (!supabase) return;

    const { data, error } = await supabase
      .from('your_table')
      .select('*');
    // Handle data and error
  };

  // Show loading state if either Supabase is loading or our component is loading
  if (isSupabaseLoading || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        <p className="ml-2 text-muted-foreground">
          {isSupabaseLoading ? 'Initializing Supabase...' : 'Loading data...'}
        </p>
      </div>
    );
  }

  return (
    <div className="p-4 border rounded-md">
      <h2 className="text-lg font-medium mb-2">Example Component</h2>
      <p>Supabase is initialized and ready to use!</p>
      <button
        className="mt-4 px-4 py-2 bg-primary text-white rounded-md"
        onClick={handleSomeAction}
      >
        Fetch Data
      </button>
    </div>
  );
}
