-- Supabase Diagnostic Script
-- This script helps to identify areas for improvement in your Supabase database schema,
-- focusing on RLS policies, user_id consistency, and table structures.

-- Helper function to check if a column exists in a table
CREATE OR REPLACE FUNCTION column_exists(p_schema_name TEXT, p_table_name TEXT, p_column_name TEXT) 
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_schema = p_schema_name 
    AND table_name = p_table_name 
    AND column_name = p_column_name
  );
END;
$$ LANGUAGE plpgsql;

DO $$
DECLARE
  r RECORD;
  table_name_text TEXT;
  policy_count INTEGER;
  user_id_column_exists BOOLEAN;
  rls_enabled BOOLEAN;
  table_count INTEGER;
  policy_total INTEGER;
  user_id_column_count INTEGER;
BEGIN
  RAISE NOTICE 'Helper function column_exists created/updated.';
  RAISE NOTICE 'Starting Supabase Diagnostic Script...';
  RAISE NOTICE '\n=== Database Overview ===';
  
  -- Get initial counts
  SELECT COUNT(*) INTO table_count
  FROM pg_class c
  JOIN pg_namespace ns ON ns.oid = c.relnamespace
  WHERE c.relkind = 'r' 
  AND ns.nspname NOT IN ('pg_catalog', 'information_schema', 'storage', 'graphql', 'graphql_public', 'pgsodium', 'pgsodium_masks', 'realtime', 'vault');
  
  SELECT COUNT(*) INTO policy_total
  FROM pg_policies
  WHERE schemaname NOT IN ('pg_catalog', 'information_schema', 'storage', 'graphql', 'graphql_public', 'pgsodium', 'pgsodium_masks', 'realtime', 'vault');
  
  SELECT COUNT(*) INTO user_id_column_count
  FROM information_schema.columns c
  JOIN information_schema.tables t ON t.table_schema = c.table_schema AND t.table_name = c.table_name
  WHERE c.column_name ILIKE '%user_id%'
  AND t.table_type = 'BASE TABLE'
  AND c.table_schema NOT IN ('pg_catalog', 'information_schema', 'storage', 'graphql', 'graphql_public', 'pgsodium', 'pgsodium_masks', 'realtime', 'vault');
  
  RAISE NOTICE 'Total Tables: %', table_count;
  RAISE NOTICE 'Total RLS Policies: %', policy_total;
  RAISE NOTICE 'Tables with user_id columns: %', user_id_column_count;
  RAISE NOTICE '';


  -- Section 1: RLS Policy Checks
  RAISE NOTICE '--- Section 1: RLS Policy Checks ---';
  RAISE NOTICE 'Scanning tables for RLS configuration...';

  -- Initialize counters for RLS status
  DECLARE
    tables_with_rls INTEGER := 0;
    tables_with_forced_rls INTEGER := 0;
    tables_without_rls INTEGER := 0;
  BEGIN

  FOR r IN 
    SELECT c.relname AS table_name, ns.nspname AS schema_name, c.relrowsecurity AS rls_enabled_bool, c.relforcerowsecurity AS rls_forced_bool
    FROM pg_class c
    JOIN pg_namespace ns ON ns.oid = c.relnamespace
    WHERE c.relkind = 'r' AND ns.nspname NOT IN ('pg_catalog', 'information_schema', 'storage', 'graphql', 'graphql_public', 'pgsodium', 'pgsodium_masks', 'realtime', 'vault')
    ORDER BY ns.nspname, c.relname
  LOOP
    table_name_text := r.schema_name || '.' || r.table_name;
    rls_enabled := r.rls_enabled_bool;

    RAISE NOTICE 'Checking RLS status for table: %', table_name_text;
    IF rls_enabled THEN
      tables_with_rls := tables_with_rls + 1;
      RAISE NOTICE '  RLS is ENABLED for %', table_name_text;
      IF r.rls_forced_bool THEN
        tables_with_forced_rls := tables_with_forced_rls + 1;
        RAISE NOTICE '  RLS is FORCED for % (recommended)', table_name_text;
      ELSE
        RAISE NOTICE '  RLS is NOT FORCED for % (consider enabling force RLS)', table_name_text;
      END IF;
    ELSE
      tables_without_rls := tables_without_rls + 1;
      RAISE NOTICE '  RLS is DISABLED for % (CRITICAL: Enable RLS for all user data tables)', table_name_text;
    END IF;

    -- Count policies for the table
    SELECT count(*) INTO policy_count FROM pg_policies WHERE schemaname = r.schema_name AND tablename = r.table_name;
    RAISE NOTICE '  Table % has % RLS policies defined.', table_name_text, policy_count;
    IF rls_enabled AND policy_count = 0 THEN
        RAISE NOTICE '  WARNING: RLS is enabled for % but NO policies are defined. This means NO access is granted.', table_name_text;
    END IF;
    RAISE NOTICE 'Finished RLS status check for table: %', table_name_text;
  END LOOP;

  -- Output RLS summary statistics
  RAISE NOTICE '';
  RAISE NOTICE 'RLS Configuration Summary:';
  RAISE NOTICE '  Tables with RLS enabled: %', tables_with_rls;
  RAISE NOTICE '  Tables with forced RLS: %', tables_with_forced_rls;
  RAISE NOTICE '  Tables without RLS: %', tables_without_rls;
  END;
  RAISE NOTICE '--- Finished Section 1: RLS Policy Checks ---';
END;

  -- Section 2: Detailed RLS Policy Listing
  RAISE NOTICE '--- Section 2: Detailed RLS Policy Listing ---';
  RAISE NOTICE 'Scanning RLS policies...';

  DECLARE
    policy_found BOOLEAN := FALSE;
  BEGIN
    FOR r IN 
      SELECT policyname, schemaname, tablename, cmd, qual, with_check 
      FROM pg_policies 
      WHERE schemaname NOT IN ('pg_catalog', 'information_schema', 'storage', 'graphql', 'graphql_public', 'pgsodium', 'pgsodium_masks', 'realtime', 'vault')
      ORDER BY schemaname, tablename, policyname
    LOOP
      policy_found := TRUE;
      RAISE NOTICE 'Policy: %, Table: %.%, Command: %, Definition: %, With Check: %', 
                   r.policyname, r.schemaname, r.tablename, r.cmd, r.qual, r.with_check;
    END LOOP;

    IF NOT policy_found THEN
      RAISE NOTICE 'No RLS policies found in the database.';
    END IF;
  END;
  RAISE NOTICE '--- Finished Section 2: Detailed RLS Policy Listing ---';

  -- Section 3: user_id Column and Data Type Consistency
  RAISE NOTICE '--- Section 3: user_id Column and Data Type Consistency ---';
  RAISE NOTICE 'Scanning for user_id columns and checking data types...';

  DECLARE
    columns_found BOOLEAN := FALSE;
    uuid_columns INTEGER := 0;
    text_columns INTEGER := 0;
    other_type_columns INTEGER := 0;
  BEGIN
    FOR r IN 
      SELECT c.table_schema, c.table_name, c.column_name, c.data_type
      FROM information_schema.columns c
      JOIN information_schema.tables t ON t.table_schema = c.table_schema AND t.table_name = c.table_name
      WHERE c.column_name ILIKE '%user_id%' 
        AND t.table_type = 'BASE TABLE'
        AND c.table_schema NOT IN ('pg_catalog', 'information_schema', 'storage', 'graphql', 'graphql_public', 'pgsodium', 'pgsodium_masks', 'realtime', 'vault')
      ORDER BY c.table_schema, c.table_name, c.column_name
    LOOP
      columns_found := TRUE;
      RAISE NOTICE 'Table: %.%, Column: %, Data Type: %', r.table_schema, r.table_name, r.column_name, r.data_type;
      
      IF r.data_type = 'uuid' THEN
        uuid_columns := uuid_columns + 1;
      ELSIF r.data_type = 'text' THEN
        text_columns := text_columns + 1;
        RAISE NOTICE '  NOTE: Column %.%.% uses text type - verify if UUID would be more appropriate', r.table_schema, r.table_name, r.column_name;
      ELSE
        other_type_columns := other_type_columns + 1;
        RAISE NOTICE '  WARNING: Column %.%.% has data type %. Expected uuid (or text for specific cases, review carefully).', r.table_schema, r.table_name, r.column_name, r.data_type;
      END IF;
    END LOOP;

    IF NOT columns_found THEN
      RAISE NOTICE 'No user_id columns found in the database.';
    ELSE
      RAISE NOTICE '';
      RAISE NOTICE 'User ID Column Type Summary:';
      RAISE NOTICE '  UUID type columns: %', uuid_columns;
      RAISE NOTICE '  Text type columns: %', text_columns;
      RAISE NOTICE '  Other type columns: %', other_type_columns;
      IF other_type_columns > 0 THEN
        RAISE NOTICE '  WARNING: Found % column(s) with non-standard types that should be reviewed.', other_type_columns;
      END IF;
    END IF;
  END;
  RAISE NOTICE '--- Finished Section 3: user_id Column and Data Type Consistency ---';

  -- Section 4: Tables without user_id (potential candidates for RLS or public tables)
  RAISE NOTICE '--- Section 4: Tables without user_id ---';
  FOR r IN 
    SELECT t.table_schema, t.table_name
    FROM information_schema.tables t
    WHERE t.table_type = 'BASE TABLE'
      AND t.table_schema NOT IN ('pg_catalog', 'information_schema', 'storage', 'graphql', 'graphql_public', 'pgsodium', 'pgsodium_masks', 'realtime', 'vault')
      AND NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns c 
        WHERE c.table_schema = t.table_schema 
        AND c.table_name = t.table_name 
        AND c.column_name ILIKE '%user_id%'
      )
    ORDER BY t.table_schema, t.table_name
  LOOP
    RAISE NOTICE 'Table %.% does not have a user_id column. Review if this is intentional (e.g., public data) or if RLS based on user_id is needed.', r.table_schema, r.table_name;
  END LOOP;
  RAISE NOTICE '--- Finished Section 4: Tables without user_id ---';

  -- Section 5: Check for auth.uid() usage and type casting in RLS policies
  RAISE NOTICE '--- Section 5: RLS Policy auth.uid() Usage and Casting ---';
  FOR r IN 
    SELECT policyname, schemaname, tablename, cmd, qual, with_check 
    FROM pg_policies 
    WHERE schemaname NOT IN ('pg_catalog', 'information_schema', 'storage', 'graphql', 'graphql_public', 'pgsodium', 'pgsodium_masks', 'realtime', 'vault')
      AND (qual LIKE '%auth.uid()%' OR with_check LIKE '%auth.uid()%')
    ORDER BY schemaname, tablename, policyname
  LOOP
    RAISE NOTICE 'Policy: %, Table: %.%, Command: % uses auth.uid().', 
                 r.policyname, r.schemaname, r.tablename, r.cmd;
    IF (r.qual IS NOT NULL AND r.qual LIKE '%auth.uid()%' AND r.qual NOT LIKE '%auth.uid()::text%' AND r.qual NOT LIKE '%auth.uid()::uuid%') OR 
       (r.with_check IS NOT NULL AND r.with_check LIKE '%auth.uid()%' AND r.with_check NOT LIKE '%auth.uid()::text%' AND r.with_check NOT LIKE '%auth.uid()::uuid%') THEN
      RAISE NOTICE '  WARNING: Policy % for table %.% uses auth.uid() without explicit ::text or ::uuid casting. Review for potential type mismatches.', r.policyname, r.schemaname, r.tablename;
      RAISE NOTICE '    USING: %', r.qual;
      RAISE NOTICE '    WITH CHECK: %', r.with_check;
    ELSE
       RAISE NOTICE '  Policy % for table %.% appears to use auth.uid() with casting or does not require it based on context.', r.policyname, r.schemaname, r.tablename;
       RAISE NOTICE '    USING: %', r.qual;
       RAISE NOTICE '    WITH CHECK: %', r.with_check;
    END IF;
  END LOOP;
  RAISE NOTICE '--- Finished Section 5: RLS Policy auth.uid() Usage and Casting ---';

  -- Section 6: Check for common RLS policy naming conventions (example: _select_user, _insert_user)
  RAISE NOTICE '--- Section 6: RLS Policy Naming Conventions ---';
  FOR r IN 
    SELECT policyname, schemaname, tablename, cmd
    FROM pg_policies 
    WHERE schemaname NOT IN ('pg_catalog', 'information_schema', 'storage', 'graphql', 'graphql_public', 'pgsodium', 'pgsodium_masks', 'realtime', 'vault')
    ORDER BY schemaname, tablename, policyname
  LOOP
    IF r.policyname NOT LIKE '%' || r.tablename || '%' OR 
       (r.cmd = 'SELECT' AND r.policyname NOT LIKE '%_select_%') OR 
       (r.cmd = 'INSERT' AND r.policyname NOT LIKE '%_insert_%') OR 
       (r.cmd = 'UPDATE' AND r.policyname NOT LIKE '%_update_%') OR 
       (r.cmd = 'DELETE' AND r.policyname NOT LIKE '%_delete_%') THEN
      RAISE NOTICE 'Policy naming review: %.%.% (Command: %) - Consider standardizing (e.g., <tablename>_<action>_<role/user>).', r.schemaname, r.tablename, r.policyname, r.cmd;
    END IF;
  END LOOP;
  RAISE NOTICE '--- Finished Section 6: RLS Policy Naming Conventions ---';

  RAISE NOTICE 'Supabase Diagnostic Script finished.';

  -- Clean up helper function if not needed elsewhere
  -- DROP FUNCTION IF EXISTS column_exists(TEXT, TEXT, TEXT);
  -- RAISE NOTICE 'Helper function column_exists dropped if it existed (commented out).';

  RAISE NOTICE 'Script execution complete. Review the notices above for diagnostics.';
END $$;