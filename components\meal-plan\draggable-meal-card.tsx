'use client';

import { useState } from 'react';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, Utensils } from 'lucide-react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { MealContextMenu } from './meal-context-menu';
import { toast } from 'sonner';

interface DraggableMealCardProps {
  meal: any;
  day: string;
  onSwap?: (fromDay: string, fromMealId: string, toDay: string, toMealId: string) => void;
  onEdit?: (mealId: string) => void;
  onDelete?: (mealId: string) => void;
  onAddToCalendar?: (mealId: string) => void;
  className?: string;
}

export function DraggableMealCard({
  meal,
  day,
  onSwap,
  onEdit,
  onDelete,
  onAddToCalendar,
  className
}: DraggableMealCardProps) {
  const [isDragging, setIsDragging] = useState(false);
  
  const handleDragStart = (e: React.DragEvent) => {
    setIsDragging(true);
    e.dataTransfer.setData('application/json', JSON.stringify({
      mealId: meal.id,
      day: day
    }));
    
    // Create a drag image
    const dragImage = new Image();
    dragImage.src = meal.image || 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c';
    e.dataTransfer.setDragImage(dragImage, 20, 20);
  };
  
  const handleDragEnd = () => {
    setIsDragging(false);
  };
  
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    
    try {
      const data = JSON.parse(e.dataTransfer.getData('application/json'));
      
      if (data.day && data.mealId && onSwap) {
        // Don't swap with itself
        if (data.day === day && data.mealId === meal.id) {
          return;
        }
        
        onSwap(data.day, data.mealId, day, meal.id);
      }
    } catch (error) {
      console.error('Error parsing drag data:', error);
    }
  };
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };
  
  return (
    <MealContextMenu
      onEdit={() => onEdit?.(meal.id)}
      onDelete={() => onDelete?.(meal.id)}
      onAddToCalendar={() => onAddToCalendar?.(meal.id)}
      onSwapMeal={() => toast.info('Drag and drop to swap meals')}
    >
      <Card 
        className={cn(
          'cursor-grab relative overflow-hidden transition-all duration-200',
          isDragging && 'opacity-50 scale-95',
          className
        )}
        draggable={true}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
      >
        {meal.image && (
          <div className="relative w-full h-32 overflow-hidden">
            <Image
              src={meal.image}
              alt={meal.name}
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
            <Badge className="absolute top-2 left-2 bg-primary">{meal.type}</Badge>
          </div>
        )}
        
        <CardContent className={cn(
          'p-4',
          meal.image ? 'pt-2' : 'pt-4'
        )}>
          <h3 className="font-semibold text-base line-clamp-1">{meal.name}</h3>
          
          <div className="flex items-center mt-2 text-sm text-muted-foreground">
            <Clock className="h-3.5 w-3.5 mr-1" />
            <span className="mr-3">{meal.prepTime + meal.cookTime} min</span>
            <Utensils className="h-3.5 w-3.5 mr-1" />
            <span>{meal.servings} servings</span>
          </div>
        </CardContent>
        
        <CardFooter className="p-4 pt-0 flex justify-between">
          <div className="flex items-center text-sm">
            <span className="font-medium">{meal.nutrition?.calories || '---'}</span>
            <span className="text-muted-foreground ml-1">cal</span>
          </div>
          
          {meal.tags && meal.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 justify-end">
              {meal.tags.slice(0, 2).map((tag: string, i: number) => (
                <Badge key={i} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </CardFooter>
      </Card>
    </MealContextMenu>
  );
}
