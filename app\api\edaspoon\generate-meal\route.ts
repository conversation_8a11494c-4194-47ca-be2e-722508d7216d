import { NextRequest, NextResponse } from 'next/server';
import { generateMealWithEdaSpoon } from '@/lib/meal-plan-generators/edaspoon-generator';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { mealType, calories, dietaryPreferences, excludeIngredients } = body;
    
    if (!mealType || !calories) {
      return NextResponse.json(
        { error: 'Missing required parameters: mealType and calories are required' },
        { status: 400 }
      );
    }
    
    const meal = await generateMealWithEdaSpoon({
      mealType,
      calories,
      dietaryPreferences: dietaryPreferences || [],
      excludeIngredients: excludeIngredients || []
    });
    
    return NextResponse.json(meal);
  } catch (error: any) {
    console.error('Error in EdaSpoon generate meal API route:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while generating the meal' },
      { status: 500 }
    );
  }
}
