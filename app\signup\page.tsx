"use client";

import { useState } from "react";
import { getSupabaseBrowserClient } from '@/lib/supabase-browser';
import { useRouter } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MultiSelect } from "@/components/ui/multi-select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";

const dietaryRestrictions = [
  { id: "vegan", label: "Vegan" },
  { id: "vegetarian", label: "Vegetarian" },
  { id: "gluten-free", label: "Gluten-Free" },
  { id: "dairy-free", label: "Dairy-Free" },
  { id: "nut-free", label: "Nut-Free" },
];

// Add error type
interface SignupError extends Error {
  message: string;
  status?: number;
}

// Constants for error messages and routes
const AUTH_ERRORS = {
  REQUIRED_FIELDS: "Email and password are required",
  SIGNUP_FAILED: "An error occurred during signup"
};

const AUTH_ROUTES = {
  CALLBACK: "/auth/callback",
  LOGIN: "/login"
};

export default function SignUp() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [weeklyBudget, setWeeklyBudget] = useState("");
  const [householdSize, setHouseholdSize] = useState("");
  const [selectedRestrictions, setSelectedRestrictions] = useState<string[]>([]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if passwords match
    if (password !== confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    // Check if Supabase is available
    if (isSupabaseLoading || !supabase) {
      toast.error("Authentication service is not available yet. Please try again.");
      return;
    }

    setIsLoading(true);

    try {
      if (!email || !password) {
        throw new Error(AUTH_ERRORS.REQUIRED_FIELDS);
      }

      const supabase = getSupabaseBrowserClient();
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}${AUTH_ROUTES.CALLBACK}`
        }
      });

      if (error) throw error;

      if (data?.user) {
        try {
          const { error: profileError } = await supabase
            .from('users')
            .upsert({
              id: data.user.id,
              email,
              weekly_budget: parseFloat(weeklyBudget) || 0,
              household_size: parseInt(householdSize) || 1,
              dietary_restrictions: selectedRestrictions,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (profileError) throw profileError;

          toast.success("Account created successfully! Please check your email to verify your account.");
          router.push(AUTH_ROUTES.LOGIN);
        } catch (profileError) {
          // Note: Cannot clean up auth user from client side for security reasons
          console.error('Profile creation failed:', profileError);
          throw profileError;
        }
      }
    } catch (error: unknown) {
      const err = error as SignupError;
      console.error('Signup error:', err);
      toast.error(err.message || AUTH_ERRORS.SIGNUP_FAILED);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">Create your account</CardTitle>
          <CardDescription className="text-center">
            Sign up for LeanEats to start planning your meals
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                placeholder="••••••••"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="weeklyBudget">Weekly Budget ($)</Label>
              <Input
                id="weeklyBudget"
                type="number"
                placeholder="50"
                value={weeklyBudget}
                onChange={(e) => setWeeklyBudget(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="householdSize">Household Size</Label>
              <Select value={householdSize} onValueChange={setHouseholdSize}>
                <SelectTrigger>
                  <SelectValue placeholder="Select household size" />
                </SelectTrigger>
                <SelectContent>
                  {[1, 2, 3, 4, 5, 6].map((size) => (
                    <SelectItem key={size} value={size.toString()}>
                      {size} {size === 1 ? "person" : "people"}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Dietary Restrictions</Label>
              <div className="grid grid-cols-2 gap-2">
                {dietaryRestrictions.map((restriction) => (
                  <div key={restriction.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={restriction.id}
                      checked={selectedRestrictions.includes(restriction.id)}
                      onCheckedChange={(checked) => {
                        setSelectedRestrictions(
                          checked
                            ? [...selectedRestrictions, restriction.id]
                            : selectedRestrictions.filter((id) => id !== restriction.id)
                        );
                      }}
                    />
                    <Label htmlFor={restriction.id}>{restriction.label}</Label>
                  </div>
                ))}
              </div>
            </div>
            <Button
              type="submit"
              className="w-full"
              disabled={isLoading || isSupabaseLoading}
            >
              {isLoading
                ? "Creating account..."
                : isSupabaseLoading
                ? "Initializing..."
                : "Sign up"}
            </Button>
          </form>
          <div className="mt-4 text-center text-sm">
            Already have an account?{" "}
            <Link href="/login" className="text-primary hover:underline">
              Log in
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}


