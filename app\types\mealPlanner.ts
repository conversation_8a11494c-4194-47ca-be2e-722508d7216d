export interface Meal {
  id: string;
  name: string;
  calories: number;
  ingredients: string[];
  instructions: string[];
}

export interface GroceryList {
  categories: GroceryCategory[];
  isLoading: boolean;
  error: string | null;
}

export interface GroceryCategory {
  id: string;
  name: string;
  items: GroceryItem[];
}

export interface GroceryItem {
  id: string;
  name: string;
  checked: boolean;
  quantity: number;
  unit: string;
}

export interface MealPlannerState {
  todaysMeals: Meal[];
  weeklyPlan: Meal[];
  alternativeMeals: Meal[];
  isLoading: boolean;
  error: string | null;
  groceryList: GroceryList;
}

export interface MealPlannerContextType extends MealPlannerState {
  loadTodaysMeals: () => Promise<void>;
  loadWeeklyPlan: () => Promise<void>;
  loadAlternativeMeals: () => Promise<void>;
  swapMeal: (oldMealId: string, newMealId: string) => Promise<void>;
  toggleFavorite: (mealId: string) => Promise<void>;
  loadGroceryList: () => Promise<void>;
  toggleGroceryItem: (categoryId: string, itemId: string, checked: boolean) => Promise<void>;
}





