-- This script standardizes RLS policies for all tables
-- Run this script in the Supabase SQL Editor

-- First, create the standardize_rls_policies function
CREATE OR REPLACE FUNCTION public.standardize_rls_policies(
  p_table_name text,
  p_user_id_column text DEFAULT 'user_id',
  p_enable_rls boolean DEFAULT true
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_policy_name_select text;
  v_policy_name_insert text;
  v_policy_name_update text;
  v_policy_name_delete text;
  v_using_expr text;
  v_check_expr text;
BEGIN
  -- Set policy names
  v_policy_name_select := 'Users can view their own ' || p_table_name;
  v_policy_name_insert := 'Users can insert their own ' || p_table_name;
  v_policy_name_update := 'Users can update their own ' || p_table_name;
  v_policy_name_delete := 'Users can delete their own ' || p_table_name;
  
  -- Set expressions based on user_id column
  IF p_user_id_column = 'user_id' THEN
    v_using_expr := 'auth.uid() = ' || p_user_id_column;
    v_check_expr := 'auth.uid() = ' || p_user_id_column;
  ELSE
    -- For tables with foreign key relationships
    v_using_expr := p_user_id_column || ' IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid())';
    v_check_expr := p_user_id_column || ' IN (SELECT id FROM public.meal_plans WHERE user_id = auth.uid())';
  END IF;
  
  -- Enable or disable RLS
  EXECUTE 'ALTER TABLE public.' || p_table_name || 
          CASE WHEN p_enable_rls THEN ' ENABLE' ELSE ' DISABLE' END || 
          ' ROW LEVEL SECURITY';
  
  -- If disabling RLS, we're done
  IF NOT p_enable_rls THEN
    RETURN;
  END IF;
  
  -- Drop existing policies
  EXECUTE 'DROP POLICY IF EXISTS "' || v_policy_name_select || '" ON public.' || p_table_name;
  EXECUTE 'DROP POLICY IF EXISTS "' || v_policy_name_insert || '" ON public.' || p_table_name;
  EXECUTE 'DROP POLICY IF EXISTS "' || v_policy_name_update || '" ON public.' || p_table_name;
  EXECUTE 'DROP POLICY IF EXISTS "' || v_policy_name_delete || '" ON public.' || p_table_name;
  
  -- Create standardized policies
  EXECUTE 'CREATE POLICY "' || v_policy_name_select || '" ON public.' || p_table_name || 
          ' FOR SELECT USING (' || v_using_expr || ')';
          
  EXECUTE 'CREATE POLICY "' || v_policy_name_insert || '" ON public.' || p_table_name || 
          ' FOR INSERT WITH CHECK (' || v_check_expr || ')';
          
  EXECUTE 'CREATE POLICY "' || v_policy_name_update || '" ON public.' || p_table_name || 
          ' FOR UPDATE USING (' || v_using_expr || ') WITH CHECK (' || v_check_expr || ')';
          
  EXECUTE 'CREATE POLICY "' || v_policy_name_delete || '" ON public.' || p_table_name || 
          ' FOR DELETE USING (' || v_using_expr || ')';
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.standardize_rls_policies TO authenticated;

-- Now apply standardized policies to each table
-- Customize this list based on your schema

-- Meal Plans table
SELECT public.standardize_rls_policies('meal_plans', 'user_id', true);

-- Meals table
SELECT public.standardize_rls_policies('meals', 'meal_plan_id', true);

-- Recipes table
SELECT public.standardize_rls_policies('recipes', 'user_id', true);

-- Add more tables as needed
-- SELECT public.standardize_rls_policies('table_name', 'user_id_column', true);
