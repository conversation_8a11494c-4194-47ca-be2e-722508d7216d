"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { CalendarDays, Clock, Download, Bell } from "lucide-react";

interface CalendarExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  mealPlan: any;
}

export function CalendarExportModal({ isOpen, onClose, mealPlan }: CalendarExportModalProps) {
  const [includeBreakfast, setIncludeBreakfast] = useState(true);
  const [includeLunch, setIncludeLunch] = useState(true);
  const [includeDinner, setIncludeDinner] = useState(true);
  const [includeSnacks, setIncludeSnacks] = useState(false);
  const [reminderTime, setReminderTime] = useState("30");
  const [calendarType, setCalendarType] = useState("ical");

  const handleExport = () => {
    // In a real app, this would generate an iCal file or integrate with calendar APIs
    // For now, we'll just show a success message and log what would be exported

    // Filter meals based on user selection
    const mealsToExport = [];

    if (mealPlan?.meal_data?.mealPlan?.week) {
      mealPlan.meal_data.mealPlan.week.forEach((day: any) => {
        day.meals.forEach((meal: any) => {
          const mealType = meal.type?.toLowerCase() || '';

          if (
            (includeBreakfast && mealType.includes('breakfast')) ||
            (includeLunch && mealType.includes('lunch')) ||
            (includeDinner && mealType.includes('dinner')) ||
            (includeSnacks && mealType.includes('snack'))
          ) {
            mealsToExport.push({
              date: day.date,
              meal: meal
            });
          }
        });
      });
    }

    // Generate iCal content (simplified example)
    let icalContent = 'BEGIN:VCALENDAR\nVERSION:2.0\nPRODID:-//LeanEats//Meal Plan//EN\n';

    mealsToExport.forEach((item) => {
      const { date, meal } = item;
      const mealDate = new Date(date);

      // Set time based on meal type
      if (meal.type?.toLowerCase().includes('breakfast')) {
        mealDate.setHours(8, 0, 0);
      } else if (meal.type?.toLowerCase().includes('lunch')) {
        mealDate.setHours(12, 30, 0);
      } else if (meal.type?.toLowerCase().includes('dinner')) {
        mealDate.setHours(18, 30, 0);
      } else {
        mealDate.setHours(15, 0, 0); // Snacks at 3pm
      }

      // Calculate reminder time
      const reminderMinutes = parseInt(reminderTime);
      const reminderDate = new Date(mealDate.getTime() - (reminderMinutes * 60 * 1000));

      // Calculate end time (30 minutes after start)
      const endDate = new Date(mealDate.getTime() + (30 * 60 * 1000));

      icalContent += 'BEGIN:VEVENT\n';
      icalContent += `SUMMARY:${meal.type}: ${meal.name}\n`;
      icalContent += `DTSTART:${formatDateForICal(mealDate)}\n`;
      icalContent += `DTEND:${formatDateForICal(endDate)}\n`;
      icalContent += `DESCRIPTION:Prepare ${meal.name}. Calories: ${meal.nutrition?.calories || 'N/A'}\n`;

      // Add reminder/alarm
      icalContent += 'BEGIN:VALARM\n';
      icalContent += 'ACTION:DISPLAY\n';
      icalContent += `DESCRIPTION:Reminder to prepare ${meal.name}\n`;
      icalContent += `TRIGGER:-PT${reminderMinutes}M\n`;
      icalContent += 'END:VALARM\n';

      icalContent += 'END:VEVENT\n';
    });

    icalContent += 'END:VCALENDAR';

    // In a real app, we would create a downloadable file with this content
    console.log('iCal content generated:', icalContent);

    // Create a downloadable file
    const blob = new Blob([icalContent], { type: 'text/calendar' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'meal-plan.ics';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('Meal plan exported to calendar');
    onClose();
  };

  // Helper function to format date for iCal
  const formatDateForICal = (date: Date) => {
    return date.toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Export to Calendar</DialogTitle>
          <DialogDescription>
            Add your meal plan to your calendar with reminders.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4 space-y-6">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Include Meal Types</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="breakfast"
                  checked={includeBreakfast}
                  onCheckedChange={(checked) => setIncludeBreakfast(!!checked)}
                />
                <Label htmlFor="breakfast">Breakfast</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="lunch"
                  checked={includeLunch}
                  onCheckedChange={(checked) => setIncludeLunch(!!checked)}
                />
                <Label htmlFor="lunch">Lunch</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="dinner"
                  checked={includeDinner}
                  onCheckedChange={(checked) => setIncludeDinner(!!checked)}
                />
                <Label htmlFor="dinner">Dinner</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="snacks"
                  checked={includeSnacks}
                  onCheckedChange={(checked) => setIncludeSnacks(!!checked)}
                />
                <Label htmlFor="snacks">Snacks</Label>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-sm font-medium">Reminder Settings</h3>
            <div className="flex items-end gap-4">
              <div className="flex-1">
                <Label htmlFor="reminder-time">Remind me</Label>
                <Select value={reminderTime} onValueChange={setReminderTime}>
                  <SelectTrigger id="reminder-time">
                    <SelectValue placeholder="Select time" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15 minutes before</SelectItem>
                    <SelectItem value="30">30 minutes before</SelectItem>
                    <SelectItem value="60">1 hour before</SelectItem>
                    <SelectItem value="120">2 hours before</SelectItem>
                    <SelectItem value="1440">1 day before</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex-1">
                <Label htmlFor="calendar-type">Calendar Format</Label>
                <Select value={calendarType} onValueChange={setCalendarType}>
                  <SelectTrigger id="calendar-type">
                    <SelectValue placeholder="Select format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ical">iCalendar (.ics)</SelectItem>
                    <SelectItem value="google">Google Calendar</SelectItem>
                    <SelectItem value="outlook">Outlook</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="bg-muted p-4 rounded-lg">
            <div className="flex items-start">
              <Bell className="h-5 w-5 mr-2 text-primary mt-0.5" />
              <div>
                <h4 className="text-sm font-medium">Reminder Preview</h4>
                <p className="text-xs text-muted-foreground mt-1">
                  You'll receive reminders {reminderTime === "1440" ? "1 day" : `${reminderTime} minutes`} before each meal to help you prepare.
                </p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
