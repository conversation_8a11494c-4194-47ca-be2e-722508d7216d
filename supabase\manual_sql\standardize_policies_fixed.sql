-- Standardize RLS policies for all tables with proper type casting

-- Function to standardize policies with comprehensive type checking
CREATE OR REPLACE FUNCTION standardize_table_policies(
    p_table_name TEXT,
    p_user_id_column TEXT DEFAULT 'user_id',
    p_parent_table TEXT DEFAULT NULL,
    p_parent_column TEXT DEFAULT 'user_id'
)
RETURNS VOID AS $$
DECLARE
    v_column_type TEXT;
    v_parent_column_type TEXT;
    v_using_expr TEXT;
    v_check_expr TEXT;
    v_policy_select TEXT;
    v_policy_insert TEXT;
    v_policy_update TEXT;
    v_policy_delete TEXT;
BEGIN
    -- Set policy names
    v_policy_select := 'Users can view their own ' || p_table_name;
    v_policy_insert := 'Users can insert their own ' || p_table_name;
    v_policy_update := 'Users can update their own ' || p_table_name;
    v_policy_delete := 'Users can delete their own ' || p_table_name;
    
    -- Drop existing policies
    EXECUTE 'DROP POLICY IF EXISTS "' || v_policy_select || '" ON public.' || p_table_name;
    EXECUTE 'DROP POLICY IF EXISTS "' || v_policy_insert || '" ON public.' || p_table_name;
    EXECUTE 'DROP POLICY IF EXISTS "' || v_policy_update || '" ON public.' || p_table_name;
    EXECUTE 'DROP POLICY IF EXISTS "' || v_policy_delete || '" ON public.' || p_table_name;
    
    -- Enable RLS
    EXECUTE 'ALTER TABLE public.' || p_table_name || ' ENABLE ROW LEVEL SECURITY';
    
    -- If this is a child table with a foreign key relationship
    IF p_parent_table IS NOT NULL THEN
        -- Get the data type of the parent column
        EXECUTE 'SELECT data_type FROM information_schema.columns 
                 WHERE table_schema = ''public'' 
                 AND table_name = ''' || p_parent_table || '''
                 AND column_name = ''' || p_parent_column || ''''
        INTO v_parent_column_type;
        
        -- Set expressions based on parent column type
        IF v_parent_column_type = 'uuid' THEN
            v_using_expr := p_user_id_column || ' IN (SELECT id FROM public.' || p_parent_table || ' WHERE ' || p_parent_column || ' = auth.uid())';
            v_check_expr := p_user_id_column || ' IN (SELECT id FROM public.' || p_parent_table || ' WHERE ' || p_parent_column || ' = auth.uid())';
        ELSIF v_parent_column_type = 'text' OR v_parent_column_type = 'character varying' THEN
            v_using_expr := p_user_id_column || ' IN (SELECT id FROM public.' || p_parent_table || ' WHERE ' || p_parent_column || ' = auth.uid()::text)';
            v_check_expr := p_user_id_column || ' IN (SELECT id FROM public.' || p_parent_table || ' WHERE ' || p_parent_column || ' = auth.uid()::text)';
        ELSE
            RAISE EXCEPTION 'Unsupported parent column type: %', v_parent_column_type;
        END IF;
    ELSE
        -- Get the data type of the user_id column
        EXECUTE 'SELECT data_type FROM information_schema.columns 
                 WHERE table_schema = ''public'' 
                 AND table_name = ''' || p_table_name || '''
                 AND column_name = ''' || p_user_id_column || ''''
        INTO v_column_type;
        
        -- Set expressions based on column type
        IF v_column_type = 'uuid' THEN
            v_using_expr := p_user_id_column || ' = auth.uid()';
            v_check_expr := p_user_id_column || ' = auth.uid()';
        ELSIF v_column_type = 'text' OR v_column_type = 'character varying' THEN
            v_using_expr := p_user_id_column || ' = auth.uid()::text';
            v_check_expr := p_user_id_column || ' = auth.uid()::text';
        ELSE
            RAISE EXCEPTION 'Unsupported column type: %', v_column_type;
        END IF;
    END IF;
    
    -- Create standardized policies
    EXECUTE 'CREATE POLICY "' || v_policy_select || '" ON public.' || p_table_name || 
            ' FOR SELECT USING (' || v_using_expr || ')';
            
    EXECUTE 'CREATE POLICY "' || v_policy_insert || '" ON public.' || p_table_name || 
            ' FOR INSERT WITH CHECK (' || v_check_expr || ')';
            
    EXECUTE 'CREATE POLICY "' || v_policy_update || '" ON public.' || p_table_name || 
            ' FOR UPDATE USING (' || v_using_expr || ') WITH CHECK (' || v_check_expr || ')';
            
    EXECUTE 'CREATE POLICY "' || v_policy_delete || '" ON public.' || p_table_name || 
            ' FOR DELETE USING (' || v_using_expr || ')';
            
    -- Grant permissions
    EXECUTE 'GRANT ALL ON public.' || p_table_name || ' TO authenticated';
    
    RAISE NOTICE 'Standardized policies for table: %', p_table_name;
END;
$$ LANGUAGE plpgsql;

-- Apply standardized policies to each table one by one to isolate any issues

-- 1. meal_plans table
SELECT standardize_table_policies('meal_plans');

-- 2. meals table (child table with foreign key to meal_plans)
SELECT standardize_table_policies('meals', 'meal_plan_id', 'meal_plans', 'user_id');

-- 3. pantry_items table
SELECT standardize_table_policies('pantry_items');

-- 4. recipes table
SELECT standardize_table_policies('recipes');

-- 5. shopping_lists table
SELECT standardize_table_policies('shopping_lists');

-- 6. shopping_items table (child table with foreign key to shopping_lists)
SELECT standardize_table_policies('shopping_items', 'list_id', 'shopping_lists', 'user_id');

-- 7. user_preferences table
SELECT standardize_table_policies('user_preferences');

-- 8. users table
SELECT standardize_table_policies('users', 'id');

-- 9. Clean up legacy tables (optional)
-- DROP TABLE IF EXISTS public.meal_plans_old;

-- 10. Disable RLS for system tables (optional)
ALTER TABLE public._prisma_migrations DISABLE ROW LEVEL SECURITY;

-- Drop the function when done
DROP FUNCTION standardize_table_policies;
