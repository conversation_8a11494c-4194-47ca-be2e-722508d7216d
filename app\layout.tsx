import './globals.css';
import { Inter } from 'next/font/google';
import { Providers } from '@/components/providers';
import { SupabaseProvider } from '@/components/supabase-provider';
import { MealPlannerProvider } from '@/app/context/MealPlannerContext';
import { ClientLayout } from '@/app/components/layout/ClientLayout';

const inter = Inter({ subsets: ['latin'] });

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className} suppressHydrationWarning>
        <SupabaseProvider>
          <Providers>
            <MealPlannerProvider>
              <ClientLayout>
                {children}
              </ClientLayout>
            </MealPlannerProvider>
          </Providers>
        </SupabaseProvider>
      </body>
    </html>
  );
}
