-- Create meal_completions table
CREATE TABLE IF NOT EXISTS meal_completions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  meal_plan_id UUID NOT NULL REFERENCES meal_plans(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  meal_type TEXT NOT NULL,
  completed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  
  -- Ensure a user can only complete a specific meal once
  UNIQUE(user_id, meal_plan_id, date, meal_type)
);

-- Add RLS policies
ALTER TABLE meal_completions ENABLE ROW LEVEL SECURITY;

-- Policy for selecting meal completions (users can only see their own)
CREATE POLICY "Users can view their own meal completions"
  ON meal_completions
  FOR SELECT
  USING (user_id::text = auth.uid()::text);

-- Policy for inserting meal completions
CREATE POLICY "Users can insert their own meal completions"
  ON meal_completions
  FOR INSERT
  WITH CHECK (user_id::text = auth.uid()::text);

-- Policy for updating meal completions
CREATE POLICY "Users can update their own meal completions"
  ON meal_completions
  FOR UPDATE
  USING (user_id::text = auth.uid()::text);

-- Policy for deleting meal completions
CREATE POLICY "Users can delete their own meal completions"
  ON meal_completions
  FOR DELETE
  USING (user_id::text = auth.uid()::text);

-- Create index for faster queries
CREATE INDEX meal_completions_user_id_idx ON meal_completions(user_id);
CREATE INDEX meal_completions_meal_plan_id_idx ON meal_completions(meal_plan_id);
CREATE INDEX meal_completions_date_idx ON meal_completions(date);
