import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/utils/logger';

interface MealPlanQueryParams {
  userId: string;
  status: string;
  currentDate: Date;
  limit: number;
  offset: number;
}

interface MealPlanQueryResult {
  success: boolean;
  data?: any;
  error?: string;
}

/**
 * Fetches meal plans with pagination and error handling for prepared statement conflicts
 * @param params Query parameters for meal plans
 * @param retryAttempt Current retry attempt number
 * @returns Promise<MealPlanQueryResult>
 */
export async function fetchMealPlans(
  params: MealPlanQueryParams,
  retryAttempt = 0
): Promise<MealPlanQueryResult> {
  const MAX_RETRIES = 2;

  try {
    // Perform the Prisma query
    const mealPlans = await prisma.meal_plans.findMany({
      where: {
        user_id: params.userId,
        status: params.status,
        end_date: {
          gte: params.currentDate
        }
      },
      include: {
        meals: {
          include: {
            recipe: true
          }
        }
      },
      orderBy: {
        start_date: 'desc'
      },
      take: params.limit,
      skip: params.offset
    });

    return {
      success: true,
      data: mealPlans
    };

  } catch (error) {
    // Check if error is a Prisma error
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      // Handle prepared statement conflict (Postgres error code 42P05)
      if (error.code === 'P2024' || error.message.includes('42P05')) {
        logger.warn(
          `Prepared statement conflict detected (attempt ${retryAttempt + 1}/${MAX_RETRIES})`,
          { error: error.message }
        );

        // Disconnect Prisma to clear prepared statements
        await prisma.$disconnect();

        // Retry the query if we haven't exceeded max retries
        if (retryAttempt < MAX_RETRIES) {
          logger.info('Retrying query after disconnection...');
          
          // Reconnect Prisma
          await prisma.$connect();
          
          // Recursive retry with incremented attempt counter
          return fetchMealPlans(params, retryAttempt + 1);
        }

        return {
          success: false,
          error: 'Database connection error: Maximum retry attempts exceeded'
        };
      }

      // Handle other Prisma errors
      logger.error('Prisma query error:', {
        code: error.code,
        message: error.message,
        meta: error.meta
      });

      return {
        success: false,
        error: 'Database query failed'
      };
    }

    // Handle unexpected errors
    logger.error('Unexpected error during meal plan fetch:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  } finally {
    // Ensure connection is properly handled
    try {
      await prisma.$disconnect();
    } catch (disconnectError) {
      logger.error('Error disconnecting from database:', disconnectError);
    }
  }
}

// Example usage:
/*
async function getMealPlans() {
  const params: MealPlanQueryParams = {
    userId: 'user123',
    status: 'active',
    currentDate: new Date(),
    limit: 10,
    offset: 0
  };

  const result = await fetchMealPlans(params);
  
  if (!result.success) {
    console.error('Failed to fetch meal plans:', result.error);
    return;
  }

  console.log('Meal plans:', result.data);
}
*/

