import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // For now, return static data to ensure the API works
    return NextResponse.json({
      data: {
        meals: [
          {
            id: "1",
            type: "Breakfast",
            name: "Avocado Toast",
            image: "https://images.unsplash.com/photo-1528505086635-4c69d5f10908",
            calories: 350,
            prepTime: 15,
            rating: 4,
            isFavorite: true
          },
          {
            id: "2",
            type: "Lunch",
            name: "Quinoa Buddha Bowl",
            image: "https://images.unsplash.com/photo-1546069901-ba9599a7e63c",
            calories: 450,
            prepTime: 25,
            rating: 5,
            isFavorite: false
          },
          {
            id: "3",
            type: "Dinner",
            name: "Grilled Salmon",
            image: "https://images.unsplash.com/photo-1467003909585-2f8a72700288",
            calories: 550,
            prepTime: 30,
            rating: 4,
            isFavorite: true
          }
        ]
      }
    });
  } catch (error) {
    console.error('[API Error] /api/meal-planner/today:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}


