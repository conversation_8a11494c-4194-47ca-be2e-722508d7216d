-- This migration creates all the necessary tables and RLS policies for the meal planning application
-- It is designed to be idempotent, so it can be run multiple times without error

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    first_name TEXT,
    last_name TEXT,
    avatar_url TEXT,
    weekly_budget DECIMAL DEFAULT 0,
    household_size INTEGER DEFAULT 1,
    dietary_restrictions TEXT[] DEFAULT ARRAY[]::TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    CONSTRAINT users_email_key UNIQUE (email)
);

-- Create meal_plans table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.meal_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_cost DECIMAL NOT NULL,
    meal_data JSONB NOT NULL,
    status TEXT DEFAULT 'active'::TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create meal_plan_assignments table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.meal_plan_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    meal_plan_id UUID NOT NULL REFERENCES public.meal_plans(id) ON DELETE CASCADE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create meal_completions table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.meal_completions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    meal_plan_id UUID NOT NULL REFERENCES public.meal_plans(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    meal_type TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'completed',
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(user_id, meal_plan_id, date, meal_type)
);

-- Create recipes table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.recipes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    instructions JSONB,
    ingredients JSONB,
    prep_time INTEGER,
    cook_time INTEGER,
    servings INTEGER DEFAULT 1,
    calories_per_serving INTEGER,
    protein_per_serving DECIMAL,
    carbs_per_serving DECIMAL,
    fat_per_serving DECIMAL,
    image_url TEXT,
    tags TEXT[],
    is_favorite BOOLEAN DEFAULT FALSE,
    source TEXT,
    source_url TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create favorite_meals table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.favorite_meals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    meal_id TEXT NOT NULL,
    meal_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(user_id, meal_id)
);

-- Create shopping_lists table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.shopping_lists (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    meal_plan_id UUID REFERENCES public.meal_plans(id) ON DELETE SET NULL,
    name TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create shopping_items table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.shopping_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    shopping_list_id UUID NOT NULL REFERENCES public.shopping_lists(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    quantity TEXT NOT NULL DEFAULT '1',
    unit TEXT NOT NULL DEFAULT 'item',
    category TEXT NOT NULL DEFAULT 'Other',
    checked BOOLEAN NOT NULL DEFAULT FALSE,
    in_pantry BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create pantry_items table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.pantry_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    quantity TEXT NOT NULL,
    unit TEXT NOT NULL,
    expiry_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create meal_notes table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.meal_notes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    meal_id TEXT NOT NULL,
    date DATE NOT NULL,
    meal_type TEXT NOT NULL,
    notes TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(user_id, meal_id, date, meal_type)
);

-- Create meal_generation_preferences table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.meal_generation_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    dietary_preferences JSONB,
    excluded_ingredients TEXT[],
    calorie_target INTEGER,
    budget DECIMAL,
    household_size INTEGER,
    approach TEXT DEFAULT 'hybrid',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(user_id)
);

-- Create user_preferences table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    default_view TEXT DEFAULT 'list',
    default_servings INTEGER DEFAULT 1,
    dietary_preferences JSONB,
    allergens TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(user_id)
);

-- Create search_history table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.search_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    query TEXT NOT NULL,
    filters JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS users_id_idx ON public.users(id);
CREATE INDEX IF NOT EXISTS meal_plans_user_id_idx ON public.meal_plans(user_id);
CREATE INDEX IF NOT EXISTS meal_plans_status_idx ON public.meal_plans(status);
CREATE INDEX IF NOT EXISTS meal_plan_assignments_user_id_idx ON public.meal_plan_assignments(user_id);
CREATE INDEX IF NOT EXISTS meal_plan_assignments_meal_plan_id_idx ON public.meal_plan_assignments(meal_plan_id);
CREATE INDEX IF NOT EXISTS meal_plan_assignments_date_range_idx ON public.meal_plan_assignments(start_date, end_date);
CREATE INDEX IF NOT EXISTS meal_completions_user_id_idx ON public.meal_completions(user_id);
CREATE INDEX IF NOT EXISTS meal_completions_meal_plan_id_idx ON public.meal_completions(meal_plan_id);
CREATE INDEX IF NOT EXISTS meal_completions_date_idx ON public.meal_completions(date);
CREATE INDEX IF NOT EXISTS recipes_user_id_idx ON public.recipes(user_id);
CREATE INDEX IF NOT EXISTS recipes_tags_idx ON public.recipes USING GIN(tags);
CREATE INDEX IF NOT EXISTS favorite_meals_user_id_idx ON public.favorite_meals(user_id);
CREATE INDEX IF NOT EXISTS shopping_lists_user_id_idx ON public.shopping_lists(user_id);
CREATE INDEX IF NOT EXISTS shopping_lists_meal_plan_id_idx ON public.shopping_lists(meal_plan_id);
CREATE INDEX IF NOT EXISTS shopping_items_shopping_list_id_idx ON public.shopping_items(shopping_list_id);
CREATE INDEX IF NOT EXISTS shopping_items_category_idx ON public.shopping_items(category);
CREATE INDEX IF NOT EXISTS pantry_items_user_id_idx ON public.pantry_items(user_id);
CREATE INDEX IF NOT EXISTS pantry_items_category_idx ON public.pantry_items(category);
CREATE INDEX IF NOT EXISTS meal_notes_user_id_idx ON public.meal_notes(user_id);
CREATE INDEX IF NOT EXISTS meal_notes_meal_id_idx ON public.meal_notes(meal_id);
CREATE INDEX IF NOT EXISTS meal_generation_preferences_user_id_idx ON public.meal_generation_preferences(user_id);
CREATE INDEX IF NOT EXISTS user_preferences_user_id_idx ON public.user_preferences(user_id);
CREATE INDEX IF NOT EXISTS search_history_user_id_idx ON public.search_history(user_id);

-- Create triggers for updating the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for each table with updated_at column
DO $$
DECLARE
    t text;
BEGIN
    FOR t IN
        SELECT table_name 
        FROM information_schema.columns 
        WHERE column_name = 'updated_at' 
        AND table_schema = 'public'
    LOOP
        EXECUTE format('
            DROP TRIGGER IF EXISTS update_%I_updated_at ON %I;
            CREATE TRIGGER update_%I_updated_at
            BEFORE UPDATE ON %I
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        ', t, t, t, t);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Enable RLS on all tables
DO $$
DECLARE
    t text;
BEGIN
    FOR t IN
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        AND table_type = 'BASE TABLE'
    LOOP
        EXECUTE format('ALTER TABLE %I ENABLE ROW LEVEL SECURITY;', t);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create standardized RLS policies for all tables with direct user ownership
DO $$
DECLARE
    t text;
BEGIN
    FOR t IN
        SELECT table_name 
        FROM information_schema.columns 
        WHERE column_name = 'user_id' 
        AND table_schema = 'public'
    LOOP
        -- Drop existing policies
        EXECUTE format('
            DROP POLICY IF EXISTS "Users can view their own %1$I" ON %1$I;
            DROP POLICY IF EXISTS "Users can insert their own %1$I" ON %1$I;
            DROP POLICY IF EXISTS "Users can update their own %1$I" ON %1$I;
            DROP POLICY IF EXISTS "Users can delete their own %1$I" ON %1$I;
        ', t);
        
        -- Create new policies
        EXECUTE format('
            CREATE POLICY "Users can view their own %1$I"
            ON %1$I FOR SELECT
            TO authenticated
            USING (user_id::text = auth.uid()::text);

            CREATE POLICY "Users can insert their own %1$I"
            ON %1$I FOR INSERT
            TO authenticated
            WITH CHECK (user_id::text = auth.uid()::text);

            CREATE POLICY "Users can update their own %1$I"
            ON %1$I FOR UPDATE
            TO authenticated
            USING (user_id::text = auth.uid()::text);

            CREATE POLICY "Users can delete their own %1$I"
            ON %1$I FOR DELETE
            TO authenticated
            USING (user_id::text = auth.uid()::text);
        ', t);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create RLS policies for shopping_items (indirect ownership via shopping_list_id)
DROP POLICY IF EXISTS "Users can view their own shopping_items" ON shopping_items;
DROP POLICY IF EXISTS "Users can insert their own shopping_items" ON shopping_items;
DROP POLICY IF EXISTS "Users can update their own shopping_items" ON shopping_items;
DROP POLICY IF EXISTS "Users can delete their own shopping_items" ON shopping_items;

CREATE POLICY "Users can view their own shopping_items"
ON shopping_items FOR SELECT
TO authenticated
USING (shopping_list_id IN (
    SELECT id FROM shopping_lists WHERE user_id::text = auth.uid()::text
));

CREATE POLICY "Users can insert their own shopping_items"
ON shopping_items FOR INSERT
TO authenticated
WITH CHECK (shopping_list_id IN (
    SELECT id FROM shopping_lists WHERE user_id::text = auth.uid()::text
));

CREATE POLICY "Users can update their own shopping_items"
ON shopping_items FOR UPDATE
TO authenticated
USING (shopping_list_id IN (
    SELECT id FROM shopping_lists WHERE user_id::text = auth.uid()::text
));

CREATE POLICY "Users can delete their own shopping_items"
ON shopping_items FOR DELETE
TO authenticated
USING (shopping_list_id IN (
    SELECT id FROM shopping_lists WHERE user_id::text = auth.uid()::text
));

-- Grant permissions to authenticated users for all tables
DO $$
DECLARE
    t text;
BEGIN
    FOR t IN
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        AND table_type = 'BASE TABLE'
    LOOP
        EXECUTE format('GRANT ALL ON %I TO authenticated;', t);
    END LOOP;
END;
$$ LANGUAGE plpgsql;
