# Fixing RLS Policy Issues for Meal Plan Generation

This document provides instructions on how to fix the Row Level Security (RLS) policy issues that prevent meal plans from being saved to the Supabase database.

## Understanding the Issue

When you see the popup message "Note: meal plan is generated but not saved to database due to RLS policy restrictions", it means that:

1. The meal plan is being generated successfully by OpenAI.
2. The application is trying to save the meal plan to the `meal_plans` table in Supabase.
3. The RLS policies for the `meal_plans` table are preventing the insertion.

## Solution Options

### Option 1: Using the Supabase Dashboard (Recommended for beginners)

1. Log in to your Supabase dashboard at https://app.supabase.com
2. Select your project
3. Go to the "Table Editor" in the left sidebar
4. Find the `meal_plans` table (or create it if it doesn't exist)
5. Click on "Policies" at the top of the table view
6. Add the following policies:

   a. **For reading data**:
   ```
   Name: Users can view their own meal plans
   Operation: SELECT
   Using expression: auth.uid() = user_id
   ```

   b. **For inserting data**:
   ```
   Name: Users can insert their own meal plans
   Operation: INSERT
   Check expression: auth.uid() = user_id
   ```

   c. **For updating data**:
   ```
   Name: Users can update their own meal plans
   Operation: UPDATE
   Using expression: auth.uid() = user_id
   Check expression: auth.uid() = user_id
   ```

   d. **For deleting data**:
   ```
   Name: Users can delete their own meal plans
   Operation: DELETE
   Using expression: auth.uid() = user_id
   ```

7. Save each policy

### Option 2: Using SQL Migrations (Recommended for developers)

1. We've created a SQL migration file at `supabase/migrations/20230801000000_update_meal_plans_rls.sql`
2. You can apply this migration in several ways:

   a. **Using the Supabase CLI**:
   ```bash
   supabase migration up
   ```

   b. **Using the Supabase Dashboard**:
   - Go to the SQL Editor
   - Copy the contents of the migration file
   - Paste and execute the SQL

   c. **Using our custom script**:
   ```bash
   # Install dependencies if needed
   npm install @supabase/supabase-js dotenv
   
   # Make sure you have a .env file with your Supabase credentials
   # NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
   # SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   
   # Run the script
   node scripts/apply_rls_policies.js
   ```

### Option 3: Creating the Table and Policies Manually

If you prefer to create the table and policies manually, here's the SQL you need:

```sql
-- Create the meal_plans table
CREATE TABLE public.meal_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    total_cost DECIMAL(10, 2) NOT NULL,
    meal_data JSONB NOT NULL,
    status TEXT NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.meal_plans ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own meal plans"
ON public.meal_plans
FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own meal plans"
ON public.meal_plans
FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own meal plans"
ON public.meal_plans
FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own meal plans"
ON public.meal_plans
FOR DELETE
USING (auth.uid() = user_id);

-- Grant permissions
GRANT ALL ON public.meal_plans TO authenticated;
```

## Verifying the Fix

After applying the RLS policies:

1. Try generating a meal plan again
2. The popup message should no longer appear
3. You can verify that the meal plan was saved by checking the `meal_plans` table in the Supabase dashboard

## Troubleshooting

If you're still experiencing issues:

1. Check the browser console for any error messages
2. Verify that the user is properly authenticated
3. Make sure the `meal_plans` table has the correct structure
4. Check that the RLS policies are correctly applied
5. Look at the Supabase logs for any error messages

For further assistance, please contact the development team.
