-- Fix RLS policies for shopping_items table

-- First, check the column names in the shopping_items table
SELECT 
    column_name, 
    data_type, 
    is_nullable
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
    AND table_name = 'shopping_items'
ORDER BY 
    ordinal_position;

-- Enable RLS
ALTER TABLE public.shopping_items ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own shopping_items" ON public.shopping_items;
DROP POLICY IF EXISTS "Users can insert their own shopping_items" ON public.shopping_items;
DROP POLICY IF EXISTS "Users can update their own shopping_items" ON public.shopping_items;
DROP POLICY IF EXISTS "Users can delete their own shopping_items" ON public.shopping_items;

-- Check if shopping_list_id column exists
DO $$
DECLARE
    column_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'shopping_items'
        AND column_name = 'shopping_list_id'
    ) INTO column_exists;
    
    IF column_exists THEN
        RAISE NOTICE 'Using shopping_list_id column for policies';
        
        -- Check the data type of user_id in shopping_lists
        DECLARE
            column_type TEXT;
        BEGIN
            SELECT data_type INTO column_type
            FROM information_schema.columns
            WHERE table_schema = 'public' 
            AND table_name = 'shopping_lists'
            AND column_name = 'user_id';
            
            RAISE NOTICE 'shopping_lists.user_id type: %', column_type;
            
            IF column_type = 'uuid' THEN
                -- For UUID type
                CREATE POLICY "Users can view their own shopping_items" ON public.shopping_items FOR SELECT 
                USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));
                
                CREATE POLICY "Users can insert their own shopping_items" ON public.shopping_items FOR INSERT 
                WITH CHECK (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));
                
                CREATE POLICY "Users can update their own shopping_items" ON public.shopping_items FOR UPDATE 
                USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()))
                WITH CHECK (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));
                
                CREATE POLICY "Users can delete their own shopping_items" ON public.shopping_items FOR DELETE 
                USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));
            ELSE
                -- For text type
                CREATE POLICY "Users can view their own shopping_items" ON public.shopping_items FOR SELECT 
                USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));
                
                CREATE POLICY "Users can insert their own shopping_items" ON public.shopping_items FOR INSERT 
                WITH CHECK (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));
                
                CREATE POLICY "Users can update their own shopping_items" ON public.shopping_items FOR UPDATE 
                USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text))
                WITH CHECK (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));
                
                CREATE POLICY "Users can delete their own shopping_items" ON public.shopping_items FOR DELETE 
                USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));
            END IF;
        END;
    ELSE
        -- Check if user_id column exists directly in shopping_items
        SELECT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'shopping_items'
            AND column_name = 'user_id'
        ) INTO column_exists;
        
        IF column_exists THEN
            RAISE NOTICE 'Using user_id column for policies';
            
            -- Check the data type of user_id in shopping_items
            DECLARE
                column_type TEXT;
            BEGIN
                SELECT data_type INTO column_type
                FROM information_schema.columns
                WHERE table_schema = 'public' 
                AND table_name = 'shopping_items'
                AND column_name = 'user_id';
                
                RAISE NOTICE 'shopping_items.user_id type: %', column_type;
                
                IF column_type = 'uuid' THEN
                    -- For UUID type
                    CREATE POLICY "Users can view their own shopping_items" ON public.shopping_items FOR SELECT USING (user_id = auth.uid());
                    CREATE POLICY "Users can insert their own shopping_items" ON public.shopping_items FOR INSERT WITH CHECK (user_id = auth.uid());
                    CREATE POLICY "Users can update their own shopping_items" ON public.shopping_items FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());
                    CREATE POLICY "Users can delete their own shopping_items" ON public.shopping_items FOR DELETE USING (user_id = auth.uid());
                ELSE
                    -- For text type
                    CREATE POLICY "Users can view their own shopping_items" ON public.shopping_items FOR SELECT USING (user_id = auth.uid()::text);
                    CREATE POLICY "Users can insert their own shopping_items" ON public.shopping_items FOR INSERT WITH CHECK (user_id = auth.uid()::text);
                    CREATE POLICY "Users can update their own shopping_items" ON public.shopping_items FOR UPDATE USING (user_id = auth.uid()::text) WITH CHECK (user_id = auth.uid()::text);
                    CREATE POLICY "Users can delete their own shopping_items" ON public.shopping_items FOR DELETE USING (user_id = auth.uid()::text);
                END IF;
            END;
        ELSE
            RAISE EXCEPTION 'Could not find a suitable column for RLS policies in shopping_items table';
        END IF;
    END IF;
END
$$;

GRANT ALL ON public.shopping_items TO authenticated;
