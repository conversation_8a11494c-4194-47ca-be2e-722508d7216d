'use client';

import { useState } from 'react';
import { Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useFavoriteStatus } from '@/app/hooks/useFavorites';
import { cn } from '@/lib/utils';

interface FavoriteButtonProps {
  recipeId: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'ghost' | 'outline';
  className?: string;
  showText?: boolean;
}

export function FavoriteButton({ 
  recipeId, 
  size = 'md', 
  variant = 'ghost',
  className,
  showText = false
}: FavoriteButtonProps) {
  const { isFavorite, toggleFavorite, isToggling } = useFavoriteStatus(recipeId);
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      await toggleFavorite();
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12'
  };

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  return (
    <Button
      variant={variant}
      size={showText ? 'sm' : 'icon'}
      className={cn(
        !showText && sizeClasses[size],
        'transition-all duration-200',
        className
      )}
      onClick={handleClick}
      disabled={isToggling}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      aria-label={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
    >
      <Heart
        className={cn(
          iconSizes[size],
          'transition-all duration-200',
          isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-400',
          isHovered && !isFavorite && 'text-red-400',
          isToggling && 'animate-pulse'
        )}
      />
      {showText && (
        <span className="ml-2">
          {isFavorite ? 'Favorited' : 'Add to Favorites'}
        </span>
      )}
    </Button>
  );
}

export function FavoriteIcon({ 
  isFavorite, 
  size = 'md',
  className 
}: { 
  isFavorite: boolean; 
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}) {
  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  return (
    <Heart
      className={cn(
        iconSizes[size],
        'transition-colors duration-200',
        isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-400',
        className
      )}
    />
  );
}
