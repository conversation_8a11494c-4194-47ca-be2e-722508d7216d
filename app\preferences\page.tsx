"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useUserPreferences } from "@/app/hooks/useUserPreferences";
import { Settings, Clock, Globe, Bell, Shield, ChefHat, X } from "lucide-react";

const CUISINE_OPTIONS = [
  'Italian', 'Mexican', 'Asian', 'Mediterranean', 'American', 'Indian',
  'Thai', 'French', 'Japanese', 'Chinese', 'Greek', 'Spanish'
];

const COMMON_ALLERGENS = [
  'Dairy', 'Eggs', 'Fish', 'Shellfish', 'Tree nuts', 'Peanuts',
  'Wheat', 'Soy', 'Sesame', 'Gluten'
];

export default function Preferences() {
  const router = useRouter();
  const {
    preferences,
    isLoading,
    updateCookingSkillLevel,
    updateMaxCookingTime,
    updatePreferredCuisines,
    updateExcludedIngredients,
    updateNotificationPreferences,
    updatePrivacySettings,
    updateUnitsPreference,
    updateCurrencyPreference,
    getCookingSkillLevel,
    getMaxCookingTime,
    getPreferredCuisines,
    getExcludedIngredients,
    getNotificationPreferences,
    getPrivacySettings,
    getUnitsPreference,
    getCurrencyPreference,
    isUpdating
  } = useUserPreferences();

  const [localCuisines, setLocalCuisines] = useState<string[]>([]);
  const [localIngredients, setLocalIngredients] = useState<string[]>([]);
  const [newCuisine, setNewCuisine] = useState('');
  const [newIngredient, setNewIngredient] = useState('');

  useEffect(() => {
    if (preferences) {
      setLocalCuisines(getPreferredCuisines());
      setLocalIngredients(getExcludedIngredients());
    }
  }, [preferences, getPreferredCuisines, getExcludedIngredients]);

  const handleAddCuisine = (cuisine: string) => {
    if (cuisine && !localCuisines.includes(cuisine)) {
      const updated = [...localCuisines, cuisine];
      setLocalCuisines(updated);
      updatePreferredCuisines(updated);
      setNewCuisine('');
    }
  };

  const handleRemoveCuisine = (cuisine: string) => {
    const updated = localCuisines.filter(c => c !== cuisine);
    setLocalCuisines(updated);
    updatePreferredCuisines(updated);
  };

  const handleAddIngredient = (ingredient: string) => {
    if (ingredient && !localIngredients.includes(ingredient)) {
      const updated = [...localIngredients, ingredient];
      setLocalIngredients(updated);
      updateExcludedIngredients(updated);
      setNewIngredient('');
    }
  };

  const handleRemoveIngredient = (ingredient: string) => {
    const updated = localIngredients.filter(i => i !== ingredient);
    setLocalIngredients(updated);
    updateExcludedIngredients(updated);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading preferences...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3">
          <Settings className="h-8 w-8" />
          Preferences
        </h1>
        <p className="text-gray-600">
          Customize your meal planning experience
        </p>
      </div>

      <div className="grid gap-6">
        {/* Cooking Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ChefHat className="h-5 w-5" />
              Cooking Preferences
            </CardTitle>
            <CardDescription>
              Tell us about your cooking style and preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="skill-level">Cooking Skill Level</Label>
                <Select
                  value={getCookingSkillLevel()}
                  onValueChange={(value: 'beginner' | 'intermediate' | 'advanced') =>
                    updateCookingSkillLevel(value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select your skill level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="cooking-time">Maximum Cooking Time</Label>
                <div className="space-y-2">
                  <Slider
                    value={[getMaxCookingTime()]}
                    onValueChange={(value) => updateMaxCookingTime(value[0])}
                    max={180}
                    min={5}
                    step={5}
                    className="w-full"
                  />
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>5 min</span>
                    <span className="font-medium">{getMaxCookingTime()} minutes</span>
                    <span>3 hours</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Cuisine Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Cuisine Preferences
            </CardTitle>
            <CardDescription>
              Select your favorite cuisines for meal suggestions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              {localCuisines.map((cuisine) => (
                <Badge key={cuisine} variant="secondary" className="gap-1">
                  {cuisine}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleRemoveCuisine(cuisine)}
                  />
                </Badge>
              ))}
            </div>

            <div className="flex gap-2">
              <Select value={newCuisine} onValueChange={setNewCuisine}>
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Add a cuisine..." />
                </SelectTrigger>
                <SelectContent>
                  {CUISINE_OPTIONS.filter(c => !localCuisines.includes(c)).map((cuisine) => (
                    <SelectItem key={cuisine} value={cuisine}>
                      {cuisine}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                type="button"
                onClick={() => handleAddCuisine(newCuisine)}
                disabled={!newCuisine || localCuisines.includes(newCuisine)}
              >
                Add
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Dietary Restrictions */}
        <Card>
          <CardHeader>
            <CardTitle>Dietary Restrictions & Allergies</CardTitle>
            <CardDescription>
              Ingredients and allergens to avoid in meal suggestions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              {localIngredients.map((ingredient) => (
                <Badge key={ingredient} variant="destructive" className="gap-1">
                  {ingredient}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleRemoveIngredient(ingredient)}
                  />
                </Badge>
              ))}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Common Allergens</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {COMMON_ALLERGENS.map((allergen) => (
                    <Button
                      key={allergen}
                      type="button"
                      variant={localIngredients.includes(allergen) ? "destructive" : "outline"}
                      size="sm"
                      onClick={() => {
                        if (localIngredients.includes(allergen)) {
                          handleRemoveIngredient(allergen);
                        } else {
                          handleAddIngredient(allergen);
                        }
                      }}
                    >
                      {allergen}
                    </Button>
                  ))}
                </div>
              </div>

              <div>
                <Label>Custom Ingredient</Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    value={newIngredient}
                    onChange={(e) => setNewIngredient(e.target.value)}
                    placeholder="Enter ingredient to avoid..."
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddIngredient(newIngredient);
                      }
                    }}
                  />
                  <Button
                    type="button"
                    onClick={() => handleAddIngredient(newIngredient)}
                    disabled={!newIngredient || localIngredients.includes(newIngredient)}
                  >
                    Add
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notification Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notification Preferences
            </CardTitle>
            <CardDescription>
              Choose how you'd like to be notified about meal planning
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="email-notifications">Email Notifications</Label>
                  <p className="text-sm text-gray-500">Receive meal plan updates via email</p>
                </div>
                <Checkbox
                  id="email-notifications"
                  checked={getNotificationPreferences().email}
                  onCheckedChange={(checked) =>
                    updateNotificationPreferences({ email: !!checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="push-notifications">Push Notifications</Label>
                  <p className="text-sm text-gray-500">Receive browser notifications</p>
                </div>
                <Checkbox
                  id="push-notifications"
                  checked={getNotificationPreferences().push}
                  onCheckedChange={(checked) =>
                    updateNotificationPreferences({ push: !!checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="meal-reminders">Meal Reminders</Label>
                  <p className="text-sm text-gray-500">Get reminded about upcoming meals</p>
                </div>
                <Checkbox
                  id="meal-reminders"
                  checked={getNotificationPreferences().meal_reminders}
                  onCheckedChange={(checked) =>
                    updateNotificationPreferences({ meal_reminders: !!checked })
                  }
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Privacy Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Privacy Settings
            </CardTitle>
            <CardDescription>
              Control how your data is used and shared
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="data-sharing">Data Sharing</Label>
                  <p className="text-sm text-gray-500">Share anonymized data to improve our service</p>
                </div>
                <Checkbox
                  id="data-sharing"
                  checked={getPrivacySettings().data_sharing}
                  onCheckedChange={(checked) =>
                    updatePrivacySettings({ data_sharing: !!checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="analytics">Analytics</Label>
                  <p className="text-sm text-gray-500">Help us understand how you use the app</p>
                </div>
                <Checkbox
                  id="analytics"
                  checked={getPrivacySettings().analytics}
                  onCheckedChange={(checked) =>
                    updatePrivacySettings({ analytics: !!checked })
                  }
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Units & Currency */}
        <Card>
          <CardHeader>
            <CardTitle>Units & Currency</CardTitle>
            <CardDescription>
              Choose your preferred units and currency for recipes and costs
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="units">Measurement Units</Label>
                <Select
                  value={getUnitsPreference()}
                  onValueChange={(value: 'metric' | 'imperial') =>
                    updateUnitsPreference(value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select units" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="metric">Metric (kg, L, °C)</SelectItem>
                    <SelectItem value="imperial">Imperial (lbs, cups, °F)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="currency">Currency</Label>
                <Select
                  value={getCurrencyPreference()}
                  onValueChange={updateCurrencyPreference}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD ($)</SelectItem>
                    <SelectItem value="EUR">EUR (€)</SelectItem>
                    <SelectItem value="GBP">GBP (£)</SelectItem>
                    <SelectItem value="CAD">CAD (C$)</SelectItem>
                    <SelectItem value="AUD">AUD (A$)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/dashboard')}
          >
            Cancel
          </Button>
          <Button
            disabled={isUpdating}
            onClick={() => {
              toast.success('Preferences saved successfully!');
              router.push('/dashboard');
            }}
          >
            {isUpdating ? 'Saving...' : 'Save Preferences'}
          </Button>
        </div>
      </div>
    </div>
  );
}