"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useUserPreferences } from "@/app/hooks/useUserPreferences";
import { Settings, Clock, Globe, Bell, Shield, ChefHat, X } from "lucide-react";

const CUISINE_OPTIONS = [
  'Italian', 'Mexican', 'Asian', 'Mediterranean', 'American', 'Indian',
  'Thai', 'French', 'Japanese', 'Chinese', 'Greek', 'Spanish'
];

const COMMON_ALLERGENS = [
  'Dairy', 'Eggs', 'Fish', 'Shellfish', 'Tree nuts', 'Peanuts',
  'Wheat', 'Soy', 'Sesame', 'Gluten'
];

export default function Preferences() {
  const router = useRouter();
  const {
    preferences,
    isLoading,
    updateCookingSkillLevel,
    updateMaxCookingTime,
    updatePreferredCuisines,
    updateExcludedIngredients,
    updateNotificationPreferences,
    updatePrivacySettings,
    updateUnitsPreference,
    updateCurrencyPreference,
    getCookingSkillLevel,
    getMaxCookingTime,
    getPreferredCuisines,
    getExcludedIngredients,
    getNotificationPreferences,
    getPrivacySettings,
    getUnitsPreference,
    getCurrencyPreference,
    isUpdating
  } = useUserPreferences();

  const [localCuisines, setLocalCuisines] = useState<string[]>([]);
  const [localIngredients, setLocalIngredients] = useState<string[]>([]);
  const [newCuisine, setNewCuisine] = useState('');
  const [newIngredient, setNewIngredient] = useState('');

  useEffect(() => {
    if (preferences) {
      setLocalCuisines(getPreferredCuisines());
      setLocalIngredients(getExcludedIngredients());
    }
  }, [preferences, getPreferredCuisines, getExcludedIngredients]);

  const handleAddCuisine = (cuisine: string) => {
    if (cuisine && !localCuisines.includes(cuisine)) {
      const updated = [...localCuisines, cuisine];
      setLocalCuisines(updated);
      updatePreferredCuisines(updated);
      setNewCuisine('');
    }
  };

  const handleRemoveCuisine = (cuisine: string) => {
    const updated = localCuisines.filter(c => c !== cuisine);
    setLocalCuisines(updated);
    updatePreferredCuisines(updated);
  };

  const handleAddIngredient = (ingredient: string) => {
    if (ingredient && !localIngredients.includes(ingredient)) {
      const updated = [...localIngredients, ingredient];
      setLocalIngredients(updated);
      updateExcludedIngredients(updated);
      setNewIngredient('');
    }
  };

  const handleRemoveIngredient = (ingredient: string) => {
    const updated = localIngredients.filter(i => i !== ingredient);
    setLocalIngredients(updated);
    updateExcludedIngredients(updated);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading preferences...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3">
          <Settings className="h-8 w-8" />
          Preferences
        </h1>
        <p className="text-gray-600">
          Customize your meal planning experience
        </p>
      </div>

      <div className="grid gap-6">
        {/* Cooking Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ChefHat className="h-5 w-5" />
              Cooking Preferences
            </CardTitle>
            <CardDescription>
              Tell us about your cooking style and preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="skill-level">Cooking Skill Level</Label>
                <Select
                  value={getCookingSkillLevel()}
                  onValueChange={(value: 'beginner' | 'intermediate' | 'advanced') =>
                    updateCookingSkillLevel(value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select your skill level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="cooking-time">Maximum Cooking Time</Label>
                <div className="space-y-2">
                  <Slider
                    value={[getMaxCookingTime()]}
                    onValueChange={(value) => updateMaxCookingTime(value[0])}
                    max={180}
                    min={5}
                    step={5}
                    className="w-full"
                  />
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>5 min</span>
                    <span className="font-medium">{getMaxCookingTime()} minutes</span>
                    <span>3 hours</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Cuisine Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Cuisine Preferences
            </CardTitle>
            <CardDescription>
              Select your favorite cuisines for meal suggestions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              {localCuisines.map((cuisine) => (
                <Badge key={cuisine} variant="secondary" className="gap-1">
                  {cuisine}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleRemoveCuisine(cuisine)}
                  />
                </Badge>
              ))}
            </div>

            <div className="flex gap-2">
              <Select value={newCuisine} onValueChange={setNewCuisine}>
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Add a cuisine..." />
                </SelectTrigger>
                <SelectContent>
                  {CUISINE_OPTIONS.filter(c => !localCuisines.includes(c)).map((cuisine) => (
                    <SelectItem key={cuisine} value={cuisine}>
                      {cuisine}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                type="button"
                onClick={() => handleAddCuisine(newCuisine)}
                disabled={!newCuisine || localCuisines.includes(newCuisine)}
              >
                Add
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Dietary Restrictions */}
        <Card>
          <CardHeader>
            <CardTitle>Dietary Restrictions & Allergies</CardTitle>
            <CardDescription>
              Ingredients and allergens to avoid in meal suggestions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              {localIngredients.map((ingredient) => (
                <Badge key={ingredient} variant="destructive" className="gap-1">
                  {ingredient}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleRemoveIngredient(ingredient)}
                  />
                </Badge>
              ))}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Common Allergens</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {COMMON_ALLERGENS.map((allergen) => (
                    <Button
                      key={allergen}
                      type="button"
                      variant={localIngredients.includes(allergen) ? "destructive" : "outline"}
                      size="sm"
                      onClick={() => {
                        if (localIngredients.includes(allergen)) {
                          handleRemoveIngredient(allergen);
                        } else {
                          handleAddIngredient(allergen);
                        }
                      }}
                    >
                      {allergen}
                    </Button>
                  ))}
                </div>
              </div>

              <div>
                <Label>Custom Ingredient</Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    value={newIngredient}
                    onChange={(e) => setNewIngredient(e.target.value)}
                    placeholder="Enter ingredient to avoid..."
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddIngredient(newIngredient);
                      }
                    }}
                  />
                  <Button
                    type="button"
                    onClick={() => handleAddIngredient(newIngredient)}
                    disabled={!newIngredient || localIngredients.includes(newIngredient)}
                  >
                    Add
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

          {/* Household Preferences */}
          <Card>
            <CardHeader>
              <CardTitle>Household Preferences</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                {[
                  { id: "children", label: "Children", info: "Ages 8-18" },
                  { id: "adults", label: "Adults", info: "Ages 19-64" },
                  { id: "seniors", label: "Seniors", info: "Ages 65+" }
                ].map((group) => (
                  <div key={group.id} className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={group.id}
                        checked={ageGroups.includes(group.label)}
                        onCheckedChange={(checked) => {
                          const newGroups = checked
                            ? [...ageGroups, group.label]
                            : ageGroups.filter(g => g !== group.label);
                          setValue("ageGroups", newGroups);
                        }}
                      />
                      <Label htmlFor={group.id}>
                        {group.label}
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger className="ml-1">ⓘ</TooltipTrigger>
                            <TooltipContent>{group.info}</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                    </div>
                    <Input
                      type="number"
                      min="0"
                      className="w-24"
                      disabled={!ageGroups.includes(group.label)}
                      value={watch(`${group.id}Count`) || 0}
                      onChange={(e) => {
                        const value = Math.max(0, parseInt(e.target.value) || 0);
                        setValue(`${group.id}Count` as keyof PreferencesFormData, value);
                      }}
                    />
                  </div>
                ))}
                <div className="text-sm font-medium text-gray-700">
                  Total Household Size: {totalPeople}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Dietary Restrictions */}
          <Card>
            <CardHeader>
              <CardTitle>Dietary Restrictions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Allergies and Intolerances</Label>
                <div className="grid grid-cols-2 gap-4">
                  {dietaryRestrictionsOptions.map((restriction) => (
                    <div key={restriction} className="flex items-center space-x-2">
                      <Checkbox
                        id={restriction}
                        {...register("dietaryRestrictions")}
                        value={restriction}
                      />
                      <Label htmlFor={restriction}>{restriction}</Label>
                    </div>
                  ))}
                </div>
                <div className="mt-4 space-y-4">
                  <Label>Additional Allergies</Label>
                  <div className="flex space-x-2">
                    <Input
                      placeholder="Enter an allergy"
                      value={newAllergy}
                      onChange={(e) => setNewAllergy(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleAddAllergy(e);
                        }
                      }}
                    />
                    <Button
                      type="button"
                      onClick={handleAddAllergy}
                      variant="outline"
                    >
                      Add
                    </Button>
                  </div>
                  {allergies.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {allergies.map((allergy, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-1 bg-secondary px-3 py-1 rounded-full"
                        >
                          <span>{allergy}</span>
                          <button
                            type="button"
                            className="text-sm hover:text-destructive"
                            onClick={() => {
                              const updatedAllergies = allergies.filter((_, i) => i !== index);
                              setValue("allergies", updatedAllergies);
                              toast.success(`Removed ${allergy} from allergies`);
                            }}
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              <div>
                <Label>Dietary Preference</Label>
                <Select
                  onValueChange={(value) => setValue("dietaryPreference", value)}
                  defaultValue="omnivore"
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select dietary preference" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="vegan">Vegan</SelectItem>
                    <SelectItem value="vegetarian">Vegetarian</SelectItem>
                    <SelectItem value="pescatarian">Pescatarian</SelectItem>
                    <SelectItem value="omnivore">Omnivore</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Nutritional Goals */}
          <Card>
            <CardHeader>
              <CardTitle>Nutritional Goals</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <Label>Daily Calories</Label>
                  <Input
                    id="dailyCalories"
                    type="number"
                    min="0"
                    {...register("dailyCalories", {
                      required: "Daily calories is required",
                      min: { value: 0, message: "Calories must be positive" }
                    })}
                  />
                </div>
                <div className="space-y-6">
                  <Label>Macronutrient Ratios</Label>
                  {[
                    { id: "macroProtein", label: "Protein" },
                    { id: "macroCarbs", label: "Carbs" },
                    { id: "macroFats", label: "Fats" }
                  ].map((macro) => (
                    <div key={macro.id} className="space-y-2">
                      <div className="flex justify-between">
                        <Label>{macro.label}</Label>
                        <span>{watch(macro.id as keyof PreferencesFormData) || 30}%</span>
                      </div>
                      <Slider
                        defaultValue={[30]}
                        max={100}
                        step={1}
                        value={[watch(macro.id as keyof PreferencesFormData) || 30]}
                        onValueChange={(value) => {
                          setValue(macro.id as keyof PreferencesFormData, value[0]);
                        }}
                      />
                    </div>
                  ))}
                </div>
                <div className="space-y-4">
                  <Label>Micronutrient Focus</Label>
                  <div className="grid grid-cols-2 gap-4">
                    {micronutrientOptions.map((nutrient) => (
                      <div key={nutrient} className="flex items-center space-x-2">
                        <Checkbox
                          id={nutrient}
                          {...register("micronutrientFocus")}
                          value={nutrient}
                        />
                        <Label htmlFor={nutrient}>{nutrient}</Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Meal Planning Flexibility */}
          <Card>
            <CardHeader>
              <CardTitle>Meal Planning Flexibility</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Meal Variety</Label>
                <Select
                  onValueChange={(value) => setValue("mealVariety", value)}
                  defaultValue="high"
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select meal variety" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high">High Variety</SelectItem>
                    <SelectItem value="simple">Keep It Simple</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Cooking Time</Label>
                <Select
                  onValueChange={(value) => setValue("cookingTime", value)}
                  defaultValue="30-60"
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select cooking time" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="<30">Less than 30 minutes</SelectItem>
                    <SelectItem value="30-60">30-60 minutes</SelectItem>
                    <SelectItem value=">60">More than 60 minutes</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="includeLeftovers"
                  {...register("includeLeftovers")}
                />
                <Label htmlFor="includeLeftovers">Include meals with leftovers</Label>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push("/dashboard")}
            >
              Cancel
            </Button>
            <Button type="submit">Save Changes</Button>
          </div>
        </div>
      </form>
    </div>
  );
}

