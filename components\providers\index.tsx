"use client";

import { ThemeProvider } from "next-themes";
import { Toaster } from "sonner";
import { UserProvider } from "./UserProvider";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider attribute="class" defaultTheme="light">
      <Toaster position="top-right" />
      <UserProvider>
        {children}
      </UserProvider>
    </ThemeProvider>
  );
}
