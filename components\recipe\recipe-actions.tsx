'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Star,
  Check,
  Share2,
  Printer,
  Download,
  Calendar,
  Edit,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from 'sonner';

interface RecipeActionsProps {
  recipeId: string;
  isFavorite: boolean;
  isCompleted?: boolean;
  onToggleFavorite: () => void;
  onMarkCompleted?: () => void;
  onEdit?: () => void;
  className?: string;
}

export function RecipeActions({
  recipeId,
  isFavorite,
  isCompleted = false,
  onToggleFavorite,
  onMarkCompleted,
  onEdit,
  className = ''
}: RecipeActionsProps) {
  const [isSharing, setIsSharing] = useState(false);

  // Handle sharing the recipe
  const handleShare = async () => {
    setIsSharing(true);

    try {
      // Create the share URL
      const shareUrl = `${window.location.origin}/meal-detail/${recipeId}`;

      // Check if the Web Share API is available
      if (navigator.share) {
        await navigator.share({
          title: 'Check out this recipe!',
          text: 'I found this amazing recipe you might like.',
          url: shareUrl,
        });
        toast.success('Recipe shared successfully!');
      } else {
        // Fallback to clipboard copy
        await navigator.clipboard.writeText(shareUrl);
        toast.success('Recipe link copied to clipboard!');
      }
    } catch (error) {
      console.error('Error sharing recipe:', error);
      toast.error('Failed to share recipe');
    } finally {
      setIsSharing(false);
    }
  };

  // Handle printing the recipe
  const handlePrint = () => {
    window.print();
  };

  // Handle adding to calendar
  const handleAddToCalendar = () => {
    toast('Calendar integration coming soon!');
  };

  // Handle exporting the recipe
  const handleExport = () => {
    toast('Export functionality coming soon!');
  };

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      <Button
        variant={isFavorite ? "default" : "outline"}
        onClick={onToggleFavorite}
      >
        <Star className={`h-4 w-4 mr-2 ${isFavorite ? "fill-primary" : ""}`} />
        {isFavorite ? "Saved" : "Save"}
      </Button>

      {onMarkCompleted && (
        <Button
          variant={isCompleted ? "default" : "outline"}
          onClick={onMarkCompleted}
        >
          <Check className={`h-4 w-4 mr-2 ${isCompleted ? "text-green-500" : ""}`} />
          {isCompleted ? "Completed" : "Mark as Completed"}
        </Button>
      )}

      <Button
        variant="outline"
        onClick={handleShare}
        disabled={isSharing}
      >
        <Share2 className="h-4 w-4 mr-2" />
        Share
      </Button>

      {onEdit && (
        <Button
          variant="outline"
          onClick={onEdit}
        >
          <Edit className="h-4 w-4 mr-2" />
          Edit
        </Button>
      )}

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="icon">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            Print Recipe
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export Recipe
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleAddToCalendar}>
            <Calendar className="h-4 w-4 mr-2" />
            Add to Meal Plan
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
