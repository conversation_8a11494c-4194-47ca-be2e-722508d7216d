import { NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

export async function POST(request: Request) {
  try {
    // Create a Supabase admin client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Get the user ID from the request body
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }

    // First, check the table structure
    const { data: tableInfo, error: tableError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_schema', 'public')
      .eq('table_name', 'recipes');

    if (tableError) {
      console.error('Error fetching table structure:', tableError);
      return NextResponse.json(
        {
          error: "Failed to fetch table structure",
          details: tableError.message
        },
        { status: 500 }
      );
    }

    console.log('Table structure:', tableInfo);

    // Create a simple recipe with only required fields
    const simpleRecipe = {
      user_id: userId,
      name: 'Test Recipe',
      description: 'A test recipe'
    };

    // Insert the recipe
    const { data, error } = await supabase
      .from('recipes')
      .insert(simpleRecipe)
      .select();

    if (error) {
      console.error('Error seeding recipe:', error);
      return NextResponse.json(
        {
          error: "Failed to seed recipe",
          details: error.message,
          hint: error.hint,
          code: error.code
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: "Recipe seeded successfully",
      data
    });
  } catch (error) {
    console.error("Unexpected error in debug/seed-recipe API:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
