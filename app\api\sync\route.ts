import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function POST(request: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case 'UPDATE_MEAL_PLAN':
        await handleMealPlanUpdate(data, session.user.id);
        break;
      case 'UPDATE_RECIPE':
        await handleRecipeUpdate(data, session.user.id);
        break;
      case 'UPDATE_SHOPPING_LIST':
        await handleShoppingListUpdate(data, session.user.id);
        break;
      default:
        return NextResponse.json(
          { error: 'Unknown action type' },
          { status: 400 }
        );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Sync error:', error);
    return NextResponse.json(
      { error: 'Sync failed' },
      { status: 500 }
    );
  }
}

async function handleMealPlanUpdate(data: any, userId: string) {
  await prisma.meal_plans.upsert({
    where: {
      id: data.id,
      user_id: userId,
    },
    update: {
      ...data,
      updated_at: new Date(),
    },
    create: {
      ...data,
      user_id: userId,
      created_at: new Date(),
      updated_at: new Date(),
    },
  });
}

async function handleRecipeUpdate(data: any, userId: string) {
  await prisma.recipes.upsert({
    where: {
      id: data.id,
      user_id: userId,
    },
    update: {
      ...data,
      updated_at: new Date(),
    },
    create: {
      ...data,
      user_id: userId,
      created_at: new Date(),
      updated_at: new Date(),
    },
  });
}

async function handleShoppingListUpdate(data: any, userId: string) {
  await prisma.shopping_lists.upsert({
    where: {
      id: data.id,
      user_id: userId,
    },
    update: {
      ...data,
      updated_at: new Date(),
    },
    create: {
      ...data,
      user_id: userId,
      created_at: new Date(),
      updated_at: new Date(),
    },
  });
}