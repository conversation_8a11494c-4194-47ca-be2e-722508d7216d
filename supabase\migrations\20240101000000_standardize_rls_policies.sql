-- Standardize RLS Policies Migration
-- This migration standardizes all RLS policies in the database to ensure consistent type casting
-- and proper access control for all tables.
--
-- Key improvements:
-- 1. Consistent type casting (user_id::text = auth.uid()::text) to avoid type mismatch errors
-- 2. Removal of duplicate policies
-- 3. Proper permissions for authenticated users
-- 4. Standardized policy naming

-- =============================================
-- Step 1: Fix meal_plans table
-- =============================================

-- Drop existing policies (including duplicates)
DROP POLICY IF EXISTS "Users can view their own meal plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can insert their own meal plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can update their own meal plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can delete their own meal plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can view their own meal_plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can insert their own meal_plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can update their own meal_plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can delete their own meal_plans" ON meal_plans;

-- Enable RLS on meal_plans table
ALTER TABLE meal_plans ENABLE ROW LEVEL SECURITY;

-- Create policies for meal_plans table with proper type casting
-- Note: Using explicit text casting on both sides to ensure type compatibility
CREATE POLICY "Users can view their own meal plans"
ON meal_plans
FOR SELECT
USING (user_id::text = auth.uid()::text);

CREATE POLICY "Users can insert their own meal plans"
ON meal_plans
FOR INSERT
WITH CHECK (user_id::text = auth.uid()::text);

CREATE POLICY "Users can update their own meal plans"
ON meal_plans
FOR UPDATE
USING (user_id::text = auth.uid()::text);

CREATE POLICY "Users can delete their own meal plans"
ON meal_plans
FOR DELETE
USING (user_id::text = auth.uid()::text);

-- Grant permissions to authenticated users
GRANT ALL ON meal_plans TO authenticated;

-- =============================================
-- Step 2: Fix meals table
-- =============================================

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own meals" ON meals;
DROP POLICY IF EXISTS "Users can insert their own meals" ON meals;
DROP POLICY IF EXISTS "Users can update their own meals" ON meals;
DROP POLICY IF EXISTS "Users can delete their own meals" ON meals;

-- Enable RLS on meals table
ALTER TABLE meals ENABLE ROW LEVEL SECURITY;

-- Create policies for meals table with proper type casting
-- Note: This table has a foreign key relationship to meal_plans
CREATE POLICY "Users can view their own meals"
ON meals
FOR SELECT
USING (meal_plan_id IN (
  SELECT id FROM meal_plans
  WHERE user_id::text = auth.uid()::text
));

CREATE POLICY "Users can insert their own meals"
ON meals
FOR INSERT
WITH CHECK (meal_plan_id IN (
  SELECT id FROM meal_plans
  WHERE user_id::text = auth.uid()::text
));

CREATE POLICY "Users can update their own meals"
ON meals
FOR UPDATE
USING (meal_plan_id IN (
  SELECT id FROM meal_plans
  WHERE user_id::text = auth.uid()::text
));

CREATE POLICY "Users can delete their own meals"
ON meals
FOR DELETE
USING (meal_plan_id IN (
  SELECT id FROM meal_plans
  WHERE user_id::text = auth.uid()::text
));

-- Grant permissions to authenticated users
GRANT ALL ON meals TO authenticated;

-- =============================================
-- Step 3: Fix pantry_items table
-- =============================================

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own pantry_items" ON pantry_items;
DROP POLICY IF EXISTS "Users can insert their own pantry_items" ON pantry_items;
DROP POLICY IF EXISTS "Users can update their own pantry_items" ON pantry_items;
DROP POLICY IF EXISTS "Users can delete their own pantry_items" ON pantry_items;

-- Enable RLS on pantry_items table
ALTER TABLE pantry_items ENABLE ROW LEVEL SECURITY;

-- Create policies for pantry_items table with proper type casting
CREATE POLICY "Users can view their own pantry_items"
ON pantry_items
FOR SELECT
USING (user_id::text = auth.uid()::text);

CREATE POLICY "Users can insert their own pantry_items"
ON pantry_items
FOR INSERT
WITH CHECK (user_id::text = auth.uid()::text);

CREATE POLICY "Users can update their own pantry_items"
ON pantry_items
FOR UPDATE
USING (user_id::text = auth.uid()::text);

CREATE POLICY "Users can delete their own pantry_items"
ON pantry_items
FOR DELETE
USING (user_id::text = auth.uid()::text);

-- Grant permissions to authenticated users
GRANT ALL ON pantry_items TO authenticated;

-- =============================================
-- Step 4: Fix shopping_lists table
-- =============================================

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own shopping_lists" ON shopping_lists;
DROP POLICY IF EXISTS "Users can insert their own shopping_lists" ON shopping_lists;
DROP POLICY IF EXISTS "Users can update their own shopping_lists" ON shopping_lists;
DROP POLICY IF EXISTS "Users can delete their own shopping_lists" ON shopping_lists;

-- Enable RLS on shopping_lists table
ALTER TABLE shopping_lists ENABLE ROW LEVEL SECURITY;

-- Create policies for shopping_lists table with proper type casting
CREATE POLICY "Users can view their own shopping_lists"
ON shopping_lists
FOR SELECT
USING (user_id::text = auth.uid()::text);

CREATE POLICY "Users can insert their own shopping_lists"
ON shopping_lists
FOR INSERT
WITH CHECK (user_id::text = auth.uid()::text);

CREATE POLICY "Users can update their own shopping_lists"
ON shopping_lists
FOR UPDATE
USING (user_id::text = auth.uid()::text);

CREATE POLICY "Users can delete their own shopping_lists"
ON shopping_lists
FOR DELETE
USING (user_id::text = auth.uid()::text);

-- Grant permissions to authenticated users
GRANT ALL ON shopping_lists TO authenticated;

-- =============================================
-- Step 5: Fix shopping_items table
-- =============================================

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own shopping_items" ON shopping_items;
DROP POLICY IF EXISTS "Users can insert their own shopping_items" ON shopping_items;
DROP POLICY IF EXISTS "Users can update their own shopping_items" ON shopping_items;
DROP POLICY IF EXISTS "Users can delete their own shopping_items" ON shopping_items;

-- Enable RLS on shopping_items table
ALTER TABLE shopping_items ENABLE ROW LEVEL SECURITY;

-- Create policies for shopping_items table with proper type casting
-- Note: This table has a foreign key relationship to shopping_lists
CREATE POLICY "Users can view their own shopping_items"
ON shopping_items
FOR SELECT
USING (shopping_list_id IN (
  SELECT id FROM shopping_lists
  WHERE user_id::text = auth.uid()::text
));

CREATE POLICY "Users can insert their own shopping_items"
ON shopping_items
FOR INSERT
WITH CHECK (shopping_list_id IN (
  SELECT id FROM shopping_lists
  WHERE user_id::text = auth.uid()::text
));

CREATE POLICY "Users can update their own shopping_items"
ON shopping_items
FOR UPDATE
USING (shopping_list_id IN (
  SELECT id FROM shopping_lists
  WHERE user_id::text = auth.uid()::text
));

CREATE POLICY "Users can delete their own shopping_items"
ON shopping_items
FOR DELETE
USING (shopping_list_id IN (
  SELECT id FROM shopping_lists
  WHERE user_id::text = auth.uid()::text
));

-- Grant permissions to authenticated users
GRANT ALL ON shopping_items TO authenticated;

-- =============================================
-- Step 6: Fix recipes table
-- =============================================

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own recipes" ON recipes;
DROP POLICY IF EXISTS "Users can insert their own recipes" ON recipes;
DROP POLICY IF EXISTS "Users can update their own recipes" ON recipes;
DROP POLICY IF EXISTS "Users can delete their own recipes" ON recipes;

-- Enable RLS on recipes table
ALTER TABLE recipes ENABLE ROW LEVEL SECURITY;

-- Create policies for recipes table with proper type casting
CREATE POLICY "Users can view their own recipes"
ON recipes
FOR SELECT
USING (user_id::text = auth.uid()::text);

CREATE POLICY "Users can insert their own recipes"
ON recipes
FOR INSERT
WITH CHECK (user_id::text = auth.uid()::text);

CREATE POLICY "Users can update their own recipes"
ON recipes
FOR UPDATE
USING (user_id::text = auth.uid()::text);

CREATE POLICY "Users can delete their own recipes"
ON recipes
FOR DELETE
USING (user_id::text = auth.uid()::text);

-- Grant permissions to authenticated users
GRANT ALL ON recipes TO authenticated;

-- =============================================
-- Step 7: Fix user_preferences table
-- =============================================

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own user_preferences" ON user_preferences;
DROP POLICY IF EXISTS "Users can insert their own user_preferences" ON user_preferences;
DROP POLICY IF EXISTS "Users can update their own user_preferences" ON user_preferences;
DROP POLICY IF EXISTS "Users can delete their own user_preferences" ON user_preferences;

-- Enable RLS on user_preferences table
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- Create policies for user_preferences table with proper type casting
CREATE POLICY "Users can view their own user_preferences"
ON user_preferences
FOR SELECT
USING (user_id::text = auth.uid()::text);

CREATE POLICY "Users can insert their own user_preferences"
ON user_preferences
FOR INSERT
WITH CHECK (user_id::text = auth.uid()::text);

CREATE POLICY "Users can update their own user_preferences"
ON user_preferences
FOR UPDATE
USING (user_id::text = auth.uid()::text);

CREATE POLICY "Users can delete their own user_preferences"
ON user_preferences
FOR DELETE
USING (user_id::text = auth.uid()::text);

-- Grant permissions to authenticated users
GRANT ALL ON user_preferences TO authenticated;

-- =============================================
-- Step 8: Fix users table
-- =============================================

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own users" ON users;
DROP POLICY IF EXISTS "Users can insert their own users" ON users;
DROP POLICY IF EXISTS "Users can update their own users" ON users;
DROP POLICY IF EXISTS "Users can delete their own users" ON users;

-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policies for users table with proper type casting
CREATE POLICY "Users can view their own users"
ON users
FOR SELECT
USING (id::text = auth.uid()::text);

CREATE POLICY "Users can insert their own users"
ON users
FOR INSERT
WITH CHECK (id::text = auth.uid()::text);

CREATE POLICY "Users can update their own users"
ON users
FOR UPDATE
USING (id::text = auth.uid()::text);

CREATE POLICY "Users can delete their own users"
ON users
FOR DELETE
USING (id::text = auth.uid()::text);

-- Grant permissions to authenticated users
GRANT ALL ON users TO authenticated;

-- =============================================
-- Step 9: Fix meal_plan_assignments table (if exists)
-- =============================================

-- Check if the table exists before trying to create policies
DO $$
BEGIN
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'meal_plan_assignments') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "Users can view their own meal_plan_assignments" ON meal_plan_assignments;
        DROP POLICY IF EXISTS "Users can insert their own meal_plan_assignments" ON meal_plan_assignments;
        DROP POLICY IF EXISTS "Users can update their own meal_plan_assignments" ON meal_plan_assignments;
        DROP POLICY IF EXISTS "Users can delete their own meal_plan_assignments" ON meal_plan_assignments;

        -- Enable RLS on meal_plan_assignments table
        ALTER TABLE meal_plan_assignments ENABLE ROW LEVEL SECURITY;

        -- Create policies for meal_plan_assignments table with proper type casting
        EXECUTE 'CREATE POLICY "Users can view their own meal_plan_assignments"
                ON meal_plan_assignments
                FOR SELECT
                USING (user_id::text = auth.uid()::text)';

        EXECUTE 'CREATE POLICY "Users can insert their own meal_plan_assignments"
                ON meal_plan_assignments
                FOR INSERT
                WITH CHECK (user_id::text = auth.uid()::text)';

        EXECUTE 'CREATE POLICY "Users can update their own meal_plan_assignments"
                ON meal_plan_assignments
                FOR UPDATE
                USING (user_id::text = auth.uid()::text)';

        EXECUTE 'CREATE POLICY "Users can delete their own meal_plan_assignments"
                ON meal_plan_assignments
                FOR DELETE
                USING (user_id::text = auth.uid()::text)';

        -- Grant permissions to authenticated users
        GRANT ALL ON meal_plan_assignments TO authenticated;
    END IF;
END
$$;

-- =============================================
-- Step 10: Disable RLS for system tables
-- =============================================

-- Disable RLS for Prisma migrations table
ALTER TABLE _prisma_migrations DISABLE ROW LEVEL SECURITY;

-- =============================================
-- Step 11: Grant basic schema permissions
-- =============================================

-- Grant schema usage to all relevant roles
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
