"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ShoppingCart } from "lucide-react";

interface PlanSummaryProps {
  totalCost: number;
  averageCalories: number;
  macros: {
    protein: number;
    carbs: number;
    fats: number;
  };
  onGenerateShoppingList: () => void;
  viewMode: 'week' | 'month';
  mealCount: number;
  daysWithMeals: number;
  totalDays: number;
}

export function PlanSummary({
  totalCost,
  averageCalories,
  macros,
  onGenerateShoppingList,
  viewMode,
  mealCount,
  daysWithMeals,
  totalDays
}: PlanSummaryProps) {
  const isWeekly = viewMode === 'week';

  // Calculate completion percentage
  const completionPercentage = Math.round((daysWithMeals / totalDays) * 100);

  return (
    <div className="flex flex-col md:flex-row gap-6 bg-white rounded-lg overflow-hidden">
      <div className="flex-1">
        <h2 className="text-lg font-semibold mb-4">
          {isWeekly ? 'Weekly Summary' : 'Monthly Summary'}
        </h2>

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
              <p className="text-sm text-muted-foreground">Total Cost</p>
              <p className="text-2xl font-bold">${totalCost.toFixed(2)}</p>
            </div>

            <div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
              <p className="text-sm text-muted-foreground">Average Calories</p>
              <p className="text-2xl font-bold">{averageCalories}</p>
            </div>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
            <div className="flex justify-between items-center mb-1">
              <p className="text-sm text-muted-foreground">
                {isWeekly ? 'Week Completion' : 'Month Completion'}
              </p>
              <span className="text-sm font-medium bg-primary/10 text-primary px-2 py-0.5 rounded">{completionPercentage}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5 mb-2">
              <div
                className={`h-2.5 rounded-full ${completionPercentage < 30 ? 'bg-red-500' : completionPercentage < 70 ? 'bg-yellow-500' : 'bg-green-500'}`}
                style={{ width: `${completionPercentage}%` }}
              ></div>
            </div>
            <div className="flex justify-between items-center">
              <p className="text-xs text-muted-foreground">
                {daysWithMeals} of {totalDays} days planned
              </p>
              <p className="text-xs font-medium">{mealCount} meals total</p>
            </div>
          </div>

          <Button
            className="w-full mt-2"
            variant="default"
            onClick={onGenerateShoppingList}
          >
            <ShoppingCart className="mr-2 h-4 w-4" />
            Generate Shopping List
          </Button>
        </div>
      </div>

      <div className="flex-1 bg-gray-50 p-6 border-l">
        <h3 className="text-sm font-medium mb-4 text-center">Macronutrient Breakdown</h3>
        <div className="flex flex-col md:flex-row items-center justify-center gap-6">
          <div className="relative w-48 h-48">
            {/* Donut chart */}
            <svg viewBox="0 0 100 100" className="w-full h-full drop-shadow-sm">
              <circle
                cx="50"
                cy="50"
                r="40"
                fill="none"
                stroke="#f0f0f0"
                strokeWidth="20"
              />

              {/* Protein segment */}
              <circle
                cx="50"
                cy="50"
                r="40"
                fill="none"
                stroke="#f97316"
                strokeWidth="20"
                strokeDasharray={`${macros.protein * 2.51} 251`}
                strokeDashoffset="0"
                transform="rotate(-90 50 50)"
              />

              {/* Carbs segment */}
              <circle
                cx="50"
                cy="50"
                r="40"
                fill="none"
                stroke="#10b981"
                strokeWidth="20"
                strokeDasharray={`${macros.carbs * 2.51} 251`}
                strokeDashoffset={`${-macros.protein * 2.51}`}
                transform="rotate(-90 50 50)"
              />

              {/* Fats segment */}
              <circle
                cx="50"
                cy="50"
                r="40"
                fill="none"
                stroke="#1e40af"
                strokeWidth="20"
                strokeDasharray={`${macros.fats * 2.51} 251`}
                strokeDashoffset={`${-(macros.protein + macros.carbs) * 2.51}`}
                transform="rotate(-90 50 50)"
              />

              {/* Center text */}
              <text
                x="50"
                y="45"
                textAnchor="middle"
                dominantBaseline="middle"
                className="text-sm font-medium"
                fill="#666"
              >
                {mealCount}
              </text>
              <text
                x="50"
                y="58"
                textAnchor="middle"
                dominantBaseline="middle"
                className="text-xs"
                fill="#666"
              >
                meals
              </text>
            </svg>
          </div>

          <div className="flex flex-col gap-2 bg-white p-3 rounded-lg border border-gray-100 w-full max-w-xs">
            <div className="flex items-center justify-between p-1.5 hover:bg-orange-50 rounded transition-colors">
              <div className="flex items-center">
                <div className="w-4 h-4 rounded-full bg-orange-500 mr-2"></div>
                <span className="text-sm">Protein</span>
              </div>
              <span className="text-sm font-medium bg-orange-100 text-orange-800 px-2 py-0.5 rounded-md">{macros.protein}%</span>
            </div>
            <div className="flex items-center justify-between p-1.5 hover:bg-emerald-50 rounded transition-colors">
              <div className="flex items-center">
                <div className="w-4 h-4 rounded-full bg-emerald-500 mr-2"></div>
                <span className="text-sm">Carbs</span>
              </div>
              <span className="text-sm font-medium bg-emerald-100 text-emerald-800 px-2 py-0.5 rounded-md">{macros.carbs}%</span>
            </div>
            <div className="flex items-center justify-between p-1.5 hover:bg-blue-50 rounded transition-colors">
              <div className="flex items-center">
                <div className="w-4 h-4 rounded-full bg-blue-800 mr-2"></div>
                <span className="text-sm">Fats</span>
              </div>
              <span className="text-sm font-medium bg-blue-100 text-blue-800 px-2 py-0.5 rounded-md">{macros.fats}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
