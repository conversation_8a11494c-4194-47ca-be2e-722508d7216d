"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
// Import the new hook instead of the old one
import { useSupabase } from "@/components/supabase-provider";
import { toast } from "sonner";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";

// Import tab components
import ProfileTab from "./components/ProfileTab";
import DietaryPreferencesTab from "./components/DietaryPreferencesTab";
import HouseholdTab from "./components/HouseholdTab";
import AccountTab from "./components/AccountTab";

export default function SettingsPage() {
  const router = useRouter();
  // Use the new hook instead of the old one
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("profile");
  const [userData, setUserData] = useState<any>(null);
  const [userPreferences, setUserPreferences] = useState<any>(null);

  useEffect(() => {
    // Don't fetch if Supabase is still loading or not available
    if (isSupabaseLoading || !supabase) {
      return;
    }

    const fetchUserData = async () => {
      try {
        setIsLoading(true);

        // Get current user
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError || !user) {
          toast.error("You must be logged in to view this page");
          router.push("/login");
          return;
        }

        console.log("Current user:", user);

        // Fetch user data from the users table
        const { data: userData, error: userDataError } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();

        if (userDataError) {
          console.error("Error fetching user data:", userDataError);
          toast.error("Failed to load user data");
        } else {
          console.log("User data from Supabase:", userData);
          setUserData(userData);
        }

        // Fetch user preferences from the user_preferences table
        const { data: preferencesData, error: preferencesError } = await supabase
          .from('user_preferences')
          .select('*')
          .eq('user_id', user.id)
          .single();

        if (preferencesError && preferencesError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
          console.error("Error fetching user preferences:", preferencesError);
          toast.error("Failed to load user preferences");
        } else {
          console.log("User preferences from Supabase:", preferencesData || {});
          setUserPreferences(preferencesData || {});
        }

      } catch (error) {
        console.error("Error in fetchUserData:", error);
        toast.error("An unexpected error occurred");
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [supabase, isSupabaseLoading, router]);

  const handleBack = () => {
    router.push("/dashboard");
  };

  // Show loading state if either Supabase is loading or our component is loading
  if (isSupabaseLoading || isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center mb-6">
          <Button variant="ghost" onClick={handleBack} className="mr-2">
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <h1 className="text-2xl font-bold">Settings</h1>
          {isSupabaseLoading && (
            <p className="ml-4 text-muted-foreground">Initializing database connection...</p>
          )}
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={handleBack} className="mr-2">
          <ChevronLeft className="h-4 w-4 mr-1" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">Settings</h1>
      </div>

      <Tabs defaultValue="profile" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="dietary">Dietary Preferences</TabsTrigger>
          <TabsTrigger value="household">Household</TabsTrigger>
          <TabsTrigger value="account">Account</TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <ProfileTab
            userData={userData}
            supabase={supabase}
            onUpdate={(updatedData) => setUserData({...userData, ...updatedData})}
          />
        </TabsContent>

        <TabsContent value="dietary">
          <DietaryPreferencesTab
            userData={userData}
            userPreferences={userPreferences}
            supabase={supabase}
            onUpdate={(updatedPreferences) => setUserPreferences({...userPreferences, ...updatedPreferences})}
          />
        </TabsContent>

        <TabsContent value="household">
          <HouseholdTab
            userData={userData}
            userPreferences={userPreferences}
            supabase={supabase}
            onUpdate={(updatedData) => {
              // Update both user data and preferences as household settings might affect both
              setUserData({...userData, household_size: updatedData.household_size});
              setUserPreferences({...userPreferences, ...updatedData});
            }}
          />
        </TabsContent>

        <TabsContent value="account">
          <AccountTab
            userData={userData}
            supabase={supabase}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
