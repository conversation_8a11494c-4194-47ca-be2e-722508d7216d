import { NextRequest, NextResponse } from 'next/server';
import { searchRecipesServer } from '@/lib/api/edamam-server';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query') || '';
    const diet = searchParams.get('diet')?.split(',').filter(Boolean) || [];
    const health = searchParams.get('health')?.split(',').filter(Boolean) || [];
    const calories = searchParams.get('calories') || '';
    const mealType = searchParams.get('mealType')?.split(',').filter(Boolean) || [];
    const cuisineType = searchParams.get('cuisineType')?.split(',').filter(Boolean) || [];
    const excluded = searchParams.get('excluded')?.split(',').filter(Boolean) || [];
    const random = searchParams.get('random') === 'true';

    console.log('Edamam search API route called with params:', {
      query,
      diet,
      health,
      calories,
      mealType,
      cuisineType,
      excluded,
      random
    });

    // Check if we have valid API keys
    if (!process.env.NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES || !process.env.NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES) {
      console.error('Missing Edamam API keys');
      return NextResponse.json(
        { error: 'Missing Edamam API keys', hits: [] },
        { status: 500 }
      );
    }

    try {
      const results = await searchRecipesServer({
        query,
        diet,
        health,
        calories,
        mealType,
        cuisineType,
        excluded,
        random
      });

      return NextResponse.json(results);
    } catch (apiError: any) {
      console.error('Error calling Edamam API:', apiError);

      // If we have a response from the API, return that status code
      if (apiError.response) {
        return NextResponse.json(
          { error: apiError.message, hits: [] },
          { status: apiError.response.status }
        );
      }

      // Otherwise, return a generic 500 error
      return NextResponse.json(
        { error: apiError.message || 'An error occurred while searching recipes', hits: [] },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Error in Edamam search API route:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while searching recipes', hits: [] },
      { status: 500 }
    );
  }
}
