-- Fix meal_plans table RLS policies
-- This migration ensures that the meal_plans table has the correct RLS policies
-- with proper type casting to avoid permission denied errors.

-- Step 1: Drop existing policies (including any duplicates)
DROP POLICY IF EXISTS "Users can view their own meal plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can insert their own meal plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can update their own meal plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can delete their own meal plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can view their own meal_plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can insert their own meal_plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can update their own meal_plans" ON meal_plans;
DROP POLICY IF EXISTS "Users can delete their own meal_plans" ON meal_plans;

-- Step 2: Ensure RLS is enabled on the meal_plans table
ALTER TABLE meal_plans ENABLE ROW LEVEL SECURITY;

-- Step 3: Create policies for meal_plans table with proper type casting and role assignment
-- Note: Using explicit text casting on both sides to ensure type compatibility
-- Note: Using TO authenticated to ensure policies apply to authenticated users
CREATE POLICY "Users can view their own meal plans"
ON meal_plans
FOR SELECT
TO authenticated
USING (user_id::text = auth.uid()::text);

CREATE POLICY "Users can insert their own meal plans"
ON meal_plans
FOR INSERT
TO authenticated
WITH CHECK (user_id::text = auth.uid()::text);

CREATE POLICY "Users can update their own meal plans"
ON meal_plans
FOR UPDATE
TO authenticated
USING (user_id::text = auth.uid()::text);

CREATE POLICY "Users can delete their own meal plans"
ON meal_plans
FOR DELETE
TO authenticated
USING (user_id::text = auth.uid()::text);

-- Step 4: Grant permissions to authenticated users
GRANT ALL ON meal_plans TO authenticated;

-- Step 5: Check the data type of the user_id column
DO $$
DECLARE
    column_type TEXT;
BEGIN
    SELECT data_type INTO column_type
    FROM information_schema.columns
    WHERE table_schema = 'public'
      AND table_name = 'meal_plans'
      AND column_name = 'user_id';

    RAISE NOTICE 'meal_plans.user_id data type: %', column_type;

    -- If user_id is not UUID, log a warning
    IF column_type != 'uuid' THEN
        RAISE WARNING 'meal_plans.user_id is not UUID type. Current type: %', column_type;
    END IF;
END $$;
