-- Migration: Add missing tables and fix schema issues
-- Date: 2025-06-28
-- Description: Add remaining missing tables (favorite_meals, meal_notes, user_preferences) and fix schema issues

-- Create favorite_meals table
CREATE TABLE IF NOT EXISTS public.favorite_meals (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    recipe_id uuid REFERENCES public.recipes(id) ON DELETE CASCADE,
    created_at timestamp with time zone DEFAULT now(),
    UNIQUE(user_id, recipe_id)
);

-- Create meal_notes table
CREATE TABLE IF NOT EXISTS public.meal_notes (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    meal_plan_id uuid REFERENCES public.meal_plans(id) ON DELETE CASCADE,
    meal_id text NOT NULL,
    note text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Create user_preferences table (extended preferences beyond basic users table)
CREATE TABLE IF NOT EXISTS public.user_preferences (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    cooking_skill_level text DEFAULT 'intermediate' CHECK (cooking_skill_level IN ('beginner', 'intermediate', 'advanced')),
    max_cooking_time integer DEFAULT 30, -- minutes
    preferred_cuisines text[] DEFAULT ARRAY[]::text[],
    excluded_ingredients text[] DEFAULT ARRAY[]::text[],
    notification_preferences jsonb DEFAULT '{"email": true, "push": false, "meal_reminders": true}'::jsonb,
    privacy_settings jsonb DEFAULT '{"data_sharing": false, "analytics": true}'::jsonb,
    units_preference text DEFAULT 'metric' CHECK (units_preference IN ('metric', 'imperial')),
    currency_preference text DEFAULT 'USD',
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Create search_history table for analytics
CREATE TABLE IF NOT EXISTS public.search_history (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    search_query text NOT NULL,
    search_type text NOT NULL CHECK (search_type IN ('recipe', 'ingredient', 'cuisine')),
    results_count integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now()
);

-- Enable RLS on all new tables
ALTER TABLE public.favorite_meals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.meal_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.search_history ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for favorite_meals
CREATE POLICY "Users can view their own favorite meals"
ON public.favorite_meals FOR SELECT
TO authenticated
USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own favorite meals"
ON public.favorite_meals FOR INSERT
TO authenticated
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete their own favorite meals"
ON public.favorite_meals FOR DELETE
TO authenticated
USING (user_id = auth.uid());

-- Create RLS policies for meal_notes
CREATE POLICY "Users can view their own meal notes"
ON public.meal_notes FOR SELECT
TO authenticated
USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own meal notes"
ON public.meal_notes FOR INSERT
TO authenticated
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own meal notes"
ON public.meal_notes FOR UPDATE
TO authenticated
USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own meal notes"
ON public.meal_notes FOR DELETE
TO authenticated
USING (user_id = auth.uid());

-- Create RLS policies for user_preferences
CREATE POLICY "Users can view their own preferences"
ON public.user_preferences FOR SELECT
TO authenticated
USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own preferences"
ON public.user_preferences FOR INSERT
TO authenticated
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own preferences"
ON public.user_preferences FOR UPDATE
TO authenticated
USING (user_id = auth.uid());

-- Create RLS policies for search_history
CREATE POLICY "Users can view their own search history"
ON public.search_history FOR SELECT
TO authenticated
USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own search history"
ON public.search_history FOR INSERT
TO authenticated
WITH CHECK (user_id = auth.uid());

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS favorite_meals_user_id_idx ON public.favorite_meals(user_id);
CREATE INDEX IF NOT EXISTS favorite_meals_recipe_id_idx ON public.favorite_meals(recipe_id);

CREATE INDEX IF NOT EXISTS meal_notes_user_id_idx ON public.meal_notes(user_id);
CREATE INDEX IF NOT EXISTS meal_notes_meal_plan_id_idx ON public.meal_notes(meal_plan_id);

CREATE INDEX IF NOT EXISTS user_preferences_user_id_idx ON public.user_preferences(user_id);

CREATE INDEX IF NOT EXISTS search_history_user_id_idx ON public.search_history(user_id);
CREATE INDEX IF NOT EXISTS search_history_created_at_idx ON public.search_history(created_at);

-- Add missing performance indexes from diagnostic report
CREATE INDEX IF NOT EXISTS meal_plans_user_id_status_idx ON public.meal_plans(user_id, status);
CREATE INDEX IF NOT EXISTS recipes_user_id_idx ON public.recipes(user_id);

-- Create updated_at triggers for new tables
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_meal_notes_updated_at 
BEFORE UPDATE ON public.meal_notes 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at 
BEFORE UPDATE ON public.user_preferences 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions to authenticated users
GRANT ALL ON public.favorite_meals TO authenticated;
GRANT ALL ON public.meal_notes TO authenticated;
GRANT ALL ON public.user_preferences TO authenticated;
GRANT ALL ON public.search_history TO authenticated;
