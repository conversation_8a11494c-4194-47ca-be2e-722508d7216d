
import { openDB, DBSchema, IDBPDatabase } from 'idb';

interface MealPlannerDB extends DBSchema {
  offlineActions: {
    key: string;
    value: {
      id: string;
      action: string;
      data: any;
      timestamp: number;
      synced: boolean;
    };
  };
  mealPlans: {
    key: string;
    value: any;
  };
  recipes: {
    key: string;
    value: any;
  };
}

export class OfflineSync {
  private db: IDBPDatabase<MealPlannerDB> | null = null;
  private syncInProgress = false;

  async initialize() {
    this.db = await openDB<MealPlannerDB>('mealplanner', 1, {
      upgrade(db) {
        db.createObjectStore('offlineActions', { keyPath: 'id' });
        db.createObjectStore('mealPlans', { keyPath: 'id' });
        db.createObjectStore('recipes', { keyPath: 'id' });
      },
    });

    // Start listening for online/offline events
    window.addEventListener('online', () => this.syncWithServer());
    window.addEventListener('offline', () => this.handleOffline());
  }

  async queueAction(action: string, data: any) {
    if (!this.db) return;

    const offlineAction = {
      id: crypto.randomUUID(),
      action,
      data,
      timestamp: Date.now(),
      synced: false,
    };

    await this.db.add('offlineActions', offlineAction);

    if (navigator.onLine) {
      this.syncWithServer();
    }
  }

  private async syncWithServer() {
    if (this.syncInProgress || !navigator.onLine || !this.db) return;

    this.syncInProgress = true;

    try {
      const tx = this.db.transaction('offlineActions', 'readwrite');
      const store = tx.objectStore('offlineActions');
      const actions = await store.getAll();

      for (const action of actions) {
        if (action.synced) continue;

        try {
          await this.performSync(action);
          action.synced = true;
          await store.put(action);
        } catch (error) {
          console.error('Sync error for action:', action, error);
        }
      }

      // Clean up synced actions older than 24 hours
      const yesterday = Date.now() - (24 * 60 * 60 * 1000);
      const oldActions = actions.filter(
        action => action.synced && action.timestamp < yesterday
      );
      
      for (const action of oldActions) {
        await store.delete(action.id);
      }

    } finally {
      this.syncInProgress = false;
    }
  }

  private async performSync(action: any) {
    const endpoint = this.getEndpointForAction(action.action);
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(action.data),
    });

    if (!response.ok) {
      throw new Error(`Sync failed: ${response.statusText}`);
    }

    return response.json();
  }

  private getEndpointForAction(action: string): string {
    const endpoints: Record<string, string> = {
      UPDATE_MEAL_PLAN: '/api/meal-plans/sync',
      UPDATE_RECIPE: '/api/recipes/sync',
      UPDATE_SHOPPING_LIST: '/api/shopping-list/sync',
    };

    return endpoints[action] || '/api/sync';
  }

  private handleOffline() {
    toast({
      title: "You're offline",
      description: "Changes will be saved and synced when you're back online.",
      variant: "warning",
    });
  }

  async getCachedData(storeName: keyof MealPlannerDB, id: string) {
    if (!this.db) return null;
    return await this.db.get(storeName, id);
  }

  async setCachedData(storeName: keyof MealPlannerDB, data: any) {
    if (!this.db) return;
    await this.db.put(storeName, data);
  }
}

export const offlineSync = new OfflineSync();