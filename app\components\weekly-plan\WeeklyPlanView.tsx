'use client';

import { useState, useEffect } from 'react';
import { DayPanel } from './DayPanel';
import { WeeklyPlanHeader } from './WeeklyPlanHeader';
import { WeeklyPlanSummary } from './WeeklyPlanSummary';
import { Skeleton } from '@/components/ui/skeleton';
import { MealPlan } from '@/types/meal-plan';

interface WeeklyPlanViewProps {
  mealPlan: MealPlan;
}

export function WeeklyPlanView({ mealPlan }: WeeklyPlanViewProps) {
  const [expandedDay, setExpandedDay] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (mealPlan) {
      try {
        setIsLoading(true);
        setError(null);
      } catch (err) {
        console.error('Error processing meal plan:', err);
        setError(err instanceof Error ? err : new Error('Failed to process meal plan data'));
      } finally {
        setIsLoading(false);
      }
    } else {
      setIsLoading(false);
    }
  }, [mealPlan]);

  const toggleDay = (day: string) => {
    setExpandedDay(expandedDay === day ? null : day);
  };

  if (isLoading) {
    return <WeeklyPlanSkeleton />;
  }

  if (error) {
    return (
      <div className="text-red-500 p-4">
        Error loading meal plan: {error.message}
      </div>
    );
  }

  // Ensure we have valid meal plan data
  if (!mealPlan) {
    return (
      <div className="text-gray-500 p-4">
        No meal plan available
      </div>
    );
  }

  // Ensure meal_data is properly structured
  if (!mealPlan.meal_data) {
    mealPlan.meal_data = {
      mealPlan: {
        week: [],
        summary: {
          totalCost: mealPlan.total_cost || 0,
          averageCalories: 0,
          macros: { protein: '0g', carbs: '0g', fats: '0g' }
        }
      }
    };
  } else if (!mealPlan.meal_data.mealPlan) {
    mealPlan.meal_data.mealPlan = {
      week: [],
      summary: {
        totalCost: mealPlan.total_cost || 0,
        averageCalories: 0,
        macros: { protein: '0g', carbs: '0g', fats: '0g' }
      }
    };
  } else if (!mealPlan.meal_data.mealPlan.week) {
    mealPlan.meal_data.mealPlan.week = [];
  } else if (!mealPlan.meal_data.mealPlan.summary) {
    mealPlan.meal_data.mealPlan.summary = {
      totalCost: mealPlan.total_cost || 0,
      averageCalories: 0,
      macros: { protein: '0g', carbs: '0g', fats: '0g' }
    };
  } else if (!mealPlan.meal_data.mealPlan.summary.totalCost) {
    mealPlan.meal_data.mealPlan.summary.totalCost = mealPlan.total_cost || 0;
  } else if (!mealPlan.meal_data.mealPlan.summary.macros) {
    mealPlan.meal_data.mealPlan.summary.macros = { protein: '0g', carbs: '0g', fats: '0g' };
  }

  // If there are no weeks in the meal plan, show a message
  if (mealPlan.meal_data.mealPlan.week.length === 0) {
    return (
      <div className="text-gray-500 p-4">
        This meal plan has no daily meal data available
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <WeeklyPlanHeader
        startDate={mealPlan.start_date}
        endDate={mealPlan.end_date}
      />

      <div className="space-y-4">
        {mealPlan.meal_data.mealPlan.week.map((dayPlan) => (
          <DayPanel
            key={dayPlan.day}
            day={dayPlan.day}
            isExpanded={expandedDay === dayPlan.day}
            onToggle={() => toggleDay(dayPlan.day)}
            meals={dayPlan.meals}
          />
        ))}
      </div>

      <WeeklyPlanSummary
        totalCost={mealPlan.total_cost}
        averageCalories={mealPlan.meal_data.mealPlan.summary.averageCalories}
        macros={mealPlan.meal_data.mealPlan.summary.macros}
      />
    </div>
  );
}

function WeeklyPlanSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-8 w-24" />
      </div>

      <div className="space-y-4">
        {Array.from({ length: 7 }).map((_, i) => (
          <div key={i} className="border rounded-lg p-4">
            <div className="flex justify-between items-center">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-6 w-24" />
            </div>
          </div>
        ))}
      </div>

      <div className="border rounded-lg p-4">
        <Skeleton className="h-6 w-full mb-4" />
        <div className="grid grid-cols-3 gap-4">
          <Skeleton className="h-16" />
          <Skeleton className="h-16" />
          <Skeleton className="h-16" />
        </div>
      </div>
    </div>
  );
}








