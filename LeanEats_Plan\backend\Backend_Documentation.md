# LeanEats Backend Documentation

**Version:** 1.0
**Date:** June 28, 2025
**Last Updated:** Based on codebase analysis and schema review

---

## **1. Overview**

The LeanEats backend is built on a modern serverless architecture using Supabase as the primary database and authentication provider, with Next.js API routes handling business logic and external service integrations.

### **Technology Stack**
- **Database:** Supabase (PostgreSQL)
- **Authentication:** Supabase Auth
- **API Framework:** Next.js API Routes
- **ORM:** Prisma (with Supabase integration)
- **External APIs:** OpenAI, Edamam, Spoonacular
- **Deployment:** Vercel (recommended)

---

## **2. Database Schema Reference**

### **2.1 Core Tables**

#### **`users` Table**
```sql
CREATE TABLE public.users (
    id text PRIMARY KEY,                    -- User ID from Supabase Auth
    email text NOT NULL UNIQUE,            -- User email address
    weekly_budget decimal DEFAULT 0,       -- Weekly grocery budget
    household_size integer DEFAULT 1,      -- Number of people in household
    dietary_restrictions text[] DEFAULT ARRAY[]::text[], -- Dietary restrictions array
    created_at timestamptz DEFAULT now(),  -- Account creation timestamp
    updated_at timestamptz DEFAULT now()   -- Last update timestamp
);
```
**RLS Policies:**
- Users can view/update their own records only
- Policies use `auth.uid()::text = id::text` for user matching

#### **`meal_plans` Table**
```sql
CREATE TABLE public.meal_plans (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id text REFERENCES public.users(id) NOT NULL,
    start_date timestamptz NOT NULL,       -- Plan start date
    end_date timestamptz NOT NULL,         -- Plan end date  
    total_cost decimal NOT NULL,           -- Estimated total cost
    meal_data jsonb NOT NULL,              -- Complete meal plan data
    status text DEFAULT 'active',          -- Plan status (active, draft, archived)
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);
```
**Indexes:**
- `meal_plans_user_id_idx` on user_id
- `meal_plans_status_idx` on status

#### **`recipes` Table**
```sql
CREATE TABLE public.recipes (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id text REFERENCES public.users(id) NOT NULL,
    name text NOT NULL,                    -- Recipe name
    description text,                      -- Recipe description
    instructions jsonb,                    -- Cooking instructions
    ingredients jsonb,                     -- Ingredients list with quantities
    prep_time integer,                     -- Preparation time in minutes
    cook_time integer,                     -- Cooking time in minutes
    servings integer,                      -- Number of servings
    calories_per_serving integer,          -- Calories per serving
    protein_per_serving double precision,  -- Protein in grams
    carbs_per_serving double precision,    -- Carbohydrates in grams
    fat_per_serving double precision,      -- Fat in grams
    image_url text,                        -- Recipe image URL
    tags text[],                          -- Recipe tags array
    is_favorite boolean DEFAULT false,     -- User favorite flag
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);
```

#### **`shopping_lists` Table**
```sql
CREATE TABLE public.shopping_lists (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id text REFERENCES public.users(id) NOT NULL,
    meal_plan_id uuid REFERENCES public.meal_plans(id),
    name text NOT NULL,                    -- Shopping list name
    status text DEFAULT 'active',          -- List status
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);
```

#### **`shopping_items` Table**
```sql
CREATE TABLE public.shopping_items (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    shopping_list_id uuid REFERENCES public.shopping_lists(id) NOT NULL,
    name text NOT NULL,                    -- Item name
    quantity text NOT NULL,                -- Quantity (e.g., "2", "1 lb")
    unit text NOT NULL,                    -- Unit of measurement
    category text NOT NULL,                -- Item category (produce, dairy, etc.)
    checked boolean DEFAULT false,         -- Purchase status
    in_pantry boolean DEFAULT false,       -- Already in pantry flag
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);
```

### **2.2 Missing Tables (To Be Implemented)**

Based on the diagnostic report, these tables need to be created:

- `meal_plan_assignments` - Calendar assignments
- `meal_completions` - Meal completion tracking  
- `favorite_meals` - User favorite recipes
- `pantry_items` - Pantry management
- `meal_notes` - User meal notes
- `user_preferences` - Extended preferences
- `search_history` - Search analytics

---

## **3. API Endpoints Overview**

### **3.1 Authentication Endpoints**
- `POST /api/auth/signup` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/callback` - OAuth callback handler
- `POST /api/auth/reset-password` - Password reset (planned)

### **3.2 Meal Plan Endpoints**
- `POST /api/meal-planner` - Generate meal plan
- `POST /api/edaspoon` - EdaSpoon generation approach
- `POST /api/openai` - AI generation approach  
- `POST /api/hybrid` - Hybrid generation approach
- `GET /api/meal-plans` - Get user meal plans
- `POST /api/meal-plans` - Save meal plan
- `PUT /api/meal-plans/{id}` - Update meal plan
- `DELETE /api/meal-plans/{id}` - Delete meal plan

### **3.3 Recipe Endpoints**
- `GET /api/recipes` - Browse/search recipes
- `GET /api/recipes/{id}` - Get recipe details
- `POST /api/recipes` - Create custom recipe
- `PUT /api/recipes/{id}` - Update recipe
- `DELETE /api/recipes/{id}` - Delete recipe

### **3.4 Shopping List Endpoints**
- `GET /api/shopping-lists` - Get user shopping lists
- `POST /api/shopping-lists` - Create shopping list
- `PUT /api/shopping-lists/{id}` - Update shopping list
- `DELETE /api/shopping-lists/{id}` - Delete shopping list
- `POST /api/shopping-lists/{id}/items` - Add item to list
- `PUT /api/shopping-lists/{id}/items/{itemId}` - Update item
- `DELETE /api/shopping-lists/{id}/items/{itemId}` - Remove item

### **3.5 Dashboard Endpoints**
- `GET /api/dashboard/stats` - Dashboard statistics
- `GET /api/dashboard/upcoming-meals` - Upcoming meals (planned)
- `GET /api/dashboard/nutrition-summary` - Nutrition summary (planned)

---

## **4. Backend Services & Logic**

### **4.1 Meal Plan Generation Service**
**Location:** `lib/meal-plan-generators/`

**Key Components:**
- `edaspoon-generator.ts` - Edamam + Spoonacular approach
- `ai-generator.ts` - OpenAI-based generation
- `hybrid-generator.ts` - Combined AI + API approach

**Business Logic:**
- Budget adherence validation
- Dietary restriction filtering
- Repetition rule enforcement (max 2-3 identical recipes per week)
- Nutritional balance optimization
- Cost estimation and optimization

### **4.2 Authentication Service**
**Location:** `services/auth-service.ts`

**Key Functions:**
- User registration with profile creation
- Session management and validation
- Password hashing and verification
- OAuth provider integration

### **4.3 Database Services**
**Location:** `app/services/`

**Key Services:**
- `database-client.ts` - Supabase client management
- `meal-plan-service.ts` - Meal plan CRUD operations
- `shopping-list-service.ts` - Shopping list management
- `dashboardService.ts` - Dashboard data aggregation

---

## **5. External API Integrations**

### **5.1 OpenAI API**
**Purpose:** AI-powered meal plan generation
**Configuration:** 
- API Key: `OPENAI_API_KEY`
- Model: GPT-3.5-turbo (upgradeable to GPT-4)
- Rate Limits: 3 requests/minute (tier dependent)

**Usage:**
```typescript
const response = await openai.chat.completions.create({
  model: "gpt-3.5-turbo",
  messages: [{ role: "user", content: prompt }],
  temperature: 0.7
});
```

### **5.2 Edamam API**
**Purpose:** Recipe data and nutritional information
**Configuration:**
- App ID: `NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES`
- App Key: `NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES`
- Rate Limits: 10 calls/minute (free tier)

**Usage:**
```typescript
const response = await fetch(
  `https://api.edamam.com/api/recipes/v2?type=public&q=${query}&app_id=${appId}&app_key=${appKey}`
);
```

### **5.3 Spoonacular API**
**Purpose:** Ingredient cost data and recipe information
**Configuration:**
- API Key: `SPOONACULAR_API_KEY`
- Rate Limits: 150 calls/day (free tier)

**Usage:**
```typescript
const response = await fetch(
  `https://api.spoonacular.com/recipes/complexSearch?apiKey=${apiKey}&query=${query}`
);
```

---

## **6. Error Handling Strategy**

### **6.1 API Error Responses**
**Standard Format:**
```json
{
  "error": {
    "code": "MEAL_PLAN_GENERATION_FAILED",
    "message": "Failed to generate meal plan",
    "details": "External API rate limit exceeded",
    "timestamp": "2025-06-28T12:00:00Z"
  }
}
```

### **6.2 Fallback Mechanisms**
- **External API Failures:** Default to cached/static recipe data
- **Database Errors:** Retry with exponential backoff
- **Authentication Errors:** Redirect to login with error context
- **Validation Errors:** Return detailed field-level error messages

### **6.3 Logging Strategy**
**Implementation:** Winston logger with structured logging
**Log Levels:** ERROR, WARN, INFO, DEBUG
**Log Destinations:** Console (development), File/Cloud (production)

---

## **7. Security Implementation**

### **7.1 Authentication & Authorization**
- **JWT Tokens:** Managed by Supabase Auth
- **Session Management:** Server-side session validation
- **Route Protection:** Middleware-based authentication checks
- **API Security:** Request validation and rate limiting

### **7.2 Data Protection**
- **Row-Level Security:** Implemented on all user data tables
- **Input Validation:** Zod schemas for all API inputs
- **SQL Injection Prevention:** Parameterized queries via Prisma/Supabase
- **XSS Protection:** Input sanitization and CSP headers

---

## **8. Performance Considerations**

### **8.1 Database Optimization**
- **Indexes:** Strategic indexes on frequently queried columns
- **Query Optimization:** Efficient joins and aggregations
- **Connection Pooling:** Managed by Supabase
- **Caching:** React Query for client-side caching

### **8.2 API Performance**
- **Rate Limiting:** Implemented for external API calls
- **Request Batching:** Combine multiple operations where possible
- **Async Processing:** Background processing for long-running tasks
- **CDN Integration:** Static asset optimization

---

## **9. Environment Configuration**

### **9.1 Required Environment Variables**
```bash
# Database
DATABASE_URL=postgresql://...
DIRECT_URL=postgresql://...

# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://...
NEXT_PUBLIC_SUPABASE_ANON_KEY=...
SUPABASE_SERVICE_ROLE_KEY=...

# External APIs
OPENAI_API_KEY=sk-...
NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES=...
NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES=...
SPOONACULAR_API_KEY=...

# Application
NEXTAUTH_SECRET=...
NEXTAUTH_URL=http://localhost:3000
```

### **9.2 Development vs Production**
- **Development:** Local Supabase instance or staging database
- **Production:** Production Supabase project with backup/monitoring
- **API Keys:** Separate keys for development and production environments

---

## **10. Deployment & Operations**

### **10.1 Deployment Strategy**
- **Platform:** Vercel (recommended for Next.js)
- **Database:** Supabase managed PostgreSQL
- **CDN:** Vercel Edge Network for static assets
- **Monitoring:** Supabase built-in monitoring + custom analytics

### **10.2 Backup & Recovery**
- **Database Backups:** Automated daily backups via Supabase
- **Point-in-time Recovery:** Available for last 7 days
- **Disaster Recovery:** Multi-region backup strategy
- **Data Export:** User data export functionality implemented

### **10.3 Monitoring & Alerting**
- **Application Monitoring:** Error tracking and performance metrics
- **Database Monitoring:** Query performance and connection monitoring
- **API Monitoring:** External API usage and rate limit tracking
- **Security Monitoring:** Authentication failures and suspicious activity

---

## **11. Development Workflow**

### **11.1 Local Development Setup**
1. Clone repository and install dependencies
2. Set up local environment variables
3. Run database migrations
4. Start development server with `npm run dev`
5. Access application at `http://localhost:3000`

### **11.2 Testing Strategy**
- **Unit Tests:** Jest for service functions and utilities
- **Integration Tests:** API endpoint testing with test database
- **Component Tests:** React Testing Library for UI components
- **E2E Tests:** Playwright for critical user journeys (planned)

### **11.3 Code Quality**
- **TypeScript:** Strict type checking enabled
- **ESLint:** Code linting with Next.js configuration
- **Prettier:** Code formatting automation
- **Husky:** Pre-commit hooks for quality checks
