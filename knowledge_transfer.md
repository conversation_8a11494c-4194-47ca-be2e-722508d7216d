Project Overview: LeanEats - Meal Planning Application

Tech Stack:
- Next.js (with App Router)
- TypeScript
- Prisma (PostgreSQL)
- Supabase (Auth & Database)
- OpenAI API (for meal plan generation)
- shadcn/ui (UI components)
- Tailwind CSS
- Lucide React (icons)

Key Architecture Components:
1. Database:
   - PostgreSQL via Supabase
   - Prisma as ORM
   - Multi-schema support enabled
   - Key tables: users, meal_plans, recipes, shopping_lists

2. Authentication:
   - Supabase Auth with PKCE flow
   - Server-side session management
   - Protected routes in middleware.ts

3. API Structure:
   - Route handlers in app/api/*
   - OpenAI integration for meal plan generation
   - Error handling middleware
   - Type-safe API responses

4. Frontend:
   - Server & Client Components
   - Context Providers: Theme, MealPlanner, Supabase
   - Form validation with Zod
   - Error boundaries implemented

Environment Setup:
- Required env variables:
  - NEXT_PUBLIC_SUPABASE_URL
  - NEXT_PUBLIC_SUPABASE_ANON_KEY
  - DATABASE_URL
  - DIRECT_URL
  - OPENAI_API_KEY

DO's:
1. Always use 'use client' directive for client-side components
2. Use shadcn/ui components for consistency
3. Follow the established error handling pattern
4. Use Prisma transactions for related database operations
5. Implement proper type checking with Zod
6. Use Winston logger for error tracking
7. Keep OpenAI prompts consistent with established format
8. Use Supabase's row-level security
9. Follow the established auth flow
10. Use unsplash for images (configured in next.config.js)

DON'Ts:
1. Don't modify database schema without updating Prisma schema
2. Don't skip error handling in API routes
3. Don't use client-side hooks in server components
4. Don't install additional UI libraries
5. Don't modify auth flow without testing all scenarios
6. Don't expose sensitive data in API responses
7. Don't skip type checking
8. Don't modify existing table relationships without migration
9. Don't bypass middleware auth checks
10. Don't use direct SQL queries (use Prisma)

Database Operations:
- Always use Prisma Client for database operations
- Run 'npx prisma generate' after schema changes
- Use 'npx prisma db push' for development
- Create proper migrations for production changes
- Never run reset commands on production

Current State:
- Database and Prisma schema are in sync
- All required tables and relationships are established
- Auth flow is properly configured
- API routes are type-safe and error-handled
- UI components follow shadcn/ui patterns

Next Steps for Enhancement:
1. Implement caching for meal plans
2. Add rate limiting for API routes
3. Enhance error reporting
4. Implement real-time updates
5. Add performance monitoring
6. Enhance test coverage
7. Implement backup strategies
8. Add analytics tracking
9. Enhance security measures
10. Optimize database queries

Critical Files:
- app/api/mealplan/route.ts (meal plan generation)
- middleware.ts (auth protection)
- lib/prisma.ts (database client)
- services/auth-service.ts (auth logic)
- app/layout.tsx (root layout)