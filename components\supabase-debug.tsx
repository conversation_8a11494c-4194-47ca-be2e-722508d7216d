'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Context } from './supabase-provider';

/**
 * SupabaseDebug component
 * 
 * This component helps diagnose issues with the Supabase provider.
 * It displays the current state of the Supabase context.
 */
export function SupabaseDebug() {
  const [mounted, setMounted] = useState(false);
  const context = useContext(Context);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    setMounted(true);
    
    try {
      // Check if we're in a browser environment
      if (typeof window !== 'undefined') {
        // Log the context to the console
        console.log('SupabaseDebug: Context in browser:', context);
      }
    } catch (err: any) {
      setError(err.message || 'Unknown error');
    }
  }, [context]);
  
  if (!mounted) {
    return <div>Loading debug info...</div>;
  }
  
  return (
    <div className="p-4 border border-gray-300 rounded-md bg-gray-50 my-4 text-sm">
      <h3 className="font-bold mb-2">Supabase Context Debug</h3>
      
      {error ? (
        <div className="text-red-500">Error: {error}</div>
      ) : (
        <div>
          <div>
            <span className="font-semibold">Context exists:</span> {context ? 'Yes' : 'No'}
          </div>
          
          {context && (
            <>
              <div>
                <span className="font-semibold">Is initialized:</span> {context.isInitialized ? 'Yes' : 'No'}
              </div>
              <div>
                <span className="font-semibold">Supabase client exists:</span> {context.supabase ? 'Yes' : 'No'}
              </div>
            </>
          )}
          
          <div className="mt-2 text-xs text-gray-500">
            This component is for debugging purposes only.
          </div>
        </div>
      )}
    </div>
  );
}
