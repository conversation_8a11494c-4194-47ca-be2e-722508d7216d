"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash, User, Users } from "lucide-react";

// Define the form schema
const householdFormSchema = z.object({
  household_size: z.coerce.number().min(1, "Household size must be at least 1").max(10, "Maximum household size is 10"),
  age_groups: z.array(z.string()),
  weekly_budget: z.coerce.number().min(0, "Budget cannot be negative"),
  budget_type: z.string(),
  budget_flexibility: z.string(),
  meal_frequency: z.string(),
  household_members: z.array(
    z.object({
      name: z.string().min(1, "Name is required"),
      age_group: z.string(),
      dietary_restrictions: z.array(z.string()).optional().default([]),
    })
  ).optional().default([]),
});

type HouseholdFormValues = z.infer<typeof householdFormSchema>;

interface HouseholdTabProps {
  userData: any;
  userPreferences: any;
  supabase: any;
  onUpdate: (data: any) => void;
}

export default function HouseholdTab({ userData, userPreferences, supabase, onUpdate }: HouseholdTabProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showAddMember, setShowAddMember] = useState(false);
  const [newMemberName, setNewMemberName] = useState("");
  const [newMemberAgeGroup, setNewMemberAgeGroup] = useState("adult");
  const [newMemberDietaryRestrictions, setNewMemberDietaryRestrictions] = useState<string[]>([]);

  // Log data for debugging
  console.log("HouseholdTab received userData:", userData);
  console.log("HouseholdTab received userPreferences:", userPreferences);

  // Initialize form with user data
  const form = useForm<HouseholdFormValues>({
    resolver: zodResolver(householdFormSchema),
    defaultValues: {
      household_size: userData?.household_size || 1,
      age_groups: userPreferences?.age_groups || [],
      weekly_budget: userData?.weekly_budget || 0,
      budget_type: userPreferences?.budget_type || "weekly",
      budget_flexibility: userPreferences?.budget_flexibility || "strict",
      meal_frequency: userPreferences?.meal_frequency || "3 meals per day",
      household_members: userPreferences?.household_members || [],
    },
  });

  const { watch, setValue } = form;
  const watchedHouseholdMembers = watch("household_members") || [];

  const handleAddMember = () => {
    if (!newMemberName.trim()) {
      toast.error("Member name is required");
      return;
    }

    const newMember = {
      name: newMemberName,
      age_group: newMemberAgeGroup,
      dietary_restrictions: newMemberDietaryRestrictions,
    };

    setValue("household_members", [...watchedHouseholdMembers, newMember]);
    setValue("household_size", watchedHouseholdMembers.length + 1);

    // Reset form
    setNewMemberName("");
    setNewMemberAgeGroup("adult");
    setNewMemberDietaryRestrictions([]);
    setShowAddMember(false);

    toast.success(`Added ${newMemberName} to household`);
  };

  const handleRemoveMember = (index: number) => {
    const memberName = watchedHouseholdMembers[index].name;
    const updatedMembers = watchedHouseholdMembers.filter((_, i) => i !== index);

    setValue("household_members", updatedMembers);
    setValue("household_size", updatedMembers.length);

    toast.success(`Removed ${memberName} from household`);
  };

  const onSubmit = async (data: HouseholdFormValues) => {
    try {
      setIsLoading(true);

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        toast.error("Authentication error. Please log in again.");
        return;
      }

      // Update household size and budget in users table
      const { error: userUpdateError } = await supabase
        .from('users')
        .update({
          household_size: data.household_size,
          weekly_budget: data.weekly_budget,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (userUpdateError) {
        throw userUpdateError;
      }

      // Prepare preferences data
      const preferencesData = {
        user_id: user.id,
        age_groups: data.age_groups,
        budget_type: data.budget_type,
        budget_flexibility: data.budget_flexibility,
        meal_frequency: data.meal_frequency,
        household_members: data.household_members,
        updated_at: new Date().toISOString(),
      };

      // Check if user preferences exist
      if (userPreferences?.id) {
        // Update existing preferences
        const { error: preferencesUpdateError } = await supabase
          .from('user_preferences')
          .update(preferencesData)
          .eq('id', userPreferences.id);

        if (preferencesUpdateError) {
          throw preferencesUpdateError;
        }
      } else {
        // Insert new preferences
        const { error: preferencesInsertError } = await supabase
          .from('user_preferences')
          .insert({
            ...preferencesData,
            created_at: new Date().toISOString(),
          });

        if (preferencesInsertError) {
          throw preferencesInsertError;
        }
      }

      // Update local state
      onUpdate({
        ...preferencesData,
        household_size: data.household_size,
        weekly_budget: data.weekly_budget,
      });

      toast.success("Household settings updated successfully");
    } catch (error) {
      console.error("Error updating household settings:", error);
      toast.error("Failed to update household settings");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Household Settings</CardTitle>
          <CardDescription>
            Configure your household size, budget, and meal preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="household_size"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Household Size</FormLabel>
                      <FormControl>
                        <Input type="number" min={1} max={10} {...field} />
                      </FormControl>
                      <FormDescription>
                        Number of people in your household
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="weekly_budget"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Weekly Budget</FormLabel>
                      <FormControl>
                        <Input type="number" min={0} {...field} />
                      </FormControl>
                      <FormDescription>
                        Your weekly food budget
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="budget_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Budget Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select budget type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="per-meal">Per Meal</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        How you prefer to budget for meals
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="budget_flexibility"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Budget Flexibility</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select budget flexibility" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="strict">Strict</SelectItem>
                          <SelectItem value="moderate">Moderate</SelectItem>
                          <SelectItem value="flexible">Flexible</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        How strictly you want to adhere to your budget
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="meal_frequency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Meal Frequency</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select meal frequency" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="3 meals per day">3 meals per day</SelectItem>
                        <SelectItem value="3 meals with snacks">3 meals with snacks</SelectItem>
                        <SelectItem value="2 meals per day">2 meals per day</SelectItem>
                        <SelectItem value="1 meal per day">1 meal per day</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      How many meals you typically eat per day
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Separator className="my-6" />

              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">Household Members</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAddMember(!showAddMember)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Member
                  </Button>
                </div>

                {showAddMember && (
                  <Card className="mb-4 border-dashed">
                    <CardContent className="pt-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <FormLabel htmlFor="newMemberName">Name</FormLabel>
                          <Input
                            id="newMemberName"
                            value={newMemberName}
                            onChange={(e) => setNewMemberName(e.target.value)}
                            placeholder="Member name"
                          />
                        </div>
                        <div>
                          <FormLabel htmlFor="newMemberAgeGroup">Age Group</FormLabel>
                          <Select
                            value={newMemberAgeGroup}
                            onValueChange={setNewMemberAgeGroup}
                          >
                            <SelectTrigger id="newMemberAgeGroup">
                              <SelectValue placeholder="Select age group" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="infant">Infant (0-1)</SelectItem>
                              <SelectItem value="toddler">Toddler (1-3)</SelectItem>
                              <SelectItem value="child">Child (4-12)</SelectItem>
                              <SelectItem value="teen">Teen (13-18)</SelectItem>
                              <SelectItem value="adult">Adult (19-64)</SelectItem>
                              <SelectItem value="senior">Senior (65+)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div className="flex justify-end space-x-2">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setShowAddMember(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          type="button"
                          onClick={handleAddMember}
                        >
                          Add Member
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {watchedHouseholdMembers.length === 0 ? (
                  <div className="text-center py-8 border rounded-md bg-muted/20">
                    <Users className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                    <p className="text-muted-foreground">No household members added yet</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Add household members to personalize meal plans
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {watchedHouseholdMembers.map((member, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 border rounded-md"
                      >
                        <div className="flex items-center">
                          <User className="h-5 w-5 mr-3 text-muted-foreground" />
                          <div>
                            <p className="font-medium">{member.name}</p>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge variant="outline">{member.age_group}</Badge>
                              {member.dietary_restrictions?.map((restriction, i) => (
                                <Badge key={i} variant="secondary">{restriction}</Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => handleRemoveMember(index)}
                          className="text-destructive hover:text-destructive hover:bg-destructive/10"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <Button type="submit" disabled={isLoading} className="mt-6">
                {isLoading ? "Saving..." : "Save Changes"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
