'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Star, Clock, DollarSign, ChefHat, Utensils, Trash2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export interface Recipe {
  id: string;
  name: string;
  description: string;
  prep_time: number;
  cook_time: number;
  cost_per_serving: number;
  image_url: string;
  cuisine: string;
  dietary_restrictions: string[];
  is_favorite: boolean;
  user_id?: string;
  instructions?: string[];
  servings?: number;
  difficulty?: string;
  nutrition?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
  ingredients?: Array<{
    name: string;
    amount: string;
    unit: string;
  }>;
}

interface RecipeCardProps {
  recipe: Recipe;
  onToggleFavorite: (recipeId: string) => void;
  onDelete?: (recipeId: string) => void;
  isSelectable?: boolean;
  isSelected?: boolean;
  onSelect?: (recipeId: string) => void;
}

export function RecipeCard({
  recipe,
  onToggleFavorite,
  onDelete,
  isSelectable = false,
  isSelected = false,
  onSelect
}: RecipeCardProps) {
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const handleViewRecipe = () => {
    router.push(`/meal-detail/${recipe.id}`);
  };

  const handleEditRecipe = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push(`/recipes/edit/${recipe.id}`);
  };

  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleFavorite(recipe.id);
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDeleteDialog(true);
  };

  const confirmDelete = () => {
    if (onDelete) {
      onDelete(recipe.id);
    }
    setShowDeleteDialog(false);
  };

  const handleCardClick = () => {
    if (isSelectable && onSelect) {
      onSelect(recipe.id);
    } else {
      handleViewRecipe();
    }
  };

  return (
    <>
      <Card 
        className={`overflow-hidden flex flex-col h-full transition-all duration-200 hover:shadow-md cursor-pointer ${
          isSelected ? 'ring-2 ring-primary' : ''
        }`}
        onClick={handleCardClick}
      >
        <div className="relative h-48 bg-muted">
          <div className="absolute inset-0 flex items-center justify-center">
            <img
              src={recipe.image_url || 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c'}
              alt={recipe.name}
              className="w-full h-full object-cover"
            />
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 bg-background/80 hover:bg-background"
            onClick={handleToggleFavorite}
          >
            <Star className={`h-5 w-5 ${recipe.is_favorite ? 'fill-yellow-400 text-yellow-400' : 'text-muted-foreground'}`} />
          </Button>
          <div className="absolute bottom-2 left-2">
            <Badge className="bg-background/80 text-foreground">
              {recipe.cuisine}
            </Badge>
          </div>
          {recipe.difficulty && (
            <div className="absolute bottom-2 right-2">
              <Badge variant="outline" className="bg-background/80 text-foreground">
                {recipe.difficulty}
              </Badge>
            </div>
          )}
        </div>
        <CardContent className="p-4 flex-1 flex flex-col">
          <h3 className="font-semibold text-lg mb-1">{recipe.name}</h3>
          <p className="text-sm text-muted-foreground mb-3 line-clamp-2 flex-1">{recipe.description}</p>
          <div className="flex flex-wrap gap-2 mb-3">
            {recipe.dietary_restrictions && recipe.dietary_restrictions.slice(0, 3).map((restriction) => (
              <Badge key={restriction} variant="outline">{restriction}</Badge>
            ))}
            {recipe.dietary_restrictions && recipe.dietary_restrictions.length > 3 && (
              <Badge variant="outline">+{recipe.dietary_restrictions.length - 3} more</Badge>
            )}
          </div>
          <div className="grid grid-cols-2 gap-2 text-sm text-muted-foreground mb-3">
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              {recipe.prep_time + recipe.cook_time} min
            </div>
            <div className="flex items-center">
              <DollarSign className="h-4 w-4 mr-1" />
              ${recipe.cost_per_serving.toFixed(2)}
            </div>
            <div className="flex items-center">
              <ChefHat className="h-4 w-4 mr-1" />
              {recipe.difficulty || 'Medium'}
            </div>
            <div className="flex items-center">
              <Utensils className="h-4 w-4 mr-1" />
              {recipe.servings || 2} servings
            </div>
          </div>
          <div className="mt-auto pt-3 border-t flex gap-2">
            {!isSelectable && (
              <>
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={handleEditRecipe}
                >
                  Edit
                </Button>
                <Button
                  variant="default"
                  className="flex-1"
                  onClick={handleViewRecipe}
                >
                  View
                </Button>
                {onDelete && (
                  <Button
                    variant="destructive"
                    size="icon"
                    onClick={handleDeleteClick}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </>
            )}
          </div>
        </CardContent>
      </Card>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the recipe "{recipe.name}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
