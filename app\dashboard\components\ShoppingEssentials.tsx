'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>art, ChevronRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { useSupabase } from "@/components/supabase-provider";
import { toast } from "sonner";

interface ShoppingItem {
  id: string;
  name: string;
  checked: boolean;
}

interface ShoppingEssentialsProps {
  isLoading?: boolean;
}

export function ShoppingEssentials({ isLoading = false }: ShoppingEssentialsProps) {
  const router = useRouter();
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const [items, setItems] = useState<ShoppingItem[]>([]);
  const [loadingItems, setLoadingItems] = useState(true);

  useEffect(() => {
    // Don't fetch if Supabase is still loading or not available
    if (isSupabaseLoading || !supabase) {
      return;
    }

    const fetchShoppingItems = async () => {
      try {
        setLoadingItems(true);

        // Get current user
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError || !user) {
          console.log('User not authenticated, using mock data');
          setItems(mockItems);
          return;
        }

        // Check if shopping_items table exists
        try {
          // First, try to fetch from shopping_list table which is more likely to exist
          const { data: shoppingListData, error: shoppingListError } = await supabase
            .from('shopping_list')
            .select('id, name, checked')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })
            .limit(3);

          if (!shoppingListError && shoppingListData && shoppingListData.length > 0) {
            setItems(shoppingListData);
            return;
          }

          // If that fails, try shopping_items
          const { data, error } = await supabase
            .from('shopping_items')
            .select('id, name, checked')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })
            .limit(3);

          if (!error && data && data.length > 0) {
            setItems(data);
          } else {
            // Use mock data if no items found
            console.log('No shopping items found, using mock data');
            setItems(mockItems);
          }
        } catch (tableError) {
          // If there's an error with the table queries, use mock data
          console.log('Error with shopping tables, using mock data');
          setItems(mockItems);
        }
      } catch (error) {
        console.log('Error fetching shopping items, using mock data');
        setItems(mockItems);
      } finally {
        setLoadingItems(false);
      }
    };

    fetchShoppingItems();
  }, [supabase, isSupabaseLoading]);

  const handleToggleItem = async (id: string, checked: boolean) => {
    // Check if this is a mock item (starts with 'mock-')
    if (id.startsWith('mock-')) {
      // Just update local state for mock items
      setItems(items.map(item =>
        item.id === id ? { ...item, checked } : item
      ));
      return;
    }

    // Update local state immediately for better UX
    setItems(items.map(item =>
      item.id === id ? { ...item, checked } : item
    ));

    try {
      // Try to update in shopping_list table first
      try {
        const { error: listError } = await supabase
          .from('shopping_list')
          .update({ checked })
          .eq('id', id);

        if (!listError) return; // Success, no need to try the other table
      } catch (listUpdateError) {
        // If that fails, try shopping_items
      }

      // Try to update in shopping_items table
      const { error } = await supabase
        .from('shopping_items')
        .update({ checked })
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      console.log('Error updating item, reverting UI state');
      // Revert local state if update fails
      setItems(items);
    }
  };

  const handleViewAll = () => {
    router.push('/shopping-list');
  };

  if (isLoading || loadingItems) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <ShoppingCart className="h-5 w-5 text-primary" />
            <CardTitle>Shopping Essentials</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center space-x-2">
                <Skeleton className="h-4 w-4 rounded" />
                <Skeleton className="h-4 w-full" />
              </div>
            ))}
          </div>
        </CardContent>
        <CardFooter>
          <Skeleton className="h-9 w-full" />
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center space-x-2">
          <ShoppingCart className="h-5 w-5 text-primary" />
          <CardTitle>Shopping Essentials</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {items.map((item) => (
            <div key={item.id} className="flex items-center space-x-2">
              <Checkbox
                id={`item-${item.id}`}
                checked={item.checked}
                onCheckedChange={(checked) => handleToggleItem(item.id, checked as boolean)}
              />
              <label
                htmlFor={`item-${item.id}`}
                className={`text-sm ${item.checked ? 'line-through text-muted-foreground' : ''}`}
              >
                {item.name}
              </label>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter>
        <Button
          variant="ghost"
          className="w-full justify-between"
          onClick={handleViewAll}
        >
          <span>View Shopping List</span>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
}

// Mock data for when no items are found
const mockItems: ShoppingItem[] = [
  { id: 'mock-1', name: 'Chicken breast (500g)', checked: false },
  { id: 'mock-2', name: 'Sweet potatoes (4)', checked: false },
  { id: 'mock-3', name: 'Broccoli (1 head)', checked: false },
];

