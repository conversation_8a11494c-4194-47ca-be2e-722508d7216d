import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const assignMealPlanSchema = z.object({
  meal_plan_id: z.string().uuid('Invalid meal plan ID'),
  assigned_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format'),
});

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get current user
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');

    let query = supabase
      .from('meal_plan_assignments')
      .select(`
        *,
        meal_plans (
          id,
          start_date,
          end_date,
          total_cost,
          meal_data,
          status
        )
      `)
      .eq('user_id', session.user.id)
      .order('assigned_date', { ascending: true });

    if (startDate) {
      query = query.gte('assigned_date', startDate);
    }
    if (endDate) {
      query = query.lte('assigned_date', endDate);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching meal plan assignments:', error);
      return NextResponse.json(
        { error: 'Failed to fetch meal plan assignments' },
        { status: 500 }
      );
    }

    return NextResponse.json({ data: data || [] });

  } catch (error) {
    console.error('Meal plan assignments GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get current user
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { meal_plan_id, assigned_date } = assignMealPlanSchema.parse(body);

    // Check if meal plan exists and belongs to user
    const { data: mealPlan, error: mealPlanError } = await supabase
      .from('meal_plans')
      .select('id')
      .eq('id', meal_plan_id)
      .eq('user_id', session.user.id)
      .single();

    if (mealPlanError || !mealPlan) {
      return NextResponse.json(
        { error: 'Meal plan not found' },
        { status: 404 }
      );
    }

    // Check if there's already an assignment for this date
    const { data: existingAssignment, error: existingError } = await supabase
      .from('meal_plan_assignments')
      .select('id, meal_plan_id')
      .eq('user_id', session.user.id)
      .eq('assigned_date', assigned_date)
      .single();

    if (existingAssignment) {
      // Update existing assignment
      const { data, error } = await supabase
        .from('meal_plan_assignments')
        .update({
          meal_plan_id,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingAssignment.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating meal plan assignment:', error);
        return NextResponse.json(
          { error: 'Failed to update meal plan assignment' },
          { status: 500 }
        );
      }

      return NextResponse.json({ 
        data, 
        message: 'Meal plan assignment updated successfully',
        action: 'updated'
      });
    } else {
      // Create new assignment
      const { data, error } = await supabase
        .from('meal_plan_assignments')
        .insert({
          user_id: session.user.id,
          meal_plan_id,
          assigned_date,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating meal plan assignment:', error);
        return NextResponse.json(
          { error: 'Failed to create meal plan assignment' },
          { status: 500 }
        );
      }

      return NextResponse.json({ 
        data, 
        message: 'Meal plan assigned successfully',
        action: 'created'
      });
    }

  } catch (error) {
    console.error('Meal plan assignments POST error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
