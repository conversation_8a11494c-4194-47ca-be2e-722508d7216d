'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { ShoppingCart } from 'lucide-react';

interface Ingredient {
  name: string;
  amount: string;
  unit: string;
}

interface IngredientListProps {
  ingredients: Ingredient[];
  servings: number;
  scaleFactor?: number;
  onAddToShoppingList?: (ingredients: Ingredient[]) => void;
}

export function IngredientList({
  ingredients,
  servings,
  scaleFactor = 1,
  onAddToShoppingList
}: IngredientListProps) {
  const [checkedIngredients, setCheckedIngredients] = useState<Record<string, boolean>>({});
  
  // Helper function to scale ingredient amounts
  const scaleAmount = (amount: string, factor: number): string => {
    // Try to parse the amount as a number
    const numericAmount = parseFloat(amount);
    
    if (isNaN(numericAmount)) {
      // If it's not a number (e.g., "a pinch"), return as is
      return amount;
    }
    
    // Scale the amount and round to 2 decimal places
    const scaledAmount = Math.round(numericAmount * factor * 100) / 100;
    
    // Format the scaled amount
    if (scaledAmount % 1 === 0) {
      // If it's a whole number, return as integer
      return scaledAmount.toString();
    } else if (scaledAmount < 0.25) {
      // For very small amounts, show more precision
      return scaledAmount.toFixed(2);
    } else {
      // For normal amounts, show 1 decimal place
      return scaledAmount.toFixed(1);
    }
  };
  
  // Toggle ingredient checked state
  const toggleIngredient = (index: number) => {
    setCheckedIngredients(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };
  
  // Handle adding ingredients to shopping list
  const handleAddToShoppingList = () => {
    if (onAddToShoppingList) {
      // Scale ingredients before adding to shopping list
      const scaledIngredients = ingredients.map(ingredient => ({
        ...ingredient,
        amount: scaleAmount(ingredient.amount, scaleFactor)
      }));
      
      onAddToShoppingList(scaledIngredients);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Ingredients</CardTitle>
        <CardDescription>For {Math.round(servings * scaleFactor)} servings</CardDescription>
      </CardHeader>
      <CardContent>
        <ul className="space-y-3">
          {ingredients.map((ingredient, index) => (
            <li key={index} className="flex items-start">
              <Checkbox
                id={`ingredient-${index}`}
                checked={checkedIngredients[index] || false}
                onCheckedChange={() => toggleIngredient(index)}
                className="mt-1 mr-3"
              />
              <label
                htmlFor={`ingredient-${index}`}
                className={`flex-1 cursor-pointer ${checkedIngredients[index] ? 'line-through text-muted-foreground' : ''}`}
              >
                <span className="font-medium">
                  {scaleAmount(ingredient.amount, scaleFactor)} {ingredient.unit}
                </span>{' '}
                {ingredient.name}
              </label>
            </li>
          ))}
        </ul>

        {onAddToShoppingList && (
          <Button
            className="w-full mt-6"
            onClick={handleAddToShoppingList}
            variant="default"
          >
            <ShoppingCart className="h-4 w-4 mr-2" />
            Add to Shopping List
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
