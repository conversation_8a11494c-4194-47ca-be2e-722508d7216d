'use client';

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import dashboardService, {
  UpcomingMeal,
  NutritionData,
  MealStatus,
  WeeklyChartData,
  DashboardStats
} from '@/app/services/dashboardService';

// Hook for fetching upcoming meals
export function useUpcomingMeals() {
  return useQuery({
    queryKey: ['upcomingMeals'],
    queryFn: dashboardService.getUpcomingMeals,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  });
}

// Hook for fetching nutrition data
export function useNutritionData() {
  return useQuery({
    queryKey: ['nutritionData'],
    queryFn: dashboardService.getNutritionData,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  });
}

// Hook for fetching meal status
export function useMealStatus() {
  return useQuery({
    queryKey: ['mealStatus'],
    queryFn: dashboardService.getMealStatus,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  });
}

// Hook for fetching weekly chart data
export function useWeeklyChartData() {
  return useQuery({
    queryKey: ['weeklyChartData'],
    queryFn: dashboardService.getWeeklyChartData,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  });
}

// Hook for fetching dashboard stats
export function useDashboardStats() {
  return useQuery({
    queryKey: ['dashboardStats'],
    queryFn: dashboardService.getDashboardStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  });
}

// Combined hook for all dashboard data
export function useDashboardData() {
  const upcomingMeals = useUpcomingMeals();
  const nutritionData = useNutritionData();
  const mealStatus = useMealStatus();
  const weeklyChartData = useWeeklyChartData();
  const dashboardStats = useDashboardStats();
  
  const isLoading = 
    upcomingMeals.isLoading || 
    nutritionData.isLoading || 
    mealStatus.isLoading || 
    weeklyChartData.isLoading || 
    dashboardStats.isLoading;
  
  const isError = 
    upcomingMeals.isError || 
    nutritionData.isError || 
    mealStatus.isError || 
    weeklyChartData.isError || 
    dashboardStats.isError;
  
  return {
    upcomingMeals,
    nutritionData,
    mealStatus,
    weeklyChartData,
    dashboardStats,
    isLoading,
    isError
  };
}
