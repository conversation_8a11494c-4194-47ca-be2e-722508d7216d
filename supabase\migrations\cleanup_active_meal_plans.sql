CREATE OR REPLACE FUNCTION cleanup_active_meal_plans()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  WITH ranked_plans AS (
    SELECT 
      id,
      user_id,
      ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn
    FROM meal_plans
    WHERE status = 'active'
  )
  UPDATE meal_plans
  SET status = 'inactive'
  FROM ranked_plans
  WHERE meal_plans.id = ranked_plans.id
  AND ranked_plans.rn > 1;
END;
$$;