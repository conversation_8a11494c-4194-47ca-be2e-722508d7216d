const { execSync } = require('child_process');
const fs = require('fs');

async function setup() {
  console.log('Starting setup...');

  // Only clean if --clean flag is passed
  if (process.argv.includes('--clean')) {
    const cleanDirs = ['.next', 'node_modules'];
    for (const dir of cleanDirs) {
      if (fs.existsSync(dir)) {
        fs.rmSync(dir, { recursive: true, force: true });
        console.log(`Removed ${dir}`);
      }
    }
  }

  // Install dependencies if node_modules doesn't exist
  if (!fs.existsSync('node_modules')) {
    console.log('Installing dependencies...');
    execSync('npm install', { stdio: 'inherit' });
  }

  // Generate Prisma client only if schema changed
  const schemaPath = 'prisma/schema.prisma';
  const schemaHash = fs.existsSync(schemaPath) ? 
    require('crypto').createHash('md5').update(fs.readFileSync(schemaPath)).digest('hex') : 
    '';
  
  const hashPath = 'node_modules/.prisma-schema-hash';
  const oldHash = fs.existsSync(hashPath) ? fs.readFileSync(hashPath, 'utf8') : '';

  if (schemaHash !== oldHash) {
    console.log('Generating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });
    fs.writeFileSync(hashPath, schemaHash);
  }

  console.log('Setup complete!');
}

setup().catch(console.error);
