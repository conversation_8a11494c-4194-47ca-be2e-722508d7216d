"use client";

import { useState } from 'react';
import { MealGenerationOptions } from '@/types/new-meal-plan';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2 } from 'lucide-react';

interface GeneratePlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (options: MealGenerationOptions) => Promise<void>;
  isLoading: boolean;
}

export function GeneratePlanModal({
  isOpen,
  onClose,
  onGenerate,
  isLoading
}: GeneratePlanModalProps) {
  const [options, setOptions] = useState<MealGenerationOptions>({
    budget: 100,
    caloriesPerDay: 2000,
    dietType: 'balanced',
    preferences: {
      usePantryItems: true,
      optimizeIngredients: true,
      includeLeftovers: false
    }
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onGenerate(options);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Generate Meal Plan</DialogTitle>
          <DialogDescription>
            Customize your meal plan preferences and we'll generate a plan for you.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="budget">Weekly Budget: ${options.budget}</Label>
              <Slider
                id="budget"
                min={50}
                max={300}
                step={10}
                value={[options.budget]}
                onValueChange={(value) => setOptions({ ...options, budget: value[0] })}
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>$50</span>
                <span>$300</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="calories">Calories per Day: {options.caloriesPerDay}</Label>
              <Slider
                id="calories"
                min={1200}
                max={3000}
                step={100}
                value={[options.caloriesPerDay]}
                onValueChange={(value) => setOptions({ ...options, caloriesPerDay: value[0] })}
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>1200</span>
                <span>3000</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="dietType">Diet Type</Label>
              <Select
                value={options.dietType}
                onValueChange={(value) => setOptions({ ...options, dietType: value })}
              >
                <SelectTrigger id="dietType">
                  <SelectValue placeholder="Select diet type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="balanced">Balanced</SelectItem>
                  <SelectItem value="vegetarian">Vegetarian</SelectItem>
                  <SelectItem value="vegan">Vegan</SelectItem>
                  <SelectItem value="paleo">Paleo</SelectItem>
                  <SelectItem value="keto">Keto</SelectItem>
                  <SelectItem value="lowCarb">Low Carb</SelectItem>
                  <SelectItem value="highProtein">High Protein</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <Label>Preferences</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="usePantryItems"
                  checked={options.preferences.usePantryItems}
                  onCheckedChange={(checked) =>
                    setOptions({
                      ...options,
                      preferences: {
                        ...options.preferences,
                        usePantryItems: checked as boolean
                      }
                    })
                  }
                />
                <Label htmlFor="usePantryItems" className="text-sm font-normal">
                  Use pantry items when possible
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="optimizeIngredients"
                  checked={options.preferences.optimizeIngredients}
                  onCheckedChange={(checked) =>
                    setOptions({
                      ...options,
                      preferences: {
                        ...options.preferences,
                        optimizeIngredients: checked as boolean
                      }
                    })
                  }
                />
                <Label htmlFor="optimizeIngredients" className="text-sm font-normal">
                  Optimize ingredients to reduce waste
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeLeftovers"
                  checked={options.preferences.includeLeftovers}
                  onCheckedChange={(checked) =>
                    setOptions({
                      ...options,
                      preferences: {
                        ...options.preferences,
                        includeLeftovers: checked as boolean
                      }
                    })
                  }
                />
                <Label htmlFor="includeLeftovers" className="text-sm font-normal">
                  Include leftover meals in plan
                </Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Generate Plan
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
