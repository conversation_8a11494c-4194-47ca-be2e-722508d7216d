import { NextRequest, NextResponse } from 'next/server';
import { getRecipePriceBreakdown } from '@/lib/api/spoonacular';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const recipeId = parseInt(params.id, 10);
    
    if (isNaN(recipeId)) {
      return NextResponse.json(
        { error: 'Invalid recipe ID. Expected a number.' },
        { status: 400 }
      );
    }
    
    const priceBreakdown = await getRecipePriceBreakdown(recipeId);

    return NextResponse.json(priceBreakdown);
  } catch (error: any) {
    console.error(`Error in Spoonacular price breakdown API route for ID ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while fetching the price breakdown' },
      { status: 500 }
    );
  }
}
