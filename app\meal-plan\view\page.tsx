'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { ChevronLeft } from 'lucide-react';
// Import the service from the dedicated service file, not the database-client
import { mealPlanService } from '@/app/services/meal-plan-service';
import { useSupabase } from '@/components/supabase-provider';
import { MealPlan } from '@/types/meal-plan';
import { createClient } from '@supabase/supabase-js';

// Import the components from the correct locations
import { WeeklyPlanView } from '@/app/components/weekly-plan/WeeklyPlanView';
import { WeeklyPlanSkeleton } from '@/app/components/weekly-plan/WeeklyPlanSkeleton';
import { CalendarAssignmentModal } from '@/app/components/weekly-plan/CalendarAssignmentModal';
import { ShoppingListGeneratorFixed } from '@/app/components/weekly-plan/ShoppingListGeneratorFixed';
import { MealPlanCard } from '@/components/meal-plan/meal-plan-card';

function ViewMealPlanContent() {
  const router = useRouter();
  // const searchParams = useSearchParams(); // Unused for now
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const [isLoading, setIsLoading] = useState(true);
  const [mealPlans, setMealPlans] = useState<MealPlan[]>([]);
  const [filteredPlans, setFilteredPlans] = useState<MealPlan[]>([]);
  const [activePlan, setActivePlan] = useState<MealPlan | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // State for UI controls - prepared for future filter/sort UI implementation
  // These are currently used only in the applyFiltersAndSort function
  const [activeFilters] = useState<string[]>([]);
  const [sortBy] = useState<string>('created_at');
  const [sortDirection] = useState<'asc' | 'desc'>('desc');
  const [searchQuery] = useState<string>('');
  const [showDetailView, setShowDetailView] = useState(false);

  // State for modals
  const [showCalendarModal, setShowCalendarModal] = useState(false);
  const [showShoppingListModal, setShowShoppingListModal] = useState(false);
  const [selectedPlanForModal, setSelectedPlanForModal] = useState<MealPlan | null>(null);

  // Check authentication status and fetch meal plans when authenticated
  useEffect(() => {
    const checkAuth = async () => {
      if (isSupabaseLoading || !supabase) {
        // Still loading Supabase client, wait
        return;
      }

      try {
        console.log('Checking authentication status...');
        const { data: { session } } = await supabase.auth.getSession();

        if (session) {
          console.log('User is authenticated, session found');
          setIsAuthenticated(true);
          // We'll fetch meal plans in a separate useEffect to avoid race conditions
        } else {
          console.log('No session found, redirecting to login');
          setIsAuthenticated(false);
          setIsLoading(false); // Stop loading state
          router.push('/login');
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        setIsAuthenticated(false);
        setIsLoading(false); // Stop loading state
        router.push('/login');
      }
    };

    checkAuth();
  }, [isSupabaseLoading, supabase, router]);

  // Fetch meal plans when authenticated
  useEffect(() => {
    if (isAuthenticated && supabase) {
      console.log('Authentication confirmed, fetching meal plans...');
      fetchMealPlans();
    }
  }, [isAuthenticated, supabase]);

  // Fetch meal plans from the service
  const fetchMealPlans = async () => {
    if (!isAuthenticated || !supabase) {
      console.log('Cannot fetch meal plans: not authenticated or no Supabase client');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      console.log('Fetching meal plans...');

      // Get the current user ID
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError) {
        console.error('Error getting user:', userError);
        setError('Failed to get user information');
        toast.error('Failed to get user information');
        return;
      }

      if (!user) {
        console.error('No authenticated user found');
        setError('No authenticated user found');
        toast.error('No authenticated user found');
        setIsAuthenticated(false); // Update authentication state
        router.push('/login');
        return;
      }

      console.log('User ID:', user.id);

      // Use the API endpoint instead of the service
      console.log('Calling meal-plans API with user ID:', user.id);

      // Add a cache-busting parameter to prevent caching
      const cacheBuster = new Date().getTime();
      const response = await fetch(`/api/meal-plans?userId=${user.id}&_=${cacheBuster}`, {
        // Add cache control headers
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error:', errorData);
        setError(errorData.error || 'Failed to fetch meal plans');
        toast.error('Failed to fetch meal plans');
        return;
      }

      const responseData = await response.json();
      const data = responseData;
      const error = null;

      // Log the actual data received
      console.log('Meal plans received:', data);

      console.log('meal-plans API result:', { data: data ? 'data received' : 'null', error });

      if (error) {
        console.error('Error fetching meal plans:', error);
        setError(typeof error === 'string' ? error : 'Failed to fetch meal plans');
        toast.error('Failed to fetch meal plans');
        return;
      }

      // Handle empty data case - this is not an error, just no meal plans yet
      if (!data || data.length === 0) {
        console.log('No meal plans found for user');
        setMealPlans([]);
        setFilteredPlans([]);
        setIsLoading(false);
        return;
      }

      console.log(`Successfully fetched ${data.length} meal plans`);

      // Ensure all meal plans have the required properties
      const processedData = data.map((plan: any) => ({
        ...plan,
        // Ensure meal_data is an object
        meal_data: plan.meal_data || { mealPlan: { week: [], summary: { macros: { protein: '0g', carbs: '0g', fats: '0g' } } } },
        // Ensure status is set
        status: plan.status || 'active',
        // Ensure dates are strings
        start_date: plan.start_date || plan.created_at,
        end_date: plan.end_date || new Date(new Date(plan.created_at).getTime() + 7 * 24 * 60 * 60 * 1000).toISOString()
      }));

      setMealPlans(processedData);
      setFilteredPlans(processedData);
    } catch (err) {
      console.error('Error fetching meal plans:', err);
      setError('An unexpected error occurred');
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle back navigation
  const handleBack = () => {
    router.push('/dashboard');
  };

  // Handle create plan navigation
  const handleCreatePlan = () => {
    router.push('/meal-plan/generate');
  };

  // Apply filters and sorting to meal plans
  const applyFiltersAndSort = (query: string, filters: string[], sort: string, direction: 'asc' | 'desc') => {
    let result = [...mealPlans];

    // Apply search query
    if (query) {
      const lowerQuery = query.toLowerCase();
      result = result.filter(plan =>
        (plan.name?.toLowerCase().includes(lowerQuery) || false) ||
        (plan.description?.toLowerCase().includes(lowerQuery) || false)
      );
    }

    // Apply filters
    if (filters.length > 0) {
      result = result.filter(plan => {
        return filters.every(filter => {
          if (filter === 'active') return plan.status === 'active';
          if (filter === 'archived') return plan.status === 'archived';
          return true;
        });
      });
    }

    // Apply sorting
    result.sort((a, b) => {
      let valueA: any = a[sort as keyof MealPlan];
      let valueB: any = b[sort as keyof MealPlan];

      // Handle special cases
      if (sort === 'name') {
        valueA = a.name || '';
        valueB = b.name || '';
      }

      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return direction === 'asc'
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA);
      }

      if (typeof valueA === 'number' && typeof valueB === 'number') {
        return direction === 'asc' ? valueA - valueB : valueB - valueA;
      }

      // Default comparison
      if (valueA < valueB) return direction === 'asc' ? -1 : 1;
      if (valueA > valueB) return direction === 'asc' ? 1 : -1;
      return 0;
    });

    return result;
  };

  // These filter and search functions are prepared for future UI implementation
  // but are not currently used in the UI

  // // Handle search input
  // const handleSearch = (query: string) => {
  //   setSearchQuery(query);
  //   const filtered = applyFiltersAndSort(query, activeFilters, sortBy, sortDirection);
  //   setFilteredPlans(filtered);
  // };

  // // Handle sort change
  // const handleSort = (sort: string, direction: 'asc' | 'desc') => {
  //   setSortBy(sort);
  //   setSortDirection(direction);
  //   const filtered = applyFiltersAndSort(searchQuery, activeFilters, sort, direction);
  //   setFilteredPlans(filtered);
  // };

  // // Handle filter change
  // const handleFilter = (filter: { type: string, value: string }) => {
  //   let newFilters = [...activeFilters];

  //   if (filter.type === 'status') {
  //     // Remove any existing status filters
  //     newFilters = newFilters.filter(f => f !== 'active' && f !== 'archived');

  //     // Add the new status filter if it's not already there
  //     if (!newFilters.includes(filter.value)) {
  //       newFilters.push(filter.value);
  //     }
  //   }

  //   setActiveFilters(newFilters);
  //   const filtered = applyFiltersAndSort(searchQuery, newFilters, sortBy, sortDirection);
  //   setFilteredPlans(filtered);
  // };

  // // Handle clear filters
  // const handleClearFilters = () => {
  //   setActiveFilters([]);
  //   const filtered = applyFiltersAndSort(searchQuery, [], sortBy, sortDirection);
  //   setFilteredPlans(filtered);
  // };

  // Handle view plan detail
  const handleViewPlanDetail = (plan: MealPlan) => {
    setActivePlan(plan);
    setShowDetailView(true);
  };

  // Handle add to calendar
  const handleAddToCalendar = (plan: MealPlan) => {
    setSelectedPlanForModal(plan);
    setShowCalendarModal(true);
  };

  // Handle duplicate plan
  const handleDuplicatePlan = async (plan: MealPlan) => {
    try {
      setIsLoading(true);

      const { data, error } = await mealPlanService.duplicateMealPlan(plan.id, `${plan.name || 'Meal Plan'} (Copy)`);

      if (error) {
        console.error('Error duplicating meal plan:', error);
        toast.error('Failed to duplicate meal plan');
        return;
      }

      if (data) {
        setMealPlans(prev => [...prev, data]);
        setFilteredPlans(prev => [...prev, data]);
        toast.success('Meal plan duplicated successfully');
      }
    } catch (err) {
      console.error('Error duplicating meal plan:', err);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle edit plan
  const handleEditPlan = (plan: MealPlan) => {
    router.push(`/meal-plan/edit/${plan.id}`);
  };

  // Handle archive plan
  const handleArchivePlan = async (plan: MealPlan) => {
    try {
      setIsLoading(true);

      const { data, error } = await mealPlanService.archiveMealPlan(plan.id);

      if (error) {
        console.error('Error archiving meal plan:', error);
        toast.error('Failed to archive meal plan');
        return;
      }

      if (data) {
        // Update the plan in the state
        const updatedPlans = mealPlans.map(p =>
          p.id === plan.id ? { ...p, status: 'archived' } : p
        );

        setMealPlans(updatedPlans);
        setFilteredPlans(applyFiltersAndSort(searchQuery, activeFilters, sortBy, sortDirection));

        // Update active plan if needed
        if (activePlan?.id === plan.id) {
          setActivePlan({ ...activePlan, status: 'archived' });
        }

        toast.success('Meal plan archived successfully');
      }
    } catch (err) {
      console.error('Error archiving meal plan:', err);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle generate shopping list
  const handleGenerateShoppingList = async (plan: MealPlan) => {
    setSelectedPlanForModal(plan);
    setShowShoppingListModal(true);
  };

  // Handle delete plan
  const handleDeletePlan = async (planId: string) => {
    try {
      setIsLoading(true);

      console.log('Deleting meal plan with ID:', planId);

      // Format check for UUID
      let formattedId = planId;
      if (!planId.includes('-') && planId.length === 32) {
        // Convert to UUID format (8-4-4-4-12)
        formattedId = `${planId.slice(0, 8)}-${planId.slice(8, 12)}-${planId.slice(12, 16)}-${planId.slice(16, 20)}-${planId.slice(20)}`;
        console.log('Formatted ID:', formattedId);
      }

      // In development mode, use the debug API endpoint
      if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_BYPASS_AUTH === 'true') {
        console.log('Development mode: Using debug API endpoint');

        try {
          // Call the debug API endpoint
          const response = await fetch(`/api/debug/delete-meal-plan?id=${formattedId}`, {
            method: 'DELETE',
          });

          const result = await response.json();

          if (!response.ok) {
            console.error('Error deleting meal plan with debug API:', result);
            toast.error(result.error || 'Failed to delete meal plan');
            return;
          }

          console.log('Meal plan deleted successfully with debug API');

          // Update the UI state
          const updatedPlans = mealPlans.filter(plan => plan.id !== planId);
          setMealPlans(updatedPlans);
          setFilteredPlans(updatedPlans);

          // Handle active plan changes if needed
          if (activePlan?.id === planId) {
            setActivePlan(null);
            setShowDetailView(false);
          }

          toast.success('Meal plan deleted successfully');
          return;
        } catch (apiError) {
          console.error('Error calling debug API:', apiError);
          toast.error('Failed to delete meal plan with debug API');
          // Continue with the regular approach as a fallback
        }
      }

      // Regular approach using the meal plan service
      const { success, error } = await mealPlanService.deleteMealPlan(formattedId);

      if (error) {
        console.error('Error deleting meal plan:', error);

        // Check if it's an authentication error
        if (typeof error === 'string' && error.includes('Authentication error')) {
          toast.error('Please log in to delete meal plans');

          // In development mode with auth bypass, we can show a more helpful message
          if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_BYPASS_AUTH === 'true') {
            toast('Using debug API endpoint as a fallback...', { duration: 3000 });

            // Try the debug API endpoint as a fallback
            try {
              const response = await fetch(`/api/debug/delete-meal-plan?id=${formattedId}`, {
                method: 'DELETE',
              });

              const result = await response.json();

              if (!response.ok) {
                console.error('Error deleting meal plan with debug API fallback:', result);
                toast.error(result.error || 'Failed to delete meal plan');
                return;
              }

              console.log('Meal plan deleted successfully with debug API fallback');

              // Update the UI state
              const updatedPlans = mealPlans.filter(plan => plan.id !== planId);
              setMealPlans(updatedPlans);
              setFilteredPlans(updatedPlans);

              // Handle active plan changes if needed
              if (activePlan?.id === planId) {
                setActivePlan(null);
                setShowDetailView(false);
              }

              toast.success('Meal plan deleted successfully');
              return;
            } catch (fallbackError) {
              console.error('Error with debug API fallback:', fallbackError);
              toast.error('All deletion methods failed');
            }
          } else {
            toast.error('Please log in to delete meal plans');
          }
        } else {
          toast.error(typeof error === 'string' ? error : 'Failed to delete meal plan');
        }
        return;
      }

      if (success) {
        // Update the UI state
        const updatedPlans = mealPlans.filter(plan => plan.id !== planId);
        setMealPlans(updatedPlans);
        setFilteredPlans(updatedPlans);

        // Handle active plan changes if needed
        if (activePlan?.id === planId) {
          setActivePlan(null);
          setShowDetailView(false);
        }

        toast.success('Meal plan deleted successfully');
      }
    } catch (err) {
      console.error('Error deleting meal plan:', err);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Render loading state
  if (isSupabaseLoading) {
    // Initial loading state while Supabase client is initializing
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" size="icon" disabled>
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">My Meal Plans</h1>
          <Button disabled>Create New Plan</Button>
        </div>
        <div className="flex justify-center items-center py-12">
          <WeeklyPlanSkeleton />
        </div>
      </div>
    );
  }

  // Render loading state for data fetching
  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" size="icon" onClick={handleBack}>
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">My Meal Plans</h1>
          <Button onClick={handleCreatePlan}>Create New Plan</Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Show skeleton cards while loading */}
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="border rounded-lg p-4 animate-pulse">
              <div className="flex justify-between items-start mb-4">
                <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                <div className="h-8 bg-gray-200 rounded-full w-8"></div>
              </div>
              <div className="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
              <div className="grid grid-cols-3 gap-2 mb-4">
                <div className="h-12 bg-gray-200 rounded"></div>
                <div className="h-12 bg-gray-200 rounded"></div>
                <div className="h-12 bg-gray-200 rounded"></div>
              </div>
              <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-2 bg-gray-200 rounded w-full mb-4"></div>
              <div className="flex justify-between">
                <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    // Check if the error is related to RLS permissions
    const isRlsError = error.includes('permission denied') || error.includes('RLS');

    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" size="icon" onClick={handleBack}>
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">My Meal Plans</h1>
          <Button onClick={handleCreatePlan}>Create New Plan</Button>
        </div>
        <div className="flex flex-col items-center justify-center py-12">
          <div className="text-red-500 mb-4 text-center">{error}</div>

          {isRlsError ? (
            <div className="flex flex-col items-center gap-4">
              <p className="text-center text-gray-600 max-w-md">
                This appears to be a database permissions issue. Please contact your administrator to fix the Row Level Security (RLS) policies.
              </p>
              <div className="flex gap-2">
                <Button onClick={fetchMealPlans}>Try Again</Button>
              </div>
            </div>
          ) : (
            <Button onClick={fetchMealPlans}>Try Again</Button>
          )}
        </div>
      </div>
    );
  }

  // Render empty state - no meal plans found but not an error
  if (mealPlans.length === 0) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" size="icon" onClick={handleBack}>
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">My Meal Plans</h1>
          <Button onClick={handleCreatePlan}>Create New Plan</Button>
        </div>
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 max-w-md w-full">
            <h2 className="text-2xl font-bold mb-4">No Meal Plans Yet</h2>
            <p className="text-gray-500 mb-6">
              You haven't created any meal plans yet. Generate your first meal plan to get started with planning your meals for the week.
            </p>
            <div className="flex flex-col space-y-4">
              <Button onClick={handleCreatePlan} size="lg" className="w-full">
                Generate Meal Plan
              </Button>
              <p className="text-sm text-gray-400">
                Our AI will create a personalized meal plan based on your preferences, dietary restrictions, and budget.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render meal plan list or detail view
  return (
    <div className="container mx-auto py-8 px-4">
      {showDetailView && activePlan ? (
        <div>
          <div className="flex items-center mb-6">
            <Button variant="ghost" size="icon" onClick={() => setShowDetailView(false)}>
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-2xl font-bold ml-2">{activePlan.name || 'Meal Plan'}</h1>
          </div>

          <WeeklyPlanView mealPlan={activePlan} />
        </div>
      ) : (
        <div>
          <div className="flex items-center justify-between mb-6">
            <Button variant="ghost" size="icon" onClick={handleBack}>
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-2xl font-bold">My Meal Plans</h1>
            <Button onClick={handleCreatePlan}>Create New Plan</Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPlans.map(plan => (
              <MealPlanCard
                key={plan.id}
                plan={plan}
                isActive={activePlan?.id === plan.id}
                onSelect={handleViewPlanDetail}
                onEdit={() => handleEditPlan(plan)}
                onDelete={handleDeletePlan}
                onDuplicate={() => handleDuplicatePlan(plan)}
                onArchive={() => handleArchivePlan(plan)}
                onAddToCalendar={handleAddToCalendar}
                onGenerateShoppingList={() => handleGenerateShoppingList(plan)}
              />
            ))}
          </div>
        </div>
      )}

      {/* Modals */}
      <CalendarAssignmentModal
        mealPlan={selectedPlanForModal}
        open={showCalendarModal}
        onOpenChange={setShowCalendarModal}
        onSuccess={fetchMealPlans}
      />

      <ShoppingListGeneratorFixed
        mealPlan={selectedPlanForModal}
        open={showShoppingListModal}
        onOpenChange={setShowShoppingListModal}
      />
    </div>
  );
}

export default function ViewMealPlanPage() {
  return <ViewMealPlanContent />;
}
