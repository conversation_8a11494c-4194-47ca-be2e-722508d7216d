"use client";

import React, { useState, useEffect } from "react";
import { useSupabase } from "@/components/supabase-provider";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { ShoppingCart, Plus, Trash2, Save } from "lucide-react";

interface ShoppingListGeneratorProps {
  isOpen: boolean;
  onClose: () => void;
  mealPlan?: any;
}

interface ShoppingItem {
  id: string;
  name: string;
  quantity: string;
  unit: string;
  category: string;
  checked: boolean;
}

interface ShoppingCategory {
  name: string;
  items: ShoppingItem[];
}

export function ShoppingListGeneratorFixed({ isOpen, onClose, mealPlan }: ShoppingListGeneratorProps) {
  const { supabase } = useSupabase();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("list");
  const [categories, setCategories] = useState<ShoppingCategory[]>([]);
  const [newItem, setNewItem] = useState({ name: "", quantity: "1", unit: "item", category: "Other" });

  useEffect(() => {
    if (isOpen) {
      // Mock data for demonstration
      setCategories([
        {
          name: "Fruits",
          items: [
            { id: "1", name: "Apples", quantity: "4", unit: "", category: "Fruits", checked: false },
            { id: "2", name: "Bananas", quantity: "6", unit: "", category: "Fruits", checked: false }
          ]
        },
        {
          name: "Vegetables",
          items: [
            { id: "3", name: "Spinach", quantity: "1", unit: "bag", category: "Vegetables", checked: false },
            { id: "4", name: "Carrots", quantity: "1", unit: "lb", category: "Vegetables", checked: false }
          ]
        }
      ]);
    }
  }, [isOpen]);

  const handleAddItem = () => {
    if (!newItem.name.trim()) {
      toast.error("Please enter an item name");
      return;
    }

    const newItemObj: ShoppingItem = {
      id: `item_${Date.now()}`,
      name: newItem.name,
      quantity: newItem.quantity,
      unit: newItem.unit,
      category: newItem.category,
      checked: false
    };

    // Find the category
    const categoryIndex = categories.findIndex(cat => cat.name === newItem.category);

    if (categoryIndex >= 0) {
      // Category exists, add item to it
      const updatedCategories = [...categories];
      updatedCategories[categoryIndex].items.push(newItemObj);
      setCategories(updatedCategories);
    } else {
      // Create new category
      setCategories([
        ...categories,
        {
          name: newItem.category,
          items: [newItemObj]
        }
      ]);
    }

    // Reset form
    setNewItem({ name: "", quantity: "1", unit: "item", category: "Other" });
    toast.success("Item added to shopping list");
  };

  const handleToggleItem = (categoryName: string, itemId: string) => {
    const updatedCategories = categories.map(category => {
      if (category.name === categoryName) {
        return {
          ...category,
          items: category.items.map(item =>
            item.id === itemId ? { ...item, checked: !item.checked } : item
          )
        };
      }
      return category;
    });

    setCategories(updatedCategories);
  };

  const handleRemoveItem = (categoryName: string, itemId: string) => {
    const updatedCategories = categories.map(category => {
      if (category.name === categoryName) {
        return {
          ...category,
          items: category.items.filter(item => item.id !== itemId)
        };
      }
      return category;
    }).filter(category => category.items.length > 0); // Remove empty categories

    setCategories(updatedCategories);
  };

  const handleSaveList = () => {
    toast.success("Shopping list saved");
    onClose();
  };

  const getTotalItems = () => {
    return categories.reduce((total, category) => total + category.items.length, 0);
  };

  const getCheckedItems = () => {
    return categories.reduce((total, category) =>
      total + category.items.filter(item => item.checked).length, 0
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Shopping List</DialogTitle>
          <DialogDescription>
            Generate a shopping list for your meal plan
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="list">
              Shopping List ({getCheckedItems()}/{getTotalItems()})
            </TabsTrigger>
            <TabsTrigger value="add">Add Items</TabsTrigger>
          </TabsList>

          <TabsContent value="list" className="space-y-4">
            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : categories.length > 0 ? (
              <>
                <div className="flex justify-between items-center mb-4">
                  <div className="text-sm text-muted-foreground">
                    {getCheckedItems()} of {getTotalItems()} items checked
                  </div>
                </div>

                {categories.map((category) => (
                  <Card key={category.name}>
                    <CardHeader className="py-3">
                      <CardTitle className="text-md">{category.name}</CardTitle>
                    </CardHeader>
                    <CardContent className="py-0">
                      <div className="space-y-2">
                        {category.items.map((item) => (
                          <div
                            key={item.id}
                            className={`flex items-center justify-between p-2 rounded-md ${item.checked ? 'bg-muted/50' : ''}`}
                          >
                            <div className="flex items-center">
                              <Checkbox
                                id={item.id}
                                checked={item.checked}
                                onCheckedChange={() => handleToggleItem(category.name, item.id)}
                              />
                              <Label
                                htmlFor={item.id}
                                className={`ml-2 ${item.checked ? 'line-through text-muted-foreground' : ''}`}
                              >
                                {item.name} {item.quantity && `(${item.quantity} ${item.unit})`}
                              </Label>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRemoveItem(category.name, item.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </>
            ) : (
              <div className="text-center py-8">
                <ShoppingCart className="h-12 w-12 mx-auto text-muted-foreground" />
                <h3 className="mt-4 text-lg font-medium">Your shopping list is empty</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  Add items to your shopping list or generate one from your meal plan.
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="add">
            <Card>
              <CardHeader>
                <CardTitle>Add New Item</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-12 gap-4">
                  <div className="col-span-5">
                    <Label htmlFor="item-name">Item Name</Label>
                    <Input
                      id="item-name"
                      value={newItem.name}
                      onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                      placeholder="Enter item name"
                    />
                  </div>
                  <div className="col-span-2">
                    <Label htmlFor="item-quantity">Quantity</Label>
                    <Input
                      id="item-quantity"
                      value={newItem.quantity}
                      onChange={(e) => setNewItem({ ...newItem, quantity: e.target.value })}
                      type="text"
                    />
                  </div>
                  <div className="col-span-2">
                    <Label htmlFor="item-unit">Unit</Label>
                    <Input
                      id="item-unit"
                      value={newItem.unit}
                      onChange={(e) => setNewItem({ ...newItem, unit: e.target.value })}
                      placeholder="e.g., lbs, oz, etc."
                    />
                  </div>
                  <div className="col-span-3">
                    <Label htmlFor="item-category">Category</Label>
                    <Input
                      id="item-category"
                      value={newItem.category}
                      onChange={(e) => setNewItem({ ...newItem, category: e.target.value })}
                      placeholder="Category"
                      list="categories"
                    />
                    <datalist id="categories">
                      <option value="Fruits" />
                      <option value="Vegetables" />
                      <option value="Meat & Seafood" />
                      <option value="Dairy" />
                      <option value="Grains & Bread" />
                      <option value="Condiments & Spices" />
                      <option value="Other" />
                    </datalist>
                  </div>
                </div>
                <Button
                  className="mt-4"
                  onClick={handleAddItem}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Separator />

        <DialogFooter className="flex justify-between">
          <div>
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </div>
          <div>
            <Button onClick={handleSaveList} disabled={getTotalItems() === 0}>
              <Save className="h-4 w-4 mr-2" />
              Save & Close
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
