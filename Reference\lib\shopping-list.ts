"use client";

import { ShoppingItem, PantryItem } from "@/types/meal-plan";

// Mock data for development
const mockShoppingList: ShoppingItem[] = [
  {
    id: "i1",
    name: "Greek yogurt",
    quantity: "2",
    unit: "cups",
    category: "Dairy",
    cost: 3.49,
    inPantry: false
  },
  {
    id: "i2",
    name: "Mixed berries",
    quantity: "1",
    unit: "package",
    category: "Produce",
    cost: 4.99,
    inPantry: false
  },
  {
    id: "i3",
    name: "Granola",
    quantity: "1",
    unit: "package",
    category: "Cereals & Breakfast",
    cost: 3.99,
    inPantry: true
  },
  {
    id: "i4",
    name: "Honey",
    quantity: "1",
    unit: "bottle",
    category: "Condiments",
    cost: 4.49,
    inPantry: true
  },
  {
    id: "i5",
    name: "Chickpeas",
    quantity: "2",
    unit: "cans",
    category: "Canned Goods",
    cost: 1.98,
    inPantry: false
  },
  {
    id: "i6",
    name: "Cucumber",
    quantity: "1",
    unit: "",
    category: "Produce",
    cost: 0.79,
    inPantry: false
  },
  {
    id: "i7",
    name: "Cherry tomatoes",
    quantity: "1",
    unit: "pint",
    category: "Produce",
    cost: 2.99,
    inPantry: false
  },
  {
    id: "i8",
    name: "Red onion",
    quantity: "1",
    unit: "",
    category: "Produce",
    cost: 0.89,
    inPantry: false
  },
  {
    id: "i9",
    name: "Feta cheese",
    quantity: "1",
    unit: "package",
    category: "Dairy",
    cost: 3.99,
    inPantry: false
  },
  {
    id: "i10",
    name: "Olive oil",
    quantity: "1",
    unit: "bottle",
    category: "Oils & Vinegars",
    cost: 7.99,
    inPantry: true
  },
  {
    id: "i11",
    name: "Lemons",
    quantity: "3",
    unit: "",
    category: "Produce",
    cost: 1.49,
    inPantry: false
  },
  {
    id: "i12",
    name: "Whole grain bread",
    quantity: "1",
    unit: "loaf",
    category: "Bakery",
    cost: 3.49,
    inPantry: false
  },
  {
    id: "i13",
    name: "Avocados",
    quantity: "3",
    unit: "",
    category: "Produce",
    cost: 4.50,
    inPantry: false
  },
  {
    id: "i14",
    name: "Eggs",
    quantity: "1",
    unit: "dozen",
    category: "Dairy",
    cost: 2.99,
    inPantry: false
  },
  {
    id: "i15",
    name: "Pasta",
    quantity: "1",
    unit: "box",
    category: "Pasta & Rice",
    cost: 1.99,
    inPantry: true
  },
  {
    id: "i16",
    name: "Broccoli",
    quantity: "1",
    unit: "head",
    category: "Produce",
    cost: 1.79,
    inPantry: false
  },
  {
    id: "i17",
    name: "Bell pepper",
    quantity: "2",
    unit: "",
    category: "Produce",
    cost: 1.98,
    inPantry: false
  },
  {
    id: "i18",
    name: "Garlic",
    quantity: "1",
    unit: "head",
    category: "Produce",
    cost: 0.69,
    inPantry: false
  },
  {
    id: "i19",
    name: "Parmesan cheese",
    quantity: "1",
    unit: "container",
    category: "Dairy",
    cost: 3.99,
    inPantry: false
  }
];

const mockPantryItems: PantryItem[] = [
  {
    id: "p1",
    name: "Olive oil",
    quantity: "500",
    unit: "ml",
    category: "Oils & Vinegars",
    lowStock: false
  },
  {
    id: "p2",
    name: "Granola",
    quantity: "250",
    unit: "g",
    category: "Cereals & Breakfast",
    lowStock: false
  },
  {
    id: "p3",
    name: "Honey",
    quantity: "150",
    unit: "ml",
    category: "Condiments",
    lowStock: true
  },
  {
    id: "p4",
    name: "Pasta",
    quantity: "500",
    unit: "g",
    category: "Pasta & Rice",
    lowStock: false
  },
  {
    id: "p5",
    name: "Rice",
    quantity: "2",
    unit: "kg",
    category: "Pasta & Rice",
    lowStock: false
  },
  {
    id: "p6",
    name: "Black beans",
    quantity: "3",
    unit: "cans",
    category: "Canned Goods",
    lowStock: false
  },
  {
    id: "p7",
    name: "Chicken broth",
    quantity: "1",
    unit: "carton",
    category: "Canned Goods",
    lowStock: true
  },
  {
    id: "p8",
    name: "Salt",
    quantity: "200",
    unit: "g",
    category: "Spices & Seasonings",
    lowStock: false
  },
  {
    id: "p9",
    name: "Black pepper",
    quantity: "100",
    unit: "g",
    category: "Spices & Seasonings",
    lowStock: false
  },
  {
    id: "p10",
    name: "Flour",
    quantity: "1",
    unit: "kg",
    category: "Baking",
    lowStock: false
  }
];

export const generateShoppingList = async (): Promise<{
  shoppingList: ShoppingItem[];
  pantryItems: PantryItem[];
}> => {
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        shoppingList: mockShoppingList,
        pantryItems: mockPantryItems
      });
    }, 1000);
  });
};