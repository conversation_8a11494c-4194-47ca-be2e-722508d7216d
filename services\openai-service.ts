"use client";

import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";

export interface MealPlanGenerationParams {
  dietaryPreferences: string[];
  calorieGoal: number;
  allergies: string[];
  householdSize: number;
  weeklyBudget: number;
  cuisinePreferences?: string[];
  mealTypes?: string[];
  daysToGenerate?: number;
}

export interface GeneratedMealPlan {
  name: string;
  description: string;
  week: Array<{
    date: string;
    meals: Array<{
      id: string;
      name: string;
      type: string;
      calories: number;
      protein: number;
      carbs: number;
      fat: number;
      prepTime: number;
      ingredients: Array<{
        name: string;
        quantity: string;
        unit: string;
      }>;
      instructions?: string;
      tags?: string[];
    }>;
  }>;
  nutritionSummary: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sugar: number;
    sodium: number;
  };
  totalPrepTime: number;
  totalCost: number;
}

class OpenAIService {
  private supabase = createClientComponentClient();

  /**
   * Generate a meal plan using OpenAI
   */
  async generateMealPlan(params: MealPlanGenerationParams): Promise<GeneratedMealPlan> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      // Call the serverless function that handles the OpenAI API call
      const { data, error } = await this.supabase.functions.invoke('generate-meal-plan', {
        body: {
          params,
          userId: user.id
        }
      });
      
      if (error) throw error;
      
      return data.mealPlan;
    } catch (error) {
      console.error('Error generating meal plan:', error);
      throw error;
    }
  }

  /**
   * Save a generated meal plan to the database
   */
  async saveMealPlan(mealPlan: GeneratedMealPlan): Promise<string> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      // Calculate start and end dates from the week data
      const dates = mealPlan.week.map(day => day.date);
      dates.sort();
      
      const startDate = dates[0];
      const endDate = dates[dates.length - 1];
      
      // Calculate total cost
      const totalCost = mealPlan.totalCost || 0;
      
      // Insert the meal plan into the database
      const { data, error } = await this.supabase
        .from('meal_plans')
        .insert({
          user_id: user.id,
          name: mealPlan.name,
          description: mealPlan.description,
          start_date: startDate,
          end_date: endDate,
          meal_data: {
            mealPlan,
            dietaryPreferences: mealPlan.week[0]?.meals[0]?.tags || []
          },
          status: 'active',
          total_cost: totalCost
        })
        .select('id')
        .single();
      
      if (error) throw error;
      
      return data.id;
    } catch (error) {
      console.error('Error saving meal plan:', error);
      throw error;
    }
  }
}

export const openAIService = new OpenAIService();
