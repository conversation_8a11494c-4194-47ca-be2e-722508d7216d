"use client";

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Button } from '@/components/ui/button';
import { useMealPlanStore } from '@/lib/stores/meal-plan-store-supabase';
import { mealPlanService } from '@/lib/supabase/meal-plan-service';
import { format, addDays } from 'date-fns';

export default function MealPlanCalendarDebugPage() {
  const [user, setUser] = useState<any>(null);
  const [assignments, setAssignments] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  
  // Get the meal plan store state
  const storeState = useMealPlanStore(state => ({
    userId: state.userId,
    mealPlan: state.mealPlan,
    isLoading: state.isLoading
  }));
  
  // Store actions
  const { 
    setUserId, 
    loadMealPlanFromDatabase, 
    assignMealPlanToCalendar 
  } = useMealPlanStore();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const supabase = createClientComponentClient();
        const { data } = await supabase.auth.getUser();
        setUser(data.user);
        
        if (data.user) {
          // Set the user ID in the store
          setUserId(data.user.id);
          
          // Load meal plan from database
          await loadMealPlanFromDatabase();
        }
      } catch (err) {
        console.error('Error checking auth:', err);
        setError('Failed to check authentication status');
      }
    };
    
    checkAuth();
  }, [setUserId, loadMealPlanFromDatabase]);

  const fetchAssignments = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (!user) {
        setError('You must be logged in to fetch assignments');
        return;
      }
      
      // Fetch assignments using the service
      const data = await mealPlanService.getMealPlanAssignments(user.id);
      
      setAssignments(data || []);
      setResult({
        message: `Found ${data?.length || 0} meal plan assignments in the database`,
        data: data
      });
    } catch (err: any) {
      console.error('Error in fetchAssignments:', err);
      setError(`An unexpected error occurred: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const assignCurrentMealPlan = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (!user) {
        setError('You must be logged in to assign a meal plan');
        return;
      }
      
      // Check if there's a meal plan in the store
      const currentMealPlan = useMealPlanStore.getState().mealPlan;
      
      if (!currentMealPlan) {
        setError('No meal plan in the store to assign');
        return;
      }
      
      // Calculate start and end dates (today to 7 days from now)
      const startDate = format(new Date(), 'yyyy-MM-dd');
      const endDate = format(addDays(new Date(), 6), 'yyyy-MM-dd');
      
      // Assign meal plan to calendar using the store
      const success = await assignMealPlanToCalendar(startDate, endDate);
      
      if (success) {
        setResult({
          message: 'Meal plan assigned successfully to calendar',
          dates: { startDate, endDate }
        });
        
        // Refresh the assignments list
        fetchAssignments();
      } else {
        setError('Failed to assign meal plan to calendar');
      }
    } catch (err: any) {
      console.error('Error in assignCurrentMealPlan:', err);
      setError(`An unexpected error occurred: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testDirectAssignment = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (!user) {
        setError('You must be logged in to test direct assignment');
        return;
      }
      
      const supabase = createClientComponentClient();
      
      // Get the most recent meal plan
      const { data: mealPlans, error: fetchError } = await supabase
        .from('meal_plans')
        .select('id')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1);
      
      if (fetchError || !mealPlans || mealPlans.length === 0) {
        setError('Failed to find a meal plan to assign');
        return;
      }
      
      // Calculate start and end dates (today to 7 days from now)
      const startDate = format(new Date(), 'yyyy-MM-dd');
      const endDate = format(addDays(new Date(), 6), 'yyyy-MM-dd');
      
      // Create the assignment directly
      const { data, error } = await supabase
        .from('meal_plan_assignments')
        .insert({
          user_id: user.id,
          meal_plan_id: mealPlans[0].id,
          start_date: startDate,
          end_date: endDate
        })
        .select();
      
      if (error) {
        console.error('Error creating assignment:', error);
        setError(`Failed to create assignment: ${error.message}`);
        return;
      }
      
      setResult({
        message: 'Assignment created successfully via direct database operation',
        data: data
      });
      
      // Refresh the assignments list
      fetchAssignments();
    } catch (err: any) {
      console.error('Error in testDirectAssignment:', err);
      setError(`An unexpected error occurred: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const removeAssignment = async (assignmentId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      // Remove assignment using the service
      const success = await mealPlanService.removeMealPlanAssignment(assignmentId);
      
      if (success) {
        setResult({
          message: 'Assignment removed successfully',
          assignmentId
        });
        
        // Refresh the assignments list
        fetchAssignments();
      } else {
        setError('Failed to remove assignment');
      }
    } catch (err: any) {
      console.error('Error in removeAssignment:', err);
      setError(`An unexpected error occurred: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Meal Plan Calendar Debug Page</h1>
      
      <div className="mb-6 p-4 border rounded-md">
        <h2 className="text-lg font-bold mb-2">Authentication Status</h2>
        {user ? (
          <div className="text-green-600">
            Authenticated as: {user.email} (ID: {user.id})
          </div>
        ) : (
          <div className="text-red-600">Not authenticated</div>
        )}
      </div>
      
      <div className="mb-6 p-4 border rounded-md">
        <h2 className="text-lg font-bold mb-2">Meal Plan Store State</h2>
        <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-40">
          {JSON.stringify({
            userId: storeState.userId,
            hasMealPlan: !!storeState.mealPlan,
            isLoading: storeState.isLoading,
            mealPlanDays: storeState.mealPlan ? Object.keys(storeState.mealPlan) : []
          }, null, 2)}
        </pre>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="p-4 border rounded-md">
          <h2 className="text-lg font-bold mb-2">Calendar Operations</h2>
          <div className="flex flex-col gap-2">
            <Button onClick={fetchAssignments} disabled={loading}>
              Fetch Calendar Assignments
            </Button>
            <Button onClick={assignCurrentMealPlan} disabled={loading}>
              Assign Current Meal Plan to Calendar
            </Button>
            <Button onClick={testDirectAssignment} disabled={loading}>
              Test Direct Database Assignment
            </Button>
          </div>
        </div>
        
        <div className="p-4 border rounded-md">
          <h2 className="text-lg font-bold mb-2">Store Operations</h2>
          <div className="flex flex-col gap-2">
            <Button onClick={() => loadMealPlanFromDatabase()} disabled={loading}>
              Load Meal Plan from Database
            </Button>
          </div>
        </div>
      </div>
      
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <h2 className="text-lg font-bold text-red-600 mb-2">Error</h2>
          <p className="text-red-600">{error}</p>
        </div>
      )}
      
      {result && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
          <h2 className="text-lg font-bold text-green-600 mb-2">Result</h2>
          <p className="text-green-600 mb-2">{result.message}</p>
          <pre className="bg-white p-2 rounded overflow-auto max-h-96">
            {JSON.stringify(result.data || result.dates, null, 2)}
          </pre>
        </div>
      )}
      
      {assignments.length > 0 && (
        <div className="mb-6 p-4 border rounded-md">
          <h2 className="text-lg font-bold mb-2">Calendar Assignments ({assignments.length})</h2>
          <div className="overflow-auto max-h-96">
            {assignments.map((assignment) => (
              <div key={assignment.id} className="mb-4 p-3 bg-gray-50 rounded-md">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold">
                      Assignment ID: {assignment.id.substring(0, 8)}...
                    </h3>
                    <p className="text-sm text-gray-600">
                      Date Range: {format(new Date(assignment.start_date), 'MMM d, yyyy')} - {format(new Date(assignment.end_date), 'MMM d, yyyy')}
                    </p>
                    <p className="text-sm text-gray-600">
                      Meal Plan ID: {assignment.meal_plan_id.substring(0, 8)}...
                    </p>
                  </div>
                  <Button 
                    variant="destructive" 
                    size="sm"
                    onClick={() => removeAssignment(assignment.id)}
                    disabled={loading}
                  >
                    Remove
                  </Button>
                </div>
                <details className="mt-2">
                  <summary className="cursor-pointer text-blue-600">View Details</summary>
                  <pre className="mt-2 bg-white p-2 rounded overflow-auto max-h-40 text-xs">
                    {JSON.stringify(assignment, null, 2)}
                  </pre>
                </details>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
