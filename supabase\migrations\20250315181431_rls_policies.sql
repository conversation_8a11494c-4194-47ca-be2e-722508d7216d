-- First, grant basic schema permissions
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;

-- Grant table permissions
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO postgres, service_role;

-- Enable RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Create policies for users table
CREATE POLICY "Users can view own record" 
    ON public.users
    FOR SELECT
    USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update own record" 
    ON public.users
    FOR UPDATE
    USING (auth.uid()::text = id::text);

CREATE POLICY "Allow insert during signup" 
    ON public.users
    FOR INSERT
    WITH CHECK (auth.uid()::text = id::text);

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON public.users TO authenticated;
GRANT USAGE ON SEQUENCE users_id_seq TO authenticated;

-- Create policies for meal_plans table if not exists
CREATE POLICY "Users can manage own meal plans"
    ON public.meal_plans
    FOR ALL
    USING (auth.uid()::text = user_id::text);

GRANT ALL ON public.meal_plans TO authenticated;

-- Enable RLS
ALTER TABLE public.meal_plans ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Enable read for users based on user_id" ON public.meal_plans;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.meal_plans;
DROP POLICY IF EXISTS "Enable update for users based on user_id" ON public.meal_plans;
DROP POLICY IF EXISTS "Enable delete for users based on user_id" ON public.meal_plans;

-- Create policies
CREATE POLICY "Enable read for users based on user_id" 
    ON public.meal_plans
    FOR SELECT
    TO authenticated
    USING (auth.uid()::text = user_id::text);

CREATE POLICY "Enable insert for authenticated users only" 
    ON public.meal_plans
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Enable update for users based on user_id" 
    ON public.meal_plans
    FOR UPDATE
    TO authenticated
    USING (auth.uid()::text = user_id::text);

CREATE POLICY "Enable delete for users based on user_id" 
    ON public.meal_plans
    FOR DELETE
    TO authenticated
    USING (auth.uid()::text = user_id::text);



