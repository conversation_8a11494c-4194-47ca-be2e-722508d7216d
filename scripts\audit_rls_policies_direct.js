// This script audits all RLS policies in your Supabase database
// Run this script with: node scripts/audit_rls_policies_direct.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase connection details
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service role key not found in environment variables.');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { autoRefreshToken: false, persistSession: false }
});

async function auditRlsPolicies() {
  try {
    console.log('Auditing RLS policies...\n');
    
    // Step 1: Get all tables in the public schema
    const { data: tablesResponse, error: tablesError } = await supabase.auth.admin.executeRawQuery(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
    `);
    
    if (tablesError) {
      console.error('Error fetching tables:', tablesError);
      return;
    }
    
    const tables = tablesResponse.map(row => ({ table_name: row.table_name }));
    console.log(`Found ${tables.length} tables in the public schema.\n`);
    
    // Step 2: For each table, check if RLS is enabled and get its policies
    for (const table of tables) {
      const tableName = table.table_name;
      
      // Check if RLS is enabled
      const { data: rlsResponse, error: rlsError } = await supabase.auth.admin.executeRawQuery(`
        SELECT relrowsecurity 
        FROM pg_class 
        WHERE oid = '${tableName}'::regclass::oid
      `);
      
      if (rlsError) {
        console.log(`Error checking RLS for table ${tableName}:`, rlsError);
        continue;
      }
      
      const rlsEnabled = rlsResponse.length > 0 ? rlsResponse[0].relrowsecurity : false;
      
      console.log(`Table: ${tableName}`);
      console.log(`RLS Enabled: ${rlsEnabled ? 'Yes' : 'No'}`);
      
      if (rlsEnabled) {
        // Get policies for this table
        const { data: policiesResponse, error: policiesError } = await supabase.auth.admin.executeRawQuery(`
          SELECT 
            polname::text as policyname,
            relname::text as tablename,
            CASE
              WHEN polcmd = 'r' THEN 'SELECT'
              WHEN polcmd = 'a' THEN 'INSERT'
              WHEN polcmd = 'w' THEN 'UPDATE'
              WHEN polcmd = 'd' THEN 'DELETE'
              WHEN polcmd = '*' THEN 'ALL'
            END as cmd,
            polroles::text[] as roles,
            pg_get_expr(polqual, polrelid, true) as using_expr,
            pg_get_expr(polwithcheck, polrelid, true) as check_expr
          FROM pg_policy
          JOIN pg_class ON pg_class.oid = pg_policy.polrelid
          WHERE relname = '${tableName}'
        `);
        
        if (policiesError) {
          console.log(`Error fetching policies for table ${tableName}:`, policiesError);
          continue;
        }
        
        const policies = policiesResponse;
        
        if (policies && policies.length > 0) {
          console.log(`Policies (${policies.length}):`);
          policies.forEach((policy, index) => {
            console.log(`  ${index + 1}. ${policy.policyname}`);
            console.log(`     Command: ${policy.cmd}`);
            console.log(`     Roles: ${policy.roles}`);
            console.log(`     Using expression: ${policy.using_expr || 'N/A'}`);
            console.log(`     Check expression: ${policy.check_expr || 'N/A'}`);
          });
        } else {
          console.log('  No policies found, but RLS is enabled (restrictive)');
        }
      }
      
      console.log('-----------------------------------');
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the script
auditRlsPolicies();
