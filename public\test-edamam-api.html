<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Edamam API Test</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1, h2 {
      color: #333;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="text"] {
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    button:hover {
      background-color: #45a049;
    }
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
    }
    .error {
      color: #d9534f;
      font-weight: bold;
    }
    .success {
      color: #5cb85c;
      font-weight: bold;
    }
    .test-section {
      margin-bottom: 30px;
      border: 1px solid #ddd;
      padding: 15px;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <h1>Edamam API Test</h1>

  <div class="form-group">
    <label for="recipe-app-id">Recipe API App ID:</label>
    <input type="text" id="recipe-app-id" placeholder="Enter Recipe API App ID">
  </div>

  <div class="form-group">
    <label for="recipe-app-key">Recipe API App Key:</label>
    <input type="text" id="recipe-app-key" placeholder="Enter Recipe API App Key">
  </div>

  <div class="form-group">
    <label for="food-db-app-id">Food Database API App ID:</label>
    <input type="text" id="food-db-app-id" placeholder="Enter Food Database API App ID">
  </div>

  <div class="form-group">
    <label for="food-db-app-key">Food Database API App Key:</label>
    <input type="text" id="food-db-app-key" placeholder="Enter Food Database API App Key">
  </div>

  <button id="test-recipe-api">Test Recipe API</button>
  <button id="test-food-db-api">Test Food Database API</button>

  <div class="test-section">
    <h2>Recipe API Test Results</h2>
    <pre id="recipe-api-results">No results yet. Click "Test Recipe API" to run the test.</pre>
  </div>

  <div class="test-section">
    <h2>Food Database API Test Results</h2>
    <pre id="food-db-api-results">No results yet. Click "Test Food Database API" to run the test.</pre>
  </div>

  <script>
    // Recipe API test
    document.getElementById('test-recipe-api').addEventListener('click', async () => {
      const recipeAppId = document.getElementById('recipe-app-id').value;
      const recipeAppKey = document.getElementById('recipe-app-key').value;
      const resultsElement = document.getElementById('recipe-api-results');

      if (!recipeAppId || !recipeAppKey) {
        resultsElement.innerHTML = '<span class="error">Please enter Recipe API App ID and App Key</span>';
        return;
      }

      resultsElement.innerHTML = 'Testing Recipe API...';

      try {
        // Prepare the query parameters
        const queryParams = new URLSearchParams({
          type: 'public',
          app_id: recipeAppId,
          app_key: recipeAppKey,
          q: 'chicken'
        });

        const url = `https://api.edamam.com/api/recipes/v2?${queryParams.toString()}`;
        resultsElement.innerHTML = `Making request to: ${url}`;

        // Make the API request
        const response = await fetch(url, {
          headers: {
            'Edamam-Account-User': 'user123' // Add a default user ID
          }
        });
        const data = await response.json();

        // Check if the response is successful
        if (response.ok) {
          resultsElement.innerHTML = `<span class="success">Recipe Search API test: SUCCESS</span>\n\nFound ${data.hits.length} recipes\nFirst recipe: ${data.hits[0].recipe.label}\n\nFull response:\n${JSON.stringify(data, null, 2)}`;
        } else {
          resultsElement.innerHTML = `<span class="error">Recipe Search API test: FAILED</span>\n\nResponse: ${response.status} ${response.statusText}\n\nError details:\n${JSON.stringify(data, null, 2)}`;
        }
      } catch (error) {
        resultsElement.innerHTML = `<span class="error">Recipe Search API test: ERROR</span>\n\nError message: ${error.message}`;
      }
    });

    // Food Database API test
    document.getElementById('test-food-db-api').addEventListener('click', async () => {
      const foodDbAppId = document.getElementById('food-db-app-id').value;
      const foodDbAppKey = document.getElementById('food-db-app-key').value;
      const resultsElement = document.getElementById('food-db-api-results');

      if (!foodDbAppId || !foodDbAppKey) {
        resultsElement.innerHTML = '<span class="error">Please enter Food Database API App ID and App Key</span>';
        return;
      }

      resultsElement.innerHTML = 'Testing Food Database API...';

      try {
        // Prepare the query parameters
        const queryParams = new URLSearchParams({
          app_id: foodDbAppId,
          app_key: foodDbAppKey,
          ingr: 'apple'
        });

        const url = `https://api.edamam.com/api/food-database/v2/parser?${queryParams.toString()}`;
        resultsElement.innerHTML = `Making request to: ${url}`;

        // Make the API request
        const response = await fetch(url);
        const data = await response.json();

        // Check if the response is successful
        if (response.ok) {
          resultsElement.innerHTML = `<span class="success">Food Database API test: SUCCESS</span>\n\nFound ${data.hints.length} food items\nFirst food item: ${data.hints[0].food.label}\n\nFull response:\n${JSON.stringify(data, null, 2)}`;

          // Test nutrients endpoint with the first food item
          if (data.hints.length > 0) {
            const foodId = data.hints[0].food.foodId;
            await testFoodNutrientsAPI(foodId, foodDbAppId, foodDbAppKey, resultsElement);
          }
        } else {
          resultsElement.innerHTML = `<span class="error">Food Database API test: FAILED</span>\n\nResponse: ${response.status} ${response.statusText}\n\nError details:\n${JSON.stringify(data, null, 2)}`;
        }
      } catch (error) {
        resultsElement.innerHTML = `<span class="error">Food Database API test: ERROR</span>\n\nError message: ${error.message}`;
      }
    });

    // Test Food Nutrients API
    async function testFoodNutrientsAPI(foodId, appId, appKey, resultsElement) {
      try {
        resultsElement.innerHTML += '\n\nTesting Food Nutrients API...';

        // Prepare the request body
        const requestBody = {
          ingredients: [
            {
              quantity: 1,
              measureURI: 'http://www.edamam.com/ontologies/edamam.owl#Measure_unit',
              foodId: foodId
            }
          ]
        };

        const url = `https://api.edamam.com/api/food-database/v2/nutrients?app_id=${appId}&app_key=${appKey}`;
        resultsElement.innerHTML += `\nMaking request to: ${url}\nRequest body: ${JSON.stringify(requestBody, null, 2)}`;

        // Make the API request
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        });

        const data = await response.json();

        // Check if the response is successful
        if (response.ok) {
          resultsElement.innerHTML += `\n\n<span class="success">Food Nutrients API test: SUCCESS</span>\n\nCalories: ${data.calories}\nProtein: ${JSON.stringify(data.totalNutrients.PROCNT)}\nCarbs: ${JSON.stringify(data.totalNutrients.CHOCDF)}\nFat: ${JSON.stringify(data.totalNutrients.FAT)}\n\nFull response:\n${JSON.stringify(data, null, 2)}`;
        } else {
          resultsElement.innerHTML += `\n\n<span class="error">Food Nutrients API test: FAILED</span>\n\nResponse: ${response.status} ${response.statusText}\n\nError details:\n${JSON.stringify(data, null, 2)}`;
        }
      } catch (error) {
        resultsElement.innerHTML += `\n\n<span class="error">Food Nutrients API test: ERROR</span>\n\nError message: ${error.message}`;
      }
    }
  </script>
</body>
</html>
