// Simple script to insert a single recipe into Supabase
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://nhboafhjkkmuwwoolktr.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.F6v2quJgeoQubU1PIRPVjGauX6fsqaW0PKvfLebbABw';

// Use a hardcoded user ID for testing
const hardcodedUserId = '00000000-0000-0000-0000-000000000000';

console.log('Using Supabase URL:', supabaseUrl);
console.log('Using hardcoded user ID:', hardcodedUserId);

const supabase = createClient(supabaseUrl, supabaseKey);

// Define a single recipe to insert
const recipe = {
  name: "Grilled Chicken Salad",
  description: "A healthy and delicious salad with grilled chicken breast, mixed greens, and a light vinaigrette dressing.",
  prep_time: 15,
  cook_time: 20,
  servings: 2,
  cost_per_serving: 4.50,
  image: "https://images.unsplash.com/photo-1527477396000-e27163b481c2",
  is_favorite: true,
  difficulty: "Easy",
  meal_type: "Lunch",
  instructions: [
    "Season chicken breasts with salt and pepper.",
    "Grill chicken for 6-8 minutes per side until fully cooked.",
    "Let chicken rest for 5 minutes, then slice into strips.",
    "In a large bowl, combine mixed greens, tomatoes, cucumber, and red onion.",
    "In a small bowl, whisk together olive oil, lemon juice, salt, and pepper.",
    "Drizzle dressing over salad and toss to combine.",
    "Top with sliced grilled chicken and serve immediately."
  ],
  ingredients: [
    { name: "Chicken breast", amount: "2", unit: "pieces" },
    { name: "Mixed greens", amount: "4", unit: "cups" },
    { name: "Cherry tomatoes", amount: "1", unit: "cup" },
    { name: "Cucumber", amount: "1", unit: "medium" },
    { name: "Red onion", amount: "1/4", unit: "cup" },
    { name: "Olive oil", amount: "2", unit: "tbsp" },
    { name: "Lemon juice", amount: "1", unit: "tbsp" },
    { name: "Salt", amount: "1/2", unit: "tsp" },
    { name: "Black pepper", amount: "1/4", unit: "tsp" }
  ],
  nutrition: {
    calories: 350,
    protein: 35,
    carbs: 15,
    fat: 18,
    fiber: 5
  },
  dietary_restrictions: ["high-protein", "low-carb", "gluten-free"],
  user_id: hardcodedUserId
};

async function insertRecipe() {
  console.log('Inserting recipe...');
  
  try {
    // Insert recipe into Supabase
    const { data, error } = await supabase
      .from('recipes')
      .insert(recipe)
      .select();
    
    if (error) {
      console.error('Error inserting recipe:', error);
      process.exit(1);
    }
    
    console.log('Successfully inserted recipe:', data);
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Run the insert function
insertRecipe();
