export interface Ingredient {
  name: string;
  quantity: string;
  unit: string;
}

export interface Nutrition {
  protein: number;
  carbs: number;
  fat: number;
}

export interface Meal {
  id: string;
  name: string;
  ingredients: Ingredient[];
  instructions: string[];
  prepTime: number;
  calories: number;
  cost: number;
  servings: number;
  nutrition: Nutrition;
  status?: 'cooked' | 'skipped' | null;
  image?: string;
  favorite?: boolean;
  notes?: string;
}

export interface MealPlan {
  [day: string]: {
    [mealType: string]: Meal;
  };
}

export interface MealGenerationOptions {
  name: string;
  days: number;
  mealsPerDay: number;
  calories: number;
  dietaryPreferences: string[];
  excludeIngredients: string[];
  budget: 'low' | 'medium' | 'high';
  cookingTime: 'quick' | 'medium' | 'long';
  cuisineTypes?: string[];
  additionalNotes?: string;

  // Legacy fields for compatibility
  caloriesPerDay?: number;
  dietType?: string;
  preferences?: {
    usePantryItems: boolean;
    optimizeIngredients: boolean;
    includeLeftovers: boolean;
  };
}

export interface ShoppingItem {
  id: string;
  name: string;
  quantity: string;
  unit: string;
  category: string;
  cost: number;
  inPantry: boolean;
}

export interface PantryItem {
  id: string;
  name: string;
  quantity: string;
  unit: string;
  category: string;
  lowStock: boolean;
}

// Helper type for drag and drop operations
export interface MealDragItem {
  id: string;
  day: string;
  mealType: string;
  meal: Meal;
}

// Legacy type for compatibility with existing code
export interface LegacyMealPlan {
  id: string;
  user_id: string;
  start_date: string;
  end_date: string;
  total_cost: number;
  meal_data: any;
  status: string;
  created_at: string;
  updated_at: string;
  name?: string;
  description?: string;
}

/**
 * Meal plan generation approach
 */
export type MealPlanGenerationApproach = 'edaspoon' | 'ai' | 'hybrid';

/**
 * Meal plan generation status
 */
export interface MealPlanGenerationStatus {
  isGenerating: boolean;
  progress: number;
  currentStep: string;
  error: string | null;
}

/**
 * Meal plan generation result
 */
export interface MealPlanGenerationResult {
  mealPlan: MealPlan;
  approach: MealPlanGenerationApproach;
  generationTime: number;
  options: MealGenerationOptions;
  createdAt: string;
}
