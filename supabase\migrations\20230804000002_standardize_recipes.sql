-- Migration: Standardize recipes table
-- This migration ensures the recipes table has a consistent structure
-- and proper RLS policies

-- First, record this migration
INSERT INTO public.schema_migrations (version, description)
VALUES ('20230804000002', 'Standardize recipes table')
ON CONFLICT (version) DO NOTHING;

-- Ensure the recipes table exists with the correct structure
CREATE TABLE IF NOT EXISTS public.recipes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    instructions JSONB,
    ingredients JSONB,
    prep_time INTEGER,
    cook_time INTEGER,
    servings INTEGER,
    calories_per_serving INTEGER,
    protein_per_serving DOUBLE PRECISION,
    carbs_per_serving DOUBLE PRECISION,
    fat_per_serving DOUBLE PRECISION,
    image_url TEXT,
    tags TEXT[],
    is_favorite BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add foreign key constraint if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'recipes_user_id_fkey' 
        AND conrelid = 'public.recipes'::regclass
    ) THEN
        ALTER TABLE public.recipes
        ADD CONSTRAINT recipes_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END
$$;

-- Add comment to the table
COMMENT ON TABLE public.recipes IS 'Stores user recipes';

-- Enable Row Level Security
ALTER TABLE public.recipes ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own recipes" ON public.recipes;
DROP POLICY IF EXISTS "Users can insert their own recipes" ON public.recipes;
DROP POLICY IF EXISTS "Users can update their own recipes" ON public.recipes;
DROP POLICY IF EXISTS "Users can delete their own recipes" ON public.recipes;
DROP POLICY IF EXISTS "Enable read for users based on user_id" ON public.recipes;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.recipes;
DROP POLICY IF EXISTS "Enable update for users based on user_id" ON public.recipes;
DROP POLICY IF EXISTS "Enable delete for users based on user_id" ON public.recipes;

-- Create standardized policies
CREATE POLICY "Users can view their own recipes"
ON public.recipes
FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own recipes"
ON public.recipes
FOR INSERT
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own recipes"
ON public.recipes
FOR UPDATE
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete their own recipes"
ON public.recipes
FOR DELETE
USING (user_id = auth.uid());

-- Grant permissions to authenticated users
GRANT ALL ON public.recipes TO authenticated;
