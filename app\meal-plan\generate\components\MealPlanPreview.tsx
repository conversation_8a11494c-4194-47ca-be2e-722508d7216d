'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Clock, DollarSign, Utensils, Flame, Beef, Wheat, Droplet } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";


interface MealPlanPreviewProps {
  preferences: {
    dietaryRestrictions: string[];
    cuisinePreferences: string[];
    budget: number;
    householdSize: number;
    skillLevel: string;
    prepTime: number;
  };
  isLoading?: boolean;
}

// Sample meal data based on preferences
const getSampleMeals = (preferences: MealPlanPreviewProps['preferences']) => {
  // Base meals that can be customized based on preferences
  const baseMeals = {
    breakfast: [
      {
        name: "Avocado Toast with Poached Eggs",
        image: "https://images.unsplash.com/photo-1525351484163-7529414344d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80",
        prepTime: 15,
        cost: 3.50,
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: false,
        nutrition: {
          calories: 280,
          protein: 12,
          carbs: 22,
          fat: 16,
          fiber: 6
        },
      },
      {
        name: "Greek Yogurt with Berries and Granola",
        image: "https://images.unsplash.com/photo-1484723091739-30a097e8f929?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80",
        prepTime: 5,
        cost: 2.75,
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: true,
        nutrition: {
          calories: 320,
          protein: 20,
          carbs: 45,
          fat: 8,
          fiber: 5
        },
      },
      {
        name: "Vegan Smoothie Bowl",
        image: "https://images.unsplash.com/photo-1553530666-ba11a90a0868?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80",
        prepTime: 10,
        cost: 3.25,
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        nutrition: {
          calories: 350,
          protein: 10,
          carbs: 65,
          fat: 7,
          fiber: 8
        },
      }
    ],
    lunch: [
      {
        name: "Mediterranean Quinoa Salad",
        image: "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80",
        prepTime: 20,
        cost: 4.50,
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        nutrition: {
          calories: 380,
          protein: 15,
          carbs: 55,
          fat: 12,
          fiber: 8
        },
      },
      {
        name: "Chicken Caesar Wrap",
        image: "https://images.unsplash.com/photo-1550304943-4f24f54ddde9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80",
        prepTime: 15,
        cost: 5.25,
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: false,
        nutrition: {
          calories: 450,
          protein: 30,
          carbs: 35,
          fat: 22,
          fiber: 3
        },
      },
      {
        name: "Lentil Soup with Crusty Bread",
        image: "https://images.unsplash.com/photo-1547592180-85f173990554?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80",
        prepTime: 30,
        cost: 3.75,
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: false,
        nutrition: {
          calories: 320,
          protein: 18,
          carbs: 50,
          fat: 6,
          fiber: 12
        },
      }
    ],
    dinner: [
      {
        name: "Grilled Salmon with Roasted Vegetables",
        image: "https://images.unsplash.com/photo-1467003909585-2f8a72700288?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80",
        prepTime: 35,
        cost: 8.50,
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        nutrition: {
          calories: 420,
          protein: 35,
          carbs: 25,
          fat: 20,
          fiber: 6
        },
      },
      {
        name: "Vegetable Stir Fry with Tofu",
        image: "https://images.unsplash.com/photo-1512058564366-18510be2db19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80",
        prepTime: 25,
        cost: 6.25,
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        nutrition: {
          calories: 380,
          protein: 15,
          carbs: 55,
          fat: 12,
          fiber: 8
        },
      },
      {
        name: "Spaghetti Bolognese",
        image: "https://images.unsplash.com/photo-1551892374-ecf8754cf8b0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80",
        prepTime: 40,
        cost: 5.75,
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: false,
        nutrition: {
          calories: 520,
          protein: 25,
          carbs: 65,
          fat: 18,
          fiber: 4
        },
      }
    ]
  };

  // Filter meals based on dietary restrictions
  const isVegetarian = preferences.dietaryRestrictions.includes('vegetarian');
  const isVegan = preferences.dietaryRestrictions.includes('vegan');
  const isGlutenFree = preferences.dietaryRestrictions.includes('gluten-free');

  const filteredMeals = {
    breakfast: baseMeals.breakfast.filter(meal => {
      if (isVegan && !meal.isVegan) return false;
      if (isVegetarian && !meal.isVegetarian) return false;
      if (isGlutenFree && !meal.isGlutenFree) return false;
      return meal.prepTime <= preferences.prepTime;
    }),
    lunch: baseMeals.lunch.filter(meal => {
      if (isVegan && !meal.isVegan) return false;
      if (isVegetarian && !meal.isVegetarian) return false;
      if (isGlutenFree && !meal.isGlutenFree) return false;
      return meal.prepTime <= preferences.prepTime;
    }),
    dinner: baseMeals.dinner.filter(meal => {
      if (isVegan && !meal.isVegan) return false;
      if (isVegetarian && !meal.isVegetarian) return false;
      if (isGlutenFree && !meal.isGlutenFree) return false;
      return meal.prepTime <= preferences.prepTime;
    })
  };

  // If no meals match the criteria, use the original meals
  if (filteredMeals.breakfast.length === 0) filteredMeals.breakfast = baseMeals.breakfast;
  if (filteredMeals.lunch.length === 0) filteredMeals.lunch = baseMeals.lunch;
  if (filteredMeals.dinner.length === 0) filteredMeals.dinner = baseMeals.dinner;

  // Return a sample day
  return {
    breakfast: filteredMeals.breakfast[0],
    lunch: filteredMeals.lunch[0],
    dinner: filteredMeals.dinner[0]
  };
};

export function MealPlanPreview({ preferences, isLoading = false }: MealPlanPreviewProps) {
  const sampleMeals = getSampleMeals(preferences);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-8 w-3/4" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-1/2" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="monday">
            <TabsList className="mb-4">
              <Skeleton className="h-10 w-20 rounded-md" />
              <Skeleton className="h-10 w-20 rounded-md ml-2" />
              <Skeleton className="h-10 w-20 rounded-md ml-2" />
            </TabsList>
            <TabsContent value="monday">
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-start space-x-4 border rounded-lg p-4">
                    <Skeleton className="h-24 w-24 rounded-md" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-6 w-3/4" />
                      <div className="flex space-x-4">
                        <Skeleton className="h-4 w-20" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Sample Meal Plan Preview</CardTitle>
        <CardDescription>
          Here's a preview of what your meal plan might look like based on your preferences
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="monday">
          <TabsList className="mb-4">
            <TabsTrigger value="monday">Monday</TabsTrigger>
            <TabsTrigger value="tuesday" disabled>Tuesday</TabsTrigger>
            <TabsTrigger value="wednesday" disabled>Wednesday</TabsTrigger>
          </TabsList>
          <TabsContent value="monday">
            <div className="space-y-4">
              {/* Breakfast */}
              <div className="flex items-start space-x-4 border rounded-lg p-4">
                <div
                  className="relative h-24 w-24 rounded-md overflow-hidden bg-cover bg-center"
                  style={{ backgroundImage: `url(${sampleMeals.breakfast.image})` }}
                  aria-label={sampleMeals.breakfast.name}
                ></div>
                <div className="flex-1">
                  <h4 className="font-medium">Breakfast: {sampleMeals.breakfast.name}</h4>
                  <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      <span>{sampleMeals.breakfast.prepTime} min</span>
                    </div>
                    <div className="flex items-center">
                      <DollarSign className="w-4 h-4 mr-1" />
                      <span>${(sampleMeals.breakfast.cost * preferences.householdSize).toFixed(2)}</span>
                    </div>
                    <div className="flex items-center">
                      <Utensils className="w-4 h-4 mr-1" />
                      <span>{preferences.skillLevel}</span>
                    </div>
                  </div>

                  {/* Nutrition Information */}
                  <div className="mt-3">
                    <TooltipProvider>
                      <div className="flex items-center space-x-2 mb-1">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center">
                              <Flame className="w-4 h-4 mr-1 text-orange-500" />
                              <span className="text-xs">{sampleMeals.breakfast.nutrition.calories} cal</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Calories</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center">
                              <Beef className="w-4 h-4 mr-1 text-red-500" />
                              <span className="text-xs">{sampleMeals.breakfast.nutrition.protein}g</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Protein</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center">
                              <Wheat className="w-4 h-4 mr-1 text-amber-500" />
                              <span className="text-xs">{sampleMeals.breakfast.nutrition.carbs}g</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Carbohydrates</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center">
                              <Droplet className="w-4 h-4 mr-1 text-blue-500" />
                              <span className="text-xs">{sampleMeals.breakfast.nutrition.fat}g</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Fat</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </TooltipProvider>
                  </div>
                </div>
              </div>

              {/* Lunch */}
              <div className="flex items-start space-x-4 border rounded-lg p-4">
                <div
                  className="relative h-24 w-24 rounded-md overflow-hidden bg-cover bg-center"
                  style={{ backgroundImage: `url(${sampleMeals.lunch.image})` }}
                  aria-label={sampleMeals.lunch.name}
                ></div>
                <div className="flex-1">
                  <h4 className="font-medium">Lunch: {sampleMeals.lunch.name}</h4>
                  <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      <span>{sampleMeals.lunch.prepTime} min</span>
                    </div>
                    <div className="flex items-center">
                      <DollarSign className="w-4 h-4 mr-1" />
                      <span>${(sampleMeals.lunch.cost * preferences.householdSize).toFixed(2)}</span>
                    </div>
                    <div className="flex items-center">
                      <Utensils className="w-4 h-4 mr-1" />
                      <span>{preferences.skillLevel}</span>
                    </div>
                  </div>

                  {/* Nutrition Information */}
                  <div className="mt-3">
                    <TooltipProvider>
                      <div className="flex items-center space-x-2 mb-1">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center">
                              <Flame className="w-4 h-4 mr-1 text-orange-500" />
                              <span className="text-xs">{sampleMeals.lunch.nutrition.calories} cal</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Calories</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center">
                              <Beef className="w-4 h-4 mr-1 text-red-500" />
                              <span className="text-xs">{sampleMeals.lunch.nutrition.protein}g</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Protein</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center">
                              <Wheat className="w-4 h-4 mr-1 text-amber-500" />
                              <span className="text-xs">{sampleMeals.lunch.nutrition.carbs}g</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Carbohydrates</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center">
                              <Droplet className="w-4 h-4 mr-1 text-blue-500" />
                              <span className="text-xs">{sampleMeals.lunch.nutrition.fat}g</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Fat</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </TooltipProvider>
                  </div>
                </div>
              </div>

              {/* Dinner */}
              <div className="flex items-start space-x-4 border rounded-lg p-4">
                <div
                  className="relative h-24 w-24 rounded-md overflow-hidden bg-cover bg-center"
                  style={{ backgroundImage: `url(${sampleMeals.dinner.image})` }}
                  aria-label={sampleMeals.dinner.name}
                ></div>
                <div className="flex-1">
                  <h4 className="font-medium">Dinner: {sampleMeals.dinner.name}</h4>
                  <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      <span>{sampleMeals.dinner.prepTime} min</span>
                    </div>
                    <div className="flex items-center">
                      <DollarSign className="w-4 h-4 mr-1" />
                      <span>${(sampleMeals.dinner.cost * preferences.householdSize).toFixed(2)}</span>
                    </div>
                    <div className="flex items-center">
                      <Utensils className="w-4 h-4 mr-1" />
                      <span>{preferences.skillLevel}</span>
                    </div>
                  </div>

                  {/* Nutrition Information */}
                  <div className="mt-3">
                    <TooltipProvider>
                      <div className="flex items-center space-x-2 mb-1">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center">
                              <Flame className="w-4 h-4 mr-1 text-orange-500" />
                              <span className="text-xs">{sampleMeals.dinner.nutrition.calories} cal</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Calories</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center">
                              <Beef className="w-4 h-4 mr-1 text-red-500" />
                              <span className="text-xs">{sampleMeals.dinner.nutrition.protein}g</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Protein</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center">
                              <Wheat className="w-4 h-4 mr-1 text-amber-500" />
                              <span className="text-xs">{sampleMeals.dinner.nutrition.carbs}g</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Carbohydrates</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center">
                              <Droplet className="w-4 h-4 mr-1 text-blue-500" />
                              <span className="text-xs">{sampleMeals.dinner.nutrition.fat}g</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Fat</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </TooltipProvider>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
