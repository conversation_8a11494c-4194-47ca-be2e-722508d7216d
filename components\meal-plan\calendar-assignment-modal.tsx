'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Calendar } from '@/components/ui/calendar';
import { format, addDays, isSameDay, isAfter, isBefore, startOfDay } from 'date-fns';
import { toast } from 'sonner';
import { mealPlanService } from '@/lib/supabase/meal-plan-service';
import { Loader2 } from 'lucide-react';
import { useSupabase } from '@/components/supabase-provider';

interface MealPlan {
  id: string;
  user_id: string;
  start_date: string;
  end_date: string;
  total_cost: number;
  meal_data: any;
  status: string;
  created_at: string;
  updated_at: string;
  name?: string;
  description?: string;
}

interface CalendarAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  mealPlan: MealPlan;
  onSuccess?: () => void;
}

export function CalendarAssignmentModal({
  isOpen,
  onClose,
  mealPlan,
  onSuccess
}: CalendarAssignmentModalProps) {
  const [startDate, setStartDate] = useState<Date | undefined>(new Date());
  const [isLoading, setIsLoading] = useState(false);
  const { supabase } = useSupabase();

  // Calculate end date based on meal plan duration
  const getEndDate = (start: Date | undefined) => {
    if (!start) return undefined;

    const originalStart = new Date(mealPlan.start_date);
    const originalEnd = new Date(mealPlan.end_date);
    const durationDays = Math.round((originalEnd.getTime() - originalStart.getTime()) / (1000 * 60 * 60 * 24));

    return addDays(start, durationDays);
  };

  const endDate = getEndDate(startDate);

  const handleAssign = async () => {
    if (!startDate) {
      toast.error('Please select a start date');
      return;
    }

    try {
      setIsLoading(true);

      // Check if this is a demo meal plan (user_id starts with '0000')
      const isDemoMealPlan = mealPlan.user_id.startsWith('0000') || mealPlan.user_id === '000000000000000';

      if (isDemoMealPlan) {
        // For demo meal plans, we'll use local storage to simulate assignment
        try {
          // Get existing assignments from local storage
          const storedAssignments = localStorage.getItem('demoMealPlanAssignments');
          const assignments = storedAssignments ? JSON.parse(storedAssignments) : [];

          // Create a new assignment
          const newAssignment = {
            id: `demo-${Date.now()}`,
            user_id: 'demo-user',
            meal_plan_id: mealPlan.id,
            start_date: format(startDate, 'yyyy-MM-dd'),
            end_date: format(endDate || startDate, 'yyyy-MM-dd'),
            created_at: new Date().toISOString(),
            meal_plans: mealPlan
          };

          // Add the new assignment
          assignments.push(newAssignment);

          // Save back to local storage
          localStorage.setItem('demoMealPlanAssignments', JSON.stringify(assignments));

          toast.success('Demo meal plan added to calendar');

          if (onSuccess) {
            onSuccess();
          }

          onClose();
          return;
        } catch (localStorageError) {
          console.error('Error with local storage:', localStorageError);
          // Continue with normal flow if local storage fails
        }
      }

      // Get the current user to ensure we're using the correct user_id
      if (supabase) {
        try {
          const { data: authData, error: authError } = await supabase.auth.getUser();
          if (!authError && authData.user) {
            // Use the current user's ID instead of the one from the meal plan
            mealPlan.user_id = authData.user.id;
          }
        } catch (authError) {
          console.error('Error getting current user:', authError);
          // Continue with the existing user_id
        }
      }

      const success = await mealPlanService.assignMealPlanToCalendar(
        mealPlan.user_id,
        mealPlan.id,
        format(startDate, 'yyyy-MM-dd'),
        format(endDate || startDate, 'yyyy-MM-dd')
      );

      if (!success) {
        toast.error('Failed to add meal plan to calendar');
        return;
      }

      toast.success('Meal plan added to calendar');

      if (onSuccess) {
        onSuccess();
      }

      onClose();
    } catch (error: any) {
      console.error('Error assigning meal plan to calendar:', error);

      // Don't show specific error message for auth errors to avoid confusion
      if (error.message && error.message.includes('Authentication')) {
        toast.error('Please log in to add meal plans to your calendar');
      } else {
        toast.error(`Failed to add to calendar: ${error.message || 'Unknown error'}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Custom day renderer for the calendar
  const renderDay = (day: Date) => {
    const isSelected = startDate && endDate &&
      (isSameDay(day, startDate) ||
       (isAfter(day, startDate) && isBefore(day, endDate)) ||
       isSameDay(day, endDate));

    return (
      <div className={`w-full h-full flex items-center justify-center rounded-md ${
        isSelected ? 'bg-primary text-primary-foreground' : ''
      }`}>
        {format(day, 'd')}
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add to Calendar</DialogTitle>
          <DialogDescription>
            Select a start date for this meal plan. The plan will be scheduled for {mealPlan.meal_data?.mealPlan?.week?.length || 7} days.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <Calendar
            mode="single"
            selected={startDate}
            onSelect={setStartDate}
            className="rounded-md border"
            components={{
              DayContent: ({ date }) => renderDay(date)
            }}
          />

          <div className="mt-4 text-sm">
            <p className="font-medium">Selected Range:</p>
            <p className="text-muted-foreground">
              {startDate ? format(startDate, 'MMMM d, yyyy') : 'No start date selected'}
              {endDate ? ` - ${format(endDate, 'MMMM d, yyyy')}` : ''}
            </p>
          </div>

          <div className="mt-4 text-sm text-muted-foreground">
            <p>This will add "{mealPlan.name || 'Meal Plan'}" to your calendar for the selected dates.</p>
            <p className="mt-2">You can always change or remove it later.</p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleAssign} disabled={!startDate || isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Add to Calendar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
