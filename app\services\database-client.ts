'use client';

import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

// Client types
export enum ClientType {
  USER,      // Regular user client (respects RLS)
  ADMIN,     // Admin client with service role (bypasses RLS)
}

// Create a singleton instance for the admin client
let adminClient: ReturnType<typeof createClient<Database>> | null = null;

/**
 * Get a Supabase client based on the specified type
 * This version is safe to use in client components
 */
export function getSupabaseClient(type: ClientType = ClientType.USER) {
  switch (type) {
    case ClientType.ADMIN:
      // Use singleton pattern for admin client
      if (!adminClient) {
        adminClient = createClient<Database>(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!, // Note: We can't use service role key in client components
          {
            auth: {
              autoRefreshToken: false,
              persistSession: false
            }
          }
        );
      }
      return adminClient;

    case ClientType.USER:
    default:
      return createClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
  }
}

/**
 * Database service for recipes (client version)
 */
export const recipeService = {
  /**
   * Get recipes with filtering and pagination
   */
  async getRecipes(params: {
    userId: string;
    query?: string;
    cuisine?: string;
    favorites?: boolean;
    limit?: number;
    offset?: number;
  }) {
    try {
      const client = getSupabaseClient(ClientType.USER);

      let query = client
        .from('recipes')
        .select('*')
        .eq('user_id', params.userId);

      // Apply search filter if provided
      if (params.query) {
        query = query.or(`name.ilike.%${params.query}%,description.ilike.%${params.query}%`);
      }

      // Filter by cuisine if provided
      if (params.cuisine) {
        query = query.eq('cuisine', params.cuisine);
      }

      // Filter by favorites if requested
      if (params.favorites) {
        query = query.eq('is_favorite', true);
      }

      // Apply pagination
      query = query
        .order('created_at', { ascending: false })
        .range(params.offset || 0, (params.offset || 0) + (params.limit || 50) - 1);

      const { data, error } = await query;

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching recipes:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch recipes'
      };
    }
  },

  /**
   * Get a recipe by ID
   */
  async getRecipeById(id: string) {
    try {
      const client = getSupabaseClient(ClientType.USER);

      const { data, error } = await client
        .from('recipes')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching recipe:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch recipe'
      };
    }
  },

  /**
   * Toggle favorite status of a recipe
   */
  async toggleFavorite(id: string, isFavorite: boolean) {
    try {
      const client = getSupabaseClient(ClientType.USER);

      const { data, error } = await client
        .from('recipes')
        .update({
          is_favorite: isFavorite,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error toggling favorite status:', error);
      return {
        data: null,
        error: error.message || 'Failed to update favorite status'
      };
    }
  }
};

/**
 * Database service for meal plans (client version)
 */
export const mealPlanService = {
  /**
   * Get meal plans for a user
   * If userId is not provided, it will get the current user's meal plans
   */
  async getMealPlans(userId?: string) {
    try {
      const client = getSupabaseClient(ClientType.USER);

      // If userId is not provided, get the current user
      if (!userId) {
        const { data: authData, error: authError } = await client.auth.getUser();

        if (authError) {
          console.error('Auth error in getMealPlans:', authError);
          throw new Error('Authentication error: ' + authError.message);
        }

        if (!authData.user) {
          throw new Error('No authenticated user found');
        }

        userId = authData.user.id;
      }

      // Now fetch the meal plans with the userId
      const { data, error } = await client
        .from('meal_plans')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Database error in getMealPlans:', error);
        throw error;
      }

      return { data: data || [], error: null };
    } catch (error: any) {
      console.error('Error fetching meal plans:', error);
      return {
        data: [],
        error: error.message || 'Failed to fetch meal plans'
      };
    }
  },

  /**
   * Get a meal plan by ID
   */
  async getMealPlanById(id: string) {
    try {
      const client = getSupabaseClient(ClientType.USER);

      const { data, error } = await client
        .from('meal_plans')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching meal plan:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch meal plan'
      };
    }
  },

  /**
   * Delete a meal plan
   */
  async deleteMealPlan(id: string) {
    try {
      console.log('Client-side deleteMealPlan called with ID:', id);

      // Use the new API endpoint instead of direct database access
      const response = await fetch(`/api/meal-plans/delete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ id })
      });

      console.log('API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error response:', errorData);
        throw new Error(errorData.error || 'Failed to delete meal plan');
      }

      const successData = await response.json();
      console.log('API success response:', successData);

      return { success: true, error: null };
    } catch (error: any) {
      console.error('Error deleting meal plan:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete meal plan'
      };
    }
  }
};

/**
 * Database service for grocery lists (client version)
 */
export const groceryService = {
  /**
   * Get grocery lists for a user
   */
  async getGroceryLists(userId: string) {
    try {
      const client = getSupabaseClient(ClientType.USER);

      const { data, error } = await client
        .from('shopping_lists')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching grocery lists:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch grocery lists'
      };
    }
  },

  /**
   * Get grocery items for a list
   */
  async getGroceryItems(listId: string) {
    try {
      const client = getSupabaseClient(ClientType.USER);

      const { data, error } = await client
        .from('shopping_items')
        .select('*')
        .eq('shopping_list_id', listId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error fetching grocery items:', error);
      return {
        data: null,
        error: error.message || 'Failed to fetch grocery items'
      };
    }
  }
};
