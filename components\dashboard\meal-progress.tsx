"use client";

import { Progress } from "@/components/ui/progress";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Check, X, AlertCircle } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useMealStatus } from "@/app/hooks/useDashboardData";

interface MealStatus {
  meal: string;
  status: 'completed' | 'missed' | 'partial';
  time: string;
}

const MEAL_STATUSES: MealStatus[] = [
  { meal: 'Breakfast', status: 'completed', time: '8:00 AM' },
  { meal: 'Morning Snack', status: 'completed', time: '10:30 AM' },
  { meal: 'Lunch', status: 'partial', time: '1:00 PM' },
  { meal: 'Afternoon Snack', status: 'missed', time: '4:00 PM' },
  { meal: 'Dinner', status: 'completed', time: '7:00 PM' }
];

const getStatusIcon = (status: MealStatus['status']) => {
  switch (status) {
    case 'completed':
      return <Check className="h-4 w-4 text-green-500" />;
    case 'missed':
      return <X className="h-4 w-4 text-red-500" />;
    case 'partial':
      return <AlertCircle className="h-4 w-4 text-yellow-500" />;
  }
};

const getStatusColor = (status: MealStatus['status']) => {
  switch (status) {
    case 'completed':
      return 'bg-green-500';
    case 'missed':
      return 'bg-red-500';
    case 'partial':
      return 'bg-yellow-500';
  }
};

export function MealProgress() {
  const { data: mealStatuses, isLoading, isError } = useMealStatus();

  // Calculate completion rate
  const completionRate = mealStatuses ?
    (mealStatuses.filter(m => m.status === 'completed').length / mealStatuses.length) * 100 : 0;

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Meal Plan Progress</CardTitle>
          <CardDescription>Today's meal completion status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <div className="flex justify-between mb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-12" />
              </div>
              <Skeleton className="h-2 w-full" />
            </div>
            <div className="space-y-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Skeleton className="w-2 h-2 rounded-full" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                  <div className="flex items-center space-x-3">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-4 rounded-full" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (isError || !mealStatuses) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Meal Plan Progress</CardTitle>
          <CardDescription>Today's meal completion status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[200px]">
            <p className="text-muted-foreground">Failed to load meal progress data</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Meal Plan Progress</CardTitle>
        <CardDescription>Today's meal completion status</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <div className="flex justify-between mb-2">
              <span className="text-sm font-medium">Daily Progress</span>
              <span className="text-sm text-muted-foreground">{Math.round(completionRate)}%</span>
            </div>
            <Progress value={completionRate} className="h-2" />
          </div>

          <div className="space-y-4">
            {mealStatuses.map((meal) => (
              <div key={meal.meal} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-2 h-2 rounded-full ${getStatusColor(meal.status)}`} />
                  <span className="font-medium">{meal.meal}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-sm text-muted-foreground">{meal.time}</span>
                  {getStatusIcon(meal.status)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
