import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Basic health check without external dependencies
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      version: '1.0.0',
      services: {
        api: 'operational',
        database: 'checking...',
        auth: 'checking...'
      }
    };

    // Test Supabase connectivity (optional)
    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      if (supabaseUrl) {
        const response = await fetch(`${supabaseUrl}/rest/v1/`, {
          method: 'HEAD',
          headers: {
            'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          },
          signal: AbortSignal.timeout(3000) // 3 second timeout
        });
        
        healthStatus.services.database = response.ok ? 'operational' : 'degraded';
        healthStatus.services.auth = response.ok ? 'operational' : 'degraded';
      } else {
        healthStatus.services.database = 'not_configured';
        healthStatus.services.auth = 'not_configured';
      }
    } catch (error) {
      console.warn('Health check - Supabase connectivity failed:', error);
      healthStatus.services.database = 'unavailable';
      healthStatus.services.auth = 'unavailable';
    }

    return NextResponse.json(healthStatus);
  } catch (error) {
    console.error('Health check failed:', error);
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed'
      },
      { status: 500 }
    );
  }
}

export async function HEAD() {
  // Simple HEAD request for basic connectivity check
  return new NextResponse(null, { status: 200 });
}
