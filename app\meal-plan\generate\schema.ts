import * as z from "zod";

export const formSchema = z.object({
  // New fields for OpenAI integration
  name: z.string().min(1, "Name is required").default("My Meal Plan"),
  calorieGoal: z.coerce.number().min(1000, "Minimum 1000 calories").max(4000, "Maximum 4000 calories").default(2000),
  daysToGenerate: z.coerce.number().min(1, "Minimum 1 day").max(14, "Maximum 14 days").default(7),
  mealTypes: z.array(z.string()).min(1, "Select at least one meal type").default(["Breakfast", "Lunch", "Dinner"]),
  notes: z.string().optional().default(""),

  // Original fields
  budget: z.coerce.number()
    .min(1, "Budget must be at least 1")
    .max(1000, "Budget must be reasonable (max 1000)")
    .refine(val => !isNaN(val), "Budget must be a valid number"),
  currency: z.enum(["USD", "EUR", "GBP"]).default("USD"),
  householdSize: z.coerce.number()
    .min(1, "Household size must be at least 1")
    .max(10, "Maximum household size is 10")
    .refine(val => Number.isInteger(val), "Household size must be a whole number"),
  dietaryRestrictions: z.array(z.string())
    .default([])
    .refine(val => val.length <= 10, "Maximum 10 dietary restrictions allowed"),
  allergies: z.string()
    .optional()
    .default("")
    .refine(val => val.length <= 500, "Allergies text is too long"),
  cuisinePreferences: z.array(z.string())
    .default([])
    .refine(val => val.length <= 10, "Maximum 10 cuisine preferences allowed"),
  exclusions: z.string()
    .optional()
    .default("")
    .refine(val => val.length <= 500, "Exclusions text is too long"),
  skillLevel: z.enum(["Beginner", "Intermediate", "Advanced"]).default("Beginner"),
  prepTime: z.coerce.number()
    .min(15, "Minimum prep time is 15 minutes")
    .max(120, "Maximum prep time is 120 minutes")
    .refine(val => !isNaN(val), "Prep time must be a valid number"),
  mealFrequency: z.enum(["3 meals per day", "3 meals + snacks", "2 meals per day"])
    .default("3 meals per day"),
  portionSize: z.enum(["Regular", "Large", "Small"])
    .default("Regular"),
  prioritizeIngredients: z.string()
    .optional()
    .default("")
    .refine(val => val.length <= 500, "Prioritized ingredients text is too long"),
  calorieTarget: z.enum(["No preference", "Low calorie", "Moderate calorie", "High calorie"])
    .default("No preference"),
  mealPrepFriendly: z.boolean()
    .default(false),
});

export const dietaryOptions = [
  { id: "vegetarian", label: "Vegetarian" },
  { id: "vegan", label: "Vegan" },
  { id: "gluten-free", label: "Gluten-Free" },
  { id: "dairy-free", label: "Dairy-Free" },
  { id: "keto", label: "Keto" },
  { id: "paleo", label: "Paleo" },
  { id: "low-carb", label: "Low Carb" },
  { id: "low-fat", label: "Low Fat" },
  { id: "high-protein", label: "High Protein" },
  { id: "pescatarian", label: "Pescatarian" },
  { id: "halal", label: "Halal" },
  { id: "kosher", label: "Kosher" },
];

export const cuisineOptions = [
  { id: "italian", label: "Italian" },
  { id: "mexican", label: "Mexican" },
  { id: "chinese", label: "Chinese" },
  { id: "indian", label: "Indian" },
  { id: "japanese", label: "Japanese" },
  { id: "thai", label: "Thai" },
  { id: "mediterranean", label: "Mediterranean" },
  { id: "french", label: "French" },
  { id: "greek", label: "Greek" },
  { id: "spanish", label: "Spanish" },
  { id: "middle-eastern", label: "Middle Eastern" },
  { id: "american", label: "American" },
];
