// This script applies the RLS policies to your Supabase project
// Run this script with: node scripts/apply_rls_policies.js

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Supabase connection details
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service role key not found in environment variables.');
  console.error('Make sure you have a .env file with NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY.');
  process.exit(1);
}

// Create Supabase client with service role key (has admin privileges)
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applyRLSPolicies() {
  try {
    console.log('Applying RLS policies to Supabase...');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../supabase/migrations/20230801000000_update_meal_plans_rls.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // Split the SQL content into individual statements
    const statements = sqlContent
      .replace(/--.*\n/g, '') // Remove comments
      .split(';')
      .filter(stmt => stmt.trim() !== '');

    console.log(`Found ${statements.length} SQL statements to execute`);

    // Execute each SQL statement separately
    for (let i = 0; i < statements.length; i++) {
      const stmt = statements[i].trim();
      if (stmt) {
        try {
          // Skip DO blocks as they're not supported via REST API
          if (stmt.toUpperCase().startsWith('DO')) {
            console.log(`Skipping DO block (statement ${i + 1})`);
            continue;
          }

          console.log(`Executing statement ${i + 1}/${statements.length}...`);
          const { error } = await supabase.rpc('_', {}, {
            headers: {
              'Content-Type': 'application/json',
              'Prefer': 'params=single-object',
              'X-Raw-SQL': stmt
            }
          });

          if (error) {
            console.warn(`Warning: Statement ${i + 1} error:`, error.message);
            // Continue with other statements even if one fails
          }
        } catch (stmtError) {
          console.warn(`Warning: Statement ${i + 1} exception:`, stmtError.message);
          // Continue with other statements even if one fails
        }
      }
    }

    console.log('SQL execution completed!');

    // Verify the table exists
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'meal_plans');

    if (tablesError) {
      console.error('Error checking if table exists:', tablesError);
    } else {
      if (tables && tables.length > 0) {
        console.log('✅ meal_plans table exists');
      } else {
        console.log('❌ meal_plans table does not exist');
      }
    }

    console.log('\nRLS policies should now be applied. Please check your Supabase dashboard to verify.');
    console.log('Go to: Table Editor > meal_plans > Policies');

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

applyRLSPolicies();
