-- Comprehensive diagnostic script to check the current state of the database
-- This script will identify missing tables, columns, and RLS policies

-- Check which tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public'
AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- Check which tables have RLS enabled
SELECT tablename, rowsecurity
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY tablename;

-- Check all columns for each table
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
ORDER BY 
    table_name, ordinal_position;

-- Check all RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM 
    pg_policies
WHERE 
    schemaname = 'public'
ORDER BY 
    tablename, policyname;

-- Check for tables with user_id column but missing RLS policies
SELECT 
    t.table_name
FROM 
    information_schema.tables t
JOIN 
    information_schema.columns c ON t.table_name = c.table_name AND t.table_schema = c.table_schema
LEFT JOIN 
    pg_policies p ON t.table_name = p.tablename AND p.schemaname = 'public'
WHERE 
    t.table_schema = 'public'
    AND t.table_type = 'BASE TABLE'
    AND c.column_name = 'user_id'
    AND p.policyname IS NULL
GROUP BY 
    t.table_name;

-- Check for tables with RLS enabled but no policies
SELECT 
    t.tablename
FROM 
    pg_tables t
LEFT JOIN 
    pg_policies p ON t.tablename = p.tablename AND t.schemaname = p.schemaname
WHERE 
    t.schemaname = 'public'
    AND t.rowsecurity = true
    AND p.policyname IS NULL;

-- Check for tables with policies assigned to public role instead of authenticated
SELECT 
    tablename,
    policyname,
    roles
FROM 
    pg_policies
WHERE 
    schemaname = 'public'
    AND 'public' = ANY(roles)
ORDER BY 
    tablename, policyname;

-- Check for type mismatches in RLS policies
SELECT 
    p.schemaname,
    p.tablename,
    p.policyname,
    p.cmd,
    c.data_type AS user_id_data_type,
    p.qual AS policy_using_expression,
    p.with_check AS policy_with_check_expression,
    CASE
        WHEN c.data_type = 'uuid' AND (
            p.qual::text LIKE '%auth.uid()%' AND p.qual::text NOT LIKE '%::text%'
        ) THEN 'MISMATCH: UUID column without text casting'
        WHEN c.data_type = 'text' AND (
            p.qual::text LIKE '%auth.uid()::uuid%'
        ) THEN 'MISMATCH: text column with UUID casting'
        ELSE 'OK'
    END AS type_check
FROM 
    pg_policies p
JOIN 
    information_schema.columns c ON p.tablename = c.table_name
WHERE 
    p.schemaname = 'public'
    AND c.column_name = 'user_id'
    AND c.table_schema = 'public'
    AND (
        p.qual::text LIKE '%auth.uid()%' OR
        p.with_check::text LIKE '%auth.uid()%'
    );

-- Check for missing indexes on foreign keys
SELECT
    tc.table_schema, 
    tc.table_name, 
    kcu.column_name,
    CASE WHEN i.indexrelid IS NULL THEN 'Missing' ELSE 'Present' END AS index_status
FROM 
    information_schema.table_constraints AS tc
JOIN 
    information_schema.key_column_usage AS kcu ON tc.constraint_name = kcu.constraint_name
LEFT JOIN 
    pg_catalog.pg_constraint pc ON tc.constraint_name = pc.conname
LEFT JOIN 
    pg_catalog.pg_index i ON pc.conindid = i.indexrelid
WHERE 
    tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public';

-- Check for missing updated_at triggers
SELECT 
    t.table_name
FROM 
    information_schema.tables t
JOIN 
    information_schema.columns c ON t.table_name = c.table_name AND t.table_schema = c.table_schema
LEFT JOIN 
    pg_trigger tr ON tr.tgname = 'update_' || t.table_name || '_updated_at'
LEFT JOIN 
    pg_class cl ON cl.oid = tr.tgrelid AND cl.relname = t.table_name
WHERE 
    t.table_schema = 'public'
    AND t.table_type = 'BASE TABLE'
    AND c.column_name = 'updated_at'
    AND tr.tgname IS NULL;

-- Check for missing permissions for authenticated users
SELECT 
    table_schema,
    table_name,
    privilege_type,
    grantee
FROM 
    information_schema.table_privileges
WHERE 
    table_schema = 'public'
    AND grantee = 'authenticated'
ORDER BY 
    table_name, privilege_type;
