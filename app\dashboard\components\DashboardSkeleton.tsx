'use client';

import { Skeleton } from "@/components/ui/skeleton";

export function DashboardSkeleton() {
  return (
    <div className="container py-6 space-y-6">
      {/* Header skeleton */}
      <div className="flex justify-between items-center mb-6">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-10 w-32" />
      </div>

      {/* Weekly plan skeleton */}
      <div className="space-y-4">
        {/* Create 7 day skeletons */}
        {Array.from({ length: 7 }).map((_, index) => (
          <div key={index} className="border rounded-lg p-4 space-y-4">
            <div className="flex justify-between items-center">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-6 w-24" />
            </div>
            
            {/* Meal slots */}
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, mealIndex) => (
                <div key={mealIndex} className="flex items-center gap-4">
                  <Skeleton className="h-16 w-16 rounded-md" /> {/* Meal image */}
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" /> {/* Meal name */}
                    <Skeleton className="h-3 w-1/2" /> {/* Meal details */}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Summary section skeleton */}
      <div className="mt-8 border rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-6 w-32" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}