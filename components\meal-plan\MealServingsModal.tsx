"use client";

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Users } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { useMealPlanStore } from '@/lib/stores/meal-plan-store-supabase';

interface MealServingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  date: string;
  mealType: string;
  currentMeal: any;
}

export function MealServingsModal({ isOpen, onClose, date, mealType, currentMeal }: MealServingsModalProps) {
  const [servings, setServings] = useState(currentMeal?.servings || 1);
  const [originalServings, setOriginalServings] = useState(currentMeal?.servings || 1);
  const [scaledIngredients, setScaledIngredients] = useState<any[]>([]);
  const [scaledNutrition, setScaledNutrition] = useState<any>({});
  const [scaledCost, setScaledCost] = useState(0);
  const [scaledCalories, setScaledCalories] = useState(0);

  const { updateMealServings } = useMealPlanStore();

  // Initialize values when the modal opens or the meal changes
  useEffect(() => {
    if (currentMeal) {
      setServings(currentMeal.servings || 1);
      setOriginalServings(currentMeal.servings || 1);
      updateScaledValues(currentMeal.servings || 1);
    }
  }, [currentMeal, isOpen]);

  // Update scaled values when servings change
  const updateScaledValues = (newServings: number) => {
    if (!currentMeal) return;

    const scaleFactor = newServings / (originalServings || 1);

    // Scale ingredients
    if (currentMeal.ingredients && Array.isArray(currentMeal.ingredients)) {
      const scaled = currentMeal.ingredients.map((ingredient: any) => {
        const newIngredient = { ...ingredient };
        // Only scale numeric quantities
        if (!isNaN(parseFloat(newIngredient.quantity))) {
          const scaledAmount = parseFloat(newIngredient.quantity) * scaleFactor;
          newIngredient.quantity = scaledAmount.toFixed(2);
        }
        return newIngredient;
      });
      setScaledIngredients(scaled);
    }

    // Scale nutrition
    if (currentMeal.nutrition) {
      setScaledNutrition({
        protein: Math.round(currentMeal.nutrition.protein * scaleFactor),
        carbs: Math.round(currentMeal.nutrition.carbs * scaleFactor),
        fat: Math.round(currentMeal.nutrition.fat * scaleFactor)
      });
    }

    // Scale cost and calories
    setScaledCost(parseFloat((currentMeal.cost * scaleFactor).toFixed(2)));
    setScaledCalories(Math.round(currentMeal.calories * scaleFactor));
  };

  // Handle servings change
  const handleServingsChange = (value: number) => {
    setServings(value);
    updateScaledValues(value);
  };

  // Handle save
  const handleSave = () => {
    try {
      updateMealServings(date, mealType, servings);
      toast.success(`Updated servings for ${currentMeal.name}`);
      onClose();
    } catch (error) {
      console.error('Error updating servings:', error);
      toast.error('Failed to update servings');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Adjust Servings
          </DialogTitle>
        </DialogHeader>

        <div className="py-4 space-y-4">
          <div className="mb-4">
            <div className="text-sm text-muted-foreground mb-2">Meal:</div>
            <div className="p-3 border rounded-md bg-gray-50">
              <div className="font-medium">{currentMeal?.name}</div>
              <div className="text-xs text-muted-foreground">
                {format(new Date(date), 'MMMM d, yyyy')} - {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <Label htmlFor="servings">Servings</Label>
              <div className="flex items-center">
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  className="h-6 w-6 rounded-full"
                  onClick={() => handleServingsChange(Math.max(1, servings - 1))}
                  disabled={servings <= 1}
                >
                  -
                </Button>
                <Input
                  id="servings"
                  type="number"
                  min="1"
                  max="20"
                  value={servings}
                  onChange={(e) => handleServingsChange(parseInt(e.target.value) || 1)}
                  className="w-16 mx-2 text-center"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  className="h-6 w-6 rounded-full"
                  onClick={() => handleServingsChange(Math.min(20, servings + 1))}
                  disabled={servings >= 20}
                >
                  +
                </Button>
              </div>
            </div>
            <Slider
              value={[servings]}
              min={1}
              max={10}
              step={1}
              onValueChange={(value) => handleServingsChange(value[0])}
            />
          </div>

          <div className="grid grid-cols-2 gap-4 mt-4">
            <div className="p-3 border rounded-md bg-gray-50">
              <div className="text-xs text-muted-foreground mb-1">Calories</div>
              <div className="font-medium">
                {scaledCalories} cal
                {originalServings !== servings && (
                  <span className="text-xs text-muted-foreground ml-1">
                    (was {currentMeal?.calories})
                  </span>
                )}
              </div>
            </div>
            <div className="p-3 border rounded-md bg-gray-50">
              <div className="text-xs text-muted-foreground mb-1">Cost</div>
              <div className="font-medium">
                ${scaledCost}
                {originalServings !== servings && (
                  <span className="text-xs text-muted-foreground ml-1">
                    (was ${currentMeal?.cost})
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Nutrition (per serving)</Label>
            <div className="grid grid-cols-3 gap-2">
              <div className="p-2 border rounded-md bg-gray-50">
                <div className="text-xs text-muted-foreground">Protein</div>
                <div className="font-medium">{scaledNutrition.protein || 0}g</div>
              </div>
              <div className="p-2 border rounded-md bg-gray-50">
                <div className="text-xs text-muted-foreground">Carbs</div>
                <div className="font-medium">{scaledNutrition.carbs || 0}g</div>
              </div>
              <div className="p-2 border rounded-md bg-gray-50">
                <div className="text-xs text-muted-foreground">Fat</div>
                <div className="font-medium">{scaledNutrition.fat || 0}g</div>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Ingredients</Label>
            <div className="max-h-40 overflow-y-auto border rounded-md p-2">
              {scaledIngredients.length > 0 ? (
                <ul className="space-y-1">
                  {scaledIngredients.map((ingredient, index) => (
                    <li key={index} className="text-sm">
                      <span className="font-medium">{ingredient.quantity} {ingredient.unit}</span> {ingredient.name}
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="text-sm text-muted-foreground text-center py-2">
                  No ingredients available
                </div>
              )}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
