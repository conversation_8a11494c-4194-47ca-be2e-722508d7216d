"use client";

import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useMealPlanStore } from '@/lib/stores/meal-plan-store-supabase';
import { toast } from 'sonner';

export function UserProvider({ children }: { children: React.ReactNode }) {
  const setUserId = useMealPlanStore((state) => state.setUserId);
  const loadMealPlanFromDatabase = useMealPlanStore((state) => state.loadMealPlanFromDatabase);
  const loadFavoritesFromDatabase = useMealPlanStore((state) => state.loadFavoritesFromDatabase);
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    const supabase = createClientComponentClient();

    // Get the current user
    const getUser = async () => {
      try {
        console.log('Getting current user...');
        const { data: { user }, error } = await supabase.auth.getUser();

        if (error) {
          console.error('Error getting user:', error);
          return;
        }

        if (user) {
          console.log('User found, setting user ID:', user.id);
          setUserId(user.id);

          // Explicitly load meal plan and favorites
          console.log('Explicitly loading meal plan and favorites...');
          try {
            await loadMealPlanFromDatabase();
            await loadFavoritesFromDatabase();
            console.log('Successfully loaded meal plan and favorites');
          } catch (loadError) {
            console.error('Error loading data:', loadError);
            toast.error('Failed to load your data');
          }
        } else {
          console.log('No user found');
        }

        setInitialized(true);
      } catch (error) {
        console.error('Error in getUser:', error);
        setInitialized(true);
      }
    };

    getUser();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event);
      if (event === 'SIGNED_IN' && session?.user) {
        console.log('User signed in, setting user ID:', session.user.id);
        setUserId(session.user.id);

        // Explicitly load meal plan and favorites
        console.log('Explicitly loading meal plan and favorites after sign in...');
        try {
          await loadMealPlanFromDatabase();
          await loadFavoritesFromDatabase();
          console.log('Successfully loaded meal plan and favorites after sign in');
        } catch (loadError) {
          console.error('Error loading data after sign in:', loadError);
          toast.error('Failed to load your data');
        }
      } else if (event === 'SIGNED_OUT') {
        console.log('User signed out, clearing user ID');
        setUserId('');
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [setUserId, loadMealPlanFromDatabase, loadFavoritesFromDatabase]);

  // Show a loading indicator until we've initialized
  if (!initialized) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-background/50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return <>{children}</>;
}
