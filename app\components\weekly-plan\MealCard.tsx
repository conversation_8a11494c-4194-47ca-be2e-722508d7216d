'use client';

import { Meal } from '@/types/meal-plan';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, DollarSign, Utensils } from 'lucide-react';

interface MealCardProps {
  meal: Meal;
}

export function MealCard({ meal }: MealCardProps): JSX.Element {
  return (
    <Card className="p-4">
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <div>
            <h3 className="font-semibold">{meal.name}</h3>
            <Badge variant="secondary" className="mt-1">
              {meal.type}
            </Badge>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              {meal.prepTime} min
            </div>
            <div className="flex items-center">
              <DollarSign className="h-4 w-4 mr-1" />
              {meal.cost.toFixed(2)}
            </div>
            <div className="flex items-center">
              <Utensils className="h-4 w-4 mr-1" />
              {meal.servings} servings
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <div className="font-medium">Calories</div>
            <div>{meal.nutrition.calories} kcal</div>
          </div>
          <div>
            <div className="font-medium">Protein</div>
            <div>{meal.nutrition.protein}g</div>
          </div>
          <div>
            <div className="font-medium">Carbs</div>
            <div>{meal.nutrition.carbs}g</div>
          </div>
          <div>
            <div className="font-medium">Fat</div>
            <div>{meal.nutrition.fats}g</div>
          </div>
        </div>
      </div>
    </Card>
  );
}
