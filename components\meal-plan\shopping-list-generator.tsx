"use client";

import React, { useState, useEffect } from "react";
import { useSupabase } from "@/components/supabase-provider";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { ShoppingCart, Plus, Trash2, Check, Save, Download, Package, Filter, Search, RefreshCw, AlertCircle } from "lucide-react";

interface ShoppingListGeneratorProps {
  isOpen: boolean;
  onClose: () => void;
  mealPlan: any;
}

interface ShoppingItem {
  id: string;
  name: string;
  quantity: string;
  unit: string;
  category: string;
  checked: boolean;
  inPantry?: boolean;
  pantryQuantity?: string;
  pantryUnit?: string;
  addToPantry?: boolean;
  estimatedPrice?: number;
}

interface ShoppingCategory {
  name: string;
  items: ShoppingItem[];
}

export function ShoppingListGenerator({ isOpen, onClose, mealPlan }: ShoppingListGeneratorProps) {
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("list");
  const [categories, setCategories] = useState<ShoppingCategory[]>([]);
  const [newItem, setNewItem] = useState({ name: "", quantity: "1", unit: "item", category: "Other", estimatedPrice: "" });

  // Pantry integration
  const [pantryItems, setPantryItems] = useState<any[]>([]);
  const [showPantryItems, setShowPantryItems] = useState(true);
  const [addAllToPantry, setAddAllToPantry] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterOptions, setFilterOptions] = useState({
    hideChecked: false,
    hidePantryItems: false,
    showOnlyNeeded: false
  });
  const [totalEstimatedCost, setTotalEstimatedCost] = useState(0);

  useEffect(() => {
    if (isOpen && mealPlan && supabase && !isSupabaseLoading) {
      fetchPantryItems();
      generateShoppingList();
    }
  }, [isOpen, mealPlan, supabase, isSupabaseLoading]);

  const fetchPantryItems = async () => {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        return;
      }

      const { data, error } = await supabase
        .from('pantry_items')
        .select('*')
        .eq('user_id', user.id);

      if (error) throw error;

      if (data) {
        setPantryItems(data);

        // Update shopping list items with pantry information
        if (categories.length > 0) {
          const updatedCategories = categories.map(category => ({
            ...category,
            items: category.items.map(item => {
              const matchingPantryItem = data.find(pantryItem =>
                pantryItem.name.toLowerCase() === item.name.toLowerCase()
              );

              return {
                ...item,
                inPantry: !!matchingPantryItem,
                pantryQuantity: matchingPantryItem?.quantity || '',
                pantryUnit: matchingPantryItem?.unit || '',
                addToPantry: item.addToPantry || false
              };
            })
          }));

          setCategories(updatedCategories);
        }
      }
    } catch (error) {
      console.error('Error fetching pantry items:', error);
      // Don't show error toast to avoid confusion
    }
  };

  const generateShoppingList = () => {
    setIsLoading(true);

    try {
      // In a real app, this would analyze the meal plan and extract ingredients
      // For now, we'll create a mock shopping list based on the meal plan

      const ingredientMap = new Map();

      // Process all meals in the plan to extract ingredients
      if (mealPlan?.meal_data?.mealPlan?.week) {
        mealPlan.meal_data.mealPlan.week.forEach((day: any) => {
          day.meals.forEach((meal: any) => {
            // Check if the meal has ingredients
            if (meal.ingredients && Array.isArray(meal.ingredients)) {
              meal.ingredients.forEach((ingredient: any) => {
                const name = ingredient.name || ingredient.item || "";
                const quantity = ingredient.quantity || ingredient.amount || "1";
                const unit = ingredient.unit || "";

                // Determine category based on ingredient name
                let category = "Other";
                if (/meat|chicken|beef|pork|fish|turkey|lamb/i.test(name)) {
                  category = "Meat & Seafood";
                } else if (/milk|cheese|yogurt|cream|butter/i.test(name)) {
                  category = "Dairy";
                } else if (/apple|banana|orange|berry|fruit|grape/i.test(name)) {
                  category = "Fruits";
                } else if (/lettuce|carrot|onion|potato|vegetable|tomato|pepper|cucumber/i.test(name)) {
                  category = "Vegetables";
                } else if (/bread|pasta|rice|cereal|flour|grain/i.test(name)) {
                  category = "Grains & Bread";
                } else if (/oil|vinegar|sauce|condiment|spice|herb|salt|pepper/i.test(name)) {
                  category = "Condiments & Spices";
                }

                const key = `${name.toLowerCase()}_${unit.toLowerCase()}`;

                if (ingredientMap.has(key)) {
                  // If ingredient already exists, update quantity
                  const existing = ingredientMap.get(key);
                  const newQuantity = parseFloat(existing.quantity) + parseFloat(quantity);
                  ingredientMap.set(key, {
                    ...existing,
                    quantity: newQuantity.toString()
                  });
                } else {
                  // Check if item is in pantry and get details
                  const matchingPantryItem = pantryItems.find(pantryItem =>
                    pantryItem.name.toLowerCase() === name.toLowerCase()
                  );

                  // Estimate price based on category and quantity
                  // This is a simple mock implementation - in a real app, you'd use a price database
                  const estimatedPrice = estimatePrice(name, parseFloat(quantity), category);

                  // Add new ingredient
                  ingredientMap.set(key, {
                    id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    name,
                    quantity,
                    unit,
                    category,
                    checked: false,
                    inPantry: !!matchingPantryItem,
                    pantryQuantity: matchingPantryItem?.quantity || '',
                    pantryUnit: matchingPantryItem?.unit || '',
                    addToPantry: !matchingPantryItem, // Default to adding to pantry if not already there
                    estimatedPrice
                  });
                }
              });
            }
          });
        });
      }

      // If no ingredients were found, create some mock data
      if (ingredientMap.size === 0) {
        const mockIngredients = [
          { name: "Chicken breast", quantity: "2", unit: "lbs", category: "Meat & Seafood", inPantry: false, estimatedPrice: 8.99 },
          { name: "Spinach", quantity: "1", unit: "bag", category: "Vegetables", inPantry: false, estimatedPrice: 3.49 },
          { name: "Brown rice", quantity: "2", unit: "cups", category: "Grains & Bread", inPantry: true, estimatedPrice: 1.99 },
          { name: "Greek yogurt", quantity: "16", unit: "oz", category: "Dairy", inPantry: false, estimatedPrice: 4.29 },
          { name: "Apples", quantity: "4", unit: "", category: "Fruits", inPantry: true, estimatedPrice: 3.99 },
          { name: "Olive oil", quantity: "1", unit: "bottle", category: "Condiments & Spices", inPantry: true, estimatedPrice: 7.99 },
          { name: "Salt", quantity: "1", unit: "tsp", category: "Condiments & Spices", inPantry: true, estimatedPrice: 0.99 },
          { name: "Black pepper", quantity: "1", unit: "tsp", category: "Condiments & Spices", inPantry: true, estimatedPrice: 1.49 },
          { name: "Garlic", quantity: "1", unit: "head", category: "Vegetables", inPantry: false, estimatedPrice: 0.79 },
          { name: "Onions", quantity: "2", unit: "", category: "Vegetables", inPantry: false, estimatedPrice: 1.29 }
        ];

        mockIngredients.forEach((ingredient, index) => {
          // Check if item is in pantry and get details
          const matchingPantryItem = pantryItems.find(pantryItem =>
            pantryItem.name.toLowerCase() === ingredient.name.toLowerCase()
          );

          ingredientMap.set(`mock_${index}`, {
            id: `item_${Date.now()}_${index}`,
            name: ingredient.name,
            quantity: ingredient.quantity,
            unit: ingredient.unit,
            category: ingredient.category,
            checked: false,
            inPantry: matchingPantryItem ? true : ingredient.inPantry,
            pantryQuantity: matchingPantryItem?.quantity || '',
            pantryUnit: matchingPantryItem?.unit || '',
            addToPantry: !matchingPantryItem && !ingredient.inPantry,
            estimatedPrice: ingredient.estimatedPrice
          });
        });

        // Calculate total estimated cost
        const totalCost = mockIngredients.reduce((sum, item) => sum + (item.estimatedPrice || 0), 0);
        setTotalEstimatedCost(totalCost);
      }

      // Convert map to array and group by category
      const allItems = Array.from(ingredientMap.values());

      // Calculate total estimated cost for real ingredients
      if (ingredientMap.size > 0 && allItems.some(item => item.estimatedPrice)) {
        const totalCost = allItems.reduce((sum, item) => sum + (item.estimatedPrice || 0), 0);
        setTotalEstimatedCost(totalCost);
      }

      const categoryGroups = allItems.reduce((groups: Record<string, ShoppingItem[]>, item) => {
        const category = item.category;
        if (!groups[category]) {
          groups[category] = [];
        }
        groups[category].push(item);
        return groups;
      }, {});

      // Convert to array of categories
      const categoriesArray = Object.keys(categoryGroups).map(categoryName => ({
        name: categoryName,
        items: categoryGroups[categoryName].sort((a, b) => a.name.localeCompare(b.name))
      }));

      // Sort categories
      const categoryOrder = [
        "Fruits",
        "Vegetables",
        "Meat & Seafood",
        "Dairy",
        "Grains & Bread",
        "Condiments & Spices",
        "Other"
      ];

      categoriesArray.sort((a, b) => {
        return categoryOrder.indexOf(a.name) - categoryOrder.indexOf(b.name);
      });

      setCategories(categoriesArray);
    } catch (error) {
      console.error("Error generating shopping list:", error);
      toast.error("Failed to generate shopping list");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddItem = () => {
    if (!newItem.name.trim()) {
      toast.error("Please enter an item name");
      return;
    }

    // Check if item is in pantry
    const matchingPantryItem = pantryItems.find(pantryItem =>
      pantryItem.name.toLowerCase() === newItem.name.toLowerCase()
    );

    // Calculate estimated price if not provided
    const estimatedPrice = newItem.estimatedPrice ?
      parseFloat(newItem.estimatedPrice) :
      estimatePrice(newItem.name, parseFloat(newItem.quantity) || 1, newItem.category);

    const newItemObj: ShoppingItem = {
      id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: newItem.name,
      quantity: newItem.quantity,
      unit: newItem.unit,
      category: newItem.category,
      checked: false,
      inPantry: !!matchingPantryItem,
      pantryQuantity: matchingPantryItem?.quantity || '',
      pantryUnit: matchingPantryItem?.unit || '',
      addToPantry: !matchingPantryItem && addAllToPantry,
      estimatedPrice
    };

    // Find the category
    const categoryIndex = categories.findIndex(cat => cat.name === newItem.category);

    if (categoryIndex >= 0) {
      // Category exists, add item to it
      const updatedCategories = [...categories];
      updatedCategories[categoryIndex].items.push(newItemObj);
      updatedCategories[categoryIndex].items.sort((a, b) => a.name.localeCompare(b.name));
      setCategories(updatedCategories);
    } else {
      // Create new category
      setCategories([
        ...categories,
        {
          name: newItem.category,
          items: [newItemObj]
        }
      ]);
    }

    // Update total estimated cost
    setTotalEstimatedCost(prev => prev + estimatedPrice);

    // Reset form
    setNewItem({ name: "", quantity: "1", unit: "item", category: "Other", estimatedPrice: "" });
    toast.success("Item added to shopping list");
  };

  const handleToggleItem = (categoryName: string, itemId: string) => {
    const updatedCategories = categories.map(category => {
      if (category.name === categoryName) {
        return {
          ...category,
          items: category.items.map(item =>
            item.id === itemId ? { ...item, checked: !item.checked } : item
          )
        };
      }
      return category;
    });

    setCategories(updatedCategories);
  };

  const handleDeleteItem = (categoryName: string, itemId: string) => {
    // Find the item to get its price before removing
    const category = categories.find(cat => cat.name === categoryName);
    const item = category?.items.find(item => item.id === itemId);

    const updatedCategories = categories.map(category => {
      if (category.name === categoryName) {
        return {
          ...category,
          items: category.items.filter(item => item.id !== itemId)
        };
      }
      return category;
    }).filter(category => category.items.length > 0); // Remove empty categories

    setCategories(updatedCategories);

    // Update total estimated cost
    if (item?.estimatedPrice) {
      setTotalEstimatedCost(prev => Math.max(0, prev - item.estimatedPrice!));
    }

    toast.success("Item removed from shopping list");
  };

  const handleSaveList = async () => {
    try {
      setIsLoading(true);

      // Flatten categories into items
      const items = categories.flatMap(category =>
        category.items.map(item => ({
          name: item.name,
          quantity: item.quantity,
          unit: item.unit,
          category: category.name,
          checked: item.checked
        }))
      );

      // In a real app, this would save to the database
      const { data, error } = await supabase
        .from('shopping_lists')
        .insert({
          user_id: (await supabase.auth.getUser()).data.user?.id,
          meal_plan_id: mealPlan.id,
          items: items,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      toast.success("Shopping list saved successfully");
      onClose();

      // Navigate to shopping list page
      router.push('/shopping-list');
    } catch (error) {
      console.error("Error saving shopping list:", error);
      toast.error("Failed to save shopping list");
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportList = () => {
    try {
      // Create CSV content
      let csvContent = "Category,Item,Quantity,Unit\n";

      categories.forEach(category => {
        category.items.forEach(item => {
          csvContent += `"${category.name}","${item.name}","${item.quantity}","${item.unit}"\n`;
        });
      });

      // Create downloadable file
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'shopping-list.csv';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success("Shopping list exported to CSV");
    } catch (error) {
      console.error("Error exporting shopping list:", error);
      toast.error("Failed to export shopping list");
    }
  };

  // Helper function to estimate price based on item name, quantity, and category
  const estimatePrice = (name: string, quantity: number, category: string): number => {
    // Base prices by category (per unit)
    const basePrices: Record<string, number> = {
      'Meat & Seafood': 4.99,
      'Vegetables': 1.99,
      'Fruits': 0.99,
      'Dairy': 2.99,
      'Grains & Bread': 1.49,
      'Condiments & Spices': 2.49,
      'Other': 1.99
    };

    // Adjust price based on specific items
    let adjustedBasePrice = basePrices[category] || basePrices['Other'];

    // Specific item adjustments
    if (name.toLowerCase().includes('chicken')) adjustedBasePrice = 3.99;
    if (name.toLowerCase().includes('beef')) adjustedBasePrice = 5.99;
    if (name.toLowerCase().includes('salmon')) adjustedBasePrice = 8.99;
    if (name.toLowerCase().includes('olive oil')) adjustedBasePrice = 7.99;
    if (name.toLowerCase().includes('rice')) adjustedBasePrice = 0.99;
    if (name.toLowerCase().includes('yogurt')) adjustedBasePrice = 3.99;

    // Calculate final price based on quantity
    return parseFloat((adjustedBasePrice * quantity).toFixed(2));
  };

  // Filter items based on search query and filter options
  const filterItems = () => {
    if (!searchQuery && !filterOptions.hideChecked && !filterOptions.hidePantryItems && !filterOptions.showOnlyNeeded) {
      return categories;
    }

    return categories.map(category => {
      const filteredItems = category.items.filter(item => {
        // Apply search filter
        const matchesSearch = !searchQuery ||
          item.name.toLowerCase().includes(searchQuery.toLowerCase());

        // Apply checked filter
        const passesCheckedFilter = !filterOptions.hideChecked || !item.checked;

        // Apply pantry filter
        const passesPantryFilter = !filterOptions.hidePantryItems || !item.inPantry;

        // Apply needed filter (items not in pantry)
        const passesNeededFilter = !filterOptions.showOnlyNeeded || !item.inPantry;

        return matchesSearch && passesCheckedFilter && passesPantryFilter && passesNeededFilter;
      });

      return {
        ...category,
        items: filteredItems
      };
    }).filter(category => category.items.length > 0); // Remove empty categories
  };

  // Handle adding all checked items to pantry
  const handleAddCheckedToPantry = async () => {
    try {
      setIsLoading(true);

      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        toast.error('Authentication error');
        return;
      }

      // Get all checked items that should be added to pantry
      const itemsToAdd = categories.flatMap(category =>
        category.items.filter(item => item.checked && (item.addToPantry || addAllToPantry))
      );

      if (itemsToAdd.length === 0) {
        toast('No items selected to add to pantry');
        setIsLoading(false);
        return;
      }

      // Format items for insertion
      const pantryInserts = itemsToAdd.map(item => ({
        user_id: user.id,
        name: item.name,
        category: item.category,
        quantity: item.quantity,
        unit: item.unit,
        created_at: new Date().toISOString()
      }));

      // Insert items into pantry
      const { data, error } = await supabase
        .from('pantry_items')
        .insert(pantryInserts);

      if (error) throw error;

      toast.success(`Added ${itemsToAdd.length} items to your pantry`);

      // Refresh pantry items
      fetchPantryItems();
    } catch (error) {
      console.error('Error adding items to pantry:', error);
      toast.error('Failed to add items to pantry');
    } finally {
      setIsLoading(false);
    }
  };

  const getTotalItems = () => {
    return categories.reduce((total, category) => total + category.items.length, 0);
  };

  const getCheckedItems = () => {
    return categories.reduce((total, category) =>
      total + category.items.filter(item => item.checked).length, 0
    );
  };

  // Toggle add to pantry for a specific item
  const handleToggleAddToPantry = (categoryName: string, itemId: string) => {
    const updatedCategories = categories.map(category => {
      if (category.name === categoryName) {
        return {
          ...category,
          items: category.items.map(item =>
            item.id === itemId ? { ...item, addToPantry: !item.addToPantry } : item
          )
        };
      }
      return category;
    });

    setCategories(updatedCategories);
  };

  // Get filtered categories based on search and filters
  const filteredCategories = filterItems();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex justify-between items-start">
            <div>
              <DialogTitle>Shopping List</DialogTitle>
              <DialogDescription>
                Generate and manage your shopping list based on your meal plan.
              </DialogDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/pantry')}
            >
              <Package className="h-4 w-4 mr-2" />
              Manage Pantry
            </Button>
          </div>
        </DialogHeader>

        {isLoading ? (
          <div className="py-8 text-center">
            <ShoppingCart className="h-12 w-12 mx-auto text-muted-foreground animate-pulse mb-4" />
            <p className="text-muted-foreground">Generating your shopping list...</p>
          </div>
        ) : (
          <Tabs defaultValue="list" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="list">Shopping List ({getCheckedItems()}/{getTotalItems()})</TabsTrigger>
              <TabsTrigger value="add">Add Items</TabsTrigger>
            </TabsList>

            <TabsContent value="list" className="space-y-4">
              {categories.length === 0 ? (
                <div className="text-center py-8">
                  <ShoppingCart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground mb-4">No items in your shopping list yet.</p>
                  <Button onClick={() => setActiveTab("add")}>Add Items</Button>
                </div>
              ) : (
                <>
                  <div className="flex flex-col gap-4 mb-4">
                    <div className="bg-muted p-4 rounded-lg">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="font-medium">Shopping Progress</h3>
                          <p className="text-sm text-muted-foreground">
                            {getCheckedItems()} of {getTotalItems()} items checked
                          </p>
                        </div>

                        <div className="flex items-center gap-2">
                          <Switch
                            id="add-to-pantry"
                            checked={addAllToPantry}
                            onCheckedChange={setAddAllToPantry}
                          />
                          <Label htmlFor="add-to-pantry" className="text-sm">Add to pantry</Label>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-4 justify-between">
                      <div className="relative w-full sm:w-64">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search items..."
                          className="pl-8"
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                        />
                      </div>

                      <div className="flex gap-2 flex-wrap">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setFilterOptions(prev => ({ ...prev, hideChecked: !prev.hideChecked }))}
                          className={filterOptions.hideChecked ? 'bg-primary/10' : ''}
                        >
                          <Filter className="h-4 w-4 mr-2" />
                          Hide Checked
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setFilterOptions(prev => ({ ...prev, hidePantryItems: !prev.hidePantryItems }))}
                          className={filterOptions.hidePantryItems ? 'bg-primary/10' : ''}
                        >
                          <Package className="h-4 w-4 mr-2" />
                          Hide Pantry Items
                        </Button>
                      </div>
                    </div>

                    <Card className="bg-muted/20">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-center">
                          <div>
                            <h3 className="font-medium">Estimated Total</h3>
                            <p className="text-sm text-muted-foreground">Based on average prices</p>
                          </div>
                          <div className="text-xl font-bold">${totalEstimatedCost.toFixed(2)}</div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                  {filteredCategories.length > 0 ? (
                    filteredCategories.map((category) => (
                      <Card key={category.name}>
                        <CardHeader className="py-3">
                          <CardTitle className="text-base">{category.name}</CardTitle>
                        </CardHeader>
                        <CardContent className="py-0">
                          <div className="space-y-2">
                            {category.items.map((item) => (
                              <div
                                key={item.id}
                                className={`flex items-center justify-between p-2 rounded-md ${item.checked ? 'bg-muted/50' : ''}`}
                              >
                                <div className="flex items-center flex-1">
                                  <Checkbox
                                    id={item.id}
                                    checked={item.checked}
                                    onCheckedChange={() => handleToggleItem(category.name, item.id)}
                                  />
                                  <div className="ml-2 flex-1">
                                    <Label
                                      htmlFor={item.id}
                                      className={`flex items-center ${item.checked ? 'line-through text-muted-foreground' : ''}`}
                                    >
                                      <span className="mr-2">{item.name}</span>
                                      {item.quantity && (
                                        <Badge variant="outline" className="font-normal">
                                          {item.quantity} {item.unit}
                                        </Badge>
                                      )}
                                      {item.inPantry && (
                                        <Badge variant="secondary" className="ml-2 font-normal">
                                          In Pantry
                                        </Badge>
                                      )}
                                    </Label>
                                    {item.estimatedPrice && (
                                      <div className="text-xs text-muted-foreground mt-1">
                                        Est. ${item.estimatedPrice.toFixed(2)}
                                      </div>
                                    )}
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Checkbox
                                    id={`pantry-${item.id}`}
                                    checked={item.addToPantry || false}
                                    onCheckedChange={() => handleToggleAddToPantry(category.name, item.id)}
                                    disabled={item.inPantry}
                                  />
                                  <Label
                                    htmlFor={`pantry-${item.id}`}
                                    className="text-xs whitespace-nowrap"
                                  >
                                    Add to Pantry
                                  </Label>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleDeleteItem(category.name, item.id)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    <div className="text-center py-8 border rounded-lg">
                      <AlertCircle className="h-12 w-12 mx-auto text-muted-foreground" />
                      <h3 className="mt-4 text-lg font-medium">No matching items</h3>
                      <p className="mt-2 text-sm text-muted-foreground">
                        Try adjusting your search or filters
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-4"
                        onClick={() => {
                          setSearchQuery('');
                          setFilterOptions({
                            hideChecked: false,
                            hidePantryItems: false,
                            showOnlyNeeded: false
                          });
                        }}
                      >
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Reset Filters
                      </Button>
                    </div>
                  )}
                </>
              )}
            </TabsContent>

            <TabsContent value="add">
              <Card>
                <CardHeader>
                  <CardTitle>Add New Item</CardTitle>
                  <CardDescription>
                    Add items to your shopping list. Items can be added to your pantry after purchase.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-12 gap-4">
                    <div className="col-span-12 sm:col-span-5">
                      <Label htmlFor="item-name">Item Name</Label>
                      <Input
                        id="item-name"
                        value={newItem.name}
                        onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                        placeholder="Enter item name"
                      />
                    </div>
                    <div className="col-span-4 sm:col-span-2">
                      <Label htmlFor="item-quantity">Quantity</Label>
                      <Input
                        id="item-quantity"
                        value={newItem.quantity}
                        onChange={(e) => setNewItem({ ...newItem, quantity: e.target.value })}
                        type="text"
                      />
                    </div>
                    <div className="col-span-4 sm:col-span-2">
                      <Label htmlFor="item-unit">Unit</Label>
                      <Input
                        id="item-unit"
                        value={newItem.unit}
                        onChange={(e) => setNewItem({ ...newItem, unit: e.target.value })}
                        placeholder="e.g., lbs, oz, etc."
                      />
                    </div>
                    <div className="col-span-4 sm:col-span-3">
                      <Label htmlFor="item-category">Category</Label>
                      <Select
                        value={newItem.category}
                        onValueChange={(value) => setNewItem({ ...newItem, category: value })}
                      >
                        <SelectTrigger id="item-category">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Fruits">Fruits</SelectItem>
                          <SelectItem value="Vegetables">Vegetables</SelectItem>
                          <SelectItem value="Meat & Seafood">Meat & Seafood</SelectItem>
                          <SelectItem value="Dairy">Dairy</SelectItem>
                          <SelectItem value="Grains & Bread">Grains & Bread</SelectItem>
                          <SelectItem value="Condiments & Spices">Condiments & Spices</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="col-span-12 sm:col-span-3">
                      <Label htmlFor="item-price">Est. Price (optional)</Label>
                      <div className="relative">
                        <span className="absolute left-3 top-2.5">$</span>
                        <Input
                          id="item-price"
                          value={newItem.estimatedPrice}
                          onChange={(e) => setNewItem({ ...newItem, estimatedPrice: e.target.value })}
                          placeholder="0.00"
                          className="pl-7"
                        />
                      </div>
                    </div>

                    <div className="col-span-12 sm:col-span-9 flex items-end">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="add-to-pantry-new"
                          checked={addAllToPantry}
                          onCheckedChange={setAddAllToPantry}
                        />
                        <Label htmlFor="add-to-pantry-new">Add to pantry after purchase</Label>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-6">
                    <div>
                      {newItem.name && newItem.quantity && (
                        <div className="text-sm text-muted-foreground">
                          Estimated price: ${estimatePrice(
                            newItem.name,
                            parseFloat(newItem.quantity) || 1,
                            newItem.category
                          ).toFixed(2)}
                        </div>
                      )}
                    </div>
                    <Button
                      onClick={handleAddItem}
                      disabled={!newItem.name.trim()}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Item
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}

        <Separator />

        <DialogFooter className="flex flex-col sm:flex-row justify-between gap-4">
          <div className="flex gap-2 w-full sm:w-auto">
            <Button
              variant="outline"
              onClick={handleExportList}
              disabled={isLoading || categories.length === 0}
              className="flex-1 sm:flex-none"
            >
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
            <Button
              variant="default"
              onClick={handleAddCheckedToPantry}
              disabled={isLoading || getCheckedItems() === 0}
              className="flex-1 sm:flex-none"
            >
              <Package className="h-4 w-4 mr-2" />
              Add to Pantry
            </Button>
          </div>
          <div className="flex gap-2 w-full sm:w-auto">
            <Button variant="outline" onClick={onClose} className="flex-1 sm:flex-none">
              Cancel
            </Button>
            <Button
              onClick={handleSaveList}
              disabled={isLoading || categories.length === 0}
              className="flex-1 sm:flex-none"
            >
              <Save className="h-4 w-4 mr-2" />
              Save & View
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
