'use client';

import React, { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

/**
 * SimplifiedSupabaseProvider component
 * 
 * This is a simplified version of the SupabaseProvider that doesn't use context.
 * It creates a Supabase client and passes it directly to its children as a prop.
 */
export function SimplifiedSupabaseProvider({ 
  children 
}: { 
  children: React.ReactNode 
}) {
  const [supabase, setSupabase] = useState<any>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  
  // Create the Supabase client on mount
  useEffect(() => {
    console.log('%c[SimplifiedProvider] Component mounted', 'color: purple; font-weight: bold');
    
    try {
      // Create a new Supabase client
      const client = createClientComponentClient();
      console.log('%c[SimplifiedProvider] Supabase client created successfully', 'color: purple');
      setSupabase(client);
      
      // Set initialized after a small delay
      setTimeout(() => {
        console.log('%c[SimplifiedProvider] Setting isInitialized to true', 'color: purple; font-weight: bold');
        setIsInitialized(true);
      }, 100);
    } catch (err) {
      console.error('%c[SimplifiedProvider] Error creating Supabase client:', 'color: red', err);
    }
    
    return () => {
      console.log('%c[SimplifiedProvider] Component unmounting', 'color: orange; font-weight: bold');
    };
  }, []);
  
  // Pass the supabase client as a prop to any child that needs it
  return (
    <>
      {React.Children.map(children, child => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, { 
            supabaseClient: supabase,
            isSupabaseInitialized: isInitialized
          });
        }
        return child;
      })}
    </>
  );
}
