'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { SupabaseClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/supabase';

// Define the context type
type SupabaseContextType = {
  supabase: SupabaseClient<Database> | null;
  isLoading: boolean;
};

// Create the context with a default value
const SupabaseContext = createContext<SupabaseContextType>({
  supabase: null,
  isLoading: true,
});

/**
 * useSupabase hook
 *
 * This hook provides access to the Supabase client and loading state.
 * It should be used within a SupabaseProvider.
 *
 * @example
 * // In your component
 * function MyComponent() {
 *   const { supabase, isLoading } = useSupabase();
 *
 *   // Handle loading state
 *   if (isLoading) {
 *     return <div>Loading...</div>;
 *   }
 *
 *   // Now you can use the supabase client
 *   useEffect(() => {
 *     if (!supabase) return;
 *     
 *     const fetchData = async () => {
 *       const { data } = await supabase.from('table').select('*');
 *       // ...
 *     };
 *
 *     fetchData();
 *   }, [supabase]);
 *
 *   return <div>My Component</div>;
 * }
 */
export const useSupabase = () => {
  const context = useContext(SupabaseContext);
  
  if (!context) {
    throw new Error('useSupabase must be used within a SupabaseProvider');
  }
  
  return context;
};

/**
 * SupabaseProvider component
 *
 * This component creates a Supabase client and provides it to all child components
 * through React Context. Any component that needs to access Supabase should be a
 * descendant of this provider.
 *
 * @example
 * // In your root layout
 * export default function RootLayout({ children }) {
 *   return (
 *     <html>
 *       <body>
 *         <SupabaseProvider>
 *           {children}
 *         </SupabaseProvider>
 *       </body>
 *     </html>
 *   );
 * }
 */
export function SupabaseProvider({ children }: { children: React.ReactNode }) {
  // State to hold the Supabase client and loading state
  const [state, setState] = useState<SupabaseContextType>({
    supabase: null,
    isLoading: true,
  });
  
  // Initialize the Supabase client
  useEffect(() => {
    console.log('SupabaseProvider: Initializing Supabase client');
    
    // Create the client
    try {
      const supabase = createClientComponentClient<Database>();
      console.log('SupabaseProvider: Supabase client created successfully');
      
      // Update the state
      setState({
        supabase,
        isLoading: false,
      });
    } catch (error) {
      console.error('SupabaseProvider: Error creating Supabase client', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
    
    // Cleanup function
    return () => {
      console.log('SupabaseProvider: Cleaning up');
    };
  }, []);
  
  // Provide the context value to children
  return (
    <SupabaseContext.Provider value={state}>
      {children}
    </SupabaseContext.Provider>
  );
}
