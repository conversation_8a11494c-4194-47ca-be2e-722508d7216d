'use client';

import React, { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { LoadingSpinner } from '@/components/ui/loading';

/**
 * SimplifiedSupabaseWrapper component
 * 
 * This is a simplified version of the SupabaseWrapper that doesn't rely on the context.
 * It creates its own Supabase client and passes it to the children.
 */
export function SimplifiedSupabaseWrapper({ 
  children,
  loadingComponent
}: { 
  children: React.ReactNode;
  loadingComponent?: React.ReactNode;
}) {
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [supabase, setSupabase] = useState<any>(null);
  
  // Create the Supabase client on mount
  useEffect(() => {
    console.log('%c[SimplifiedWrapper] Component mounted', 'color: purple; font-weight: bold');
    
    try {
      // Create a new Supabase client
      const client = createClientComponentClient();
      console.log('%c[SimplifiedWrapper] Supabase client created successfully', 'color: purple');
      setSupabase(client);
      
      // Set ready after a small delay to ensure the client is fully initialized
      setTimeout(() => {
        console.log('%c[SimplifiedWrapper] Setting isReady to true', 'color: purple; font-weight: bold');
        setIsReady(true);
      }, 100);
    } catch (err: any) {
      console.error('%c[SimplifiedWrapper] Error creating Supabase client:', 'color: red', err);
      setError(err);
    }
    
    return () => {
      console.log('%c[SimplifiedWrapper] Component unmounting', 'color: orange; font-weight: bold');
    };
  }, []);
  
  // If there was an error, show an error message
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[200px] p-4 text-red-500 bg-red-50 rounded-md">
        <h3 className="text-lg font-semibold mb-2">Supabase Client Error</h3>
        <p className="text-sm text-center">{error.message}</p>
        <p className="text-xs mt-4 text-center">There was an error creating the Supabase client</p>
      </div>
    );
  }
  
  // Show loading component while waiting for the client to initialize
  if (!isReady) {
    return loadingComponent || (
      <div className="flex items-center justify-center min-h-[200px]">
        <LoadingSpinner size="lg" />
        <p className="ml-2 text-muted-foreground">Initializing Supabase Client...</p>
      </div>
    );
  }
  
  // Client is initialized, render the children
  return (
    <div className="supabase-wrapper-simple">
      {/* Pass the supabase client as a prop to any child that needs it */}
      {React.Children.map(children, child => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, { supabaseClient: supabase });
        }
        return child;
      })}
    </div>
  );
}
