'use client';

import { <PERSON>, <PERSON>Header, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export function WeeklyPlanSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 7 }).map((_, index) => (
        <Card key={index}>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Array.from({ length: 3 }).map((_, mealIndex) => (
                <div key={mealIndex} className="p-2 border rounded">
                  <Skeleton className="h-4 w-48 mb-2" />
                  <Skeleton className="h-3 w-32" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}