"use client";

import { usePathname, useRouter } from "next/navigation";
import { BurgerMenu } from "../navigation/BurgerMenu";
import { Button } from "@/components/ui/button";
import { UserDropdownMenu } from "@/components/dashboard/user-dropdown-menu";
import { Plus, Calendar } from "lucide-react";

export function Header() {
  const pathname = usePathname();
  const router = useRouter();
  const isDashboard = pathname === "/dashboard" || pathname.startsWith("/dashboard/");

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <div className="mr-2">
              <BurgerMenu />
            </div>
          </div>
          <h1
            className="text-xl sm:text-2xl font-bold text-primary cursor-pointer"
            onClick={() => router.push('/dashboard')}
          >
            LeanEats
          </h1>
        </div>

        {isDashboard && (
          <div className="flex items-center space-x-2 sm:space-x-4">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/meal-plan/new')}
                className="transition-colors hover:bg-primary hover:text-primary-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              >
                <Calendar className="h-4 w-4 mr-2" aria-hidden="true" />
                New Meal Plans
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/meal-plan/generate')}
                className="transition-colors hover:bg-primary hover:text-primary-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              >
                <Plus className="h-4 w-4 mr-2" aria-hidden="true" />
                Create Plan
              </Button>
            </div>
            <UserDropdownMenu />
          </div>
        )}
      </div>
    </header>
  );
}
