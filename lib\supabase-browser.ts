import { createBrowserClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

// Singleton instance to avoid multiple client creation
let supabaseClient: ReturnType<typeof createBrowserClient<Database>> | null = null;

export function getSupabaseBrowserClient() {
  // Return existing client if available
  if (supabaseClient) {
    return supabaseClient;
  }

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  // Validate environment variables
  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Missing Supabase environment variables:', {
      url: !!supabaseUrl,
      key: !!supabaseAnonKey
    });
    throw new Error('Missing Supabase configuration. Please check your environment variables.');
  }

  try {
    supabaseClient = createBrowserClient<Database>(
      supabaseUrl,
      supabaseAnonKey,
      {
        auth: {
          persistSession: true,
          autoRefreshToken: true,
          detectSessionInUrl: true,
          flowType: 'pkce'
        },
        cookies: {
          get(name: string) {
            if (typeof document === 'undefined') return undefined;
            return document.cookie
              .split('; ')
              .find((row) => row.startsWith(`${name}=`))
              ?.split('=')[1];
          },
          set(name: string, value: string, options: {
            maxAge?: number;
            domain?: string;
            path?: string;
            sameSite?: string;
            secure?: boolean
          }) {
            if (typeof document === 'undefined') return;
            let cookie = `${name}=${value}`;
            if (options.maxAge) cookie += `; Max-Age=${options.maxAge}`;
            if (options.domain) cookie += `; Domain=${options.domain}`;
            if (options.path) cookie += `; Path=${options.path}`;
            if (options.sameSite) cookie += `; SameSite=${options.sameSite}`;
            if (options.secure) cookie += '; Secure';
            document.cookie = cookie;
          },
          remove(name: string, options: { path?: string; domain?: string }) {
            if (typeof document === 'undefined') return;
            let cookie = `${name}=; Max-Age=0`;
            if (options.path) cookie += `; Path=${options.path}`;
            if (options.domain) cookie += `; Domain=${options.domain}`;
            document.cookie = cookie;
          },
        },
      }
    );

    return supabaseClient;
  } catch (error) {
    console.error('Failed to create Supabase client:', error);
    throw new Error('Failed to initialize Supabase client');
  }
}

// Helper function to test Supabase connectivity with timeout
export async function testSupabaseConnection(timeoutMs: number = 5000): Promise<boolean> {
  try {
    const client = getSupabaseBrowserClient();

    // Create a timeout promise
    const timeoutPromise = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error('Connection timeout')), timeoutMs)
    );

    // Race between the actual request and timeout
    const sessionPromise = client.auth.getSession();
    const { data, error } = await Promise.race([sessionPromise, timeoutPromise]);

    if (error) {
      console.error('Supabase connection test failed:', error.message);
      return false;
    }

    console.log('Supabase connection test successful');
    return true;
  } catch (error: any) {
    console.error('Supabase connection test error:', error.message);
    return false;
  }
}

// Helper function to check if error is connection-related
export function isConnectionError(error: any): boolean {
  if (!error) return false;

  const errorMessage = error.message?.toLowerCase() || '';
  const connectionErrors = [
    'fetch failed',
    'network error',
    'connection timeout',
    'connection refused',
    'timeout',
    'enotfound',
    'econnrefused'
  ];

  return connectionErrors.some(errType => errorMessage.includes(errType));
}

// Helper function to get user-friendly error message
export function getConnectionErrorMessage(error: any): string {
  if (isConnectionError(error)) {
    return 'Unable to connect to the authentication service. Please check your internet connection and try again.';
  }

  if (error.message?.includes('Invalid login credentials')) {
    return 'Invalid email or password. Please check your credentials.';
  }

  if (error.message?.includes('Email not confirmed')) {
    return 'Please check your email and click the confirmation link.';
  }

  if (error.message?.includes('Too many requests')) {
    return 'Too many login attempts. Please wait a moment and try again.';
  }

  return error.message || 'An unexpected error occurred. Please try again.';
}
