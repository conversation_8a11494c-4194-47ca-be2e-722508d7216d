import { createBrowserClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

export function getSupabaseBrowserClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

  return createBrowserClient<Database>(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        get(name: string) {
          return document.cookie
            .split('; ')
            .find((row) => row.startsWith(`${name}=`))
            ?.split('=')[1];
        },
        set(name: string, value: string, options: { maxAge?: number; domain?: string; path?: string; sameSite?: string; secure?: boolean }) {
          let cookie = `${name}=${value}`;
          if (options.maxAge) cookie += `; Max-Age=${options.maxAge}`;
          if (options.domain) cookie += `; Domain=${options.domain}`;
          if (options.path) cookie += `; Path=${options.path}`;
          if (options.sameSite) cookie += `; SameSite=${options.sameSite}`;
          if (options.secure) cookie += '; Secure';
          document.cookie = cookie;
        },
        remove(name: string, options: { path?: string; domain?: string }) {
          const cookieString = `${name}=; Max-Age=0`;
          document.cookie = cookieString;
        },
      },
    }
  );
}
