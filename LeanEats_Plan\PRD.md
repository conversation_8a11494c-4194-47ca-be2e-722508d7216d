### **Product Requirements Document (PRD): LeanEats MVP**

**Document Version:** 1.1
**Date:** June 28, 2025
**Product:** LeanEats - Personalized Meal Planning & Grocery Management Application
**Authored By:** <PERSON>, Product Manager (Updated by Frontend Architect)

---

#### **1. Introduction / Executive Summary**

* **Product Name:** LeanEats
* **Version:** 1.0 (MVP)
* **Date:** June 28, 2025
* **Authored By:** <PERSON>, Product Manager
* **Purpose:** This document outlines the functional and non-functional requirements for LeanEats, a web-based meal planning application designed to empower users to create personalized, budget-friendly meal plans. It serves as a guiding document for the development team, ensuring alignment with user needs and business objectives for the Minimum Viable Product (MVP).

---

#### **2. Product Vision & Goals**

* **Vision:** To become the go-to platform for individuals and families seeking to eat healthily and affordably by providing intelligent, personalized meal plans and efficient grocery shopping tools.
* **Goals (MVP):**
    * Enable users to generate personalized, budget-friendly meal plans based on their preferences.
    * Provide an accurate and customizable shopping list from generated plans.
    * Achieve high user satisfaction (e.g., >80% positive feedback on meal plan relevance).
    * Lay the foundation for future growth and monetization.

---

#### **3. Target Audience**

* **Primary:** Budget-conscious individuals and families in the **US, EU, and Asian countries** who want to eat healthier but struggle with meal planning, grocery budgeting, and finding suitable recipes.
* **Secondary:** People with specific dietary needs (e.g., vegan, keto, gluten-free) looking for cost-effective solutions, and individuals seeking efficient meal prep tools.

---

#### **4. Key Features (MVP)**

This section details the core functionalities of the LeanEats MVP.

##### **4.1. User Authentication & Profile Management**

* **Description:** Users can securely sign up, log in, and manage their core profile information and preferences.
* **Requirements:**
    * **US: User Sign-up & Login:** Secure user registration (email/password, OAuth via Supabase) and login/session management.
    * **US: Manage User Profile:** Profile editing (name, email, profile picture).
    * **US: Initial Preference Setup & Management:** Guided step-by-step wizard for new users, and ability for existing users to easily view and modify:
        * **Household Size:** Number of people to plan for.
        * **Budget Tier:** Selection from "Budget Conscious", "Moderate", "Flexible".
            * **Note:** Users will select their preferred currency (e.g., USD, EUR, GBP, JPY, INR). Initial cost estimations for EU and Asian currencies will be approximate due to API limitations for MVP.
        * **Dietary Restrictions/Preferences:** Multi-selectable (e.g., vegan, gluten-free, keto) and ability to list excluded ingredients.
        * **Cooking Skill Level:** Selection from "Beginner", "Intermediate", "Advanced" based on quantitative definitions.
        * **Max Cooking Time:** Numerical input (e.g., 30 minutes), representing total prep + cook time.
        * **Cuisine Preferences:** Selection of preferred cuisines.
    * **US: Account Security:** Users can change their password and update their email address.
    * **US: Notification Settings:** Users can control types and delivery methods of notifications.
    * **US: Privacy Controls:** Users can view and manage data sharing preferences.
    * **US: Data Export & Account Deletion:** Users have the option to export their personal data and permanently delete their account.

##### **4.2. Meal Plan Generation & Customization**

* **Description:** The core functionality allowing users to generate a customized 7-day meal plan based on their preferences, with options for review and modification.
* **Requirements:**
    * **US: Generate New Meal Plan:** Utilize the **Hybrid (OpenAI + Edamam + Spoonacular) approach** for initial user-facing generation. A clear form to gather user preferences before generation.
    * **US: View Weekly Meal Plan:** Present a clear, navigable 7-day meal plan overview showing breakfast, lunch, dinner, and optional snacks.
    * **US: Meal Plan Preview & Customization:** Display a preview of the generated plan including estimated costs, enabling customization before saving.
    * **US: Meal Swapping:** Ability to manually swap a meal for 3-5 suggested alternatives that strictly adhere to user's preferences (budget, dietary, skill, time) and prioritize using ingredients already in the shopping list.
    * **US: Meal Plan Editing:** Users can reorder meals within their plan (e.g., drag-and-drop).
    * **Repetition Rules:** Maximum of 2-3 identical recipes per week for the same meal type; no identical meals on consecutive days for lunch/dinner.
    * **Recipe Content for EU/Asia:** Acknowledge that the breadth and specificity of EU and Asian cuisine representation, along with accurate ingredient availability/naming, might be limited in the initial launch due to current API integrations.

##### **4.3. Shopping List Generation & Management**

* **Description:** Automatically generates a comprehensive grocery list from the selected meal plan, with options for user modification and export.
* **Requirements:**
    * **US: Generate Shopping List:** Aggregate all ingredients from the weekly plan, calculate total quantities, and group ingredients by logical categories (e.g., produce, dairy, pantry) or simulated store sections.
    * **US: View & Organize Shopping List:** Display estimated total shopping list cost based on Spoonacular API data.
    * **US: Manage Shopping List Items:** Users can manually add, remove, modify quantities, and **mark items as purchased** on the list.
    * **US: Export Shopping List:** Option to export the shopping list (e.g., PDF or simple text, copy to clipboard).

##### **4.4. Recipe Discovery & Interaction**

* **Description:** Enables users to browse, search, and interact with individual recipes.
* **Requirements:**
    * **US: Browse & Search Recipes:** Users can browse and search for recipes based on various criteria (e.g., cuisine, dietary restriction, cooking time).
    * **US: View Detailed Recipes:** Each meal card should link to a detailed recipe view including: Recipe Name, Ingredients with quantities, Simple cooking instructions, Estimated cost per meal, Basic nutritional information (calories, protein, carbs, fat).
    * **US: Scale Recipe Portions:** Users can dynamically adjust the number of servings for a recipe, so the ingredient quantities automatically scale.
    * **US: Favorite Recipes:** Users can mark recipes as favorites.
    * **US: Recipe Calendar Integration:** As a user, I want to be able to add any recipe from the app directly to my external Google Calendar, so I can keep track of my meal schedule in my preferred calendar application.
    * **Media:** One high-quality hero image per recipe, sourced from APIs or curated stock library.

##### **4.5. Backend & Integrations**

* **Description:** Defines the underlying technical components and external services that power LeanEats.
* **Requirements:**
    * **Database:** Supabase for user authentication and core data storage (users, recipes, meal plans, shopping lists, user preferences).
    * **External APIs:**
        * **Edamam API:** For recipe data, nutritional analysis, and meal suggestions.
        * **Spoonacular API:** For ingredient cost information and pricing, and potentially basic recipe data.
        * **OpenAI API:** For generative AI capabilities in the Hybrid meal plan approach.
    * **Cloud Storage/CDN:** For recipe images and other media assets.

##### **4.6. Grocery Integration**

* **Description:** Facilitates users in purchasing groceries based on their generated shopping list.
* **Requirements (MVP):**
    * Display estimated ingredient costs sourced from Spoonacular within our generated shopping list.
    * Provide a clear "Shop Now" button that links out to the websites of major online grocery retailers (e.g., Walmart, Amazon Fresh/Whole Foods, Instacart), primarily focusing on **US retailers** for accurate pricing and direct linking in MVP.
    * For EU and Asian users, "Shop Now" links might be generic (e.g., Google Shopping results) and price estimations approximate.

---

#### **5. Non-Functional Requirements**

##### **5.1. Performance**

* **US: Application Performance & Responsiveness:** As a user, I want the LeanEats application to load quickly and respond smoothly to my interactions on various devices (desktop, tablet, mobile), so I have an efficient and frustration-free experience.
* **Technical Requirements:**
    * Meal plan generation should complete within 10-15 seconds.
    * Shopping list generation within 5 seconds.
    * Page load times: ≤3 seconds for core pages.
    * API response times: ≤500ms for critical endpoints.

##### **5.2. Scalability**

* **Description:** The system's ability to handle increasing user load and data volume.
* **Requirements:**
    * Architecture capable of handling 10,000 concurrent users (initial target).
    * Database designed for future expansion of recipe count and user base.
    * Ability to expand localization beyond US in future phases.

##### **5.3. Security**

* **Description:** Measures to protect user data and ensure system integrity.
* **Requirements:**
    * User data encrypted at rest and in transit.
    * Adherence to OWASP Top 10 security principles.
    * Rate limiting on API calls and authentication attempts.
    * Row-Level Security (RLS) policies in Supabase.

##### **5.4. Reliability & Error Handling**

* **US: User-Friendly Error Notifications:** As a user, I want to receive clear, concise, and actionable error messages when something goes wrong (e.g., API failure, network issue, invalid input), so I understand the problem and what steps I can take next.
* **Technical Requirements:**
    * Comprehensive error handling and logging.
    * Graceful degradation for external API failures (e.g., fallback to basic plan generation, informative error messages).
    * Retry mechanisms for transient errors.

##### **5.5. Usability (UX)**

* **Description:** Ease of use and overall user experience of the application.
* **Requirements:**
    * Intuitive and easy-to-navigate user interface.
    * Clear and concise language in all communication (on-screen, error messages).
    * Responsive design for various devices (desktop, tablet, mobile).
    * **US: Accessibility for Core Features:** As a user, I want to be able to navigate and interact with the main features of the LeanEats application (onboarding, dashboard, shopping list, recipe view, settings) using standard accessibility tools (e.g., keyboard navigation, screen readers), so I can use the app regardless of my abilities.

##### **5.6. Localization (MVP Focus)**

* **Description:** Support for different geographical and cultural contexts.
* **Requirements:**
    * **Currency Selection:** Allow users to choose their display currency (e.g., USD, EUR, GBP, JPY, INR).
    * **Units of Measurement:** Support toggling between Imperial (for US) and Metric (for EU/Asia) units for recipe ingredients.
    * **Language:** English will be the primary language for MVP.

---

#### **6. Success Metrics (KPIs)**

* **User Acquisition:** % of sign-ups converted to active users (e.g., generated first meal plan).
* **Engagement:**
    * Weekly Active Users (WAU).
    * Number of meal plans generated per user per week.
    * Shopping list generation rate (e.g., % of generated plans converted to lists).
    * Time spent in the app per session.
* **Retention:**
    * D7, D30 retention rates.
    * Churn rate.
* **Performance:** Meal plan generation time, page load speed.
* **Quality:** User feedback score on plan relevance and accuracy (e.g., in-app rating).

---

#### **7. Monetization Strategy (Future - Post-MVP Foundation)**

* **Freemium Model (Foundation laid in MVP):**
    * **Free Tier:** Basic personalized weekly meal plans (limited generations), basic shopping list, core nutritional info.
    * **Premium Tier (Deferred features):** Unlimited generations, advanced customization (macro targets), ad-free experience, exclusive recipes, priority support.
* **Affiliate Marketing (Future - Post-MVP Foundation):** Linking out to major grocery retailers (Instacart, Amazon Fresh, Walmart Grocery) and budget-friendly kitchenware (Amazon Affiliates), with a goal to expand to budget meal kit services (EveryPlate/Dinnerly).

---

#### **8. Scope & Future Phases (Deferred features from MVP)**

* **Post-MVP:**
    * User-submitted recipes and moderation process.
    * Advanced nutritional tracking and goal setting.
    * Weekly challenges and social sharing.
    * Deep, localized "Add to Cart" integration with specific grocery services in EU and Asian countries.
    * Push notifications and advanced email preferences.
    * Comprehensive multi-language support.
    * AI image generation for unique recipes.
    * Implementation of full freemium model (payment gateway, access control for premium features).
    * Expansion of regional recipe diversity for EU and Asian cuisines.

---

#### **9. Technical Considerations & Dependencies**

* **Frontend Framework:** Next.js (React) - (Determined during Frontend Architecture).
* **Backend:** Node.js/Python (common for API services) - (General placeholder, detailed in API Docs/Integration).
* **Database:** Supabase (PostgreSQL).
* **Cloud Platform:** AWS/Azure/GCP (for hosting, scaling, CDN).
* **APIs:** Edamam, Spoonacular, OpenAI.
* **Version Control:** Git.

---

#### **10. Risks & Assumptions**

* **Risks:**
    * Accuracy of external API data (recipes, nutrition, costs) across all targeted regions.
    * OpenAI's ability to consistently generate high-quality, budget-friendly, and nutritionally balanced plans.
    * User adoption and retention in a competitive global market.
    * API rate limits and costs becoming prohibitive with increased regional usage.
    * Difficulty in defining and scaling user-submitted recipe moderation for diverse cuisines.
* **Assumptions:**
    * Users are willing to input detailed dietary/budget preferences.
    * API services (Edamam, Spoonacular, OpenAI) remain stable and accessible.
    * Initial approximate grocery price estimations for EU and Asian regions are acceptable to users.
    * English is sufficient as the primary language for MVP.

---