"use client";
import { getSupabaseBrowserClient, testSupabaseConnection, isConnectionError, getConnectionErrorMessage } from '@/lib/supabase-browser';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { AlertCircle, Wifi, WifiOff } from "lucide-react";

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [returnTo, setReturnTo] = useState("/dashboard");
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const router = useRouter();

  // Check for returnTo parameter in URL
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const returnPath = params.get('returnTo');
      if (returnPath) {
        setReturnTo(returnPath);
      }
    }
  }, []);

  // Test Supabase connection on component mount
  useEffect(() => {
    const checkConnection = async () => {
      try {
        setConnectionStatus('checking');
        const isConnected = await testSupabaseConnection(3000); // 3 second timeout
        setConnectionStatus(isConnected ? 'connected' : 'disconnected');

        if (!isConnected) {
          toast.error("Unable to connect to authentication service. Please check your internet connection.");
        }
      } catch (error) {
        console.error('Connection check failed:', error);
        setConnectionStatus('disconnected');
        toast.error("Authentication service is currently unavailable.");
      }
    };

    checkConnection();

    // Retry connection check every 30 seconds if disconnected
    const interval = setInterval(() => {
      if (connectionStatus === 'disconnected') {
        checkConnection();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [connectionStatus]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check connection status before attempting login
    if (connectionStatus === 'disconnected') {
      toast.error("Cannot login: Authentication service is unavailable");
      return;
    }

    setIsLoading(true);

    try {
      const supabase = getSupabaseBrowserClient();

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
        options: {
          redirectTo: `${window.location.origin}${returnTo}`,
        }
      });

      if (error) {
        console.error("Login error:", error);

        // Use the enhanced error handling
        const userMessage = getConnectionErrorMessage(error);
        toast.error(userMessage);

        // Update connection status if it's a connection error
        if (isConnectionError(error)) {
          setConnectionStatus('disconnected');
        }

        return;
      }

      if (data?.user) {
        // Check if session was created successfully
        const supabase = getSupabaseBrowserClient();
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          throw new Error('No session created');
        }

        toast.success("Logged in successfully");
        // Force a hard navigation to ensure cookies are set
        window.location.href = returnTo;
      }
    } catch (error: any) {
      console.error("Login error:", error);

      // Use the enhanced error handling
      const userMessage = getConnectionErrorMessage(error);
      toast.error(userMessage);

      // Update connection status if it's a connection error
      if (isConnectionError(error)) {
        setConnectionStatus('disconnected');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) {
        toast.error(error.message);
      }
    } catch (error) {
      console.error("Google sign-in error:", error);
      toast.error("Failed to sign in with Google");
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">Welcome back</CardTitle>
          <CardDescription className="text-center">
            Log in to your LeanEats account
            {connectionStatus === 'checking' && (
              <div className="mt-2 p-2 bg-blue-100 text-blue-800 rounded text-sm flex items-center justify-center gap-2">
                <Wifi className="h-4 w-4 animate-pulse" />
                Checking connection...
              </div>
            )}
            {connectionStatus === 'disconnected' && (
              <div className="mt-2 p-2 bg-red-100 text-red-800 rounded text-sm">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <WifiOff className="h-4 w-4" />
                  Authentication service unavailable
                </div>
                <div className="text-xs text-center">
                  You can still browse the app in demo mode by clicking "Continue as Guest" below.
                </div>
              </div>
            )}
            {connectionStatus === 'connected' && (
              <div className="mt-2 p-2 bg-green-100 text-green-800 rounded text-sm flex items-center justify-center gap-2">
                <Wifi className="h-4 w-4" />
                Connected
              </div>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            <Button
              type="submit"
              className="w-full"
              disabled={isLoading || connectionStatus === 'disconnected'}
            >
              {isLoading ? "Logging in..." :
               connectionStatus === 'disconnected' ? "Service Unavailable" :
               connectionStatus === 'checking' ? "Checking Connection..." :
               "Log in"}
            </Button>

            {connectionStatus === 'disconnected' && (
              <Button
                type="button"
                variant="outline"
                className="w-full mt-2"
                onClick={() => {
                  toast.success("Continuing in demo mode");
                  router.push(returnTo);
                }}
              >
                Continue as Guest (Demo Mode)
              </Button>
            )}
          </form>

          <div className="mt-4">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white dark:bg-gray-800 text-gray-500">
                  Or continue with
                </span>
              </div>
            </div>
            <Button
              type="button"
              onClick={handleGoogleSignIn}
              className="mt-4 w-full flex items-center justify-center"
              variant="outline"
            >
              <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              Continue with Google
            </Button>
          </div>
          <div className="mt-4 text-center text-sm">
            <Link href="/forgot-password" className="text-primary hover:underline">
              Forgot your password?
            </Link>
          </div>
          <div className="mt-4 text-center text-sm">
            Don't have an account?{" "}
            <Link href="/signup" className="text-primary hover:underline">
              Sign up
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

