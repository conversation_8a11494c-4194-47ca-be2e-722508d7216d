'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Check, Star, Trash2, X } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface BatchActionsProps {
  selectedIds: string[];
  onClearSelection: () => void;
  onAddToFavorites: (ids: string[]) => void;
  onRemoveFromFavorites: (ids: string[]) => void;
  onDelete: (ids: string[]) => void;
}

export function BatchActions({
  selectedIds,
  onClearSelection,
  onAddToFavorites,
  onRemoveFromFavorites,
  onDelete
}: BatchActionsProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  
  const handleAddToFavorites = () => {
    onAddToFavorites(selectedIds);
  };
  
  const handleRemoveFromFavorites = () => {
    onRemoveFromFavorites(selectedIds);
  };
  
  const handleDeleteClick = () => {
    setShowDeleteDialog(true);
  };
  
  const confirmDelete = () => {
    onDelete(selectedIds);
    setShowDeleteDialog(false);
  };

  if (selectedIds.length === 0) {
    return null;
  }

  return (
    <>
      <Card className="sticky bottom-4 left-0 right-0 z-10 mx-auto max-w-2xl shadow-lg border-primary/20">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Check className="h-5 w-5 mr-2 text-primary" />
              <span className="font-medium">{selectedIds.length} recipes selected</span>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onClearSelection}
              >
                <X className="h-4 w-4 mr-1" />
                Clear
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleAddToFavorites}
              >
                <Star className="h-4 w-4 mr-1 fill-yellow-400 text-yellow-400" />
                Favorite
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRemoveFromFavorites}
              >
                <Star className="h-4 w-4 mr-1" />
                Unfavorite
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDeleteClick}
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Delete
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete {selectedIds.length} selected recipes. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
