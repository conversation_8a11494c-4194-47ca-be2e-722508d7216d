import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { FavoriteMeal, CreateFavoriteMealRequest } from '@/types/database-extended';

export class FavoritesService {
  private supabase = createClientComponentClient();

  async getFavorites(userId: string): Promise<{ data: FavoriteMeal[] | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('favorite_meals')
        .select(`
          *,
          recipes (
            id,
            name,
            description,
            image_url,
            prep_time,
            cook_time,
            servings,
            calories_per_serving
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching favorites:', error);
        return { data: null, error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Unexpected error fetching favorites:', error);
      return { data: null, error: 'Failed to fetch favorites' };
    }
  }

  async addToFavorites(userId: string, request: CreateFavoriteMealRequest): Promise<{ data: FavoriteMeal | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('favorite_meals')
        .insert({
          user_id: userId,
          recipe_id: request.recipe_id,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Error adding to favorites:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Unexpected error adding to favorites:', error);
      return { data: null, error: 'Failed to add to favorites' };
    }
  }

  async removeFromFavorites(userId: string, recipeId: string): Promise<{ error: string | null }> {
    try {
      const { error } = await this.supabase
        .from('favorite_meals')
        .delete()
        .eq('user_id', userId)
        .eq('recipe_id', recipeId);

      if (error) {
        console.error('Error removing from favorites:', error);
        return { error: error.message };
      }

      return { error: null };
    } catch (error) {
      console.error('Unexpected error removing from favorites:', error);
      return { error: 'Failed to remove from favorites' };
    }
  }

  async isFavorite(userId: string, recipeId: string): Promise<{ data: boolean; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('favorite_meals')
        .select('id')
        .eq('user_id', userId)
        .eq('recipe_id', recipeId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
        console.error('Error checking favorite status:', error);
        return { data: false, error: error.message };
      }

      return { data: !!data, error: null };
    } catch (error) {
      console.error('Unexpected error checking favorite status:', error);
      return { data: false, error: 'Failed to check favorite status' };
    }
  }

  async getFavoriteRecipeIds(userId: string): Promise<{ data: string[] | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('favorite_meals')
        .select('recipe_id')
        .eq('user_id', userId);

      if (error) {
        console.error('Error fetching favorite recipe IDs:', error);
        return { data: null, error: error.message };
      }

      const recipeIds = data?.map(item => item.recipe_id) || [];
      return { data: recipeIds, error: null };
    } catch (error) {
      console.error('Unexpected error fetching favorite recipe IDs:', error);
      return { data: null, error: 'Failed to fetch favorite recipe IDs' };
    }
  }
}

export const favoritesService = new FavoritesService();
