"use client";

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const DATA = {
  calories: [
    { day: 'Mon', breakfast: 400, lunch: 600, dinner: 500, snacks: 300, target: 2000 },
    { day: 'Tue', breakfast: 350, lunch: 650, dinner: 550, snacks: 250, target: 2000 },
    { day: 'Wed', breakfast: 450, lunch: 550, dinner: 500, snacks: 350, target: 2000 },
    { day: 'Thu', breakfast: 400, lunch: 600, dinner: 450, snacks: 300, target: 2000 },
    { day: 'Fri', breakfast: 500, lunch: 500, dinner: 600, snacks: 200, target: 2000 },
    { day: 'Sat', breakfast: 400, lunch: 700, dinner: 500, snacks: 400, target: 2000 },
    { day: 'Sun', breakfast: 350, lunch: 650, dinner: 550, snacks: 300, target: 2000 },
  ],
  macros: [
    { day: 'Mon', protein: 120, carbs: 240, fats: 65 },
    { day: 'Tue', protein: 115, carbs: 220, fats: 70 },
    { day: 'Wed', protein: 125, carbs: 250, fats: 60 },
    { day: 'Thu', protein: 118, carbs: 235, fats: 68 },
    { day: 'Fri', protein: 122, carbs: 245, fats: 63 },
    { day: 'Sat', protein: 130, carbs: 260, fats: 70 },
    { day: 'Sun', protein: 116, carbs: 230, fats: 65 },
  ]
};

export function WeeklyOverview() {
  const [view, setView] = useState<'calories' | 'macros'>('calories');

  // Calculate the maximum value for scaling
  const getMaxValue = () => {
    if (view === 'calories') {
      return Math.max(...DATA.calories.map(item =>
        item.breakfast + item.lunch + item.dinner + item.snacks
      ));
    } else {
      return Math.max(
        ...DATA.macros.map(item => item.protein),
        ...DATA.macros.map(item => item.carbs),
        ...DATA.macros.map(item => item.fats)
      );
    }
  };

  const maxValue = getMaxValue();

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Weekly Overview</CardTitle>
            <CardDescription>Track your nutrition progress</CardDescription>
          </div>
          <Select
            value={view}
            onValueChange={(value: 'calories' | 'macros') => setView(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select view" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="calories">Calories</SelectItem>
              <SelectItem value="macros">Macronutrients</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <div className="h-[400px] w-full">
          {view === 'calories' ? (
            <div className="flex h-full items-end space-x-2">
              {DATA.calories.map((item) => (
                <div key={item.day} className="flex flex-col items-center flex-1">
                  <div className="w-full flex flex-col-reverse">
                    <div
                      className="w-full bg-orange-500 rounded-t-sm"
                      style={{ height: `${(item.breakfast / maxValue) * 300}px` }}
                      title={`Breakfast: ${item.breakfast} kcal`}
                    />
                    <div
                      className="w-full bg-cyan-600 rounded-t-sm"
                      style={{ height: `${(item.lunch / maxValue) * 300}px` }}
                      title={`Lunch: ${item.lunch} kcal`}
                    />
                    <div
                      className="w-full bg-cyan-800 rounded-t-sm"
                      style={{ height: `${(item.dinner / maxValue) * 300}px` }}
                      title={`Dinner: ${item.dinner} kcal`}
                    />
                    <div
                      className="w-full bg-amber-500 rounded-t-sm"
                      style={{ height: `${(item.snacks / maxValue) * 300}px` }}
                      title={`Snacks: ${item.snacks} kcal`}
                    />
                  </div>
                  <div className="text-xs mt-2">{item.day}</div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex h-full items-end space-x-2">
              {DATA.macros.map((item) => (
                <div key={item.day} className="flex flex-col items-center flex-1">
                  <div className="w-full flex space-x-1">
                    <div
                      className="flex-1 bg-orange-500 rounded-t-sm"
                      style={{ height: `${(item.protein / maxValue) * 300}px` }}
                      title={`Protein: ${item.protein}g`}
                    />
                    <div
                      className="flex-1 bg-cyan-600 rounded-t-sm"
                      style={{ height: `${(item.carbs / maxValue) * 300}px` }}
                      title={`Carbs: ${item.carbs}g`}
                    />
                    <div
                      className="flex-1 bg-cyan-800 rounded-t-sm"
                      style={{ height: `${(item.fats / maxValue) * 300}px` }}
                      title={`Fats: ${item.fats}g`}
                    />
                  </div>
                  <div className="text-xs mt-2">{item.day}</div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Legend */}
        <div className="flex justify-center mt-4 space-x-4">
          {view === 'calories' ? (
            <>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                <span className="text-sm">Breakfast</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-cyan-600 rounded-full mr-2"></div>
                <span className="text-sm">Lunch</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-cyan-800 rounded-full mr-2"></div>
                <span className="text-sm">Dinner</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-amber-500 rounded-full mr-2"></div>
                <span className="text-sm">Snacks</span>
              </div>
            </>
          ) : (
            <>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                <span className="text-sm">Protein</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-cyan-600 rounded-full mr-2"></div>
                <span className="text-sm">Carbs</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-cyan-800 rounded-full mr-2"></div>
                <span className="text-sm">Fats</span>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
