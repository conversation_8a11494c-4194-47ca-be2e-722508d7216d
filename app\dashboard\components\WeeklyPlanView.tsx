'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { useMealPlanner } from '@/app/context/MealPlannerContext';
import { WeeklyPlanSkeleton } from './WeeklyPlanSkeleton';
import { toast } from 'sonner';
import { DndContext, DragEndEvent, closestCenter } from '@dnd-kit/core';
import { SortableContext, arrayMove, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface Meal {
  id: string;
  name: string;
  time: string;
  calories: number;
  type: string;
}

interface DayPlan {
  id: string;
  date: string;
  meals: Meal[];
}

export function WeeklyPlanView() {
  const { weeklyPlan, isLoading, error, updateMealPlan } = useMealPlanner();
  
  if (isLoading) return <WeeklyPlanSkeleton />;
  if (error) return <div className="text-red-500">{error}</div>;

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    const oldIndex = weeklyPlan.findIndex(day => day.id === active.id);
    const newIndex = weeklyPlan.findIndex(day => day.id === over.id);
    
    const newPlan = arrayMove(weeklyPlan, oldIndex, newIndex);
    updateMealPlan(newPlan);
  };

  return (
    <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
      <SortableContext items={weeklyPlan} strategy={verticalListSortingStrategy}>
        <div className="space-y-4">
          {weeklyPlan.map((day) => (
            <DayCard key={day.id} day={day} />
          ))}
        </div>
      </SortableContext>
    </DndContext>
  );
}

function DayCard({ day }: { day: DayPlan }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{new Date(day.date).toLocaleDateString('en-US', { weekday: 'long' })}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {day.meals.map((meal) => (
            <div key={meal.id} className="p-2 border rounded">
              <h3 className="font-medium">{meal.name}</h3>
              <p className="text-sm text-gray-500">{meal.time} - {meal.calories} cal</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

