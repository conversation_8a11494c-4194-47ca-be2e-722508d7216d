// Meal Plan Utilities and Types
export interface MealPlan {
  id: string;
  user_id: string;
  start_date: string;
  end_date: string;
  total_cost: number;
  meal_data: any;
  status: 'draft' | 'active' | 'completed' | 'archived';
  created_at: Date;
  updated_at: Date;
}

export interface Meal {
  id: string;
  name: string;
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  recipe_id?: string;
  servings: number;
  prep_time?: number;
  cook_time?: number;
  calories?: number;
  cost?: number;
  image_url?: string;
  ingredients?: Ingredient[];
  instructions?: string[];
  nutrition?: NutritionInfo;
  tags?: string[];
}

export interface Ingredient {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  category?: string;
  cost?: number;
}

export interface NutritionInfo {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
  sugar?: number;
  sodium?: number;
}

export interface DayMeals {
  date: string;
  meals: Meal[];
  total_calories: number;
  total_cost: number;
}

export interface WeeklyMealPlan {
  week_start: string;
  week_end: string;
  days: DayMeals[];
  total_cost: number;
  total_calories: number;
}

// Meal types
export const MEAL_TYPES = ['breakfast', 'lunch', 'dinner', 'snack'] as const;
export type MealType = typeof MEAL_TYPES[number];

// Utility functions
export function calculateMealPlanDuration(startDate: string, endDate: string): number {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
}

export function calculateTotalCost(meals: Meal[]): number {
  return meals.reduce((total, meal) => total + (meal.cost || 0), 0);
}

export function calculateTotalCalories(meals: Meal[]): number {
  return meals.reduce((total, meal) => total + (meal.calories || 0), 0);
}

export function calculateTotalNutrition(meals: Meal[]): NutritionInfo {
  return meals.reduce(
    (total, meal) => {
      if (meal.nutrition) {
        total.calories += meal.nutrition.calories;
        total.protein += meal.nutrition.protein;
        total.carbs += meal.nutrition.carbs;
        total.fat += meal.nutrition.fat;
        total.fiber += meal.nutrition.fiber || 0;
        total.sugar += meal.nutrition.sugar || 0;
        total.sodium += meal.nutrition.sodium || 0;
      }
      return total;
    },
    {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0,
      fiber: 0,
      sugar: 0,
      sodium: 0,
    }
  );
}

export function groupMealsByType(meals: Meal[]): Record<MealType, Meal[]> {
  const grouped = MEAL_TYPES.reduce((acc, type) => {
    acc[type] = [];
    return acc;
  }, {} as Record<MealType, Meal[]>);

  meals.forEach(meal => {
    if (MEAL_TYPES.includes(meal.type)) {
      grouped[meal.type].push(meal);
    }
  });

  return grouped;
}

export function getMealsByDay(mealPlan: MealPlan): DayMeals[] {
  if (!mealPlan.meal_data || !mealPlan.meal_data.week) {
    return [];
  }

  return mealPlan.meal_data.week.map((day: any) => ({
    date: day.date,
    meals: day.meals || [],
    total_calories: calculateTotalCalories(day.meals || []),
    total_cost: calculateTotalCost(day.meals || []),
  }));
}

export function formatMealType(type: MealType): string {
  return type.charAt(0).toUpperCase() + type.slice(1);
}

export function formatCookingTime(prepTime?: number, cookTime?: number): string {
  const total = (prepTime || 0) + (cookTime || 0);
  if (total === 0) return 'Unknown';
  
  if (total < 60) {
    return `${total} min`;
  }
  
  const hours = Math.floor(total / 60);
  const minutes = total % 60;
  
  if (minutes === 0) {
    return `${hours}h`;
  }
  
  return `${hours}h ${minutes}m`;
}

export function scaleMealServings(meal: Meal, newServings: number): Meal {
  const scaleFactor = newServings / meal.servings;
  
  return {
    ...meal,
    servings: newServings,
    cost: meal.cost ? meal.cost * scaleFactor : undefined,
    calories: meal.calories ? Math.round(meal.calories * scaleFactor) : undefined,
    ingredients: meal.ingredients?.map(ingredient => ({
      ...ingredient,
      quantity: ingredient.quantity * scaleFactor,
      cost: ingredient.cost ? ingredient.cost * scaleFactor : undefined,
    })),
    nutrition: meal.nutrition ? {
      calories: Math.round(meal.nutrition.calories * scaleFactor),
      protein: Math.round(meal.nutrition.protein * scaleFactor),
      carbs: Math.round(meal.nutrition.carbs * scaleFactor),
      fat: Math.round(meal.nutrition.fat * scaleFactor),
      fiber: meal.nutrition.fiber ? Math.round(meal.nutrition.fiber * scaleFactor) : undefined,
      sugar: meal.nutrition.sugar ? Math.round(meal.nutrition.sugar * scaleFactor) : undefined,
      sodium: meal.nutrition.sodium ? Math.round(meal.nutrition.sodium * scaleFactor) : undefined,
    } : undefined,
  };
}

export function generateShoppingListFromMealPlan(mealPlan: MealPlan): Ingredient[] {
  const allIngredients: Ingredient[] = [];
  const days = getMealsByDay(mealPlan);
  
  days.forEach(day => {
    day.meals.forEach(meal => {
      if (meal.ingredients) {
        allIngredients.push(...meal.ingredients);
      }
    });
  });
  
  // Group and combine similar ingredients
  const ingredientMap = new Map<string, Ingredient>();
  
  allIngredients.forEach(ingredient => {
    const key = `${ingredient.name.toLowerCase()}-${ingredient.unit}`;
    const existing = ingredientMap.get(key);
    
    if (existing) {
      existing.quantity += ingredient.quantity;
      existing.cost = (existing.cost || 0) + (ingredient.cost || 0);
    } else {
      ingredientMap.set(key, { ...ingredient });
    }
  });
  
  return Array.from(ingredientMap.values());
}

export function validateMealPlan(mealPlan: Partial<MealPlan>): string[] {
  const errors: string[] = [];
  
  if (!mealPlan.start_date) {
    errors.push('Start date is required');
  }
  
  if (!mealPlan.end_date) {
    errors.push('End date is required');
  }
  
  if (mealPlan.start_date && mealPlan.end_date) {
    const startDate = new Date(mealPlan.start_date);
    const endDate = new Date(mealPlan.end_date);
    
    if (startDate > endDate) {
      errors.push('Start date must be before end date');
    }
  }
  
  if (!mealPlan.meal_data) {
    errors.push('Meal data is required');
  }
  
  return errors;
}

export function isMealPlanActive(mealPlan: MealPlan): boolean {
  const today = new Date();
  const startDate = new Date(mealPlan.start_date);
  const endDate = new Date(mealPlan.end_date);
  
  return mealPlan.status === 'active' && today >= startDate && today <= endDate;
}

export function getMealPlanProgress(mealPlan: MealPlan): number {
  const today = new Date();
  const startDate = new Date(mealPlan.start_date);
  const endDate = new Date(mealPlan.end_date);
  
  if (today < startDate) return 0;
  if (today > endDate) return 100;
  
  const totalDays = calculateMealPlanDuration(mealPlan.start_date, mealPlan.end_date);
  const daysPassed = Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  
  return Math.round((daysPassed / totalDays) * 100);
}
