"use client";

import { create } from 'zustand';
import { Meal, MealPlan } from '@/types/meal-plan';

interface MealPlanState {
  mealPlan: MealPlan | null;
  isLoading: boolean;
  setMealPlan: (mealPlan: MealPlan) => void;
  setIsLoading: (isLoading: boolean) => void;
  addMeal: (day: string, mealType: string, meal: Meal) => void;
  removeMeal: (day: string, mealType: string) => void;
  setMealStatus: (day: string, mealType: string, status: 'cooked' | 'skipped' | null) => void;
  moveMeal: (sourceMeal: { day: string, mealType: string }, targetMeal: { day: string, mealType: string }) => void;
}

export const useMealPlanStore = create<MealPlanState>((set) => ({
  mealPlan: null,
  isLoading: true,
  setMealPlan: (mealPlan) => set({ mealPlan }),
  setIsLoading: (isLoading) => set({ isLoading }),
  addMeal: (day, mealType, meal) => {
    set((state) => {
      const newMealPlan = { ...state.mealPlan } || {};
      
      if (!newMealPlan[day]) {
        newMealPlan[day] = {};
      }
      
      newMealPlan[day][mealType] = meal;
      
      return { mealPlan: newMealPlan };
    });
  },
  removeMeal: (day, mealType) => {
    set((state) => {
      if (!state.mealPlan || !state.mealPlan[day]) {
        return state;
      }
      
      const newMealPlan = { ...state.mealPlan };
      const newDay = { ...newMealPlan[day] };
      
      delete newDay[mealType];
      newMealPlan[day] = newDay;
      
      // If the day has no meals left, remove the day
      if (Object.keys(newDay).length === 0) {
        delete newMealPlan[day];
      }
      
      return { mealPlan: newMealPlan };
    });
  },
  setMealStatus: (day, mealType, status) => {
    set((state) => {
      if (!state.mealPlan || !state.mealPlan[day] || !state.mealPlan[day][mealType]) {
        return state;
      }
      
      const newMealPlan = { ...state.mealPlan };
      const meal = { ...newMealPlan[day][mealType] };
      
      meal.status = status;
      newMealPlan[day][mealType] = meal;
      
      return { mealPlan: newMealPlan };
    });
  },
  moveMeal: (sourceMeal, targetMeal) => {
    set((state) => {
      if (!state.mealPlan || !state.mealPlan[sourceMeal.day] || !state.mealPlan[sourceMeal.day][sourceMeal.mealType]) {
        return state;
      }
      
      const newMealPlan = { ...state.mealPlan };
      const meal = { ...newMealPlan[sourceMeal.day][sourceMeal.mealType] };
      
      // Add to target
      if (!newMealPlan[targetMeal.day]) {
        newMealPlan[targetMeal.day] = {};
      }
      newMealPlan[targetMeal.day][targetMeal.mealType] = meal;
      
      // Remove from source
      delete newMealPlan[sourceMeal.day][sourceMeal.mealType];
      
      // If the source day has no meals left, remove the day
      if (Object.keys(newMealPlan[sourceMeal.day]).length === 0) {
        delete newMealPlan[sourceMeal.day];
      }
      
      return { mealPlan: newMealPlan };
    });
  },
}));