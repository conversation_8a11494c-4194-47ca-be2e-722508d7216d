"use client";

import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { useSupabase } from "@/components/supabase-provider";
import { AnimatePresence } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";

import { UserDropdownMenu } from "@/components/dashboard/user-dropdown-menu";
import { MealCard } from "@/components/dashboard/meal-card";
import { StatsCard } from "@/components/dashboard/stats-card";
import { WeeklyChart } from "@/components/dashboard/weekly-chart";
import { NutritionChart } from "@/components/dashboard/nutrition-chart";
import { MealProgress } from "@/components/dashboard/meal-progress";
import { useUpcomingMeals, useDashboardStats } from "@/app/hooks/useDashboardData";

import { ShoppingEssentials } from "./components/ShoppingEssentials";
import { PlanSummary } from "./components/PlanSummary";
import { MealCompletionReminder } from "./components/MealCompletionReminder";

import {
  ChefHat,
  ShoppingCart,
  Calendar,
  TrendingUp,
  Plus,
} from "lucide-react";

// Fallback data in case the API fails
const fallbackData = {
  upcomingMeals: [
    {
      id: '1',
      name: "Grilled Chicken Salad",
      time: "Today, 1:00 PM",
      calories: 450,
      image: "https://images.unsplash.com/photo-1527477396000-e27163b481c2"
    },
    {
      id: '2',
      name: "Quinoa Buddha Bowl",
      time: "Today, 7:00 PM",
      calories: 380,
      image: "https://images.unsplash.com/photo-1546069901-ba9599a7e63c"
    },
    {
      id: '3',
      name: "Smoothie Bowl",
      time: "Tomorrow, 8:00 AM",
      calories: 320,
      image: "https://images.unsplash.com/photo-1553530666-ba11a90a0868"
    }
  ]
};

export default function DashboardPage() {
  const router = useRouter();
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const [isPageLoading, setIsPageLoading] = useState(true);

  // Fetch upcoming meals using the custom hook
  const { data: upcomingMeals, isLoading: isUpcomingMealsLoading, isError: isUpcomingMealsError } = useUpcomingMeals();

  // Fetch dashboard stats using the custom hook
  const { data: dashboardStats, isLoading: isStatsLoading, isError: isStatsError } = useDashboardStats();

  useEffect(() => {
    // Only set page as loaded when Supabase is ready
    if (isSupabaseLoading) {
      return;
    }

    // Simulate loading for demo purposes
    const timer = setTimeout(() => {
      setIsPageLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [isSupabaseLoading]);

  return (
    <div className="min-h-screen bg-background">

      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <AnimatePresence>
          {!isPageLoading && (
            <>
              {/* Top Section: Today's Meals, Nutrition Chart, and Meal Progress */}
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
                {/* Today's Meals (50% width) */}
                <div className="lg:col-span-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Today's Meals</CardTitle>
                      <CardDescription>Your next scheduled meals</CardDescription>
                    </CardHeader>
                    <CardContent>
                      {isUpcomingMealsLoading ? (
                        <div className="grid grid-cols-1 gap-4">
                          {[1, 2, 3].map((i) => (
                            <div key={i} className="rounded-lg overflow-hidden border bg-card text-card-foreground shadow">
                              <Skeleton className="h-48 w-full" />
                              <div className="p-4 space-y-2">
                                <Skeleton className="h-5 w-3/4" />
                                <Skeleton className="h-4 w-1/2" />
                                <Skeleton className="h-4 w-1/4" />
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : isUpcomingMealsError ? (
                        <div className="text-center py-8">
                          <p className="text-muted-foreground">Failed to load upcoming meals</p>
                          <div className="grid grid-cols-1 gap-4 mt-4">
                            {fallbackData.upcomingMeals.map((meal) => (
                              <MealCard key={meal.id} {...meal} />
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div className="grid grid-cols-1 gap-4">
                          {upcomingMeals && upcomingMeals.length > 0 ? (
                            upcomingMeals.map((meal) => (
                              <MealCard key={meal.id} {...meal} />
                            ))
                          ) : (
                            <div className="text-center py-8">
                              <p className="text-muted-foreground">No upcoming meals found</p>
                              <Button
                                variant="outline"
                                className="mt-4"
                                onClick={() => router.push('/meal-plan/combined')}
                              >
                                Create a Meal Plan
                              </Button>
                            </div>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>

                {/* Nutrition Chart (25% width) */}
                <div className="lg:col-span-1">
                  <NutritionChart />
                </div>

                {/* Meal Completion Reminder (25% width) */}
                <div className="lg:col-span-1">
                  <MealCompletionReminder />
                </div>
              </div>

              {/* Middle Section: Weekly Overview, Shopping Essentials, and Plan Summary */}
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
                {/* Weekly Overview (75% width) */}
                <div className="lg:col-span-3">
                  <WeeklyChart />
                </div>

                {/* Right Column (25% width) */}
                <div className="lg:col-span-1 space-y-6">
                  {/* Shopping Essentials */}
                  <ShoppingEssentials isLoading={isPageLoading} />

                  {/* Plan Summary */}
                  <PlanSummary isLoading={isPageLoading} />
                </div>
              </div>

              {/* Stats Cards at the bottom */}
              {isStatsLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="rounded-lg border bg-card text-card-foreground shadow p-6">
                      <div className="flex items-center space-x-4">
                        <Skeleton className="h-10 w-10 rounded-full" />
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-6 w-16" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : isStatsError ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                  <StatsCard
                    href="/recipes"
                    icon={ChefHat}
                    label="Total Recipes"
                    value={248}
                  />
                  <StatsCard
                    href="/shopping-list"
                    icon={ShoppingCart}
                    label="Shopping Lists"
                    value={12}
                  />
                  <StatsCard
                    href="/meal-plan/combined"
                    icon={Calendar}
                    label="Meal Plans"
                    value={36}
                  />
                  <StatsCard
                    href="/statistics"
                    icon={TrendingUp}
                    label="Calories Tracked"
                    value={1842}
                  />
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                  <StatsCard
                    href="/recipes"
                    icon={ChefHat}
                    label="Total Recipes"
                    value={dashboardStats?.totalRecipes || 0}
                  />
                  <StatsCard
                    href="/shopping-list"
                    icon={ShoppingCart}
                    label="Shopping Lists"
                    value={dashboardStats?.shoppingLists || 0}
                  />
                  <StatsCard
                    href="/meal-plan/combined"
                    icon={Calendar}
                    label="Meal Plans"
                    value={dashboardStats?.mealPlans || 0}
                  />
                  <StatsCard
                    href="/statistics"
                    icon={TrendingUp}
                    label="Calories Tracked"
                    value={dashboardStats?.caloriesTracked || 0}
                  />
                </div>
              )}
            </>
          )}
        </AnimatePresence>
      </main>
    </div>
  );
}

