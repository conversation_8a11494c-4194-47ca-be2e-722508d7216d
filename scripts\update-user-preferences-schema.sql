-- Update Supabase Schema for User Preferences

-- First, check if the user_preferences table exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'user_preferences') THEN
        -- Create the user_preferences table if it doesn't exist
        CREATE TABLE public.user_preferences (
            id SERIAL PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id) NOT NULL,
            dietary_preference TEXT,
            allergens TEXT[] DEFAULT '{}'::TEXT[],
            custom_exclusions TEXT[] DEFAULT '{}'::TEXT[],
            age_groups TEXT[] DEFAULT '{}'::TEXT[],
            budget_type TEXT,
            budget_flexibility TEXT,
            meal_frequency TEXT,
            household_members JSONB DEFAULT '[]'::JSON<PERSON>,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );

        -- Add RLS policies
        ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
        
        -- Create policy to allow users to see only their own preferences
        CREATE POLICY "Users can view their own preferences" 
            ON public.user_preferences 
            FOR SELECT 
            USING (auth.uid() = user_id);
            
        -- Create policy to allow users to insert their own preferences
        CREATE POLICY "Users can insert their own preferences" 
            ON public.user_preferences 
            FOR INSERT 
            WITH CHECK (auth.uid() = user_id);
            
        -- Create policy to allow users to update their own preferences
        CREATE POLICY "Users can update their own preferences" 
            ON public.user_preferences 
            FOR UPDATE 
            USING (auth.uid() = user_id);
            
        -- Create policy to allow users to delete their own preferences
        CREATE POLICY "Users can delete their own preferences" 
            ON public.user_preferences 
            FOR DELETE 
            USING (auth.uid() = user_id);
    ELSE
        -- If the table exists, add any missing columns
        
        -- Check and add dietary_preference column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'user_preferences' AND column_name = 'dietary_preference') THEN
            ALTER TABLE public.user_preferences ADD COLUMN dietary_preference TEXT;
        END IF;
        
        -- Check and add allergens column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'user_preferences' AND column_name = 'allergens') THEN
            ALTER TABLE public.user_preferences ADD COLUMN allergens TEXT[] DEFAULT '{}'::TEXT[];
        END IF;
        
        -- Check and add custom_exclusions column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'user_preferences' AND column_name = 'custom_exclusions') THEN
            ALTER TABLE public.user_preferences ADD COLUMN custom_exclusions TEXT[] DEFAULT '{}'::TEXT[];
        END IF;
        
        -- Check and add age_groups column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'user_preferences' AND column_name = 'age_groups') THEN
            ALTER TABLE public.user_preferences ADD COLUMN age_groups TEXT[] DEFAULT '{}'::TEXT[];
        END IF;
        
        -- Check and add budget_type column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'user_preferences' AND column_name = 'budget_type') THEN
            ALTER TABLE public.user_preferences ADD COLUMN budget_type TEXT;
        END IF;
        
        -- Check and add budget_flexibility column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'user_preferences' AND column_name = 'budget_flexibility') THEN
            ALTER TABLE public.user_preferences ADD COLUMN budget_flexibility TEXT;
        END IF;
        
        -- Check and add meal_frequency column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'user_preferences' AND column_name = 'meal_frequency') THEN
            ALTER TABLE public.user_preferences ADD COLUMN meal_frequency TEXT;
        END IF;
        
        -- Check and add household_members column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' AND table_name = 'user_preferences' AND column_name = 'household_members') THEN
            ALTER TABLE public.user_preferences ADD COLUMN household_members JSONB DEFAULT '[]'::JSONB;
        END IF;
    END IF;
END
$$;

-- Update the users table with additional fields if needed
DO $$
BEGIN
    -- Check and add full_name column if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.columns 
                  WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'full_name') THEN
        ALTER TABLE public.users ADD COLUMN full_name TEXT;
    END IF;
    
    -- Check and add bio column if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.columns 
                  WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'bio') THEN
        ALTER TABLE public.users ADD COLUMN bio TEXT;
    END IF;
    
    -- Check and add height column if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.columns 
                  WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'height') THEN
        ALTER TABLE public.users ADD COLUMN height DECIMAL(5, 2);
    END IF;
    
    -- Check and add weight column if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.columns 
                  WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'weight') THEN
        ALTER TABLE public.users ADD COLUMN weight DECIMAL(5, 2);
    END IF;
    
    -- Check and add age column if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.columns 
                  WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'age') THEN
        ALTER TABLE public.users ADD COLUMN age INTEGER;
    END IF;
    
    -- Check and add dietary_restrictions column if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.columns 
                  WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'dietary_restrictions') THEN
        ALTER TABLE public.users ADD COLUMN dietary_restrictions TEXT[] DEFAULT '{}'::TEXT[];
    END IF;
    
    -- Check and add weekly_budget column if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.columns 
                  WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'weekly_budget') THEN
        ALTER TABLE public.users ADD COLUMN weekly_budget DECIMAL(10, 2) DEFAULT 0;
    END IF;
    
    -- Check and add household_size column if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.columns 
                  WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'household_size') THEN
        ALTER TABLE public.users ADD COLUMN household_size INTEGER DEFAULT 1;
    END IF;
END
$$;

-- Add indexes for better performance
DO $$
BEGIN
    -- Add index on user_id for user_preferences
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_user_preferences_user_id') THEN
        CREATE INDEX idx_user_preferences_user_id ON public.user_preferences(user_id);
    END IF;
END
$$;
