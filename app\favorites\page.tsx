"use client";

import { useState } from 'react';
import { Star, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { FavoriteMeals } from '@/components/meal-plan/FavoriteMeals';
import Link from 'next/link';

export default function FavoritesPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Link href="/dashboard">
            <Button variant="ghost" size="icon" className="mr-2">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold flex items-center">
            <Star className="h-5 w-5 fill-yellow-400 text-yellow-400 mr-2" />
            My Favorite Meals
          </h1>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border p-6">
        <FavoriteMeals />
      </div>
    </div>
  );
}
