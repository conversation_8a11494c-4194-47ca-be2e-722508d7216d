# Meal Plan Generation Implementation Checklist

**Epic:** Core Meal Plan Generation
**Description:** The core functionality allowing users to generate customized 7-day meal plans based on their preferences using hybrid AI and recipe database approaches.
**Current Status (Codebase Audit):**
* [x] Frontend: Meal plan generation form and UI components exist
* [x] Frontend: Three generation approaches implemented (EdaSpoon, AI, Hybrid)
* [x] Backend: OpenAI integration functional (lib/meal-plan-generators/)
* [x] Backend: Edamam API integration implemented with fallbacks
* [x] Backend: Spoonacular API integration for cost data
* [/] Integration: Meal plan storage in Supabase partially working
* [/] Gaps/Incomplete: Error handling, fallback mechanisms, performance optimization

---

## **Overall Completion Status:** [/] In Progress

---

## **Detailed Implementation Tasks:**

### **1. Core Logic & Data Flow**
* **Backend - API Endpoints:**
    * [x] Design/Implement `/api/meal-planner` for meal plan generation (POST `/api/meal-planner`)
    * [x] Design/Implement `/api/edaspoon` for Eda<PERSON>poon approach (POST `/api/edaspoon`)
    * [x] Design/Implement `/api/openai` for AI approach (POST `/api/openai`)
    * [x] Design/Implement `/api/hybrid` for Hybrid approach (POST `/api/hybrid`)
    * [/] Implement input validation for meal generation parameters
    * [x] Implement authentication/authorization middleware for generation endpoints
    * [/] Ensure proper error handling and standardized error responses
    * [ ] Implement `/api/meal-plans/preview` for plan preview before saving
    * [ ] Implement `/api/meal-plans/regenerate` for plan regeneration

* **Backend - Service Logic:**
    * [x] Implement `generateMealPlanWithEdaSpoon` in EdaSpoon generator (lib/meal-plan-generators/edaspoon-generator.ts)
    * [x] Implement `generateMealPlanWithAI` in AI generator (lib/meal-plan-generators/ai-generator.ts)
    * [x] Implement `generateMealPlanWithHybrid` in Hybrid generator (lib/meal-plan-generators/hybrid-generator.ts)
    * [/] Integrate with Edamam API for recipe data and nutrition
    * [/] Integrate with Spoonacular API for ingredient costs and pricing
    * [/] Integrate with OpenAI API for intelligent meal plan generation
    * [/] Handle external API rate limits and errors with proper fallbacks
    * [x] Implement business logic for budget adherence and dietary restrictions
    * [/] Implement business logic for repetition rules (max 2-3 identical recipes per week)
    * [ ] Ensure data consistency and integrity for generated meal plans
    * [ ] Implement meal plan validation before storage

* **Database Interactions:**
    * [x] CRUD operations for `meal_plans` table implemented
    * [/] CRUD operations for `recipes` table (partially implemented)
    * [x] Apply Row-Level Security (RLS) policies for meal plan access
    * [ ] Optimize queries for meal plan generation and storage
    * [ ] Implement efficient recipe search and matching queries
    * [ ] Create indexes for meal plan generation performance

### **2. Frontend Integration & UI/UX**
* **Pages/Routes:**
    * [x] Create/Update `/meal-plan/generate` page (app/meal-plan/generate/)
    * [x] Create/Update `/meal-plan/new` page (app/meal-plan/new/)
    * [/] Implement dynamic routing for meal plan viewing

* **Components:**
    * [x] Develop/Refine `MealPlanGenerator` component (app/components/meal-planner.tsx)
    * [/] Develop/Refine `MealPlanPreview` component for plan preview
    * [/] Develop/Refine `GenerationOptionsForm` component for user preferences
    * [x] Develop/Refine `MealCard` component for individual meal display
    * [ ] Implement responsive design for meal plan generation interface
    * [ ] Ensure accessibility standards for generation forms
    * [ ] Implement `GenerationProgress` component for loading states
    * [ ] Implement `ApproachSelector` component for choosing generation method

* **State Management:**
    * [/] Define `mealPlanStore` for meal plan state management (lib/stores/)
    * [x] Implement `React Query` hooks for meal plan generation (app/hooks/useMealPlan.ts)
    * [x] Handle loading, error, and success states in UI for generation process
    * [ ] Implement real-time progress updates during generation
    * [ ] Implement caching strategy for generated meal plans

* **User Interaction & Feedback:**
    * [x] Implement meal plan generation forms with validation
    * [/] Provide loading indicators for generation process (10-15 seconds)
    * [/] Display clear success/error messages for generation results
    * [ ] Implement generation progress tracking and user feedback
    * [ ] Implement plan preview and approval workflow

### **3. Cross-Cutting Concerns**
* **Authentication & Authorization:**
    * [x] Ensure user is authenticated for meal plan generation
    * [x] Handle session management during generation process
    * [ ] Implement proper error handling for authentication failures

* **Error Handling:**
    * [/] Implement client-side error boundaries for generation interface
    * [/] Display user-friendly error messages for API failures
    * [x] Implement retry mechanisms for failed external API calls
    * [x] Implement fallback to default recipes when APIs fail
    * [ ] Handle timeout scenarios for long-running generation

* **Performance Optimization:**
    * [/] Implement data caching for recipe data and nutrition information
    * [/] Optimize API calls to minimize external service usage
    * [ ] Implement background processing for complex meal plan generation
    * [ ] Optimize meal plan generation algorithms for speed
    * [ ] Implement request queuing for high-load scenarios

* **Analytics & Logging:**
    * [ ] Implement tracking for meal plan generation attempts
    * [ ] Track generation method preferences and success rates
    * [ ] Ensure generation errors are logged to analytics system
    * [ ] Track user satisfaction with generated meal plans

### **4. Testing**
* [ ] Write unit tests for meal plan generation components
* [ ] Write unit tests for generation service functions
* [ ] Write integration tests for external API integrations
* [ ] Write tests for generation error scenarios and fallbacks
* [ ] Write tests for meal plan validation and storage
* [ ] (Future) Plan E2E tests for complete generation workflow

---

## **Dependencies & Notes:**
* [x] This feature depends on `User Authentication` being completed.
* [x] This feature depends on `User Profile & Preferences` for generation parameters.
* [ ] This feature depends on `Recipe Discovery` for recipe data integration.
* [ ] Important considerations: External API rate limits (Edamam: 10 calls/min, Spoonacular: 150 calls/day)
* [ ] Important considerations: Generation timeout should be 10-15 seconds maximum
* [ ] Important considerations: Fallback mechanisms are critical for reliability
* [ ] Important considerations: Cost estimation accuracy varies by region (US vs EU/Asia)

## **Current File References:**
- `lib/meal-plan-generators/` - Generation algorithm directory
- `lib/meal-plan-generators/edaspoon-generator.ts` - EdaSpoon approach
- `lib/meal-plan-generators/ai-generator.ts` - AI approach  
- `lib/meal-plan-generators/hybrid-generator.ts` - Hybrid approach
- `app/meal-plan/generate/` - Generation page directory
- `app/components/meal-planner.tsx` - Main generation component
- `app/hooks/useMealPlan.ts` - Meal plan data hooks
- `app/api/meal-planner/` - Generation API endpoints
