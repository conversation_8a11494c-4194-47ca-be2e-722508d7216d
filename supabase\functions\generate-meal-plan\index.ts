// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { corsHeaders } from "../_shared/cors.ts";

interface MealPlanGenerationParams {
  dietaryPreferences: string[];
  calorieGoal: number;
  allergies: string[];
  householdSize: number;
  weeklyBudget: number;
  cuisinePreferences?: string[];
  mealTypes?: string[];
  daysToGenerate?: number;
}

interface RequestBody {
  params: MealPlanGenerationParams;
  userId: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }
  
  try {
    const { params, userId } = await req.json() as RequestBody;
    
    // Create a Supabase client with the service role key
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      {
        global: {
          headers: { Authorization: req.headers.get("Authorization")! },
        },
      }
    );
    
    // Verify the user exists
    const { data: user, error: userError } = await supabaseClient
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: "Unauthorized" }),
        { status: 401, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }
    
    // Prepare the prompt for OpenAI
    const prompt = createMealPlanPrompt(params);
    
    // Call OpenAI API
    const openAIResponse = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${Deno.env.get("OPENAI_API_KEY")}`
      },
      body: JSON.stringify({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a professional nutritionist and meal planner. Your task is to create detailed, realistic meal plans based on user preferences. Output should be in valid JSON format only."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 4000
      })
    });
    
    if (!openAIResponse.ok) {
      const errorData = await openAIResponse.json();
      throw new Error(`OpenAI API error: ${JSON.stringify(errorData)}`);
    }
    
    const openAIData = await openAIResponse.json();
    const mealPlanJson = openAIData.choices[0].message.content;
    
    // Parse the JSON response
    let mealPlan;
    try {
      // Extract JSON if it's wrapped in markdown code blocks
      const jsonMatch = mealPlanJson.match(/```json\n([\s\S]*)\n```/) || 
                        mealPlanJson.match(/```\n([\s\S]*)\n```/);
      
      const jsonString = jsonMatch ? jsonMatch[1] : mealPlanJson;
      mealPlan = JSON.parse(jsonString);
    } catch (error) {
      console.error("Error parsing JSON:", error);
      return new Response(
        JSON.stringify({ error: "Failed to parse meal plan data" }),
        { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }
    
    // Return the meal plan
    return new Response(
      JSON.stringify({ mealPlan }),
      { status: 200, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
    
  } catch (error) {
    console.error("Error:", error.message);
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  }
});

function createMealPlanPrompt(params: MealPlanGenerationParams): string {
  const {
    dietaryPreferences,
    calorieGoal,
    allergies,
    householdSize,
    weeklyBudget,
    cuisinePreferences = [],
    mealTypes = ["Breakfast", "Lunch", "Dinner"],
    daysToGenerate = 7
  } = params;
  
  // Calculate per-person daily budget
  const dailyBudgetPerPerson = (weeklyBudget / 7) / householdSize;
  
  return `
    Create a detailed meal plan with the following requirements:
    
    - Dietary preferences: ${dietaryPreferences.join(", ")}
    - Daily calorie goal: ${calorieGoal} calories per person
    - Allergies to avoid: ${allergies.join(", ")}
    - Household size: ${householdSize} people
    - Weekly budget: $${weeklyBudget} (approximately $${dailyBudgetPerPerson.toFixed(2)} per person per day)
    ${cuisinePreferences.length > 0 ? `- Preferred cuisines: ${cuisinePreferences.join(", ")}` : ""}
    - Meal types to include: ${mealTypes.join(", ")}
    - Number of days: ${daysToGenerate}
    
    For each meal, include:
    - Name
    - Type (breakfast, lunch, dinner, etc.)
    - Nutritional information (calories, protein, carbs, fat)
    - Preparation time in minutes
    - List of ingredients with quantities
    - Brief preparation instructions
    - Tags (e.g., "quick", "high-protein", "vegetarian")
    
    Also include a summary of total nutrition for the week and estimated total cost.
    
    Format the response as a JSON object with the following structure:
    
    {
      "name": "Meal Plan Name",
      "description": "Brief description of the meal plan",
      "week": [
        {
          "date": "YYYY-MM-DD",
          "meals": [
            {
              "id": "unique-id",
              "name": "Meal Name",
              "type": "Breakfast/Lunch/Dinner",
              "calories": 500,
              "protein": 30,
              "carbs": 40,
              "fat": 15,
              "prepTime": 20,
              "ingredients": [
                {
                  "name": "Ingredient Name",
                  "quantity": "1",
                  "unit": "cup"
                }
              ],
              "instructions": "Brief preparation instructions",
              "tags": ["tag1", "tag2"]
            }
          ]
        }
      ],
      "nutritionSummary": {
        "calories": 14000,
        "protein": 700,
        "carbs": 1400,
        "fat": 500,
        "fiber": 210,
        "sugar": 300,
        "sodium": 10500
      },
      "totalPrepTime": 420,
      "totalCost": 120
    }
    
    Use realistic values for nutrition, preparation time, and cost. Make sure the meals are varied and appropriate for the dietary preferences and allergies specified.
    
    The dates should start from today and continue for ${daysToGenerate} days.
    
    Return ONLY the JSON object with no additional text.
  `;
}
