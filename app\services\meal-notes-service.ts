import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { MealNote, CreateMealNoteRequest } from '@/types/database-extended';

export class MealNotesService {
  private supabase = createClientComponentClient();

  async getMealNotes(userId: string, mealPlanId?: string): Promise<{ data: MealNote[] | null; error: string | null }> {
    try {
      let query = this.supabase
        .from('meal_notes')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (mealPlanId) {
        query = query.eq('meal_plan_id', mealPlanId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching meal notes:', error);
        return { data: null, error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Unexpected error fetching meal notes:', error);
      return { data: null, error: 'Failed to fetch meal notes' };
    }
  }

  async getMealNote(userId: string, mealPlanId: string, mealId: string): Promise<{ data: MealNote | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('meal_notes')
        .select('*')
        .eq('user_id', userId)
        .eq('meal_plan_id', mealPlanId)
        .eq('meal_id', mealId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
        console.error('Error fetching meal note:', error);
        return { data: null, error: error.message };
      }

      return { data: data || null, error: null };
    } catch (error) {
      console.error('Unexpected error fetching meal note:', error);
      return { data: null, error: 'Failed to fetch meal note' };
    }
  }

  async createMealNote(userId: string, request: CreateMealNoteRequest): Promise<{ data: MealNote | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('meal_notes')
        .insert({
          user_id: userId,
          meal_plan_id: request.meal_plan_id,
          meal_id: request.meal_id,
          note: request.note,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating meal note:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Unexpected error creating meal note:', error);
      return { data: null, error: 'Failed to create meal note' };
    }
  }

  async updateMealNote(userId: string, noteId: string, note: string): Promise<{ data: MealNote | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('meal_notes')
        .update({
          note,
          updated_at: new Date().toISOString()
        })
        .eq('id', noteId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        console.error('Error updating meal note:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Unexpected error updating meal note:', error);
      return { data: null, error: 'Failed to update meal note' };
    }
  }

  async deleteMealNote(userId: string, noteId: string): Promise<{ error: string | null }> {
    try {
      const { error } = await this.supabase
        .from('meal_notes')
        .delete()
        .eq('id', noteId)
        .eq('user_id', userId);

      if (error) {
        console.error('Error deleting meal note:', error);
        return { error: error.message };
      }

      return { error: null };
    } catch (error) {
      console.error('Unexpected error deleting meal note:', error);
      return { error: 'Failed to delete meal note' };
    }
  }

  async upsertMealNote(userId: string, request: CreateMealNoteRequest): Promise<{ data: MealNote | null; error: string | null }> {
    try {
      // First, try to find existing note
      const { data: existingNote } = await this.getMealNote(userId, request.meal_plan_id, request.meal_id);

      if (existingNote) {
        // Update existing note
        return await this.updateMealNote(userId, existingNote.id, request.note);
      } else {
        // Create new note
        return await this.createMealNote(userId, request);
      }
    } catch (error) {
      console.error('Unexpected error upserting meal note:', error);
      return { data: null, error: 'Failed to save meal note' };
    }
  }
}

export const mealNotesService = new MealNotesService();
