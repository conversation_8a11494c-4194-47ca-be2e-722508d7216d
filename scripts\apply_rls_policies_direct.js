// This script applies the RLS policies directly to your Supabase project
// Run this script with: node scripts/apply_rls_policies_direct.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase connection details
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service role key not found in environment variables.');
  console.error('Make sure you have a .env file with NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY.');
  process.exit(1);
}

// Create Supabase client with service role key (has admin privileges)
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applyRLSPolicies() {
  try {
    console.log('Applying RLS policies to Supabase...');

    // SQL statements to execute
    const sqlStatements = [
      // Create the meal_plans table if it doesn't exist
      `CREATE TABLE IF NOT EXISTS public.meal_plans (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        start_date TIMESTAMP WITH TIME ZONE NOT NULL,
        end_date TIMESTAMP WITH TIME ZONE NOT NULL,
        total_cost DECIMAL(10, 2) NOT NULL,
        meal_data JSONB NOT NULL,
        status TEXT NOT NULL DEFAULT 'active',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
      )`,

      // Add comment to the table
      `COMMENT ON TABLE public.meal_plans IS 'Stores user meal plans'`,

      // Enable Row Level Security
      `ALTER TABLE public.meal_plans ENABLE ROW LEVEL SECURITY`,

      // Drop existing policies if they exist
      `DROP POLICY IF EXISTS "Users can view their own meal plans" ON public.meal_plans`,
      `DROP POLICY IF EXISTS "Users can insert their own meal plans" ON public.meal_plans`,
      `DROP POLICY IF EXISTS "Users can update their own meal plans" ON public.meal_plans`,
      `DROP POLICY IF EXISTS "Users can delete their own meal plans" ON public.meal_plans`,

      // Create policies for meal_plans table
      `CREATE POLICY "Users can view their own meal plans"
      ON public.meal_plans
      FOR SELECT
      USING (auth.uid()::uuid = user_id)`,

      `CREATE POLICY "Users can insert their own meal plans"
      ON public.meal_plans
      FOR INSERT
      WITH CHECK (auth.uid()::uuid = user_id)`,

      `CREATE POLICY "Users can update their own meal plans"
      ON public.meal_plans
      FOR UPDATE
      USING (auth.uid()::uuid = user_id)
      WITH CHECK (auth.uid()::uuid = user_id)`,

      `CREATE POLICY "Users can delete their own meal plans"
      ON public.meal_plans
      FOR DELETE
      USING (auth.uid()::uuid = user_id)`,

      // Grant permissions to authenticated users
      `GRANT ALL ON public.meal_plans TO authenticated`
    ];

    // Execute each SQL statement
    for (let i = 0; i < sqlStatements.length; i++) {
      const stmt = sqlStatements[i];
      console.log(`Executing statement ${i + 1}/${sqlStatements.length}...`);
      
      try {
        const { data, error } = await supabase.rpc('_', {}, {
          headers: {
            'Content-Type': 'application/json',
            'Prefer': 'params=single-object',
            'X-Raw-SQL': stmt
          }
        });

        if (error) {
          console.warn(`Warning: Statement ${i + 1} error:`, error.message);
          // Continue with other statements even if one fails
        }
      } catch (stmtError) {
        console.warn(`Warning: Statement ${i + 1} exception:`, stmtError.message);
        // Continue with other statements even if one fails
      }
    }

    console.log('SQL execution completed!');
    console.log('\nRLS policies should now be applied. Please check your Supabase dashboard to verify.');
    console.log('Go to: Table Editor > meal_plans > Policies');

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

applyRLSPolicies();
