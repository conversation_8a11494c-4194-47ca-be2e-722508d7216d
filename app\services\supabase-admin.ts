'use server';

import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

/**
 * Server-side function to check RLS policies for a specific table
 * This should be called from a server action or API route
 */
export async function checkRlsPolicies(tableName: string) {
  try {
    // Create a Supabase admin client with service role
    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!, // Service role key - only available server-side
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Query to get all policies for the specified table
    const { data, error } = await supabase
      .from('pg_policies')
      .select('*')
      .eq('tablename', tableName);

    if (error) {
      console.error('Error checking RLS policies:', error);
      return { success: false, error: error.message, policies: null };
    }

    // Query to get the data type of the user_id column
    const { data: columnData, error: columnError } = await supabase
      .from('information_schema.columns')
      .select('data_type')
      .eq('table_schema', 'public')
      .eq('table_name', tableName)
      .eq('column_name', 'user_id');

    if (columnError) {
      console.error('Error checking column data type:', columnError);
      return { 
        success: true, 
        policies: data,
        columnType: 'Error fetching column type'
      };
    }

    return { 
      success: true, 
      policies: data,
      columnType: columnData && columnData.length > 0 ? columnData[0].data_type : 'Column not found'
    };
  } catch (error) {
    console.error('Error in checkRlsPolicies:', error);
    return { success: false, error: String(error), policies: null };
  }
}

/**
 * Server-side function to fix RLS policies for the meal_plans table
 * This should be called from a server action or API route
 */
export async function fixMealPlansRlsPolicies() {
  try {
    // Create a Supabase admin client with service role
    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!, // Service role key - only available server-side
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Execute the migration script
    const { error } = await supabase.rpc('exec_sql', {
      sql_string: `
        -- Drop existing policies
        DROP POLICY IF EXISTS "Users can view their own meal plans" ON meal_plans;
        DROP POLICY IF EXISTS "Users can insert their own meal plans" ON meal_plans;
        DROP POLICY IF EXISTS "Users can update their own meal plans" ON meal_plans;
        DROP POLICY IF EXISTS "Users can delete their own meal plans" ON meal_plans;
        DROP POLICY IF EXISTS "Users can view their own meal_plans" ON meal_plans;
        DROP POLICY IF EXISTS "Users can insert their own meal_plans" ON meal_plans;
        DROP POLICY IF EXISTS "Users can update their own meal_plans" ON meal_plans;
        DROP POLICY IF EXISTS "Users can delete their own meal_plans" ON meal_plans;

        -- Enable RLS on meal_plans table
        ALTER TABLE meal_plans ENABLE ROW LEVEL SECURITY;

        -- Create policies for meal_plans table with proper type casting
        CREATE POLICY "Users can view their own meal plans"
        ON meal_plans
        FOR SELECT
        USING (user_id::text = auth.uid()::text);

        CREATE POLICY "Users can insert their own meal plans"
        ON meal_plans
        FOR INSERT
        WITH CHECK (user_id::text = auth.uid()::text);

        CREATE POLICY "Users can update their own meal plans"
        ON meal_plans
        FOR UPDATE
        USING (user_id::text = auth.uid()::text);

        CREATE POLICY "Users can delete their own meal plans"
        ON meal_plans
        FOR DELETE
        USING (user_id::text = auth.uid()::text);

        -- Grant permissions to authenticated users
        GRANT ALL ON meal_plans TO authenticated;
      `
    });

    if (error) {
      console.error('Error fixing RLS policies:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in fixMealPlansRlsPolicies:', error);
    return { success: false, error: String(error) };
  }
}
