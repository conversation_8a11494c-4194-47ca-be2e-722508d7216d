import { Client } from 'pg';

async function testConnection() {
  const client = new Client({
    host: process.env.DB_HOST,
    port: 5432,
    database: 'postgres',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    await client.connect();
    console.log('Successfully connected to the database!');
    
    const result = await client.query('SELECT current_database() as db_name');
    console.log('Connected to database:', result.rows[0].db_name);
    
    await client.end();
  } catch (err) {
    console.error('Connection error:', err);
  }
}

testConnection();