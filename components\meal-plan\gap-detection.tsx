'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Calendar, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

interface GapDetectionProps {
  mealDates: string[];
  startDate: string;
  endDate: string;
  onAddMeal?: (date: string) => void;
  className?: string;
}

export function GapDetection({
  mealDates,
  startDate,
  endDate,
  onAddMeal,
  className
}: GapDetectionProps) {
  const [gapDates, setGapDates] = useState<string[]>([]);
  
  useEffect(() => {
    // Find dates in the range that don't have meals
    const gaps: string[] = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    // Iterate through each day in the range
    const current = new Date(start);
    while (current <= end) {
      const dateStr = current.toISOString().split('T')[0];
      
      // Check if this date has meals
      if (!mealDates.includes(dateStr)) {
        gaps.push(dateStr);
      }
      
      // Move to next day
      current.setDate(current.getDate() + 1);
    }
    
    setGapDates(gaps);
  }, [mealDates, startDate, endDate]);
  
  if (gapDates.length === 0) {
    return null;
  }
  
  return (
    <Card className={cn('border-amber-200 bg-amber-50', className)}>
      <CardHeader className="pb-2">
        <CardTitle className="text-amber-800 text-base flex items-center">
          <AlertCircle className="h-4 w-4 mr-2" />
          Missing Meals Detected
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-amber-700 mb-3">
          Your meal plan has {gapDates.length} days without any planned meals.
        </p>
        
        <div className="space-y-2">
          {gapDates.slice(0, 3).map(date => {
            const formattedDate = new Date(date).toLocaleDateString('en-US', {
              weekday: 'short',
              month: 'short',
              day: 'numeric'
            });
            
            return (
              <Alert key={date} variant="outline" className="py-2">
                <div className="flex items-center justify-between">
                  <div>
                    <AlertTitle className="text-sm">{formattedDate}</AlertTitle>
                    <AlertDescription className="text-xs text-muted-foreground">
                      No meals planned
                    </AlertDescription>
                  </div>
                  
                  <Button 
                    size="sm" 
                    variant="outline"
                    className="h-7"
                    onClick={() => onAddMeal?.(date)}
                  >
                    <Plus className="h-3.5 w-3.5 mr-1" />
                    Add
                  </Button>
                </div>
              </Alert>
            );
          })}
          
          {gapDates.length > 3 && (
            <p className="text-xs text-amber-600 text-center">
              And {gapDates.length - 3} more days...
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
