"use client";

import { useState, useEffect } from "react";
import { useSupabase } from "@/components/supabase-provider";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { format, parseISO, isToday, isPast, isFuture, addDays } from "date-fns";
import { Check, Calendar, Clock, AlertCircle, Trophy, TrendingUp, ChevronRight } from "lucide-react";

interface MealCompletionTrackerProps {
  mealPlan: any;
  onUpdate?: () => void;
}

export function MealCompletionTracker({ mealPlan, onUpdate }: MealCompletionTrackerProps) {
  const { supabase } = useSupabase();
  const [isLoading, setIsLoading] = useState(false);
  const [completedMeals, setCompletedMeals] = useState<Record<string, string[]>>({});
  const [streak, setStreak] = useState(0);
  const [stats, setStats] = useState({
    totalMeals: 0,
    completedMeals: 0,
    completionRate: 0,
    todayCompleted: 0,
    todayTotal: 0
  });

  useEffect(() => {
    if (mealPlan?.id) {
      fetchCompletedMeals();
      calculateStats();
    }
  }, [mealPlan]);

  const fetchCompletedMeals = async () => {
    try {
      setIsLoading(true);
      
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error("User not authenticated");
      }
      
      // Fetch completed meals from the database
      const { data, error } = await supabase
        .from('meal_completions')
        .select('*')
        .eq('user_id', user.id)
        .eq('meal_plan_id', mealPlan.id);
      
      if (error) throw error;
      
      // Transform data into a more usable format
      // { "2023-01-01": ["breakfast", "lunch"], "2023-01-02": ["dinner"] }
      const completions: Record<string, string[]> = {};
      
      if (data && data.length > 0) {
        data.forEach(item => {
          if (!completions[item.date]) {
            completions[item.date] = [];
          }
          completions[item.date].push(item.meal_type);
        });
      }
      
      setCompletedMeals(completions);
      calculateStreak(completions);
      
    } catch (error) {
      console.error("Error fetching completed meals:", error);
      toast.error("Failed to load meal completion data");
    } finally {
      setIsLoading(false);
    }
  };

  const calculateStats = () => {
    if (!mealPlan?.meal_data?.mealPlan?.week) return;
    
    const week = mealPlan.meal_data.mealPlan.week;
    let totalMeals = 0;
    let completedCount = 0;
    let todayTotal = 0;
    let todayCompleted = 0;
    
    // Get today's date in the format "YYYY-MM-DD"
    const today = new Date().toISOString().split('T')[0];
    
    week.forEach((day: any) => {
      const date = day.date;
      const isToday = date === today;
      
      // Count meals for this day
      const mealsForDay = day.meals.length;
      totalMeals += mealsForDay;
      
      // Count completed meals for this day
      const completedForDay = completedMeals[date]?.length || 0;
      completedCount += completedForDay;
      
      // Track today's stats
      if (isToday) {
        todayTotal = mealsForDay;
        todayCompleted = completedForDay;
      }
    });
    
    // Calculate completion rate
    const completionRate = totalMeals > 0 ? Math.round((completedCount / totalMeals) * 100) : 0;
    
    setStats({
      totalMeals,
      completedMeals: completedCount,
      completionRate,
      todayCompleted,
      todayTotal
    });
  };

  const calculateStreak = (completions: Record<string, string[]>) => {
    // Get all dates from the meal plan
    const dates = mealPlan?.meal_data?.mealPlan?.week?.map((day: any) => day.date) || [];
    
    if (dates.length === 0) {
      setStreak(0);
      return;
    }
    
    // Sort dates in ascending order
    dates.sort();
    
    let currentStreak = 0;
    const today = new Date().toISOString().split('T')[0];
    
    // Loop through dates up to today
    for (const date of dates) {
      // Skip future dates
      if (date > today) break;
      
      // Get meals for this day
      const dayMeals = mealPlan.meal_data.mealPlan.week.find((day: any) => day.date === date)?.meals || [];
      
      // Get completed meals for this day
      const completedForDay = completions[date] || [];
      
      // Check if all meals for the day are completed
      const allCompleted = dayMeals.length > 0 && completedForDay.length === dayMeals.length;
      
      if (allCompleted) {
        currentStreak++;
      } else {
        // Reset streak if a day was missed (but only for past days)
        if (date < today) {
          currentStreak = 0;
        }
      }
    }
    
    setStreak(currentStreak);
  };

  const handleToggleMealCompletion = async (date: string, mealType: string, isCompleted: boolean) => {
    try {
      setIsLoading(true);
      
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error("User not authenticated");
      }
      
      if (isCompleted) {
        // Remove the completion
        const { error } = await supabase
          .from('meal_completions')
          .delete()
          .eq('user_id', user.id)
          .eq('meal_plan_id', mealPlan.id)
          .eq('date', date)
          .eq('meal_type', mealType);
        
        if (error) throw error;
        
        // Update local state
        setCompletedMeals(prev => {
          const updated = { ...prev };
          if (updated[date]) {
            updated[date] = updated[date].filter(type => type !== mealType);
            if (updated[date].length === 0) {
              delete updated[date];
            }
          }
          return updated;
        });
        
        toast.success("Meal marked as incomplete");
      } else {
        // Add the completion
        const { error } = await supabase
          .from('meal_completions')
          .insert({
            user_id: user.id,
            meal_plan_id: mealPlan.id,
            date,
            meal_type: mealType,
            completed_at: new Date().toISOString()
          });
        
        if (error) throw error;
        
        // Update local state
        setCompletedMeals(prev => {
          const updated = { ...prev };
          if (!updated[date]) {
            updated[date] = [];
          }
          updated[date].push(mealType);
          return updated;
        });
        
        toast.success("Meal marked as completed");
      }
      
      // Recalculate stats and streak
      calculateStats();
      calculateStreak(completedMeals);
      
      // Call the onUpdate callback if provided
      if (onUpdate) {
        onUpdate();
      }
      
    } catch (error) {
      console.error("Error toggling meal completion:", error);
      toast.error("Failed to update meal completion");
    } finally {
      setIsLoading(false);
    }
  };

  const isMealCompleted = (date: string, mealType: string): boolean => {
    return completedMeals[date]?.includes(mealType) || false;
  };

  // Get today's meals
  const today = new Date().toISOString().split('T')[0];
  const todayData = mealPlan?.meal_data?.mealPlan?.week?.find((day: any) => day.date === today);
  const todayMeals = todayData?.meals || [];
  
  // Get upcoming meals (next 2 days)
  const tomorrow = addDays(new Date(), 1).toISOString().split('T')[0];
  const dayAfterTomorrow = addDays(new Date(), 2).toISOString().split('T')[0];
  
  const upcomingDays = mealPlan?.meal_data?.mealPlan?.week?.filter((day: any) => 
    day.date === tomorrow || day.date === dayAfterTomorrow
  ) || [];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-xl flex items-center">
            <Trophy className="h-5 w-5 mr-2 text-yellow-500" />
            Meal Plan Progress
          </CardTitle>
          <CardDescription>
            Track your meal plan adherence and progress
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-muted-foreground">Completion Rate</p>
                  <p className="text-2xl font-bold">{stats.completionRate}%</p>
                </div>
                <TrendingUp className="h-5 w-5 text-primary" />
              </div>
              <Progress 
                value={stats.completionRate} 
                className="h-2 mt-2" 
              />
            </div>
            
            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-muted-foreground">Current Streak</p>
                  <p className="text-2xl font-bold">{streak} {streak === 1 ? 'day' : 'days'}</p>
                </div>
                <Trophy className="h-5 w-5 text-yellow-500" />
              </div>
              <div className="flex mt-2">
                {[...Array(7)].map((_, i) => (
                  <div 
                    key={i} 
                    className={`h-2 flex-1 mr-1 rounded-full ${i < streak ? 'bg-yellow-500' : 'bg-muted'}`}
                  />
                ))}
              </div>
            </div>
            
            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-muted-foreground">Today's Progress</p>
                  <p className="text-2xl font-bold">
                    {stats.todayCompleted}/{stats.todayTotal} meals
                  </p>
                </div>
                <Calendar className="h-5 w-5 text-green-500" />
              </div>
              <Progress 
                value={stats.todayTotal > 0 ? (stats.todayCompleted / stats.todayTotal) * 100 : 0} 
                className="h-2 mt-2" 
              />
            </div>
          </div>
          
          {todayMeals.length > 0 ? (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Today's Meals</h3>
              <div className="space-y-2">
                {todayMeals.map((meal: any, index: number) => {
                  const isCompleted = isMealCompleted(today, meal.type);
                  
                  return (
                    <div 
                      key={`${meal.id || index}`}
                      className={`p-3 border rounded-lg flex items-center justify-between ${
                        isCompleted ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-900' : ''
                      }`}
                    >
                      <div className="flex items-center">
                        <Checkbox 
                          id={`meal-${today}-${meal.type}-${index}`}
                          checked={isCompleted}
                          onCheckedChange={(checked) => {
                            handleToggleMealCompletion(today, meal.type, isCompleted);
                          }}
                          className="mr-3"
                        />
                        <div>
                          <Label 
                            htmlFor={`meal-${today}-${meal.type}-${index}`}
                            className={`font-medium ${isCompleted ? 'line-through text-muted-foreground' : ''}`}
                          >
                            {meal.name}
                          </Label>
                          <div className="flex items-center text-sm text-muted-foreground mt-1">
                            <Badge variant="outline" className="mr-2">{meal.type}</Badge>
                            <Clock className="h-3 w-3 mr-1" />
                            <span>{meal.prepTime} min</span>
                          </div>
                        </div>
                      </div>
                      
                      {isCompleted && (
                        <Badge className="bg-green-500 hover:bg-green-600">
                          <Check className="h-3 w-3 mr-1" />
                          Completed
                        </Badge>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ) : (
            <div className="text-center py-6 border rounded-lg">
              <AlertCircle className="h-10 w-10 mx-auto text-muted-foreground mb-2" />
              <h3 className="text-lg font-medium">No meals planned for today</h3>
              <p className="text-sm text-muted-foreground mt-1">
                Check your upcoming meals or add meals to today's plan
              </p>
            </div>
          )}
          
          {upcomingDays.length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-4">Upcoming Meals</h3>
              <div className="space-y-4">
                {upcomingDays.map((day: any) => (
                  <div key={day.date} className="border rounded-lg overflow-hidden">
                    <div className="bg-muted p-3 flex justify-between items-center">
                      <h4 className="font-medium">
                        {format(parseISO(day.date), "EEEE, MMMM d")}
                      </h4>
                      <Badge variant="outline">
                        {day.meals.length} {day.meals.length === 1 ? 'meal' : 'meals'}
                      </Badge>
                    </div>
                    <div className="p-3 space-y-2">
                      {day.meals.slice(0, 2).map((meal: any, index: number) => (
                        <div key={`${meal.id || index}`} className="flex justify-between items-center">
                          <div>
                            <p className="font-medium">{meal.name}</p>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Badge variant="outline" className="mr-2">{meal.type}</Badge>
                              <Clock className="h-3 w-3 mr-1" />
                              <span>{meal.prepTime} min</span>
                            </div>
                          </div>
                        </div>
                      ))}
                      
                      {day.meals.length > 2 && (
                        <Button variant="ghost" size="sm" className="w-full mt-2">
                          View all {day.meals.length} meals
                          <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
