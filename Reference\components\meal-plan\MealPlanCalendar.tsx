"use client";

import { useState, useRef, useEffect } from "react";
import { format, addDays, startOfWeek, addMonths, subMonths, isSameMonth, isSameDay, parse } from "date-fns";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Grid, List, ArrowUp } from "lucide-react";
import MealCard from "@/components/meal-plan/MealCard";
import { useMealPlanStore } from "@/lib/stores/meal-plan-store";
import { cn } from "@/lib/utils";

interface MealPlanCalendarProps {
  onAddMeal: (day: string, mealType: string) => void;
}

export default function MealPlanCalendar({ onAddMeal }: MealPlanCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState<"week" | "month">("week");
  const [displayMode, setDisplayMode] = useState<"grid" | "list">("grid");
  const { mealPlan, isLoading } = useMealPlanStore();
  const calendarRef = useRef<HTMLDivElement>(null);

  const startOfCurrentWeek = startOfWeek(currentDate, { weekStartsOn: 1 });
  
  const weekDays = Array.from({ length: 7 }).map((_, i) => {
    const date = addDays(startOfCurrentWeek, i);
    const dayStr = format(date, "yyyy-MM-dd");
    return {
      date,
      dayStr,
      dayName: format(date, "EEE"),
      dayOfMonth: format(date, "d"),
      isToday: isSameDay(date, new Date()),
      hasPlannedMeals: mealPlan && Object.keys(mealPlan).includes(dayStr) && 
        ["breakfast", "lunch", "dinner"].some(
          mealType => mealPlan[dayStr]?.[mealType]
        )
    };
  });

  const mealTypes = [
    { id: "breakfast", label: "Breakfast" },
    { id: "lunch", label: "Lunch" },
    { id: "dinner", label: "Dinner" }
  ];

  const navigateWeek = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate);
    if (direction === "prev") {
      newDate.setDate(newDate.getDate() - 7);
    } else {
      newDate.setDate(newDate.getDate() + 7);
    }
    setCurrentDate(newDate);
  };

  const navigateMonth = (direction: "prev" | "next") => {
    setCurrentDate(direction === "prev" ? subMonths(currentDate, 1) : addMonths(currentDate, 1));
  };

  const scrollToToday = () => {
    setCurrentDate(new Date());
    if (calendarRef.current) {
      calendarRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  const generateMonthDays = () => {
    const start = startOfWeek(currentDate, { weekStartsOn: 1 });
    const days = [];
    for (let i = 0; i < 35; i++) {
      const date = addDays(start, i);
      const dayStr = format(date, "yyyy-MM-dd");
      days.push({
        date,
        dayStr,
        dayName: format(date, "EEE"),
        dayOfMonth: format(date, "d"),
        isToday: isSameDay(date, new Date()),
        isCurrentMonth: isSameMonth(date, currentDate),
        hasPlannedMeals: mealPlan && Object.keys(mealPlan).includes(dayStr) && 
          ["breakfast", "lunch", "dinner"].some(
            mealType => mealPlan[dayStr]?.[mealType]
          )
      });
    }
    return days;
  };

  if (isLoading) {
    return (
      <div className="mt-8 space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">My Meal Plan</h1>
          <div className="flex items-center gap-2">
            <Tabs defaultValue="week" className="w-[200px]">
              <TabsList>
                <TabsTrigger value="week">Week</TabsTrigger>
                <TabsTrigger value="month">Month</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>
        <div className="grid place-items-center h-96">
          <div className="animate-pulse flex flex-col items-center">
            <div className="h-8 w-40 bg-muted rounded mb-4"></div>
            <div className="grid grid-cols-7 gap-4 w-full">
              {Array.from({ length: 7 }).map((_, i) => (
                <div key={i} className="h-20 bg-muted rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8 space-y-4" ref={calendarRef}>
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">My Meal Plan</h1>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={scrollToToday}
            className="flex items-center gap-2"
          >
            <CalendarIcon className="h-4 w-4" />
            Today
          </Button>
          
          <div className="flex items-center rounded-md border">
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "px-3 rounded-none rounded-l-md",
                displayMode === "grid" && "bg-muted"
              )}
              onClick={() => setDisplayMode("grid")}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "px-3 rounded-none rounded-r-md",
                displayMode === "list" && "bg-muted"
              )}
              onClick={() => setDisplayMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
          
          <Tabs 
            value={view} 
            onValueChange={(v) => setView(v as "week" | "month")}
            className="w-[200px]"
          >
            <TabsList>
              <TabsTrigger value="week">Week</TabsTrigger>
              <TabsTrigger value="month">Month</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium">
          {view === "week" ? (
            <>
              {format(startOfCurrentWeek, "MMMM d")} - {format(addDays(startOfCurrentWeek, 6), "MMMM d, yyyy")}
            </>
          ) : (
            format(currentDate, "MMMM yyyy")
          )}
        </h2>
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="icon"
            onClick={() => view === "week" ? navigateWeek("prev") : navigateMonth("prev")}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => view === "week" ? navigateWeek("next") : navigateMonth("next")}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {view === "week" ? (
        <>
          <div className="grid grid-cols-7 gap-2">
            {weekDays.map((day) => (
              <div
                key={day.dayStr}
                className={cn(
                  "text-center p-2 rounded-lg",
                  day.isToday ? "bg-primary/10" : ""
                )}
              >
                <p className="font-medium">{day.dayName}</p>
                <p className={cn(
                  "h-8 w-8 rounded-full inline-flex items-center justify-center",
                  day.isToday ? "bg-primary text-primary-foreground" : ""
                )}>
                  {day.dayOfMonth}
                </p>
              </div>
            ))}
          </div>

          {displayMode === "grid" ? (
            <div className="grid grid-cols-7 gap-2">
              {weekDays.map((day) => (
                <div
                  key={day.dayStr}
                  className={cn(
                    "border-2 rounded-lg p-2",
                    day.hasPlannedMeals 
                      ? "border-green-500/50" 
                      : "border-red-500/50"
                  )}
                >
                  {mealTypes.map((mealType) => {
                    const meal = mealPlan?.[day.dayStr]?.[mealType.id];
                    return (
                      <div key={`${day.dayStr}-${mealType.id}`} className="mb-2 last:mb-0">
                        <p className="text-xs text-muted-foreground mb-1">{mealType.label}</p>
                        {meal ? (
                          <MealCard 
                            meal={meal} 
                            day={day.dayStr} 
                            mealType={mealType.id} 
                          />
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full h-20 border-dashed flex items-center justify-center"
                            onClick={() => onAddMeal(day.dayStr, mealType.id)}
                          >
                            <span className="text-xl">+</span>
                          </Button>
                        )}
                      </div>
                    );
                  })}
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-6">
              {weekDays.map((day) => (
                <div key={day.dayStr} className="border rounded-lg p-4">
                  <h3 className="font-medium mb-4">
                    {format(day.date, "EEEE, MMMM d")}
                    {day.isToday && (
                      <span className="ml-2 text-sm bg-primary/10 text-primary px-2 py-1 rounded">
                        Today
                      </span>
                    )}
                  </h3>
                  <div className="space-y-4">
                    {mealTypes.map((mealType) => {
                      const meal = mealPlan?.[day.dayStr]?.[mealType.id];
                      return (
                        <div key={`${day.dayStr}-${mealType.id}`}>
                          <p className="text-sm font-medium text-muted-foreground mb-2">
                            {mealType.label}
                          </p>
                          {meal ? (
                            <MealCard 
                              meal={meal} 
                              day={day.dayStr} 
                              mealType={mealType.id} 
                            />
                          ) : (
                            <Button
                              variant="outline"
                              className="w-full h-16 border-dashed"
                              onClick={() => onAddMeal(day.dayStr, mealType.id)}
                            >
                              Add {mealType.label}
                            </Button>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          )}
        </>
      ) : (
        <div className="grid grid-cols-7 gap-2">
          {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map((day) => (
            <div key={day} className="text-center p-2 text-sm font-medium">
              {day}
            </div>
          ))}
          {generateMonthDays().map((day) => (
            <div
              key={day.dayStr}
              className={cn(
                "min-h-[100px] p-2 border rounded-lg",
                !day.isCurrentMonth && "opacity-50",
                day.isToday && "bg-primary/10",
                day.hasPlannedMeals ? "border-green-500/50" : "border-red-500/50"
              )}
            >
              <p className={cn(
                "text-sm font-medium mb-2",
                day.isToday && "text-primary"
              )}>
                {day.dayOfMonth}
              </p>
              {mealPlan?.[day.dayStr] && (
                <div className="space-y-1">
                  {Object.entries(mealPlan[day.dayStr]).map(([type, meal]) => (
                    <div
                      key={type}
                      className="text-xs p-1 bg-muted rounded truncate"
                      title={meal.name}
                    >
                      {meal.name}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}