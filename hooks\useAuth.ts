'use client';

import { useState, useEffect } from 'react';

interface User {
  id: string;
  name: string;
  email: string;
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const signIn = async (credentials: { email: string; password: string }) => {
    // Implement sign in logic
  };

  const signOut = async () => {
    // Implement sign out logic
  };

  return { user, isLoading, signIn, signOut };
}