import { NextRequest, NextResponse } from 'next/server';
import { getIngredientInformation } from '@/lib/api/spoonacular';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const ingredientId = parseInt(params.id, 10);
    
    if (isNaN(ingredientId)) {
      return NextResponse.json(
        { error: 'Invalid ingredient ID. Expected a number.' },
        { status: 400 }
      );
    }
    
    const searchParams = request.nextUrl.searchParams;
    const amount = parseFloat(searchParams.get('amount') || '1');
    const unit = searchParams.get('unit') || 'unit';
    
    const ingredientInfo = await getIngredientInformation(ingredientId, amount, unit);

    return NextResponse.json(ingredientInfo);
  } catch (error: any) {
    console.error(`Error in Spoonacular ingredient API route for ID ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while fetching the ingredient information' },
      { status: 500 }
    );
  }
}
