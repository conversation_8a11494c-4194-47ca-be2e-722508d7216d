generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
  schemas   = ["public"]
}

model meal_plans {
  id         String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  user_id    String
  start_date DateTime @db.Date
  end_date   DateTime @db.Date
  total_cost Float    @default(0)
  status     String   @default("draft")
  created_at DateTime @default(now()) @db.Timestamptz(6)
  updated_at DateTime @db.Timestamptz(6)
  meal_data  Json?
  users      users    @relation(fields: [user_id], references: [id], onDelete: Cascade)
  meals      meals[]

  @@index([status])
  @@index([user_id])
  @@schema("public")
}

model meals {
  id           String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  created_at   DateTime   @default(now()) @db.Timestamptz(6)
  updated_at   DateTime   @db.Timestamptz(6)
  meal_plan_id String     @db.Uuid
  recipe_id    String     @db.Uuid
  date         DateTime   @db.Date
  meal_type    String
  servings     Int       @default(1)
  meal_plan    meal_plans @relation(fields: [meal_plan_id], references: [id], onDelete: Cascade)

  @@index([meal_plan_id])
  @@schema("public")
}

model recipes {
  id               String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  user_id          String
  name             String
  description      String?
  instructions     String[]
  prep_time        Int
  cook_time        Int
  servings         Int
  cost_per_serving Float
  created_at       DateTime @default(now()) @db.Timestamptz(6)
  updated_at       DateTime @db.Timestamptz(6)
  meals            meals[]
  users            users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@schema("public")
}

model shopping_items {
  id               String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  shopping_list_id String   @db.Uuid
  name             String
  quantity         Int      @default(1)
  unit             String?
  price            Float    @default(0)
  completed        Boolean  @default(false)
  created_at       DateTime @default(now()) @db.Timestamptz(6)
  updated_at       DateTime @db.Timestamptz(6)

  @@schema("public")
}

model shopping_lists {
  id         String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  user_id    String
  name       String
  total_cost Float    @default(0)
  status     String   @default("active")
  created_at DateTime @default(now()) @db.Timestamptz(6)
  updated_at DateTime @db.Timestamptz(6)
  user       users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id, status])
  @@schema("public")
}

model user_preferences {
  id                  String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  user_id             String   @unique
  budget_type         String   @default("weekly")
  budget_flexibility  String   @default("strict")
  age_groups          String[] @default([])
  activity_level      String   @default("moderate")
  dietary_preference  String   @default("omnivore")
  custom_exclusions   String[] @default([])
  daily_calories      Int      @default(2000)
  macro_protein       Int      @default(30)
  macro_carbs         Int      @default(40)
  macro_fats          Int      @default(30)
  micronutrient_focus String[] @default([])
  health_conditions   String[] @default([])
  meal_variety        String   @default("high")
  cooking_time        String   @default("30-60")
  include_leftovers   Boolean  @default(true)
  created_at          DateTime @default(now()) @db.Timestamptz(6)
  updated_at          DateTime @db.Timestamptz(6)
  users               users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@schema("public")
}

model users {
  id                   String            @id
  email                String            @unique
  weekly_budget        Float             @default(0)
  household_size       Int               @default(1)
  dietary_restrictions String[]          @default([])
  created_at           DateTime          @default(now()) @db.Timestamptz(6)
  updated_at           DateTime          @db.Timestamptz(6)
  hashed_password      String?
  meal_plans           meal_plans[]
  recipes              recipes[]
  shopping_lists       shopping_lists[]
  user_preferences     user_preferences?

  @@schema("public")
}



