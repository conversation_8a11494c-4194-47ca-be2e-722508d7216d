"use client";

import { useState, useEffect } from 'react';
import { format, addDays, subDays, startOfWeek, endOfWeek } from 'date-fns';
import { Button } from '@/components/ui/button';
import { MealDropZone } from './MealDropZone';
import { MealCard } from './MealCard';
import { Meal, MealPlan } from '@/types/new-meal-plan';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { getCurrentWeekDays } from '@/lib/meal-plan-utils';
import { useDragDrop } from './DragDropProvider';
import { motion } from 'framer-motion';

interface MealPlanCalendarProps {
  mealPlan: MealPlan;
  onViewMealDetails: (meal: Meal) => void;
  onRemoveMeal: (day: string, mealType: string) => void;
  onStatusChange: (day: string, mealType: string, status: 'cooked' | 'skipped' | null) => void;
  onRegenerateMeal: (day: string, mealType: string) => void;
  onAddMeal: (day: string, mealType: string) => void;
  layout?: 'grid' | 'list';
}

export function MealPlanCalendar({
  mealPlan,
  onViewMealDetails,
  onRemoveMeal,
  onStatusChange,
  onRegenerateMeal,
  onAddMeal,
  layout = 'grid'
}: MealPlanCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const weekDays = getCurrentWeekDays(currentDate);

  // Get drag and drop context
  const { isDragging, draggedItem } = useDragDrop();

  // State to track which drop zones are valid targets
  const [validTargets, setValidTargets] = useState<{[key: string]: boolean}>({});

  const mealTypes = ['breakfast', 'lunch', 'dinner'];

  const goToPreviousWeek = () => {
    setCurrentDate(prevDate => subDays(prevDate, 7));
  };

  const goToNextWeek = () => {
    setCurrentDate(prevDate => addDays(prevDate, 7));
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const startDate = startOfWeek(currentDate, { weekStartsOn: 1 });
  const endDate = endOfWeek(currentDate, { weekStartsOn: 1 });

  // Calculate meal status statistics
  const mealStats = {
    total: 0,
    cooked: 0,
    skipped: 0,
    pending: 0
  };

  // Count meals by status
  if (mealPlan) {
    Object.values(mealPlan).forEach(dayMeals => {
      Object.values(dayMeals).forEach(meal => {
        mealStats.total++;
        if (meal.status === 'cooked') {
          mealStats.cooked++;
        } else if (meal.status === 'skipped') {
          mealStats.skipped++;
        } else {
          mealStats.pending++;
        }
      });
    });
  }

  // Update valid targets when dragging starts
  useEffect(() => {
    if (isDragging && draggedItem) {
      // Calculate valid targets - all drop zones except the current one
      const targets: {[key: string]: boolean} = {};

      weekDays.forEach(({ dayStr }) => {
        mealTypes.forEach(type => {
          const key = `${dayStr}-${type}`;
          const currentKey = `${draggedItem.day}-${draggedItem.mealType}`;

          // A target is valid if it's not the current position
          targets[key] = key !== currentKey;
        });
      });

      setValidTargets(targets);
    } else {
      // Reset valid targets when not dragging
      setValidTargets({});
    }
  }, [isDragging, draggedItem, weekDays, mealTypes]);

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
        <div className="flex flex-col md:flex-row items-start md:items-center gap-2">
          <div className="text-lg font-medium">
            {format(startDate, 'MMM d')} - {format(endDate, 'MMM d, yyyy')}
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" onClick={goToPreviousWeek}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button variant="outline" onClick={goToToday}>
              Today
            </Button>
            <Button variant="outline" size="icon" onClick={goToNextWeek}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Meal Status Summary */}
        {mealStats.total > 0 && (
          <div className="flex flex-col gap-2 min-w-[200px]">
            <div className="flex flex-wrap items-center gap-3 text-sm">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span>Cooked: {mealStats.cooked}</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span>Skipped: {mealStats.skipped}</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-gray-300"></div>
                <span>Pending: {mealStats.pending}</span>
              </div>
              <div className="text-muted-foreground">
                ({Math.round((mealStats.cooked / mealStats.total) * 100)}% complete)
              </div>
            </div>

            {/* Progress Bar */}
            <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
              <div className="flex h-full">
                <div
                  className="bg-green-500 h-full"
                  style={{ width: `${(mealStats.cooked / mealStats.total) * 100}%` }}
                />
                <div
                  className="bg-red-500 h-full"
                  style={{ width: `${(mealStats.skipped / mealStats.total) * 100}%` }}
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {layout === 'grid' ? (
        <motion.div
          className="grid grid-cols-7 gap-4"
          animate={{ opacity: isDragging ? 0.95 : 1 }}
        >
          {/* Day headers */}
          {weekDays.map(({ dayName, dayOfMonth }) => (
            <div key={dayName} className="text-center p-2 border rounded-md bg-muted/20">
              <div className="font-medium">{dayName}</div>
              <div className="text-sm text-muted-foreground">{dayOfMonth}</div>
            </div>
          ))}

          {/* Meal grid */}
          {mealTypes.map(mealType => (
            <>
              {weekDays.map(({ dayStr }) => {
                const meal = mealPlan[dayStr]?.[mealType];
                return (
                  <MealDropZone
                    key={`${dayStr}-${mealType}`}
                    day={dayStr}
                    mealType={mealType}
                    onAddMeal={onAddMeal}
                    isEmpty={!meal}
                    isValidTarget={!isDragging || validTargets[`${dayStr}-${mealType}`]}
                  >
                    {meal && (
                      <MealCard
                        meal={meal}
                        day={dayStr}
                        mealType={mealType}
                        onViewDetails={onViewMealDetails}
                        onRemove={onRemoveMeal}
                        onStatusChange={onStatusChange}
                        onRegenerate={onRegenerateMeal}
                        compact
                      />
                    )}
                  </MealDropZone>
                );
              })}
            </>
          ))}
        </motion.div>
      ) : (
        <div className="space-y-6">
          {weekDays.map(({ dayStr, dayName, dayOfMonth }) => (
            <div key={dayStr} className="space-y-2">
              <div className="font-medium border-b pb-2">
                {dayName} {dayOfMonth}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {mealTypes.map(mealType => {
                  const meal = mealPlan[dayStr]?.[mealType];
                  return (
                    <MealDropZone
                      key={`${dayStr}-${mealType}`}
                      day={dayStr}
                      mealType={mealType}
                      onAddMeal={onAddMeal}
                      isEmpty={!meal}
                      isValidTarget={!isDragging || validTargets[`${dayStr}-${mealType}`]}
                    >
                      {meal && (
                        <MealCard
                          meal={meal}
                          day={dayStr}
                          mealType={mealType}
                          onViewDetails={onViewMealDetails}
                          onRemove={onRemoveMeal}
                          onStatusChange={onStatusChange}
                          onRegenerate={onRegenerateMeal}
                        />
                      )}
                    </MealDropZone>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
