"use client";

import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { useRouter } from "next/navigation";
import Image from "next/image";

interface MealCardProps {
  id: string | number;
  name: string;
  time: string;
  calories: number;
  image: string;
}

export function MealCard({ id, name, time, calories, image }: MealCardProps) {
  const router = useRouter();

  const handleClick = () => {
    router.push(`/meal-detail/${id}`);
  };

  return (
    <div
      className="group hover:scale-105 transition-transform duration-200 focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2 rounded-lg cursor-pointer"
      onClick={handleClick}
    >
      <Card className="flex items-center space-x-4 p-4 hover:bg-accent/50 transition-colors">
        <div className="relative h-16 w-16 rounded-lg overflow-hidden">
          <Image
            src={image}
            alt={`${name} presentation`}
            width={64}
            height={64}
            className="object-cover h-full w-full"
          />
        </div>
        <div className="flex-1">
          <h4 className="font-medium" tabIndex={0}>{name}</h4>
          <p className="text-sm text-muted-foreground">{time}</p>
          <Badge variant="secondary" className="mt-1">
            {calories} cal
          </Badge>
        </div>
      </Card>
    </div>
  );
}
