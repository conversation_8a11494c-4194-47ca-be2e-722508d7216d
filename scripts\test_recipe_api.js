// This script tests the recipe API endpoints
// Run with: node scripts/test_recipe_api.js

const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config();

// Base URL for API requests
const BASE_URL = 'http://localhost:3000/api';

// Test data
const testRecipe = {
  name: 'Test Recipe',
  description: 'A test recipe created by the API test script',
  instructions: JSON.stringify([
    'Step 1: Test the API',
    'Step 2: Verify it works',
    'Step 3: Celebrate'
  ]),
  ingredients: JSON.stringify([
    { name: 'Test Ingredient 1', quantity: 1, unit: 'cup' },
    { name: 'Test Ingredient 2', quantity: 2, unit: 'tbsp' }
  ]),
  prep_time: 10,
  cook_time: 20,
  servings: 4,
  calories_per_serving: 300,
  protein_per_serving: 15,
  carbs_per_serving: 30,
  fat_per_serving: 10,
  image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c',
  tags: JSON.stringify(['test', 'api', 'recipe'])
};

// Store created recipe ID
let createdRecipeId = null;

// Helper function to log results
function logResult(testName, success, data) {
  console.log(`\n--- ${testName} ---`);
  if (success) {
    console.log('✅ SUCCESS');
  } else {
    console.log('❌ FAILED');
  }
  console.log(data);
}

// Test functions
async function testCreateRecipe() {
  try {
    const response = await fetch(`${BASE_URL}/recipes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testRecipe)
    });

    const data = await response.json();

    if (response.ok && data.id) {
      createdRecipeId = data.id;
      logResult('Create Recipe', true, data);
      return true;
    } else {
      logResult('Create Recipe', false, data);
      return false;
    }
  } catch (error) {
    logResult('Create Recipe', false, error.message);
    return false;
  }
}

async function testGetRecipes() {
  try {
    const response = await fetch(`${BASE_URL}/recipes`);
    const data = await response.json();

    if (response.ok && Array.isArray(data)) {
      logResult('Get Recipes', true, `Found ${data.length} recipes`);
      return true;
    } else {
      logResult('Get Recipes', false, data);
      return false;
    }
  } catch (error) {
    logResult('Get Recipes', false, error.message);
    return false;
  }
}

async function testGetRecipeById() {
  if (!createdRecipeId) {
    logResult('Get Recipe By ID', false, 'No recipe ID available');
    return false;
  }

  try {
    const response = await fetch(`${BASE_URL}/recipes/${createdRecipeId}`);
    const data = await response.json();

    if (response.ok && data.id === createdRecipeId) {
      logResult('Get Recipe By ID', true, data);
      return true;
    } else {
      logResult('Get Recipe By ID', false, data);
      return false;
    }
  } catch (error) {
    logResult('Get Recipe By ID', false, error.message);
    return false;
  }
}

async function testUpdateRecipe() {
  if (!createdRecipeId) {
    logResult('Update Recipe', false, 'No recipe ID available');
    return false;
  }

  const updates = {
    name: 'Updated Test Recipe',
    description: 'This recipe has been updated by the API test script'
  };

  try {
    const response = await fetch(`${BASE_URL}/recipes/${createdRecipeId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updates)
    });

    const data = await response.json();

    if (response.ok && data.name === updates.name) {
      logResult('Update Recipe', true, data);
      return true;
    } else {
      logResult('Update Recipe', false, data);
      return false;
    }
  } catch (error) {
    logResult('Update Recipe', false, error.message);
    return false;
  }
}

async function testToggleFavorite() {
  if (!createdRecipeId) {
    logResult('Toggle Favorite', false, 'No recipe ID available');
    return false;
  }

  try {
    const response = await fetch(`${BASE_URL}/recipes/${createdRecipeId}/favorite`, {
      method: 'PATCH'
    });

    const data = await response.json();

    if (response.ok && typeof data.is_favorite === 'boolean') {
      logResult('Toggle Favorite', true, data);
      return true;
    } else {
      logResult('Toggle Favorite', false, data);
      return false;
    }
  } catch (error) {
    logResult('Toggle Favorite', false, error.message);
    return false;
  }
}

async function testDeleteRecipe() {
  if (!createdRecipeId) {
    logResult('Delete Recipe', false, 'No recipe ID available');
    return false;
  }

  try {
    const response = await fetch(`${BASE_URL}/recipes/${createdRecipeId}`, {
      method: 'DELETE'
    });

    const data = await response.json();

    if (response.ok) {
      logResult('Delete Recipe', true, data);
      return true;
    } else {
      logResult('Delete Recipe', false, data);
      return false;
    }
  } catch (error) {
    logResult('Delete Recipe', false, error.message);
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log('🧪 Starting API tests...');

  // Test create
  const createSuccess = await testCreateRecipe();
  if (!createSuccess) {
    console.log('❌ Create recipe failed, stopping tests');
    return;
  }

  // Test get all
  await testGetRecipes();

  // Test get by ID
  await testGetRecipeById();

  // Test update
  await testUpdateRecipe();

  // Test toggle favorite
  await testToggleFavorite();

  // Test delete
  await testDeleteRecipe();

  console.log('\n🏁 All tests completed!');
}

// Run the tests
runTests();
