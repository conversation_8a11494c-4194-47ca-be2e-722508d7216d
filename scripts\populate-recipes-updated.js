// Script to populate Supabase with real recipe data
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL and key must be provided as environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Define recipes to insert
const recipes = [
  {
    name: "Grilled Chicken Salad",
    description: "A healthy and delicious salad with grilled chicken breast, mixed greens, and a light vinaigrette dressing.",
    prep_time: 15,
    cook_time: 20,
    servings: 2,
    cost_per_serving: 4.50,
    image_url: "https://images.unsplash.com/photo-1527477396000-e27163b481c2",
    is_favorite: true,
    difficulty: "Easy",
    meal_type: "Lunch",
    instructions: [
      "Season chicken breasts with salt and pepper.",
      "Grill chicken for 6-8 minutes per side until fully cooked.",
      "Let chicken rest for 5 minutes, then slice into strips.",
      "In a large bowl, combine mixed greens, tomatoes, cucumber, and red onion.",
      "In a small bowl, whisk together olive oil, lemon juice, salt, and pepper.",
      "Drizzle dressing over salad and toss to combine.",
      "Top with sliced grilled chicken and serve immediately."
    ],
    ingredients: [
      { name: "Chicken breast", amount: "2", unit: "pieces" },
      { name: "Mixed greens", amount: "4", unit: "cups" },
      { name: "Cherry tomatoes", amount: "1", unit: "cup" },
      { name: "Cucumber", amount: "1", unit: "medium" },
      { name: "Red onion", amount: "1/4", unit: "cup" },
      { name: "Olive oil", amount: "2", unit: "tbsp" },
      { name: "Lemon juice", amount: "1", unit: "tbsp" },
      { name: "Salt", amount: "1/2", unit: "tsp" },
      { name: "Black pepper", amount: "1/4", unit: "tsp" }
    ],
    nutrition: {
      calories: 350,
      protein: 35,
      carbs: 15,
      fat: 18,
      fiber: 5
    },
    dietary_restrictions: ["high-protein", "low-carb", "gluten-free"]
  },
  {
    name: "Quinoa Buddha Bowl",
    description: "A nutritious and colorful bowl packed with quinoa, roasted vegetables, and a tahini dressing.",
    prep_time: 20,
    cook_time: 25,
    servings: 2,
    cost_per_serving: 3.75,
    image_url: "https://images.unsplash.com/photo-1546069901-ba9599a7e63c",
    is_favorite: false,
    difficulty: "Medium",
    meal_type: "Dinner",
    instructions: [
      "Preheat oven to 400°F (200°C).",
      "Cook quinoa according to package instructions.",
      "Dice sweet potato, toss with olive oil, salt, and cumin, and roast for 25 minutes.",
      "Cut broccoli into florets, toss with olive oil and salt, and roast for 15 minutes.",
      "Drain and rinse chickpeas, toss with olive oil and spices, and roast for 15 minutes.",
      "Make dressing by whisking together tahini, lemon juice, maple syrup, and water.",
      "Assemble bowls with quinoa, roasted vegetables, chickpeas, and sliced avocado.",
      "Drizzle with tahini dressing and serve."
    ],
    ingredients: [
      { name: "Quinoa", amount: "1", unit: "cup" },
      { name: "Sweet potato", amount: "1", unit: "medium" },
      { name: "Broccoli", amount: "1", unit: "head" },
      { name: "Chickpeas", amount: "1", unit: "can" },
      { name: "Avocado", amount: "1", unit: "" },
      { name: "Tahini", amount: "2", unit: "tbsp" },
      { name: "Lemon juice", amount: "1", unit: "tbsp" },
      { name: "Maple syrup", amount: "1", unit: "tsp" },
      { name: "Olive oil", amount: "2", unit: "tbsp" },
      { name: "Salt", amount: "1/2", unit: "tsp" },
      { name: "Cumin", amount: "1/2", unit: "tsp" }
    ],
    nutrition: {
      calories: 450,
      protein: 15,
      carbs: 65,
      fat: 20,
      fiber: 12
    },
    dietary_restrictions: ["vegetarian", "high-fiber", "plant-based"]
  },
  {
    name: "Smoothie Bowl",
    description: "A refreshing and nutritious smoothie bowl topped with fresh fruits, granola, and nuts.",
    prep_time: 10,
    cook_time: 0,
    servings: 1,
    cost_per_serving: 2.95,
    image_url: "https://images.unsplash.com/photo-1553530666-ba11a90a0868",
    is_favorite: true,
    difficulty: "Easy",
    meal_type: "Breakfast",
    instructions: [
      "In a blender, combine frozen banana, frozen berries, Greek yogurt, almond milk, and honey.",
      "Blend until smooth and thick. Add more almond milk if needed.",
      "Pour into a bowl.",
      "Top with granola, fresh berries, sliced banana, chia seeds, and a drizzle of almond butter.",
      "Serve immediately."
    ],
    ingredients: [
      { name: "Frozen banana", amount: "1", unit: "large" },
      { name: "Frozen berries", amount: "1", unit: "cup" },
      { name: "Greek yogurt", amount: "1/2", unit: "cup" },
      { name: "Almond milk", amount: "1/4", unit: "cup" },
      { name: "Honey", amount: "1", unit: "tbsp" },
      { name: "Granola", amount: "1/4", unit: "cup" },
      { name: "Fresh berries", amount: "1/4", unit: "cup" },
      { name: "Sliced banana", amount: "1/2", unit: "" },
      { name: "Chia seeds", amount: "1", unit: "tsp" },
      { name: "Almond butter", amount: "1", unit: "tbsp" }
    ],
    nutrition: {
      calories: 320,
      protein: 15,
      carbs: 55,
      fat: 8,
      fiber: 10
    },
    dietary_restrictions: ["vegetarian", "quick"]
  },
  {
    name: "Grilled Salmon with Asparagus",
    description: "Perfectly grilled salmon served with roasted asparagus and a lemon butter sauce.",
    prep_time: 15,
    cook_time: 15,
    servings: 2,
    cost_per_serving: 8.95,
    image_url: "https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2",
    is_favorite: true,
    difficulty: "Medium",
    meal_type: "Dinner",
    instructions: [
      "Preheat grill to medium-high heat.",
      "Season salmon fillets with salt, pepper, and a drizzle of olive oil.",
      "Trim asparagus ends and toss with olive oil, salt, and pepper.",
      "Grill salmon for 4-5 minutes per side until it flakes easily with a fork.",
      "Grill asparagus for 3-4 minutes, turning occasionally.",
      "In a small saucepan, melt butter with minced garlic and the juice of half a lemon.",
      "Plate salmon and asparagus, drizzle with lemon butter sauce.",
      "Garnish with fresh dill and lemon wedges."
    ],
    ingredients: [
      { name: "Salmon fillets", amount: "2", unit: "6 oz each" },
      { name: "Asparagus", amount: "1", unit: "bunch" },
      { name: "Butter", amount: "2", unit: "tbsp" },
      { name: "Lemon", amount: "1", unit: "" },
      { name: "Garlic", amount: "2", unit: "cloves" },
      { name: "Olive oil", amount: "2", unit: "tbsp" },
      { name: "Salt", amount: "1/2", unit: "tsp" },
      { name: "Black pepper", amount: "1/4", unit: "tsp" },
      { name: "Fresh dill", amount: "2", unit: "tbsp" }
    ],
    nutrition: {
      calories: 420,
      protein: 40,
      carbs: 10,
      fat: 25,
      fiber: 4
    },
    dietary_restrictions: ["high-protein", "low-carb", "omega-3"]
  }
];

async function populateRecipes() {
  console.log('Starting to populate recipes...');
  
  try {
    // Get the current user for user_id
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('Authentication error:', authError);
      console.log('Please sign in to Supabase first using the app');
      
      // Use a hardcoded user ID for testing
      const hardcodedUserId = process.env.TEST_USER_ID || '00000000-0000-0000-0000-000000000000';
      console.log('Using hardcoded user ID for testing:', hardcodedUserId);
      
      // Add user_id to each recipe
      const recipesWithUserId = recipes.map(recipe => ({
        ...recipe,
        user_id: hardcodedUserId
      }));
      
      // Insert recipes into Supabase
      const { data, error } = await supabase
        .from('recipes')
        .insert(recipesWithUserId)
        .select();
      
      if (error) {
        console.error('Error inserting recipes:', error);
        process.exit(1);
      }
      
      console.log(`Successfully inserted ${data.length} recipes:`);
      data.forEach(recipe => {
        console.log(`- ID: ${recipe.id}, Name: ${recipe.name}`);
      });
    } else {
      console.log('Using authenticated user ID:', user.id);
      
      // Add user_id to each recipe
      const recipesWithUserId = recipes.map(recipe => ({
        ...recipe,
        user_id: user.id
      }));
      
      // Insert recipes into Supabase
      const { data, error } = await supabase
        .from('recipes')
        .insert(recipesWithUserId)
        .select();
      
      if (error) {
        console.error('Error inserting recipes:', error);
        process.exit(1);
      }
      
      console.log(`Successfully inserted ${data.length} recipes:`);
      data.forEach(recipe => {
        console.log(`- ID: ${recipe.id}, Name: ${recipe.name}`);
      });
    }
    
    console.log('Recipe population complete!');
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Run the population script
populateRecipes();
