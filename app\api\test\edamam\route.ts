import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function GET(request: NextRequest) {
  try {
    // Recipe API configuration
    const EDAMAM_RECIPE_APP_ID = process.env.NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES;
    const EDAMAM_RECIPE_APP_KEY = process.env.NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES;
    const EDAMAM_RECIPE_BASE_URL = 'https://api.edamam.com/api/recipes/v2';

    // Food Database API configuration
    const EDAMAM_FOOD_DB_APP_ID = process.env.NEXT_PUBLIC_EDAMAM_APP_ID_FOOD_DATABASE;
    const EDAMAM_FOOD_DB_APP_KEY = process.env.NEXT_PUBLIC_EDAMAM_APP_KEY_FOOD_DATABASE;
    const EDAMAM_FOOD_DB_BASE_URL = 'https://api.edamam.com/api/food-database/v2';

    // Test results
    const results: any = {
      environment: {
        EDAMAM_RECIPE_APP_ID: EDAMAM_RECIPE_APP_ID ? 'Set' : 'Not set',
        EDAMAM_RECIPE_APP_KEY: EDAMAM_RECIPE_APP_KEY ? 'Set' : 'Not set',
        EDAMAM_FOOD_DB_APP_ID: EDAMAM_FOOD_DB_APP_ID ? 'Set' : 'Not set',
        EDAMAM_FOOD_DB_APP_KEY: EDAMAM_FOOD_DB_APP_KEY ? 'Set' : 'Not set'
      },
      recipeApiTest: null,
      foodDbApiTest: null,
      errors: []
    };

    // Test Recipe Search API
    try {
      // Prepare the query parameters
      const queryParams = new URLSearchParams({
        type: 'public',
        app_id: EDAMAM_RECIPE_APP_ID || '',
        app_key: EDAMAM_RECIPE_APP_KEY || '',
        q: 'chicken'
      });

      // Make the API request
      const response = await axios.get(`${EDAMAM_RECIPE_BASE_URL}?${queryParams.toString()}`, {
        headers: {
          'Edamam-Account-User': 'user123' // Add a default user ID
        }
      });

      // Check if the response is successful
      if (response.status === 200) {
        results.recipeApiTest = {
          success: true,
          message: 'Recipe Search API test: SUCCESS',
          recipeCount: response.data.hits.length,
          firstRecipe: response.data.hits.length > 0 ? response.data.hits[0].recipe.label : 'No recipes found'
        };
      } else {
        results.recipeApiTest = {
          success: false,
          message: 'Recipe Search API test: FAILED',
          status: response.status,
          statusText: response.statusText
        };
      }
    } catch (error: any) {
      results.recipeApiTest = {
        success: false,
        message: 'Recipe Search API test: ERROR',
        error: error.message
      };

      if (error.response) {
        results.recipeApiTest.status = error.response.status;
        results.recipeApiTest.data = error.response.data;
      }

      results.errors.push({
        api: 'Recipe Search API',
        error: error.message,
        stack: error.stack
      });
    }

    // Test Food Database API
    try {
      // Prepare the query parameters
      const queryParams = new URLSearchParams({
        app_id: EDAMAM_FOOD_DB_APP_ID || '',
        app_key: EDAMAM_FOOD_DB_APP_KEY || '',
        ingr: 'apple'
      });

      // Make the API request
      const response = await axios.get(`${EDAMAM_FOOD_DB_BASE_URL}/parser?${queryParams.toString()}`);

      // Check if the response is successful
      if (response.status === 200) {
        results.foodDbApiTest = {
          success: true,
          message: 'Food Database API test: SUCCESS',
          foodCount: response.data.hints.length,
          firstFood: response.data.hints.length > 0 ? response.data.hints[0].food.label : 'No food items found'
        };

        // Test nutrients endpoint with the first food item
        if (response.data.hints.length > 0) {
          const foodId = response.data.hints[0].food.foodId;

          try {
            // Prepare the request body
            const requestBody = {
              ingredients: [
                {
                  quantity: 1,
                  measureURI: 'http://www.edamam.com/ontologies/edamam.owl#Measure_unit',
                  foodId: foodId
                }
              ]
            };

            // Make the API request
            const nutrientsResponse = await axios.post(
              `${EDAMAM_FOOD_DB_BASE_URL}/nutrients`,
              requestBody,
              {
                params: {
                  app_id: EDAMAM_FOOD_DB_APP_ID,
                  app_key: EDAMAM_FOOD_DB_APP_KEY
                }
              }
            );

            // Check if the response is successful
            if (nutrientsResponse.status === 200) {
              results.foodDbApiTest.nutrientsTest = {
                success: true,
                message: 'Food Nutrients API test: SUCCESS',
                calories: nutrientsResponse.data.calories,
                protein: nutrientsResponse.data.totalNutrients.PROCNT,
                carbs: nutrientsResponse.data.totalNutrients.CHOCDF,
                fat: nutrientsResponse.data.totalNutrients.FAT
              };
            } else {
              results.foodDbApiTest.nutrientsTest = {
                success: false,
                message: 'Food Nutrients API test: FAILED',
                status: nutrientsResponse.status,
                statusText: nutrientsResponse.statusText
              };
            }
          } catch (error: any) {
            results.foodDbApiTest.nutrientsTest = {
              success: false,
              message: 'Food Nutrients API test: ERROR',
              error: error.message
            };

            if (error.response) {
              results.foodDbApiTest.nutrientsTest.status = error.response.status;
              results.foodDbApiTest.nutrientsTest.data = error.response.data;
            }

            results.errors.push({
              api: 'Food Nutrients API',
              error: error.message,
              stack: error.stack
            });
          }
        }
      } else {
        results.foodDbApiTest = {
          success: false,
          message: 'Food Database API test: FAILED',
          status: response.status,
          statusText: response.statusText
        };
      }
    } catch (error: any) {
      results.foodDbApiTest = {
        success: false,
        message: 'Food Database API test: ERROR',
        error: error.message
      };

      if (error.response) {
        results.foodDbApiTest.status = error.response.status;
        results.foodDbApiTest.data = error.response.data;
      }

      results.errors.push({
        api: 'Food Database API',
        error: error.message,
        stack: error.stack
      });
    }

    return NextResponse.json(results);
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      message: 'Error running tests',
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
