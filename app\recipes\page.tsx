"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSupabase } from "@/components/supabase-provider";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ChevronLeft, Search, Filter, Plus, CheckSquare } from "lucide-react";
import { toast } from "sonner";
import { RecipeCard, Recipe } from "@/components/recipe/recipe-card";
import { AdvancedFilter, FilterOptions } from "@/components/recipe/advanced-filter";
import { RecipePagination } from "@/components/recipe/recipe-pagination";
import { BatchActions } from "@/components/recipe/batch-actions";

// Recipe interface is now imported from RecipeCard component

export default function RecipesPage() {
  const router = useRouter();
  const { supabase, isLoading: isSupabaseLoading } = useSupabase();
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecipes, setTotalRecipes] = useState(0);
  const [recipesPerPage, setRecipesPerPage] = useState(9);

  // Batch selection state
  const [isSelectMode, setIsSelectMode] = useState(false);
  const [selectedRecipes, setSelectedRecipes] = useState<string[]>([]);

  // Advanced filtering state
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterOptions>({
    cuisine: "",
    difficulty: "",
    dietaryRestrictions: [],
    prepTimeMax: 120,
    cookTimeMax: 120,
    costMax: 20,
    sortBy: "created_at",
    sortOrder: "desc"
  });

  // Define cuisine options
  const cuisineOptions = [
    { label: "All", value: "" },
    { label: "American", value: "American" },
    { label: "Italian", value: "Italian" },
    { label: "Mexican", value: "Mexican" },
    { label: "Asian", value: "Asian" },
    { label: "Mediterranean", value: "Mediterranean" },
    { label: "Indian", value: "Indian" },
    { label: "French", value: "French" },
    { label: "Greek", value: "Greek" },
    { label: "Spanish", value: "Spanish" },
    { label: "Middle Eastern", value: "Middle Eastern" },
    { label: "Thai", value: "Thai" },
    { label: "Japanese", value: "Japanese" },
    { label: "Chinese", value: "Chinese" },
    { label: "Korean", value: "Korean" },
    { label: "Vietnamese", value: "Vietnamese" },
    { label: "Vegetarian", value: "Vegetarian" },
    { label: "Vegan", value: "Vegan" },
    { label: "Other", value: "Other" },
  ];

  // Define difficulty options
  const difficultyOptions = [
    { label: "All", value: "" },
    { label: "Easy", value: "Easy" },
    { label: "Medium", value: "Medium" },
    { label: "Hard", value: "Hard" },
  ];

  // Demo recipes for fallback
  const getDemoRecipes = () => {
    return [
      {
        id: "1",
        name: "Grilled Chicken Salad",
        description: "A healthy and delicious salad with grilled chicken breast",
        prep_time: 15,
        cook_time: 20,
        cost_per_serving: 3.50,
        image_url: "https://images.unsplash.com/photo-1527477396000-e27163b481c2",
        cuisine: "Mediterranean",
        dietary_restrictions: ["gluten-free", "high-protein"],
        is_favorite: true,
        user_id: "demo",
        instructions: ["Season chicken", "Grill chicken", "Prepare salad", "Combine and serve"],
        servings: 2,
        difficulty: "Easy"
      },
      {
        id: "2",
        name: "Quinoa Buddha Bowl",
        description: "Nutrient-rich bowl with quinoa, roasted vegetables, and tahini dressing",
        prep_time: 20,
        cook_time: 30,
        cost_per_serving: 4.25,
        image_url: "https://images.unsplash.com/photo-**********-ba9599a7e63c",
        cuisine: "Vegetarian",
        dietary_restrictions: ["vegan", "gluten-free"],
        is_favorite: false,
        user_id: "demo",
        instructions: ["Cook quinoa", "Roast vegetables", "Prepare dressing", "Assemble bowl"],
        servings: 2,
        difficulty: "Medium"
      },
      {
        id: "3",
        name: "Salmon with Roasted Vegetables",
        description: "Oven-baked salmon fillet with seasonal roasted vegetables",
        prep_time: 15,
        cook_time: 25,
        cost_per_serving: 6.75,
        image_url: "https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2",
        cuisine: "Scandinavian",
        dietary_restrictions: ["high-protein", "low-carb"],
        is_favorite: true,
        user_id: "demo",
        instructions: ["Prepare salmon", "Season vegetables", "Bake in oven", "Serve hot"],
        servings: 2,
        difficulty: "Medium"
      },
      {
        id: "4",
        name: "Vegetable Stir Fry",
        description: "Quick and easy vegetable stir fry with tofu and soy sauce",
        prep_time: 10,
        cook_time: 15,
        cost_per_serving: 2.80,
        image_url: "https://images.unsplash.com/photo-1543362906-acfc16c67564",
        cuisine: "Asian",
        dietary_restrictions: ["vegan", "low-calorie"],
        is_favorite: false,
        user_id: "demo",
        instructions: ["Prepare vegetables", "Cook tofu", "Stir fry everything", "Add sauce", "Serve hot"],
        servings: 2,
        difficulty: "Easy"
      }
    ];
  };

  // Function to fetch recipes with pagination and filtering
  const fetchRecipes = async (page = 1) => {
    try {
      setIsLoading(true);
      setCurrentPage(page);

      // Make sure Supabase is available
      if (!supabase) {
        console.error('Supabase client not available');
        toast.error('Unable to connect to the database. Using demo recipes.');
        const demoRecipes = getDemoRecipes();
        setRecipes(demoRecipes);
        setTotalRecipes(demoRecipes.length);
        return;
      }

      // Try to get the current user
      const { data: authData } = await supabase.auth.getUser();
      const user = authData?.user;

      // If user is authenticated, try to fetch their recipes from Supabase
      if (user) {
        try {
          // Calculate pagination offsets
          const offset = (page - 1) * recipesPerPage;

          // First, get the total count for pagination
          const countQuery = supabase
            .from('recipes')
            .select('id', { count: 'exact' })
            .eq('user_id', user.id);

          // Apply filters
          if (activeTab !== 'all' && activeTab !== 'favorites') {
            countQuery.eq('cuisine', activeTab);
          } else if (activeTab === 'favorites') {
            countQuery.eq('is_favorite', true);
          }

          if (searchQuery) {
            countQuery.or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
          }

          if (filters.cuisine) {
            countQuery.eq('cuisine', filters.cuisine);
          }

          if (filters.difficulty) {
            countQuery.eq('difficulty', filters.difficulty);
          }

          if (filters.dietaryRestrictions.length > 0) {
            // For each dietary restriction, add an overlap condition
            filters.dietaryRestrictions.forEach(restriction => {
              countQuery.contains('dietary_restrictions', [restriction]);
            });
          }

          if (filters.prepTimeMax < 120) {
            countQuery.lte('prep_time', filters.prepTimeMax);
          }

          if (filters.cookTimeMax < 120) {
            countQuery.lte('cook_time', filters.cookTimeMax);
          }

          if (filters.costMax < 20) {
            countQuery.lte('cost_per_serving', filters.costMax);
          }

          const { count, error: countError } = await countQuery;

          if (countError) {
            console.error('Error counting recipes:', countError);
            throw countError;
          }

          setTotalRecipes(count || 0);

          // Now fetch the actual data with pagination
          let dataQuery = supabase
            .from('recipes')
            .select('*')
            .eq('user_id', user.id);

          // Apply the same filters as the count query
          if (activeTab !== 'all' && activeTab !== 'favorites') {
            dataQuery.eq('cuisine', activeTab);
          } else if (activeTab === 'favorites') {
            dataQuery.eq('is_favorite', true);
          }

          if (searchQuery) {
            dataQuery.or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
          }

          if (filters.cuisine) {
            dataQuery.eq('cuisine', filters.cuisine);
          }

          if (filters.difficulty) {
            dataQuery.eq('difficulty', filters.difficulty);
          }

          if (filters.dietaryRestrictions.length > 0) {
            // For each dietary restriction, add an overlap condition
            filters.dietaryRestrictions.forEach(restriction => {
              dataQuery.contains('dietary_restrictions', [restriction]);
            });
          }

          if (filters.prepTimeMax < 120) {
            dataQuery.lte('prep_time', filters.prepTimeMax);
          }

          if (filters.cookTimeMax < 120) {
            dataQuery.lte('cook_time', filters.cookTimeMax);
          }

          if (filters.costMax < 20) {
            dataQuery.lte('cost_per_serving', filters.costMax);
          }

          // Apply sorting
          dataQuery.order(filters.sortBy, { ascending: filters.sortOrder === 'asc' });

          // Apply pagination
          dataQuery.range(offset, offset + recipesPerPage - 1);

          const { data, error } = await dataQuery;

          if (error) {
            console.error('Error fetching recipes:', error);
            toast.error('Error fetching recipes. Using demo recipes instead.');
            const demoRecipes = getDemoRecipes();
            setRecipes(demoRecipes);
            setTotalRecipes(demoRecipes.length);
          } else if (data && data.length > 0) {
            // If we got recipes from Supabase, use them
            setRecipes(data);
          } else {
            // If no recipes found, use demo recipes
            toast('No recipes found. Using demo recipes.');
            const demoRecipes = getDemoRecipes();
            setRecipes(demoRecipes);
            setTotalRecipes(demoRecipes.length);
          }
        } catch (supabaseError) {
          console.error('Supabase error:', supabaseError);
          toast.error('Database error. Using demo recipes instead.');
          const demoRecipes = getDemoRecipes();
          setRecipes(demoRecipes);
          setTotalRecipes(demoRecipes.length);
        }
      } else {
        // If not authenticated, use demo recipes
        console.log('Not authenticated, using demo recipes');
        toast('Not logged in. Using demo recipes.');
        const demoRecipes = getDemoRecipes();
        setRecipes(demoRecipes);
        setTotalRecipes(demoRecipes.length);
      }
    } catch (error) {
      console.error('Error in recipe fetching process:', error);
      toast.error('An unexpected error occurred. Using demo recipes.');
      const demoRecipes = getDemoRecipes();
      setRecipes(demoRecipes);
      setTotalRecipes(demoRecipes.length);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle filter changes
  const handleFilterChange = (newFilters: FilterOptions) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
    fetchRecipes(1);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    fetchRecipes(page);
  };

  // Handle recipe selection
  const handleRecipeSelect = (recipeId: string) => {
    setSelectedRecipes(prev => {
      if (prev.includes(recipeId)) {
        return prev.filter(id => id !== recipeId);
      } else {
        return [...prev, recipeId];
      }
    });
  };

  // Toggle select mode
  const toggleSelectMode = () => {
    setIsSelectMode(!isSelectMode);
    if (isSelectMode) {
      setSelectedRecipes([]);
    }
  };

  // Clear selection
  const clearSelection = () => {
    setSelectedRecipes([]);
  };

  // Batch favorite/unfavorite
  const batchAddToFavorites = async (ids: string[]) => {
    try {
      // Update UI immediately
      setRecipes(prev =>
        prev.map(recipe =>
          ids.includes(recipe.id) ? { ...recipe, is_favorite: true } : recipe
        )
      );

      // Update in database
      for (const id of ids) {
        await fetch(`/api/recipes/${id}/favorite`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ is_favorite: true })
        });
      }

      toast.success(`Added ${ids.length} recipes to favorites`);
      clearSelection();
    } catch (error) {
      console.error('Error adding to favorites:', error);
      toast.error('Failed to update favorites');
      fetchRecipes(currentPage); // Refresh to get correct state
    }
  };

  const batchRemoveFromFavorites = async (ids: string[]) => {
    try {
      // Update UI immediately
      setRecipes(prev =>
        prev.map(recipe =>
          ids.includes(recipe.id) ? { ...recipe, is_favorite: false } : recipe
        )
      );

      // Update in database
      for (const id of ids) {
        await fetch(`/api/recipes/${id}/favorite`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ is_favorite: false })
        });
      }

      toast.success(`Removed ${ids.length} recipes from favorites`);
      clearSelection();
    } catch (error) {
      console.error('Error removing from favorites:', error);
      toast.error('Failed to update favorites');
      fetchRecipes(currentPage); // Refresh to get correct state
    }
  };

  // Batch delete
  const batchDeleteRecipes = async (ids: string[]) => {
    try {
      // Update UI immediately
      setRecipes(prev => prev.filter(recipe => !ids.includes(recipe.id)));

      // Delete from database
      for (const id of ids) {
        await fetch(`/api/recipes/${id}`, {
          method: 'DELETE',
        });
      }

      toast.success(`Deleted ${ids.length} recipes`);
      clearSelection();
      fetchRecipes(currentPage); // Refresh to get correct count
    } catch (error) {
      console.error('Error deleting recipes:', error);
      toast.error('Failed to delete recipes');
      fetchRecipes(currentPage); // Refresh to get correct state
    }
  };

  useEffect(() => {
    // Only fetch recipes if Supabase is available
    if (isSupabaseLoading || !supabase) {
      return;
    }

    fetchRecipes(1);
  }, [supabase, isSupabaseLoading]);

  // Reset page when search or tab changes
  useEffect(() => {
    if (!isSupabaseLoading && supabase) {
      setCurrentPage(1);
      fetchRecipes(1);
    }
  }, [searchQuery, activeTab, supabase, isSupabaseLoading]);

  const toggleFavorite = async (recipeId: string) => {
    try {
      // Find the recipe in the state
      const recipe = recipes.find(r => r.id === recipeId);
      if (!recipe) {
        toast.error('Recipe not found');
        return;
      }

      // Check if this is a demo recipe
      const isDemoRecipe = recipe.user_id === 'demo';

      // Update the UI immediately for a better user experience
      const updatedRecipes = recipes.map(r => {
        if (r.id === recipeId) {
          return { ...r, is_favorite: !r.is_favorite };
        }
        return r;
      });
      setRecipes(updatedRecipes);

      // If it's a demo recipe, just show a toast and don't try to update the database
      if (isDemoRecipe) {
        const newStatus = !recipe.is_favorite;
        toast.success(newStatus ? 'Added to favorites (demo mode)' : 'Removed from favorites (demo mode)');
        return;
      }

      // For real recipes, update the favorite status in the database
      try {
        const response = await fetch(`/api/recipes/${recipeId}/favorite`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to update favorite status');
        }

        const data = await response.json();
        toast.success(data.is_favorite ? 'Added to favorites' : 'Removed from favorites');
      } catch (apiError) {
        console.error('Error updating favorite status in API:', apiError);
        toast.error('Failed to update favorite status in database');
        // We don't revert the UI change for a better user experience
      }
    } catch (error) {
      console.error('Error in toggleFavorite function:', error);
      toast.error('An unexpected error occurred');

      // Revert the UI change if there was an unexpected error
      fetchRecipes();
    }
  };

  const handleBack = () => {
    router.push('/dashboard');
  };

  // Show loading state if either Supabase is loading or our component is loading
  if (isSupabaseLoading || isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          <p className="ml-4 text-muted-foreground">
            {isSupabaseLoading ? 'Initializing database connection...' : 'Loading recipes...'}
          </p>
        </div>
      </div>
    );
  }

  // Calculate total pages for pagination
  const totalPages = Math.max(1, Math.ceil(totalRecipes / recipesPerPage));

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="ghost" onClick={handleBack} className="mr-2">
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">Recipes</h1>
        </div>
        <div className="flex gap-2">
          <Button
            variant={isSelectMode ? "secondary" : "outline"}
            onClick={toggleSelectMode}
            className="flex items-center gap-2"
          >
            <CheckSquare className="h-4 w-4" />
            {isSelectMode ? "Cancel" : "Select"}
          </Button>
          <Button onClick={() => router.push('/recipes/create')}>
            <Plus className="h-4 w-4 mr-2" />
            Add Recipe
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search recipes..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button
          variant={showFilters ? "secondary" : "outline"}
          className="flex items-center gap-2"
          onClick={() => setShowFilters(!showFilters)}
        >
          <Filter className="h-4 w-4" />
          {showFilters ? "Hide Filters" : "Filter"}
        </Button>
      </div>

      {showFilters && (
        <AdvancedFilter
          initialFilters={filters}
          onFilterChange={handleFilterChange}
          onClose={() => setShowFilters(false)}
          cuisineOptions={cuisineOptions}
          difficultyOptions={difficultyOptions}
          dietaryOptions={dietaryRestrictionOptions}
        />
      )}

      <Tabs defaultValue="all" className="mb-6" onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="all">All Recipes</TabsTrigger>
          <TabsTrigger value="favorites">Favorites</TabsTrigger>
          <TabsTrigger value="Mediterranean">Mediterranean</TabsTrigger>
          <TabsTrigger value="Asian">Asian</TabsTrigger>
          <TabsTrigger value="Vegetarian">Vegetarian</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recipes.length > 0 ? (
              recipes.map((recipe) => (
                <RecipeCard
                  key={recipe.id}
                  recipe={recipe}
                  onToggleFavorite={toggleFavorite}
                  onDelete={(id) => batchDeleteRecipes([id])}
                  isSelectable={isSelectMode}
                  isSelected={selectedRecipes.includes(recipe.id)}
                  onSelect={handleRecipeSelect}
                />
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <p className="text-muted-foreground">No recipes found. Try adjusting your search.</p>
              </div>
            )}
          </div>

          {totalPages > 1 && (
            <RecipePagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          )}
        </TabsContent>
      </Tabs>

      <BatchActions
        selectedIds={selectedRecipes}
        onClearSelection={clearSelection}
        onAddToFavorites={batchAddToFavorites}
        onRemoveFromFavorites={batchRemoveFromFavorites}
        onDelete={batchDeleteRecipes}
      />
    </div>
  );
}
