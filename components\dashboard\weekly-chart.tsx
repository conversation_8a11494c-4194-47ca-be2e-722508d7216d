"use client";

import { useState, useRef } from 'react';
import { Card, CardContent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { useWeeklyChartData } from "@/app/hooks/useDashboardData";

const DATA = {
  calories: [
    { day: 'Mon', breakfast: 400, lunch: 600, dinner: 500, snacks: 300, target: 2000 },
    { day: 'Tue', breakfast: 350, lunch: 650, dinner: 550, snacks: 250, target: 2000 },
    { day: 'Wed', breakfast: 450, lunch: 550, dinner: 500, snacks: 350, target: 2000 },
    { day: 'Thu', breakfast: 400, lunch: 600, dinner: 450, snacks: 300, target: 2000 },
    { day: 'Fri', breakfast: 500, lunch: 500, dinner: 600, snacks: 200, target: 2000 },
    { day: 'Sat', breakfast: 400, lunch: 700, dinner: 500, snacks: 400, target: 2000 },
    { day: 'Sun', breakfast: 350, lunch: 650, dinner: 550, snacks: 300, target: 2000 },
  ],
  macros: [
    { day: 'Mon', protein: 120, carbs: 240, fats: 65 },
    { day: 'Tue', protein: 115, carbs: 220, fats: 70 },
    { day: 'Wed', protein: 125, carbs: 250, fats: 60 },
    { day: 'Thu', protein: 118, carbs: 235, fats: 68 },
    { day: 'Fri', protein: 122, carbs: 245, fats: 63 },
    { day: 'Sat', protein: 130, carbs: 260, fats: 70 },
    { day: 'Sun', protein: 116, carbs: 230, fats: 65 },
  ]
};

export function WeeklyChart() {
  const { data: chartData, isLoading, isError } = useWeeklyChartData();
  const [view, setView] = useState<'calories' | 'macros'>('calories');
  const [tooltipContent, setTooltipContent] = useState<string | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const chartRef = useRef<HTMLDivElement>(null);

  // Use real data or fallback to mock data
  const data = chartData || DATA;

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Weekly Overview</CardTitle>
              <CardDescription>Track your nutrition progress</CardDescription>
            </div>
            <Skeleton className="h-10 w-[180px]" />
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="h-[350px] w-full flex">
            <Skeleton className="h-[300px] w-full" />
          </div>
          <div className="flex justify-center mt-4 space-x-4">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-6 w-24" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (isError) {
    return (
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Weekly Overview</CardTitle>
              <CardDescription>Track your nutrition progress</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="h-[350px] w-full flex items-center justify-center">
            <p className="text-muted-foreground">Failed to load weekly data</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calculate the maximum value for scaling
  const getMaxValue = () => {
    if (view === 'calories') {
      return Math.max(...data.calories.map(item =>
        item.breakfast + item.lunch + item.dinner + item.snacks
      ));
    } else {
      return Math.max(
        ...data.macros.map(item => item.protein),
        ...data.macros.map(item => item.carbs),
        ...data.macros.map(item => item.fats)
      );
    }
  };

  const maxValue = getMaxValue();
  // Round up to the nearest 500 for better Y-axis labels
  const roundedMaxValue = Math.ceil(maxValue / 500) * 500;

  // Generate Y-axis labels
  const yAxisLabels = [];
  const step = roundedMaxValue / 5;
  for (let i = 0; i <= 5; i++) {
    yAxisLabels.push(Math.round(i * step));
  }

  const handleBarMouseEnter = (e: React.MouseEvent, item: any) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const chartRect = chartRef.current?.getBoundingClientRect() || { left: 0, top: 0 };

    let content = '';
    if (view === 'calories') {
      content = `${item.day}\nBreakfast: ${item.breakfast} cal\nLunch: ${item.lunch} cal\nDinner: ${item.dinner} cal\nSnacks: ${item.snacks} cal\nTotal: ${item.breakfast + item.lunch + item.dinner + item.snacks} cal`;
    } else {
      content = `${item.day}\nProtein: ${item.protein}g\nCarbs: ${item.carbs}g\nFats: ${item.fats}g`;
    }

    setTooltipContent(content);
    setTooltipPosition({
      x: rect.left - chartRect.left + rect.width / 2,
      y: rect.top - chartRect.top
    });
  };

  const handleBarMouseLeave = () => {
    setTooltipContent(null);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Weekly Overview</CardTitle>
            <CardDescription>Track your nutrition progress</CardDescription>
          </div>
          <Select
            value={view}
            onValueChange={(value: 'calories' | 'macros') => setView(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select view" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="calories">Calories</SelectItem>
              <SelectItem value="macros">Macronutrients</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <div className="h-[350px] w-full flex">
          {/* Y-axis */}
          <div className="flex flex-col justify-between h-[300px] pr-2 text-xs text-muted-foreground">
            {yAxisLabels.slice().reverse().map((label, index) => (
              <div key={index} className="flex items-center">
                <span>{label}{view === 'calories' ? '' : 'g'}</span>
                <div className="w-2 h-px bg-muted-foreground ml-1"></div>
              </div>
            ))}
          </div>

          {/* Chart area */}
          <div className="flex-1 relative" ref={chartRef}>
            {/* Target line for calories view */}
            {view === 'calories' && (
              <div
                className="absolute w-full border-t border-dashed border-red-500 z-10 flex justify-end"
                style={{
                  top: `${300 - (data.calories[0].target / roundedMaxValue * 300)}px`,
                  height: '1px'
                }}
              >
                <span className="text-xs text-red-500 pr-1 -mt-4">Target: {data.calories[0].target} cal</span>
              </div>
            )}

            {/* Horizontal grid lines */}
            {Array.from({ length: 6 }).map((_, index) => (
              <div
                key={index}
                className="absolute w-full border-t border-gray-200 dark:border-gray-800"
                style={{ top: `${index * (300 / 5)}px`, height: '1px' }}
              />
            ))}

            {/* Bars */}
            <div className="h-[300px] w-full relative">
              {view === 'calories' ? (
                <div className="flex h-full items-end space-x-2">
                  {data.calories.map((item) => (
                    <div
                      key={item.day}
                      className="flex flex-col items-center flex-1"
                      onMouseEnter={(e) => handleBarMouseEnter(e, item)}
                      onMouseLeave={handleBarMouseLeave}
                    >
                      <div className="w-full flex flex-col-reverse cursor-pointer">
                        <div
                          className="w-full bg-orange-500 rounded-t-sm"
                          style={{ height: `${(item.breakfast / roundedMaxValue) * 300}px` }}
                        />
                        <div
                          className="w-full bg-cyan-600 rounded-t-sm"
                          style={{ height: `${(item.lunch / roundedMaxValue) * 300}px` }}
                        />
                        <div
                          className="w-full bg-cyan-800 rounded-t-sm"
                          style={{ height: `${(item.dinner / roundedMaxValue) * 300}px` }}
                        />
                        <div
                          className="w-full bg-amber-500 rounded-t-sm"
                          style={{ height: `${(item.snacks / roundedMaxValue) * 300}px` }}
                        />
                      </div>
                      <div className="text-xs mt-2">{item.day}</div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex h-full items-end space-x-2">
                  {data.macros.map((item) => (
                    <div
                      key={item.day}
                      className="flex flex-col items-center flex-1"
                      onMouseEnter={(e) => handleBarMouseEnter(e, item)}
                      onMouseLeave={handleBarMouseLeave}
                    >
                      <div className="w-full flex space-x-1 cursor-pointer">
                        <div
                          className="flex-1 bg-orange-500 rounded-t-sm"
                          style={{ height: `${(item.protein / roundedMaxValue) * 300}px` }}
                        />
                        <div
                          className="flex-1 bg-cyan-600 rounded-t-sm"
                          style={{ height: `${(item.carbs / roundedMaxValue) * 300}px` }}
                        />
                        <div
                          className="flex-1 bg-cyan-800 rounded-t-sm"
                          style={{ height: `${(item.fats / roundedMaxValue) * 300}px` }}
                        />
                      </div>
                      <div className="text-xs mt-2">{item.day}</div>
                    </div>
                  ))}
                </div>
              )}

              {/* Tooltip */}
              {tooltipContent && (
                <div
                  className="absolute bg-background border rounded-lg p-2 shadow-lg z-20 whitespace-pre-line text-xs"
                  style={{
                    left: `${tooltipPosition.x}px`,
                    top: `${tooltipPosition.y - 120}px`,
                    transform: 'translateX(-50%)'
                  }}
                >
                  {view === 'calories' ? (
                    <>
                      <div className="font-medium">{tooltipContent.split('\n')[0]}</div>
                      <div className="text-orange-500">{tooltipContent.split('\n')[1]}</div>
                      <div className="text-cyan-600">{tooltipContent.split('\n')[2]}</div>
                      <div className="text-cyan-800">{tooltipContent.split('\n')[3]}</div>
                      <div className="text-amber-500">{tooltipContent.split('\n')[4]}</div>
                      <div className="font-medium mt-1">{tooltipContent.split('\n')[5]}</div>
                    </>
                  ) : (
                    <>
                      <div className="font-medium">{tooltipContent.split('\n')[0]}</div>
                      <div className="text-orange-500">{tooltipContent.split('\n')[1]}</div>
                      <div className="text-cyan-600">{tooltipContent.split('\n')[2]}</div>
                      <div className="text-cyan-800">{tooltipContent.split('\n')[3]}</div>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Legend */}
        <div className="flex justify-center mt-4 space-x-4">
          {view === 'calories' ? (
            <>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                <span className="text-sm">Breakfast</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-cyan-600 rounded-full mr-2"></div>
                <span className="text-sm">Lunch</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-cyan-800 rounded-full mr-2"></div>
                <span className="text-sm">Dinner</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-amber-500 rounded-full mr-2"></div>
                <span className="text-sm">Snacks</span>
              </div>
            </>
          ) : (
            <>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                <span className="text-sm">Protein</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-cyan-600 rounded-full mr-2"></div>
                <span className="text-sm">Carbs</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-cyan-800 rounded-full mr-2"></div>
                <span className="text-sm">Fats</span>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
