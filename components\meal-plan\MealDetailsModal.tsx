"use client";

import { Meal } from '@/types/new-meal-plan';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Clock, DollarSign, Flame, Users } from 'lucide-react';

interface MealDetailsModalProps {
  meal: Meal | null;
  isOpen: boolean;
  onClose: () => void;
}

export function MealDetailsModal({ meal, isOpen, onClose }: MealDetailsModalProps) {
  if (!meal) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">{meal.name}</DialogTitle>
          <DialogDescription className="flex flex-wrap gap-2 mt-2">
            <div className="flex items-center text-sm">
              <Clock className="mr-1 h-4 w-4" />
              <span>{meal.prepTime} min</span>
            </div>
            <div className="flex items-center text-sm">
              <Flame className="mr-1 h-4 w-4" />
              <span>{meal.calories} cal</span>
            </div>
            <div className="flex items-center text-sm">
              <DollarSign className="mr-1 h-4 w-4" />
              <span>${meal.cost.toFixed(2)}</span>
            </div>
            <div className="flex items-center text-sm">
              <Users className="mr-1 h-4 w-4" />
              <span>{meal.servings} servings</span>
            </div>
          </DialogDescription>
        </DialogHeader>

        {meal.image && (
          <div className="relative w-full h-48 mb-4 overflow-hidden rounded-md">
            <img
              src={meal.image}
              alt={meal.name}
              className="object-cover w-full h-full"
            />
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium mb-2">Ingredients</h3>
            <ul className="space-y-2">
              {meal.ingredients.map((ingredient, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>
                    {ingredient.quantity} {ingredient.unit} {ingredient.name}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="font-medium mb-2">Nutrition</h3>
            <div className="grid grid-cols-3 gap-2 mb-4">
              <div className="bg-primary/10 p-3 rounded-md text-center">
                <div className="text-sm text-muted-foreground">Protein</div>
                <div className="font-medium">{meal.nutrition.protein}g</div>
              </div>
              <div className="bg-primary/10 p-3 rounded-md text-center">
                <div className="text-sm text-muted-foreground">Carbs</div>
                <div className="font-medium">{meal.nutrition.carbs}g</div>
              </div>
              <div className="bg-primary/10 p-3 rounded-md text-center">
                <div className="text-sm text-muted-foreground">Fat</div>
                <div className="font-medium">{meal.nutrition.fat}g</div>
              </div>
            </div>

            <h3 className="font-medium mb-2">Instructions</h3>
            <ol className="space-y-2 list-decimal list-inside">
              {meal.instructions.map((instruction, index) => (
                <li key={index} className="text-sm">
                  {instruction}
                </li>
              ))}
            </ol>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
