'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Minus, Plus } from 'lucide-react';
import { Label } from '@/components/ui/label';

interface ServingsAdjusterProps {
  initialServings: number;
  minServings?: number;
  maxServings?: number;
  onChange: (newServings: number, scaleFactor: number) => void;
}

export function ServingsAdjuster({
  initialServings,
  minServings = 1,
  maxServings = 12,
  onChange
}: ServingsAdjusterProps) {
  const [servings, setServings] = useState(initialServings);

  // Calculate scale factor whenever servings change
  useEffect(() => {
    const scaleFactor = servings / initialServings;
    onChange(servings, scaleFactor);
  }, [servings, initialServings, onChange]);

  const decreaseServings = () => {
    if (servings > minServings) {
      setServings(prev => prev - 1);
    }
  };

  const increaseServings = () => {
    if (servings < maxServings) {
      setServings(prev => prev + 1);
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <Label htmlFor="servings-adjuster" className="text-sm font-medium">
        Servings:
      </Label>
      <div className="flex items-center border rounded-md">
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 rounded-r-none"
          onClick={decreaseServings}
          disabled={servings <= minServings}
        >
          <Minus className="h-3 w-3" />
        </Button>
        <div className="px-3 py-1 text-center min-w-[40px]">
          {servings}
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 rounded-l-none"
          onClick={increaseServings}
          disabled={servings >= maxServings}
        >
          <Plus className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
}
