'use client';

import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PlusCircle, ShoppingCart, Calendar, Settings } from 'lucide-react';
import { useRouter } from 'next/navigation';

export function QuickActions() {
  const router = useRouter();

  const actions = [
    {
      title: 'Generate Meal Plan',
      description: 'Create a new personalized meal plan',
      icon: PlusCircle,
      href: '/meal-plan/generate',
      variant: 'default' as const,
    },
    {
      title: 'Shopping List',
      description: 'View and manage your shopping list',
      icon: ShoppingCart,
      href: '/shopping-list',
      variant: 'outline' as const,
    },
    {
      title: 'Calendar',
      description: 'View your meal calendar',
      icon: Calendar,
      href: '/calendar',
      variant: 'outline' as const,
    },
    {
      title: 'Settings',
      description: 'Update your profile and preferences',
      icon: Settings,
      href: '/settings',
      variant: 'outline' as const,
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent className="grid gap-4 md:grid-cols-2">
        {actions.map((action) => {
          const Icon = action.icon;
          return (
            <Button
              key={action.href}
              variant={action.variant}
              className="h-24 flex flex-col items-center justify-center space-y-2"
              onClick={() => router.push(action.href)}
            >
              <Icon className="h-6 w-6" />
              <div className="text-center">
                <div className="font-medium">{action.title}</div>
                <div className="text-xs text-muted-foreground">
                  {action.description}
                </div>
              </div>
            </Button>
          );
        })}
      </CardContent>
    </Card>
  );
}

