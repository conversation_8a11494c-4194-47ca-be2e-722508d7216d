-- Fix RLS policies for shopping_items table using the correct column name

-- Enable RLS
ALTER TABLE public.shopping_items ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own shopping_items" ON public.shopping_items;
DROP POLICY IF EXISTS "Users can insert their own shopping_items" ON public.shopping_items;
DROP POLICY IF EXISTS "Users can update their own shopping_items" ON public.shopping_items;
DROP POLICY IF EXISTS "Users can delete their own shopping_items" ON public.shopping_items;

-- Check the data type of user_id in shopping_lists
DO $$
DECLARE
    column_type TEXT;
BEGIN
    SELECT data_type INTO column_type
    FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'shopping_lists'
    AND column_name = 'user_id';
    
    RAISE NOTICE 'shopping_lists.user_id type: %', column_type;
    
    IF column_type = 'uuid' THEN
        -- For UUID type
        CREATE POLICY "Users can view their own shopping_items" ON public.shopping_items FOR SELECT 
        USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));
        
        CREATE POLICY "Users can insert their own shopping_items" ON public.shopping_items FOR INSERT 
        WITH CHECK (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));
        
        CREATE POLICY "Users can update their own shopping_items" ON public.shopping_items FOR UPDATE 
        USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()))
        WITH CHECK (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));
        
        CREATE POLICY "Users can delete their own shopping_items" ON public.shopping_items FOR DELETE 
        USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()));
    ELSE
        -- For text type
        CREATE POLICY "Users can view their own shopping_items" ON public.shopping_items FOR SELECT 
        USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));
        
        CREATE POLICY "Users can insert their own shopping_items" ON public.shopping_items FOR INSERT 
        WITH CHECK (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));
        
        CREATE POLICY "Users can update their own shopping_items" ON public.shopping_items FOR UPDATE 
        USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text))
        WITH CHECK (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));
        
        CREATE POLICY "Users can delete their own shopping_items" ON public.shopping_items FOR DELETE 
        USING (shopping_list_id IN (SELECT id FROM public.shopping_lists WHERE user_id = auth.uid()::text));
    END IF;
END
$$;

GRANT ALL ON public.shopping_items TO authenticated;
