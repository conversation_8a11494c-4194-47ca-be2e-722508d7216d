import { AUTH_COOKIE_PREFIX } from '@/constants/auth';

interface CookieAttributes {
  path?: string;
  domain?: string;
  secure?: boolean;
  sameSite?: 'Strict' | 'Lax' | 'None';
  maxAge?: number;
  expires?: Date;
}

export const COOKIE_OPTIONS: CookieAttributes = {
  path: '/',
  secure: true,
  sameSite: 'Lax',
  maxAge: 60 * 60 * 24 * 7, // 7 days
};

// Client-side only cookie operations
export const setCookie = (name: string, value: string, options: Partial<CookieAttributes> = {}) => {
  if (typeof window === 'undefined') return;

  try {
    const mergedOptions = { ...COOKIE_OPTIONS, ...options };
    let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;

    if (mergedOptions.path) cookieString += `; path=${mergedOptions.path}`;
    if (mergedOptions.domain) cookieString += `; domain=${mergedOptions.domain}`;
    if (mergedOptions.maxAge) cookieString += `; max-age=${mergedOptions.maxAge}`;
    if (mergedOptions.expires) cookieString += `; expires=${mergedOptions.expires.toUTCString()}`;
    if (mergedOptions.secure) cookieString += '; secure';
    if (mergedOptions.sameSite) cookieString += `; samesite=${mergedOptions.sameSite.toLowerCase()}`;

    document.cookie = cookieString;
  } catch (error) {
    console.error('Error setting cookie:', error);
  }
};

export const getCookie = (name: string): string | null => {
  if (typeof window === 'undefined') return null;

  try {
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [cookieName, cookieValue] = cookie.split('=').map(c => c.trim());
      if (cookieName === name) {
        return decodeURIComponent(cookieValue);
      }
    }
    return null;
  } catch (error) {
    console.error('Error getting cookie:', error);
    return null;
  }
};

export const deleteCookie = (name: string, options: Partial<CookieAttributes> = {}) => {
  if (typeof window === 'undefined') return;

  try {
    setCookie(name, '', {
      ...options,
      expires: new Date(0),
      maxAge: 0,
    });
  } catch (error) {
    console.error('Error deleting cookie:', error);
  }
};

// Clear all Supabase-related cookies on the client side
export const clearAuthCookies = () => {
  if (typeof window === 'undefined') return;

  try {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
      const cookieName = cookie.split('=')[0].trim();
      if (cookieName.startsWith(AUTH_COOKIE_PREFIX)) {
        deleteCookie(cookieName);
      }
    }
  } catch (error) {
    console.error('Error clearing auth cookies:', error);
  }
};


