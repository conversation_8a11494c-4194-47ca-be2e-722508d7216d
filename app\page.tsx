import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, ChefHat, DollarSign, Clock, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <main className="flex-1">
        <section className="w-full py-12 md:py-24 lg:py-32 xl:py-48 bg-background">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
              <div className="flex flex-col justify-center space-y-4">
                <div className="space-y-2">
                  <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/60">
                    Plan Affordable Meals in Minutes
                  </h1>
                  <p className="max-w-[600px] text-zinc-500 md:text-xl dark:text-zinc-400">
                    Save money and eat well with personalized meal plans tailored to your budget. Perfect for families, students, and anyone looking to optimize their food spending.
                  </p>
                </div>
                <div className="flex flex-col gap-2 min-[400px]:flex-row">
                  <Link href="/signup">
                    <Button size="lg" className="w-full min-[400px]:w-auto">
                      Start Free Trial
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                  <Link href="/login">
                    <Button variant="outline" size="lg" className="w-full min-[400px]:w-auto">
                      Log in
                    </Button>
                  </Link>
                </div>
                <div className="flex items-center space-x-4 text-sm">
                  <div className="flex items-center space-x-1">
                    <ChefHat className="h-4 w-4" />
                    <span className="text-zinc-500 dark:text-zinc-400">1000+ Recipes</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <DollarSign className="h-4 w-4" />
                    <span className="text-zinc-500 dark:text-zinc-400">Budget Friendly</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span className="text-zinc-500 dark:text-zinc-400">Quick Setup</span>
                  </div>
                </div>
              </div>
              <div className="mx-auto flex items-center justify-center">
                <Image
                  alt="Hero"
                  className="aspect-video overflow-hidden rounded-xl object-cover object-center"
                  height="550"
                  src="https://images.unsplash.com/photo-1547592180-85f173990554?q=80&w=1470&auto=format&fit=crop"
                  width="550"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="w-full py-12 md:py-24 lg:py-32 bg-zinc-50 dark:bg-zinc-900">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Why LeanEats?</h2>
                <p className="max-w-[900px] text-zinc-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-zinc-400">
                  Everything you need to plan affordable, healthy meals for you and your family.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-5xl items-center gap-6 py-12 lg:grid-cols-3 lg:gap-12">
              <div className="flex flex-col justify-center space-y-4">
                <DollarSign className="mx-auto h-12 w-12" />
                <h3 className="text-xl font-bold text-center">Save Money</h3>
                <p className="text-zinc-500 dark:text-zinc-400 text-center">
                  Optimize your grocery spending with budget-conscious meal plans and smart shopping lists.
                </p>
              </div>
              <div className="flex flex-col justify-center space-y-4">
                <ChefHat className="mx-auto h-12 w-12" />
                <h3 className="text-xl font-bold text-center">Personalized Plans</h3>
                <p className="text-zinc-500 dark:text-zinc-400 text-center">
                  Get meal plans tailored to your dietary needs, preferences, and household size.
                </p>
              </div>
              <div className="flex flex-col justify-center space-y-4">
                <Users className="mx-auto h-12 w-12" />
                <h3 className="text-xl font-bold text-center">Family Friendly</h3>
                <p className="text-zinc-500 dark:text-zinc-400 text-center">
                  Perfect for families of all sizes, with recipes everyone will love.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                  Loved by Families
                </h2>
                <p className="max-w-[900px] text-zinc-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-zinc-400">
                  See what our users are saying about LeanEats.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-5xl items-center gap-6 py-12 lg:grid-cols-2 lg:gap-12">
              <div className="flex flex-col justify-center space-y-4 bg-white dark:bg-zinc-800 p-6 rounded-lg shadow-lg">
                <p className="text-lg italic">"LeanEats saved me $50 a week on groceries while helping me cook healthier meals for my family!"</p>
                <p className="font-semibold">- Sarah M.</p>
              </div>
              <div className="flex flex-col justify-center space-y-4 bg-white dark:bg-zinc-800 p-6 rounded-lg shadow-lg">
                <p className="text-lg italic">"As a student, this app has been a game-changer for my budget and nutrition."</p>
                <p className="font-semibold">- James K.</p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="w-full py-12 md:py-24 lg:py-32 bg-primary">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter text-white sm:text-4xl md:text-5xl">
                  Ready to Start Saving?
                </h2>
                <p className="max-w-[600px] text-zinc-200 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  Join thousands of families who are saving money and eating better with LeanEats.
                </p>
              </div>
              <Link href="/signup">
                <Button size="lg" variant="secondary" className="w-full min-[400px]:w-auto">
                  Get Started Now
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="w-full py-6 bg-zinc-50 dark:bg-zinc-900">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
            <div className="flex gap-4">
              <Link href="/privacy" className="text-sm text-zinc-500 dark:text-zinc-400">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-sm text-zinc-500 dark:text-zinc-400">
                Terms of Service
              </Link>
            </div>
            <p className="text-sm text-zinc-500 dark:text-zinc-400">
              © 2025 LeanEats. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}