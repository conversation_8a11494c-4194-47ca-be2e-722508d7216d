"use client";

import { useState } from 'react';
import { MealPlanHeader } from '@/components/meal-plan/MealPlanHeader';

export default function TestPage3() {
  const [currentView, setCurrentView] = useState<'calendar' | 'list'>('calendar');
  const [currentLayout, setCurrentLayout] = useState<'grid' | 'list'>('grid');

  return (
    <div className="container py-6 space-y-6">
      <MealPlanHeader
        onViewChange={setCurrentView}
        onLayoutChange={setCurrentLayout}
        onGeneratePlan={() => alert('Generate Plan')}
        onAddMeal={() => alert('Add Meal')}
        onViewShoppingList={() => alert('View Shopping List')}
        currentView={currentView}
        currentLayout={currentLayout}
      />
      
      <div className="p-4 border rounded-md">
        <p>Current View: {currentView}</p>
        <p>Current Layout: {currentLayout}</p>
      </div>
    </div>
  );
}
