import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSupabase } from '@/app/providers';
import { FavoriteMeal } from '@/types/database-extended';

export function useFavorites() {
  const { supabase } = useSupabase();
  const queryClient = useQueryClient();

  const {
    data: favorites,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['favorites'],
    queryFn: async () => {
      const response = await fetch('/api/favorites');
      if (!response.ok) {
        throw new Error('Failed to fetch favorites');
      }
      const result = await response.json();
      return result.data as FavoriteMeal[];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const addToFavoritesMutation = useMutation({
    mutationFn: async (recipeId: string) => {
      const response = await fetch('/api/favorites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ recipe_id: recipeId }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to add to favorites');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['favorites'] });
    },
  });

  const removeFromFavoritesMutation = useMutation({
    mutationFn: async (recipeId: string) => {
      const response = await fetch(`/api/favorites/${recipeId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to remove from favorites');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['favorites'] });
    },
  });

  const addToFavorites = (recipeId: string) => {
    return addToFavoritesMutation.mutateAsync(recipeId);
  };

  const removeFromFavorites = (recipeId: string) => {
    return removeFromFavoritesMutation.mutateAsync(recipeId);
  };

  const isFavorite = (recipeId: string) => {
    return favorites?.some(fav => fav.recipe_id === recipeId) || false;
  };

  const toggleFavorite = async (recipeId: string) => {
    if (isFavorite(recipeId)) {
      await removeFromFavorites(recipeId);
    } else {
      await addToFavorites(recipeId);
    }
  };

  return {
    favorites: favorites || [],
    isLoading,
    error,
    refetch,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
    isFavorite,
    isAddingToFavorites: addToFavoritesMutation.isPending,
    isRemovingFromFavorites: removeFromFavoritesMutation.isPending,
  };
}

export function useFavoriteStatus(recipeId: string) {
  const queryClient = useQueryClient();

  const {
    data: isFavorite,
    isLoading,
    error
  } = useQuery({
    queryKey: ['favorite-status', recipeId],
    queryFn: async () => {
      const response = await fetch(`/api/favorites/${recipeId}`);
      if (!response.ok) {
        throw new Error('Failed to check favorite status');
      }
      const result = await response.json();
      return result.is_favorite as boolean;
    },
    enabled: !!recipeId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const toggleFavoriteMutation = useMutation({
    mutationFn: async () => {
      if (isFavorite) {
        const response = await fetch(`/api/favorites/${recipeId}`, {
          method: 'DELETE',
        });
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to remove from favorites');
        }
        return false;
      } else {
        const response = await fetch('/api/favorites', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ recipe_id: recipeId }),
        });
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to add to favorites');
        }
        return true;
      }
    },
    onSuccess: (newStatus) => {
      queryClient.setQueryData(['favorite-status', recipeId], newStatus);
      queryClient.invalidateQueries({ queryKey: ['favorites'] });
    },
  });

  const toggleFavorite = () => {
    return toggleFavoriteMutation.mutateAsync();
  };

  return {
    isFavorite: isFavorite || false,
    isLoading,
    error,
    toggleFavorite,
    isToggling: toggleFavoriteMutation.isPending,
  };
}
