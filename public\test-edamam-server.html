<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Edamam API Server Test</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1, h2 {
      color: #333;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-bottom: 20px;
    }
    button:hover {
      background-color: #45a049;
    }
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
    }
    .error {
      color: #d9534f;
      font-weight: bold;
    }
    .success {
      color: #5cb85c;
      font-weight: bold;
    }
    .test-section {
      margin-bottom: 30px;
      border: 1px solid #ddd;
      padding: 15px;
      border-radius: 4px;
    }
    .environment {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
      margin-bottom: 20px;
    }
    .environment div {
      padding: 10px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }
    .environment .key {
      font-weight: bold;
    }
    .environment .value.set {
      color: #5cb85c;
    }
    .environment .value.not-set {
      color: #d9534f;
    }
  </style>
</head>
<body>
  <h1>Edamam API Server Test</h1>
  <p>This page tests the Edamam API from the server-side environment.</p>
  
  <button id="run-test">Run Server Test</button>
  
  <div id="environment-section" style="display: none;">
    <h2>Environment Variables</h2>
    <div class="environment" id="environment-vars"></div>
  </div>
  
  <div class="test-section" id="recipe-api-section" style="display: none;">
    <h2>Recipe API Test Results</h2>
    <pre id="recipe-api-results">No results yet. Click "Run Server Test" to run the test.</pre>
  </div>
  
  <div class="test-section" id="food-db-api-section" style="display: none;">
    <h2>Food Database API Test Results</h2>
    <pre id="food-db-api-results">No results yet. Click "Run Server Test" to run the test.</pre>
  </div>
  
  <div class="test-section" id="errors-section" style="display: none;">
    <h2>Errors</h2>
    <pre id="errors-results">No errors.</pre>
  </div>
  
  <script>
    document.getElementById('run-test').addEventListener('click', async () => {
      const button = document.getElementById('run-test');
      button.disabled = true;
      button.textContent = 'Running Test...';
      
      try {
        const response = await fetch('/api/test/edamam');
        const data = await response.json();
        
        // Display environment variables
        const environmentSection = document.getElementById('environment-section');
        const environmentVars = document.getElementById('environment-vars');
        environmentSection.style.display = 'block';
        environmentVars.innerHTML = '';
        
        for (const [key, value] of Object.entries(data.environment)) {
          const div = document.createElement('div');
          div.innerHTML = `<span class="key">${key}:</span> <span class="value ${value === 'Set' ? 'set' : 'not-set'}">${value}</span>`;
          environmentVars.appendChild(div);
        }
        
        // Display Recipe API test results
        const recipeApiSection = document.getElementById('recipe-api-section');
        const recipeApiResults = document.getElementById('recipe-api-results');
        recipeApiSection.style.display = 'block';
        
        if (data.recipeApiTest) {
          if (data.recipeApiTest.success) {
            recipeApiResults.innerHTML = `<span class="success">${data.recipeApiTest.message}</span>\n\nFound ${data.recipeApiTest.recipeCount} recipes\nFirst recipe: ${data.recipeApiTest.firstRecipe}\n\nFull response:\n${JSON.stringify(data.recipeApiTest, null, 2)}`;
          } else {
            recipeApiResults.innerHTML = `<span class="error">${data.recipeApiTest.message}</span>\n\n${data.recipeApiTest.error ? `Error: ${data.recipeApiTest.error}` : ''}\n\nFull response:\n${JSON.stringify(data.recipeApiTest, null, 2)}`;
          }
        } else {
          recipeApiResults.innerHTML = 'No Recipe API test results.';
        }
        
        // Display Food Database API test results
        const foodDbApiSection = document.getElementById('food-db-api-section');
        const foodDbApiResults = document.getElementById('food-db-api-results');
        foodDbApiSection.style.display = 'block';
        
        if (data.foodDbApiTest) {
          if (data.foodDbApiTest.success) {
            let resultsHtml = `<span class="success">${data.foodDbApiTest.message}</span>\n\nFound ${data.foodDbApiTest.foodCount} food items\nFirst food item: ${data.foodDbApiTest.firstFood}\n\n`;
            
            if (data.foodDbApiTest.nutrientsTest) {
              if (data.foodDbApiTest.nutrientsTest.success) {
                resultsHtml += `<span class="success">${data.foodDbApiTest.nutrientsTest.message}</span>\n\nCalories: ${data.foodDbApiTest.nutrientsTest.calories}\nProtein: ${JSON.stringify(data.foodDbApiTest.nutrientsTest.protein)}\nCarbs: ${JSON.stringify(data.foodDbApiTest.nutrientsTest.carbs)}\nFat: ${JSON.stringify(data.foodDbApiTest.nutrientsTest.fat)}\n\n`;
              } else {
                resultsHtml += `<span class="error">${data.foodDbApiTest.nutrientsTest.message}</span>\n\n${data.foodDbApiTest.nutrientsTest.error ? `Error: ${data.foodDbApiTest.nutrientsTest.error}` : ''}\n\n`;
              }
            }
            
            resultsHtml += `Full response:\n${JSON.stringify(data.foodDbApiTest, null, 2)}`;
            foodDbApiResults.innerHTML = resultsHtml;
          } else {
            foodDbApiResults.innerHTML = `<span class="error">${data.foodDbApiTest.message}</span>\n\n${data.foodDbApiTest.error ? `Error: ${data.foodDbApiTest.error}` : ''}\n\nFull response:\n${JSON.stringify(data.foodDbApiTest, null, 2)}`;
          }
        } else {
          foodDbApiResults.innerHTML = 'No Food Database API test results.';
        }
        
        // Display errors
        const errorsSection = document.getElementById('errors-section');
        const errorsResults = document.getElementById('errors-results');
        
        if (data.errors && data.errors.length > 0) {
          errorsSection.style.display = 'block';
          errorsResults.innerHTML = JSON.stringify(data.errors, null, 2);
        } else {
          errorsSection.style.display = 'none';
        }
      } catch (error) {
        alert(`Error running test: ${error.message}`);
      } finally {
        button.disabled = false;
        button.textContent = 'Run Server Test';
      }
    });
  </script>
</body>
</html>
