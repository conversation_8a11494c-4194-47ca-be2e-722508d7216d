"use client";

import { useState } from 'react';
import { format, addWeeks, startOfWeek, endOfWeek } from 'date-fns';
import { Calendar, ChevronLeft, ChevronRight, ShoppingCart } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { useMealPlanStore } from '@/lib/stores/meal-plan-store';
import { cn } from '@/lib/utils';
import { MealCard } from '@/components/meal-plan/MealCard';
import { generateShoppingList } from '@/lib/meal-plan-utils';
import { ViewShoppingListModal } from '@/components/meal-plan/ViewShoppingListModal';

interface MealPlanDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  plan: any;
}

export function MealPlanDetailsModal({ isOpen, onClose, plan }: MealPlanDetailsModalProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'meals' | 'nutrition'>('overview');
  const [selectedWeek, setSelectedWeek] = useState<Date>(new Date());
  const [isWeekSelectorOpen, setIsWeekSelectorOpen] = useState(false);
  const [isShoppingListModalOpen, setIsShoppingListModalOpen] = useState(false);
  const [shoppingList, setShoppingList] = useState<any[]>([]);
  const [pantryItems, setPantryItems] = useState<any[]>([]);

  const { addMeal, mealPlan } = useMealPlanStore();

  // Calculate week range for display
  const startDate = startOfWeek(selectedWeek, { weekStartsOn: 1 });
  const endDate = endOfWeek(selectedWeek, { weekStartsOn: 1 });

  // Navigate between weeks
  const goToNextWeek = () => setSelectedWeek(addWeeks(selectedWeek, 1));
  const goToPreviousWeek = () => setSelectedWeek(addWeeks(selectedWeek, -1));

  // Handle adding plan to calendar
  const handleAddToCalendar = () => {
    if (!plan || !plan.plan) {
      toast.error('No meal plan to add');
      return;
    }

    try {
      // Get the selected week's start date
      const weekStart = startOfWeek(selectedWeek, { weekStartsOn: 1 });

      // Track conflicts
      const conflicts: { date: string; mealType: string; existingMeal: string; newMeal: string }[] = [];

      // Check for conflicts first
      Object.entries(plan.plan).forEach(([dateStr, dayMeals]: [string, any], index) => {
        // Calculate the target date in the selected week
        const targetDate = new Date(weekStart);
        targetDate.setDate(targetDate.getDate() + index);
        const targetDateStr = targetDate.toISOString().split('T')[0];

        // Check each meal for conflicts
        Object.entries(dayMeals).forEach(([mealType, meal]: [string, any]) => {
          if (mealPlan?.[targetDateStr]?.[mealType]) {
            conflicts.push({
              date: targetDateStr,
              mealType,
              existingMeal: mealPlan[targetDateStr][mealType].name,
              newMeal: meal.name
            });
          }
        });
      });

      // If there are conflicts, show a warning
      if (conflicts.length > 0) {
        // Format the conflict message
        const conflictMessage = conflicts.map(conflict =>
          `${format(new Date(conflict.date), 'MMM d')} - ${conflict.mealType}: "${conflict.existingMeal}" will be replaced with "${conflict.newMeal}"`
        ).join('\n');

        // Confirm with the user
        if (!window.confirm(`The following meals will be replaced:\n\n${conflictMessage}\n\nDo you want to continue?`)) {
          return; // User cancelled
        }
      }

      // Add each meal to the calendar for the selected week
      Object.entries(plan.plan).forEach(([dateStr, dayMeals]: [string, any], index) => {
        // Calculate the target date in the selected week
        const targetDate = new Date(weekStart);
        targetDate.setDate(targetDate.getDate() + index);
        const targetDateStr = targetDate.toISOString().split('T')[0];

        // Add each meal for this day
        Object.entries(dayMeals).forEach(([mealType, meal]: [string, any]) => {
          addMeal(targetDateStr, mealType, meal);
        });
      });

      if (conflicts.length > 0) {
        toast.success(`Meal plan added to week of ${format(weekStart, 'MMM d, yyyy')} (${conflicts.length} meals replaced)`);
      } else {
        toast.success(`Meal plan added to week of ${format(weekStart, 'MMM d, yyyy')}`);
      }

      onClose();
    } catch (error) {
      console.error('Error adding meal plan to calendar:', error);
      toast.error('Failed to add meal plan to calendar');
    }
  };

  // Handle view shopping list
  const handleViewShoppingList = async () => {
    try {
      const { shoppingList, pantryItems } = await generateShoppingList(plan.plan);
      setShoppingList(shoppingList);
      setPantryItems(pantryItems);
      setIsShoppingListModalOpen(true);
    } catch (error) {
      console.error('Error generating shopping list:', error);
      toast.error('Failed to generate shopping list');
    }
  };

  // Calculate plan stats
  const calculatePlanStats = () => {
    if (!plan || !plan.plan) {
      return {
        totalCost: 0,
        averageCalories: 0,
        macros: { protein: 30, carbs: 40, fats: 30 },
        mealCount: 0,
        totalDays: 7
      };
    }

    let totalCost = 0;
    let totalCalories = 0;
    let mealCount = 0;
    let totalProtein = 0;
    let totalCarbs = 0;
    let totalFats = 0;

    // Process all days in the meal plan
    Object.entries(plan.plan).forEach(([dateStr, dayMeals]: [string, any]) => {
      // Process each meal in the day
      Object.values(dayMeals).forEach((meal: any) => {
        totalCost += meal.cost || 0;
        totalCalories += meal.calories || 0;
        totalProtein += meal.nutrition?.protein || 0;
        totalCarbs += meal.nutrition?.carbs || 0;
        totalFats += meal.nutrition?.fat || 0;
        mealCount++;
      });
    });

    const averageCalories = mealCount > 0 ? Math.round(totalCalories / mealCount) : 0;

    // Calculate macro percentages
    const totalMacros = totalProtein + totalCarbs + totalFats;
    const macros = {
      protein: totalMacros > 0 ? Math.round((totalProtein / totalMacros) * 100) : 30,
      carbs: totalMacros > 0 ? Math.round((totalCarbs / totalMacros) * 100) : 40,
      fats: totalMacros > 0 ? Math.round((totalFats / totalMacros) * 100) : 30
    };

    return {
      totalCost: parseFloat(totalCost.toFixed(2)),
      averageCalories,
      macros,
      mealCount,
      totalDays: Object.keys(plan.plan).length
    };
  };

  const planStats = calculatePlanStats();

  // Get all meals from the plan
  const getAllMeals = () => {
    if (!plan || !plan.plan) return [];

    const meals: any[] = [];

    Object.entries(plan.plan).forEach(([dateStr, dayMeals]: [string, any]) => {
      Object.entries(dayMeals).forEach(([mealType, meal]: [string, any]) => {
        meals.push({
          date: dateStr,
          mealType,
          ...meal
        });
      });
    });

    return meals;
  };

  const allMeals = getAllMeals();

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl flex items-center gap-2">
              {plan?.name || 'Meal Plan Details'}
              {plan?.tags && plan.tags.length > 0 && (
                <div className="flex items-center gap-1 ml-2">
                  {plan.tags.slice(0, 3).map((tag: string) => (
                    <Badge
                      key={tag}
                      variant="outline"
                      className="text-xs px-2 py-0 bg-blue-50 text-blue-700 border-blue-200"
                    >
                      {tag.charAt(0).toUpperCase() + tag.slice(1)}
                    </Badge>
                  ))}
                  {plan.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs px-2 py-0">
                      +{plan.tags.length - 3} more
                    </Badge>
                  )}
                </div>
              )}
            </DialogTitle>
          </DialogHeader>

          <Tabs defaultValue="overview" onValueChange={(value) => setActiveTab(value as any)}>
            <TabsList className="grid w-full grid-cols-3 mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="meals">Meals</TabsTrigger>
              <TabsTrigger value="nutrition">Nutrition</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg border">
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Total Cost</h3>
                    <p className="text-2xl font-bold">${planStats.totalCost}</p>
                    <p className="text-xs text-gray-500 mt-1">For {planStats.totalDays} days</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg border">
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Avg. Calories</h3>
                    <p className="text-2xl font-bold">{planStats.averageCalories}</p>
                    <p className="text-xs text-gray-500 mt-1">Per meal</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg border">
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Total Meals</h3>
                    <p className="text-2xl font-bold">{planStats.mealCount}</p>
                    <p className="text-xs text-gray-500 mt-1">Across {planStats.totalDays} days</p>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg border">
                  <h3 className="text-sm font-medium text-gray-500 mb-3">Macronutrient Breakdown</h3>
                  <div className="flex items-center gap-4">
                    <div className="w-24 h-24 relative">
                      <svg viewBox="0 0 36 36" className="w-full h-full">
                        <circle cx="18" cy="18" r="15.91549430918954" fill="transparent" stroke="#e9ecef" strokeWidth="3"></circle>
                        <circle
                          cx="18"
                          cy="18"
                          r="15.91549430918954"
                          fill="transparent"
                          stroke="#3b82f6"
                          strokeWidth="3"
                          strokeDasharray={`${planStats.macros.protein} ${100 - planStats.macros.protein}`}
                          strokeDashoffset="25"
                        ></circle>
                        <circle
                          cx="18"
                          cy="18"
                          r="15.91549430918954"
                          fill="transparent"
                          stroke="#10b981"
                          strokeWidth="3"
                          strokeDasharray={`${planStats.macros.carbs} ${100 - planStats.macros.carbs}`}
                          strokeDashoffset={`${100 - planStats.macros.protein + 25}`}
                        ></circle>
                        <circle
                          cx="18"
                          cy="18"
                          r="15.91549430918954"
                          fill="transparent"
                          stroke="#f59e0b"
                          strokeWidth="3"
                          strokeDasharray={`${planStats.macros.fats} ${100 - planStats.macros.fats}`}
                          strokeDashoffset={`${100 - planStats.macros.protein - planStats.macros.carbs + 25}`}
                        ></circle>
                      </svg>
                    </div>
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <span className="text-sm">Protein: {planStats.macros.protein}%</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-sm">Carbs: {planStats.macros.carbs}%</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
                        <span className="text-sm">Fats: {planStats.macros.fats}%</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handleViewShoppingList}>
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    View Shopping List
                  </Button>
                  <Button onClick={() => setIsWeekSelectorOpen(true)}>
                    <Calendar className="h-4 w-4 mr-2" />
                    Add to Calendar
                  </Button>
                </div>
              </div>
            )}

            {/* Meals Tab */}
            {activeTab === 'meals' && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {allMeals.map((meal, index) => (
                    <div key={`${meal.date}-${meal.mealType}`} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-medium">{meal.name}</h3>
                          <p className="text-xs text-gray-500">
                            {format(new Date(meal.date), 'MMM d')} - {meal.mealType.charAt(0).toUpperCase() + meal.mealType.slice(1)}
                          </p>
                        </div>
                        <Badge variant="outline" className="bg-gray-50">
                          ${meal.cost?.toFixed(2) || '0.00'}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                          {meal.calories} cal
                        </Badge>
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          {meal.cookTime} min
                        </Badge>
                      </div>
                      {meal.image && (
                        <div className="mb-2">
                          <img src={meal.image} alt={meal.name} className="w-full h-32 object-cover rounded-md" />
                        </div>
                      )}
                      <div className="text-sm">
                        <h4 className="font-medium mb-1">Ingredients:</h4>
                        <ul className="list-disc list-inside text-xs text-gray-600 mb-2">
                          {meal.ingredients?.slice(0, 5).map((ingredient: any, i: number) => (
                            <li key={i}>
                              {ingredient.amount} {ingredient.unit} {ingredient.name}
                            </li>
                          ))}
                          {meal.ingredients?.length > 5 && (
                            <li>+{meal.ingredients.length - 5} more ingredients</li>
                          )}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Nutrition Tab */}
            {activeTab === 'nutrition' && (
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg border mb-4">
                  <h3 className="text-sm font-medium text-gray-500 mb-3">Daily Nutrition Summary</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <h4 className="text-xs text-gray-500">Calories</h4>
                      <p className="text-lg font-bold">{planStats.averageCalories * 3} cal</p>
                      <p className="text-xs text-gray-500">per day</p>
                    </div>
                    <div>
                      <h4 className="text-xs text-gray-500">Protein</h4>
                      <p className="text-lg font-bold">{Math.round(planStats.averageCalories * 3 * planStats.macros.protein / 100 / 4)} g</p>
                      <p className="text-xs text-gray-500">per day</p>
                    </div>
                    <div>
                      <h4 className="text-xs text-gray-500">Carbs</h4>
                      <p className="text-lg font-bold">{Math.round(planStats.averageCalories * 3 * planStats.macros.carbs / 100 / 4)} g</p>
                      <p className="text-xs text-gray-500">per day</p>
                    </div>
                    <div>
                      <h4 className="text-xs text-gray-500">Fats</h4>
                      <p className="text-lg font-bold">{Math.round(planStats.averageCalories * 3 * planStats.macros.fats / 100 / 9)} g</p>
                      <p className="text-xs text-gray-500">per day</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Nutrition by Meal</h3>
                  {allMeals.map((meal, index) => (
                    <div key={`nutrition-${meal.date}-${meal.mealType}`} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-medium">{meal.name}</h3>
                          <p className="text-xs text-gray-500">
                            {format(new Date(meal.date), 'MMM d')} - {meal.mealType.charAt(0).toUpperCase() + meal.mealType.slice(1)}
                          </p>
                        </div>
                      </div>
                      <div className="grid grid-cols-4 gap-2 mt-2">
                        <div className="bg-gray-50 p-2 rounded">
                          <h4 className="text-xs text-gray-500">Calories</h4>
                          <p className="text-sm font-bold">{meal.calories} cal</p>
                        </div>
                        <div className="bg-gray-50 p-2 rounded">
                          <h4 className="text-xs text-gray-500">Protein</h4>
                          <p className="text-sm font-bold">{meal.nutrition?.protein || 0} g</p>
                        </div>
                        <div className="bg-gray-50 p-2 rounded">
                          <h4 className="text-xs text-gray-500">Carbs</h4>
                          <p className="text-sm font-bold">{meal.nutrition?.carbs || 0} g</p>
                        </div>
                        <div className="bg-gray-50 p-2 rounded">
                          <h4 className="text-xs text-gray-500">Fats</h4>
                          <p className="text-sm font-bold">{meal.nutrition?.fat || 0} g</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </Tabs>

          <DialogFooter>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button onClick={() => setIsWeekSelectorOpen(true)}>
              <Calendar className="h-4 w-4 mr-2" />
              Add to Calendar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Week Selector Dialog */}
      <Dialog open={isWeekSelectorOpen} onOpenChange={setIsWeekSelectorOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Select Week</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <div className="flex items-center justify-between mb-4">
              <Button variant="outline" size="icon" onClick={goToPreviousWeek}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <div className="text-center font-medium">
                {format(startDate, 'MMM d')} - {format(endDate, 'MMM d, yyyy')}
              </div>
              <Button variant="outline" size="icon" onClick={goToNextWeek}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            <div className="grid grid-cols-7 gap-1 mb-4">
              {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day) => (
                <div key={day} className="text-center text-xs font-medium text-gray-500">
                  {day}
                </div>
              ))}
              {Array.from({ length: 7 }).map((_, i) => {
                const date = new Date(startDate);
                date.setDate(date.getDate() + i);
                const dateStr = format(date, 'yyyy-MM-dd');

                // Check if this date already has meals in the calendar
                const hasBreakfast = mealPlan?.[dateStr]?.breakfast;
                const hasLunch = mealPlan?.[dateStr]?.lunch;
                const hasDinner = mealPlan?.[dateStr]?.dinner;
                const hasMeals = hasBreakfast || hasLunch || hasDinner;
                const allMealsPlanned = hasBreakfast && hasLunch && hasDinner;

                return (
                  <div
                    key={i}
                    className={cn(
                      "aspect-square flex flex-col items-center justify-center border rounded-md hover:bg-gray-50 cursor-pointer relative",
                      hasMeals && "border-primary/50",
                      allMealsPlanned && "bg-primary/5"
                    )}
                  >
                    <div className="text-sm">{format(date, 'd')}</div>

                    {/* Meal indicators */}
                    {hasMeals && (
                      <div className="flex gap-1 mt-1">
                        {hasBreakfast && <div className="w-2 h-2 rounded-full bg-red-400"></div>}
                        {hasLunch && <div className="w-2 h-2 rounded-full bg-blue-400"></div>}
                        {hasDinner && <div className="w-2 h-2 rounded-full bg-green-400"></div>}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
            <div className="text-sm text-gray-500 mb-4">
              This will add all meals from the plan to the selected week, starting from Monday.
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsWeekSelectorOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddToCalendar}>
              Add to Calendar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Shopping List Modal */}
      <ViewShoppingListModal
        isOpen={isShoppingListModalOpen}
        onClose={() => setIsShoppingListModalOpen(false)}
        shoppingList={shoppingList}
        pantryItems={pantryItems}
        newMealPlan={plan?.plan}
      />
    </>
  );
}
