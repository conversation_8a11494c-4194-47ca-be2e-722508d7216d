"use client";

import { useState } from 'react';
import { useDragDrop } from './DragDropProvider';
import { useDrag } from 'react-dnd';
import { Meal, MealDragItem } from '@/types/new-meal-plan';
import { toast } from 'sonner';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Clock,
  DollarSign,
  Flame,
  MoreVertical,
  Check,
  X,
  Info,
  Trash2,
  RefreshCw,
  Repeat,
  Users,
  Star,
  StickyNote
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface MealCardProps {
  meal: Meal;
  day: string;
  mealType: string;
  onViewDetails: (meal: Meal) => void;
  onRemove: (day: string, mealType: string) => void;
  onStatusChange: (day: string, mealType: string, status: 'cooked' | 'skipped' | null) => void;
  onRegenerate: (day: string, mealType: string) => void;
  onSwap?: (day: string, mealType: string, meal: Meal) => void;
  onAdjustServings?: (day: string, mealType: string, meal: Meal) => void;
  onToggleFavorite?: (day: string, mealType: string) => void;
  onAddNotes?: (day: string, mealType: string, meal: Meal) => void;
  compact?: boolean;
}

export function MealCard({
  meal,
  day,
  mealType,
  onViewDetails,
  onRemove,
  onStatusChange,
  onRegenerate,
  onSwap,
  onAdjustServings,
  onToggleFavorite,
  onAddNotes,
  compact = false
}: MealCardProps) {
  // Get the drag and drop context
  const { draggedItem } = useDragDrop();

  // Setup drag functionality
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'MEAL',
    item: { id: meal.id, day, mealType, meal } as MealDragItem,
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
    // Add end callback to show success message
    end: (item, monitor) => {
      const didDrop = monitor.didDrop();
      if (!didDrop) {
        // If the drag was cancelled, show a message
        toast.info('Drag cancelled');
      }
    },
  }));

  // Get status color and icon for the meal
  const getStatusInfo = () => {
    switch (meal.status) {
      case 'cooked':
        return {
          color: 'bg-green-100 text-green-800 border-green-300',
          icon: <Check className="h-3 w-3 mr-1" />,
          label: 'Cooked'
        };
      case 'skipped':
        return {
          color: 'bg-red-100 text-red-800 border-red-300',
          icon: <X className="h-3 w-3 mr-1" />,
          label: 'Skipped'
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-300',
          icon: null,
          label: 'Pending'
        };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <div
      ref={drag}
      className={`cursor-move transition-all h-full ${isDragging ? 'opacity-50 scale-105 shadow-lg' : 'opacity-100 hover:scale-[1.02] shadow-sm'}`}
      style={{
        transition: 'opacity 0.2s, transform 0.2s, box-shadow 0.2s',
      }}
    >
      <Card className={`h-full relative flex flex-col ${meal.status === 'cooked' ? 'border-green-300' : meal.status === 'skipped' ? 'border-red-300' : ''} ${compact ? 'min-h-[140px]' : ''}`}>
        {meal.status && (
          <div
            className={`absolute top-0 right-0 w-0 h-0
              ${meal.status === 'cooked' ? 'border-t-green-500' : 'border-t-red-500'}
              border-t-[20px] border-l-transparent border-l-[20px]`}
          />
        )}
        <CardContent className={`${compact ? 'p-3' : 'p-4'} flex-1 flex flex-col`}>
          <div className="flex justify-between items-start mb-2">
            <div className="flex flex-col">
              <div className="flex items-center gap-2 mb-1">
                <Badge variant="outline" className="capitalize">
                  {mealType}
                </Badge>
                <Badge variant="outline" className={statusInfo.color}>
                  <div className="flex items-center">
                    {statusInfo.icon}
                    {statusInfo.label}
                  </div>
                </Badge>
              </div>
              <div className="flex items-center gap-1">
                <h3 className={`font-medium ${compact ? 'text-sm' : 'text-base'} truncate max-w-[180px]`} title={meal.name}>{meal.name}</h3>
                {meal.favorite && (
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400 flex-shrink-0" />
                )}
                {meal.notes && (
                  <StickyNote className="h-3 w-3 text-blue-500 flex-shrink-0" />
                )}
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onViewDetails(meal)}>
                  <Info className="mr-2 h-4 w-4" />
                  <span>View Details</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onStatusChange(day, mealType, 'cooked')}>
                  <Check className="mr-2 h-4 w-4" />
                  <span>Mark as Cooked</span>
                  {meal.status === 'cooked' && (
                    <Check className="ml-2 h-4 w-4 text-green-500" />
                  )}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onStatusChange(day, mealType, 'skipped')}>
                  <X className="mr-2 h-4 w-4" />
                  <span>Mark as Skipped</span>
                  {meal.status === 'skipped' && (
                    <Check className="ml-2 h-4 w-4 text-green-500" />
                  )}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onStatusChange(day, mealType, null)}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  <span>Reset Status</span>
                  {!meal.status && (
                    <Check className="ml-2 h-4 w-4 text-green-500" />
                  )}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onRegenerate(day, mealType)}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  <span>Regenerate Meal</span>
                </DropdownMenuItem>
                {onSwap && (
                  <DropdownMenuItem onClick={() => onSwap(day, mealType, meal)}>
                    <Repeat className="mr-2 h-4 w-4" />
                    <span>Swap Meal</span>
                  </DropdownMenuItem>
                )}
                {onAdjustServings && (
                  <DropdownMenuItem onClick={() => onAdjustServings(day, mealType, meal)}>
                    <Users className="mr-2 h-4 w-4" />
                    <span>Adjust Servings</span>
                    {meal.servings > 1 && (
                      <Badge variant="outline" className="ml-2 text-xs">{meal.servings}</Badge>
                    )}
                  </DropdownMenuItem>
                )}
                {onToggleFavorite && (
                  <DropdownMenuItem onClick={() => onToggleFavorite(day, mealType)}>
                    <Star className={`mr-2 h-4 w-4 ${meal.favorite ? 'fill-yellow-400 text-yellow-400' : ''}`} />
                    <span>{meal.favorite ? 'Remove from Favorites' : 'Add to Favorites'}</span>
                  </DropdownMenuItem>
                )}
                {onAddNotes && (
                  <DropdownMenuItem onClick={() => onAddNotes(day, mealType, meal)}>
                    <StickyNote className="mr-2 h-4 w-4" />
                    <span>{meal.notes ? 'Edit Notes' : 'Add Notes'}</span>
                    {meal.notes && <Badge variant="outline" className="ml-2 text-xs">✓</Badge>}
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem
                  onClick={() => onRemove(day, mealType)}
                  className="text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  <span>Remove</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className="flex-1"></div>
          {!compact && (
            <div className="mt-auto flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Clock className="mr-1 h-4 w-4" />
                <span>{meal.prepTime} min</span>
              </div>
              <div className="flex items-center">
                <Flame className="mr-1 h-4 w-4" />
                <span>{meal.calories} cal</span>
              </div>
              <div className="flex items-center">
                <DollarSign className="mr-1 h-4 w-4" />
                <span>${meal.cost.toFixed(2)}</span>
              </div>
            </div>
          )}
          {compact && (
            <div className="mt-auto flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center">
                <Clock className="mr-1 h-3 w-3 flex-shrink-0" />
                <span className="whitespace-nowrap">{meal.prepTime} min</span>
              </div>
              <div className="flex items-center">
                <Flame className="mr-1 h-3 w-3 flex-shrink-0" />
                <span className="whitespace-nowrap">{meal.calories} cal</span>
              </div>
              <div className="flex items-center">
                <DollarSign className="mr-1 h-3 w-3 flex-shrink-0" />
                <span className="whitespace-nowrap">${meal.cost.toFixed(2)}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
