// This script runs database migrations in order
// Run this script with: node scripts/run_migrations.js

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Supabase connection details
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service role key not found in environment variables.');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { autoRefreshToken: false, persistSession: false }
});

async function runMigrations() {
  try {
    console.log('Running database migrations...\n');
    
    // Get all migration files
    const migrationsDir = path.join(__dirname, '../supabase/migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Sort to ensure migrations run in order
    
    console.log(`Found ${migrationFiles.length} migration files.\n`);
    
    // Get already applied migrations
    let appliedMigrations = [];
    try {
      const { data, error } = await supabase
        .from('schema_migrations')
        .select('version')
        .order('version', { ascending: true });
      
      if (error) {
        // Table might not exist yet, which is fine for the first run
        console.log('Note: schema_migrations table might not exist yet. Will create it.');
      } else {
        appliedMigrations = data.map(row => row.version);
        console.log(`Found ${appliedMigrations.length} already applied migrations.`);
      }
    } catch (error) {
      console.log('Error checking applied migrations:', error);
      // Continue anyway, as the first migration will create the table
    }
    
    // Run each migration file
    for (const file of migrationFiles) {
      const version = file.split('_')[0]; // Extract version from filename
      
      if (appliedMigrations.includes(version)) {
        console.log(`Migration ${version} (${file}) already applied. Skipping.`);
        continue;
      }
      
      console.log(`Running migration ${version} (${file})...`);
      
      const migrationPath = path.join(migrationsDir, file);
      const migrationSql = fs.readFileSync(migrationPath, 'utf8');
      
      try {
        const { error } = await supabase.rpc('_', {}, {
          headers: {
            'Content-Type': 'application/json',
            'Prefer': 'params=single-object',
            'X-Raw-SQL': migrationSql
          }
        });
        
        if (error) {
          console.error(`Error running migration ${file}:`, error);
          
          // If this is not the initial migration, we might want to stop
          if (version !== '00000000000000') {
            console.error('Migration failed. Stopping.');
            process.exit(1);
          } else {
            console.log('Continuing despite error, as this might be the initial migration.');
          }
        } else {
          console.log(`Migration ${version} (${file}) applied successfully.`);
        }
      } catch (error) {
        console.error(`Error running migration ${file}:`, error);
        
        // If this is not the initial migration, we might want to stop
        if (version !== '00000000000000') {
          console.error('Migration failed. Stopping.');
          process.exit(1);
        } else {
          console.log('Continuing despite error, as this might be the initial migration.');
        }
      }
      
      console.log('-----------------------------------');
    }
    
    console.log('\nMigrations complete!');
    
    // Verify migrations
    try {
      const { data, error } = await supabase
        .from('schema_migrations')
        .select('version, description, applied_at')
        .order('version', { ascending: true });
      
      if (error) {
        console.error('Error verifying migrations:', error);
      } else {
        console.log('\nApplied migrations:');
        data.forEach(migration => {
          console.log(`- ${migration.version}: ${migration.description} (${new Date(migration.applied_at).toLocaleString()})`);
        });
      }
    } catch (error) {
      console.error('Error verifying migrations:', error);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the script
runMigrations();
