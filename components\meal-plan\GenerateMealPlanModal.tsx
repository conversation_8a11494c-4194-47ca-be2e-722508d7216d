"use client";

import { useState } from 'react';
import * as React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2, Sparkles, X } from 'lucide-react';
import { MealGenerationOptions, MealPlanGenerationStatus, MealPlanGenerationApproach } from '@/types/new-meal-plan';
import { EDAMAM_DIET_LABELS, EDAMAM_HEALTH_LABELS, BUDGET_LEVELS, COOKING_TIMES } from '@/lib/constants/dietary-preferences';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  Dialog<PERSON>itle,
} from '@/components/ui/dialog';

interface GenerateMealPlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (options: MealGenerationOptions) => Promise<void>;
  isGenerating: boolean;
  generationStatus?: MealPlanGenerationStatus;
  approach?: MealPlanGenerationApproach;
}

export function GenerateMealPlanModal({
  isOpen,
  onClose,
  onGenerate,
  isGenerating,
  generationStatus,
  approach = 'ai'
}: GenerateMealPlanModalProps) {
  const [options, setOptions] = useState<MealGenerationOptions>({
    name: 'Weekly Meal Plan',
    days: 7,
    mealsPerDay: 3,
    calories: 2000,
    dietaryPreferences: [],
    excludeIngredients: [],
    budget: 'medium',
    cookingTime: 'medium',
    cuisineTypes: []
  });

  const handleChange = (field: keyof MealGenerationOptions, value: any) => {
    setOptions(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async () => {
    await onGenerate(options);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-primary" />
            Generate New Meal Plan
          </DialogTitle>
          <DialogDescription>
            Customize your meal plan preferences and we'll generate a personalized plan for you.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Name
            </Label>
            <Input
              id="name"
              value={options.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right">
              Days
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Slider
                value={[options.days]}
                min={1}
                max={14}
                step={1}
                onValueChange={(value) => handleChange('days', value[0])}
                className="w-full"
              />
              <span className="w-10 text-center">{options.days}</span>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right">
              Calories
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Slider
                value={[options.calories]}
                min={1200}
                max={3000}
                step={100}
                onValueChange={(value) => handleChange('calories', value[0])}
                className="w-full"
              />
              <span className="w-16 text-center">{options.calories}</span>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right">
              Budget
            </Label>
            <Select
              value={options.budget}
              onValueChange={(value) => handleChange('budget', value)}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select budget" />
              </SelectTrigger>
              <SelectContent>
                {BUDGET_LEVELS.map((budget) => (
                  <SelectItem key={budget.value} value={budget.value}>{budget.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right">
              Cooking Time
            </Label>
            <Select
              value={options.cookingTime}
              onValueChange={(value) => handleChange('cookingTime', value)}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select cooking time" />
              </SelectTrigger>
              <SelectContent>
                {COOKING_TIMES.map((time) => (
                  <SelectItem key={time.value} value={time.value}>{time.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right">
              Generation Method
            </Label>
            <div className="col-span-3 grid grid-cols-3 gap-2">
              <div
                className={`flex flex-col items-center justify-center p-2 rounded-md border cursor-pointer transition-colors ${approach === 'edaspoon' ? 'border-primary bg-primary/5' : 'border-gray-200 hover:border-gray-300'}`}
                onClick={() => {}}
              >
                <div className="font-medium text-sm">EdaSpoon</div>
                <div className="text-xs text-muted-foreground">Real recipes & costs</div>
              </div>
              <div
                className={`flex flex-col items-center justify-center p-2 rounded-md border cursor-pointer transition-colors ${approach === 'ai' ? 'border-primary bg-primary/5' : 'border-gray-200 hover:border-gray-300'}`}
                onClick={() => {}}
              >
                <div className="font-medium text-sm">AI</div>
                <div className="text-xs text-muted-foreground">AI-generated plans</div>
              </div>
              <div
                className={`flex flex-col items-center justify-center p-2 rounded-md border cursor-pointer transition-colors ${approach === 'hybrid' ? 'border-primary bg-primary/5' : 'border-gray-200 hover:border-gray-300'}`}
                onClick={() => {}}
              >
                <div className="font-medium text-sm">Hybrid</div>
                <div className="text-xs text-muted-foreground">Best of both</div>
              </div>
            </div>

            <div className="col-span-4 mt-2">
              {approach === 'edaspoon' && (
                <div className="text-xs text-muted-foreground">
                  <p>EdaSpoon uses real recipes from Edamam API and cost information from Spoonacular API.</p>
                  <p className="mt-1">Pros: Real, tested recipes with accurate nutritional data and cost estimates.</p>
                </div>
              )}
              {approach === 'ai' && (
                <div className="text-xs text-muted-foreground">
                  <p>AI uses OpenAI to generate complete meal plans with recipes, nutritional information, and cost estimates.</p>
                  <p className="mt-1">Pros: Highly customizable recipes tailored to your exact preferences.</p>
                </div>
              )}
              {approach === 'hybrid' && (
                <div className="text-xs text-muted-foreground">
                  <p>Hybrid combines AI-generated meal plans with real recipes and accurate cost information.</p>
                  <p className="mt-1">Pros: Best of both worlds - creative AI suggestions with real recipe data.</p>
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right">
              Excluded Ingredients
            </Label>
            <Input
              value={options.excludeIngredients.join(', ')}
              onChange={(e) => {
                const ingredients = e.target.value.split(',').map(i => i.trim()).filter(Boolean);
                handleChange('excludeIngredients', ingredients);
              }}
              placeholder="e.g. peanuts, shellfish, mushrooms"
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 items-start gap-4 pt-2">
            <Label className="text-right pt-2">
              Dietary Preferences
            </Label>
            <div className="col-span-3 grid grid-cols-2 gap-2 max-h-[200px] overflow-y-auto pr-2">
              <div className="col-span-2 font-medium text-xs text-muted-foreground mb-1">Diet Labels</div>
              {EDAMAM_DIET_LABELS.map((pref) => (
                <div key={pref.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`pref-${pref.value}`}
                    checked={options.dietaryPreferences.includes(pref.value)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        handleChange('dietaryPreferences', [...options.dietaryPreferences, pref.value]);
                      } else {
                        handleChange('dietaryPreferences', options.dietaryPreferences.filter(p => p !== pref.value));
                      }
                    }}
                  />
                  <label
                    htmlFor={`pref-${pref.value}`}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {pref.label}
                  </label>
                </div>
              ))}

              <div className="col-span-2 font-medium text-xs text-muted-foreground mt-3 mb-1">Health Labels</div>
              {EDAMAM_HEALTH_LABELS.map((pref) => (
                <div key={pref.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`pref-${pref.value}`}
                    checked={options.dietaryPreferences.includes(pref.value)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        handleChange('dietaryPreferences', [...options.dietaryPreferences, pref.value]);
                      } else {
                        handleChange('dietaryPreferences', options.dietaryPreferences.filter(p => p !== pref.value));
                      }
                    }}
                  />
                  <label
                    htmlFor={`pref-${pref.value}`}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {pref.label}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>

        {isGenerating && generationStatus && (
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium">{generationStatus.currentStep}</span>
              <span className="text-sm font-medium">{generationStatus.progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className={`h-2.5 rounded-full ${generationStatus.progress < 30 ? 'bg-blue-500' : generationStatus.progress < 70 ? 'bg-blue-500' : 'bg-green-500'}`}
                style={{ width: `${generationStatus.progress}%` }}
              ></div>
            </div>
            {generationStatus.error && (
              <p className="text-sm text-red-500 mt-2">{generationStatus.error}</p>
            )}
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isGenerating}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isGenerating}>
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Generate Plan
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
