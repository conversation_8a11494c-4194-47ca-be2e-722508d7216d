'use client';

import { useState, useEffect } from 'react';
import { useDebounce } from '@/hooks/useDebounce';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';

interface Recipe {
  id: string;
  name: string;
  ingredients: string[];
  instructions: string;
  servings: number;
  prepTime: number;
  cookTime: number;
}

export function RecipeEditor({ recipeId }: { recipeId: string }) {
  const [recipe, setRecipe] = useState<Recipe | null>(null);
  const [isDirty, setIsDirty] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  
  const debouncedRecipe = useDebounce(recipe, 1000);

  // Auto-save functionality
  useEffect(() => {
    if (isDirty && debouncedRecipe) {
      saveRecipe(debouncedRecipe);
    }
  }, [debouncedRecipe, isDirty]);

  const saveRecipe = async (recipeData: Recipe) => {
    setIsSaving(true);
    try {
      const response = await fetch(`/api/recipes/${recipeId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(recipeData),
      });
      
      if (!response.ok) throw new Error('Failed to save recipe');
      
      setIsDirty(false);
      toast({ title: "Changes saved" });
    } catch (error) {
      toast({
        title: "Error saving changes",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleChange = (field: keyof Recipe, value: any) => {
    setRecipe(prev => prev ? { ...prev, [field]: value } : null);
    setIsDirty(true);
  };

  return (
    <div className="grid grid-cols-2 gap-6">
      {/* Editor */}
      <div className="space-y-4">
        <Input
          value={recipe?.name || ''}
          onChange={(e) => handleChange('name', e.target.value)}
          placeholder="Recipe name"
        />
        
        <div className="grid grid-cols-3 gap-4">
          <Input
            type="number"
            value={recipe?.servings || ''}
            onChange={(e) => handleChange('servings', parseInt(e.target.value))}
            placeholder="Servings"
          />
          <Input
            type="number"
            value={recipe?.prepTime || ''}
            onChange={(e) => handleChange('prepTime', parseInt(e.target.value))}
            placeholder="Prep time (mins)"
          />
          <Input
            type="number"
            value={recipe?.cookTime || ''}
            onChange={(e) => handleChange('cookTime', parseInt(e.target.value))}
            placeholder="Cook time (mins)"
          />
        </div>

        <Textarea
          value={recipe?.ingredients.join('\n') || ''}
          onChange={(e) => handleChange('ingredients', e.target.value.split('\n'))}
          placeholder="Ingredients (one per line)"
          rows={5}
        />

        <Textarea
          value={recipe?.instructions || ''}
          onChange={(e) => handleChange('instructions', e.target.value)}
          placeholder="Instructions"
          rows={10}
        />
      </div>

      {/* Live Preview */}
      <Card className="p-6">
        <h2 className="text-2xl font-bold mb-4">{recipe?.name || 'Recipe Preview'}</h2>
        
        <div className="flex gap-4 text-sm text-muted-foreground mb-6">
          <span>{recipe?.servings} servings</span>
          <span>{recipe?.prepTime} mins prep</span>
          <span>{recipe?.cookTime} mins cook</span>
        </div>

        <div className="mb-6">
          <h3 className="font-semibold mb-2">Ingredients</h3>
          <ul className="list-disc list-inside">
            {recipe?.ingredients.map((ingredient, i) => (
              <li key={i}>{ingredient}</li>
            ))}
          </ul>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Instructions</h3>
          <p className="whitespace-pre-wrap">{recipe?.instructions}</p>
        </div>
      </Card>
    </div>
  );
}