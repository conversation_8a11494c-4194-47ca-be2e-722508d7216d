# Supabase Schema Diagnostic Report

**Date:** June 28, 2025
**Database:** LeanEats Production Database
**Analysis Based On:** Codebase audit and schema files review

---

## **Executive Summary**

The LeanEats Supabase database has been partially implemented with core tables and authentication in place. However, several critical gaps exist in the schema implementation, RLS policies, and data consistency that need to be addressed for production readiness.

---

## **Current Schema State**

### **Implemented Tables**
Based on codebase analysis, the following tables are currently implemented:

1. **`users`** - User profile and preferences
   - Status: ✅ Implemented
   - Columns: id (text), email, weekly_budget, household_size, dietary_restrictions, created_at, updated_at
   - RLS: ✅ Enabled with policies

2. **`meal_plans`** - Meal plan storage
   - Status: ✅ Implemented  
   - Columns: id (uuid), user_id, start_date, end_date, total_cost, meal_data (jsonb), status, created_at, updated_at
   - RLS: ✅ Enabled with policies

3. **`recipes`** - Recipe information
   - Status: ✅ Implemented
   - Columns: id (uuid), user_id, name, description, instructions, ingredients, prep_time, cook_time, servings, etc.
   - RLS: ✅ Enabled with policies

4. **`shopping_lists`** - Shopping list storage
   - Status: ✅ Implemented
   - Columns: id, user_id, meal_plan_id, name, status, created_at, updated_at
   - RLS: ✅ Enabled with policies

5. **`shopping_items`** - Individual shopping list items
   - Status: ✅ Implemented
   - Columns: id, shopping_list_id, name, quantity, unit, category, checked, in_pantry, created_at, updated_at
   - RLS: ✅ Enabled with policies

### **Missing Tables**
According to the planned schema in `DatabaseSchema.md` and `Epic-UserStories.md`, the following tables are missing:

1. **`meal_plan_assignments`** - Calendar assignments for meal plans
   - Status: ❌ Missing
   - Purpose: Track which meal plans are assigned to specific calendar dates

2. **`meal_completions`** - Meal completion tracking
   - Status: ❌ Missing
   - Purpose: Track when meals are marked as cooked, skipped, or completed

3. **`favorite_meals`** - User favorite recipes
   - Status: ❌ Missing
   - Purpose: Track user's favorite recipes for quick access

4. **`pantry_items`** - User pantry management
   - Status: ❌ Missing
   - Purpose: Track ingredients user already has in pantry

5. **`meal_notes`** - User notes for meals
   - Status: ❌ Missing
   - Purpose: Allow users to add custom notes to meals

6. **`meal_generation_preferences`** - Generation preferences
   - Status: ❌ Missing
   - Purpose: Store user preferences for meal plan generation

7. **`user_preferences`** - Extended user preferences
   - Status: ❌ Missing
   - Purpose: Store detailed user preferences beyond basic profile

8. **`search_history`** - Recipe search history
   - Status: ❌ Missing
   - Purpose: Track user search patterns for analytics

---

## **Schema Inconsistencies Identified**

### **1. User ID Type Inconsistency**
**Issue:** Mixed usage of `text` and `uuid` types for user_id columns
- `public.users.id` is defined as `text` in some schemas
- `auth.users.id` is `uuid` type
- Foreign key references are inconsistent

**Impact:** Type casting required in RLS policies, potential performance issues

### **2. RLS Policy Assignment Issues**
**Issue:** Some policies assigned to `public` role instead of `authenticated`
- Policies should target `authenticated` role for logged-in users
- Some policies missing proper type casting

**Impact:** Security vulnerabilities, permission denied errors

### **3. Missing Indexes**
**Issue:** Performance indexes missing on frequently queried columns
- Missing indexes on foreign key columns
- Missing composite indexes for common query patterns

**Impact:** Poor query performance, slow application response

---

## **Proposed Schema Modifications**

### **Priority 1: Critical Fixes**

```sql
-- 1. Standardize user_id column types
ALTER TABLE public.users ALTER COLUMN id TYPE uuid USING id::uuid;

-- 2. Fix RLS policies to use authenticated role
DROP POLICY IF EXISTS "Users can view their own meal plans" ON meal_plans;
CREATE POLICY "Users can view their own meal plans"
ON meal_plans FOR SELECT
TO authenticated
USING (user_id::text = auth.uid()::text);

-- 3. Add missing indexes
CREATE INDEX IF NOT EXISTS meal_plans_user_id_status_idx ON meal_plans(user_id, status);
CREATE INDEX IF NOT EXISTS shopping_lists_user_id_idx ON shopping_lists(user_id);
CREATE INDEX IF NOT EXISTS recipes_user_id_idx ON recipes(user_id);
```

### **Priority 2: Missing Tables**

```sql
-- Create meal_plan_assignments table
CREATE TABLE IF NOT EXISTS public.meal_plan_assignments (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    meal_plan_id uuid REFERENCES public.meal_plans(id) ON DELETE CASCADE,
    assigned_date date NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    UNIQUE(user_id, assigned_date)
);

-- Create meal_completions table
CREATE TABLE IF NOT EXISTS public.meal_completions (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    meal_plan_id uuid REFERENCES public.meal_plans(id) ON DELETE CASCADE,
    meal_id text NOT NULL,
    status text NOT NULL CHECK (status IN ('completed', 'skipped', 'pending')),
    completed_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Create favorite_meals table
CREATE TABLE IF NOT EXISTS public.favorite_meals (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    recipe_id uuid REFERENCES public.recipes(id) ON DELETE CASCADE,
    created_at timestamp with time zone DEFAULT now(),
    UNIQUE(user_id, recipe_id)
);
```

### **Priority 3: Performance Optimizations**

```sql
-- Add performance indexes
CREATE INDEX IF NOT EXISTS meal_plan_assignments_user_date_idx 
ON meal_plan_assignments(user_id, assigned_date);

CREATE INDEX IF NOT EXISTS meal_completions_user_status_idx 
ON meal_completions(user_id, status);

CREATE INDEX IF NOT EXISTS favorite_meals_user_id_idx 
ON favorite_meals(user_id);

-- Add updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_meal_plan_assignments_updated_at 
BEFORE UPDATE ON meal_plan_assignments 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

---

## **Security Recommendations**

### **1. RLS Policy Standardization**
All tables with user_id columns should have consistent RLS policies:
- Use `TO authenticated` role assignment
- Implement proper type casting for user_id comparisons
- Include all CRUD operations (SELECT, INSERT, UPDATE, DELETE)

### **2. Data Validation**
Implement database-level constraints:
- Check constraints for status fields
- Foreign key constraints for all relationships
- Unique constraints where appropriate

### **3. Audit Trail**
Consider implementing:
- Row-level audit logging for sensitive operations
- Soft delete patterns for important data
- Data retention policies

---

## **Migration Strategy**

### **Phase 1: Critical Fixes (Week 1)**
1. Fix user_id type inconsistencies
2. Update RLS policies to use authenticated role
3. Add essential performance indexes

### **Phase 2: Missing Tables (Week 2)**
1. Create meal_plan_assignments table
2. Create meal_completions table  
3. Create favorite_meals table
4. Implement RLS policies for new tables

### **Phase 3: Enhancements (Week 3)**
1. Create remaining tables (pantry_items, meal_notes, etc.)
2. Add performance optimizations
3. Implement audit trails and triggers

---

## **⚠️ Important Warnings**

**These are PROPOSALS for review, not to be executed automatically.**

1. **Backup Required:** Full database backup required before any schema changes
2. **Downtime:** Some changes may require brief application downtime
3. **Data Migration:** User ID type changes require careful data migration
4. **Testing:** All changes must be tested in staging environment first
5. **RLS Impact:** Policy changes may temporarily affect data access

---

## **Next Steps**

1. Review this diagnostic with the development team
2. Create detailed migration scripts for each phase
3. Set up staging environment for testing
4. Plan maintenance windows for production changes
5. Implement monitoring for schema change impacts
