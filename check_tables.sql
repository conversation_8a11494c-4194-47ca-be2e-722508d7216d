-- Check if tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public'
ORDER BY table_name;

-- Check structure of meal_plan_assignments if it exists
SELECT EXISTS (
  SELECT FROM information_schema.tables 
  WHERE table_schema = 'public' 
  AND table_name = 'meal_plan_assignments'
);

-- Check structure of shopping_lists if it exists
SELECT EXISTS (
  SELECT FROM information_schema.tables 
  WHERE table_schema = 'public' 
  AND table_name = 'shopping_lists'
);

-- Check structure of shopping_items if it exists
SELECT EXISTS (
  SELECT FROM information_schema.tables 
  WHERE table_schema = 'public' 
  AND table_name = 'shopping_items'
);

-- Check structure of pantry_items if it exists
SELECT EXISTS (
  SELECT FROM information_schema.tables 
  WHERE table_schema = 'public' 
  AND table_name = 'pantry_items'
);

-- If meal_plan_assignments exists, check its columns
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'meal_plan_assignments'
ORDER BY ordinal_position;

-- If shopping_lists exists, check its columns
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'shopping_lists'
ORDER BY ordinal_position;

-- If shopping_items exists, check its columns
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'shopping_items'
ORDER BY ordinal_position;

-- If pantry_items exists, check its columns
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'pantry_items'
ORDER BY ordinal_position;

-- Check RLS policies for meal_plan_assignments if it exists
SELECT tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies
WHERE schemaname = 'public'
AND tablename = 'meal_plan_assignments';

-- Check RLS policies for shopping_lists if it exists
SELECT tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies
WHERE schemaname = 'public'
AND tablename = 'shopping_lists';

-- Check RLS policies for shopping_items if it exists
SELECT tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies
WHERE schemaname = 'public'
AND tablename = 'shopping_items';

-- Check RLS policies for pantry_items if it exists
SELECT tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies
WHERE schemaname = 'public'
AND tablename = 'pantry_items';
