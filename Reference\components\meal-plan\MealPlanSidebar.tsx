"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ChevronLeft, ChevronRight, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";

interface SavedMealPlan {
  id: string;
  name: string;
  date: string;
  type: string;
  meals: number;
}

interface MealPlanSidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

export default function MealPlanSidebar({ isOpen, onToggle }: MealPlanSidebarProps) {
  // Mock data for saved meal plans
  const [savedMealPlans] = useState<SavedMealPlan[]>([
    {
      id: "1",
      name: "French Cuisine Week",
      date: "2025-04-20",
      type: "French",
      meals: 21
    },
    {
      id: "2",
      name: "Mediterranean Diet",
      date: "2025-04-15",
      type: "Mediterranean",
      meals: 21
    },
    {
      id: "3",
      name: "Asian Fusion Week",
      date: "2025-04-10",
      type: "Asian",
      meals: 21
    }
  ]);

  return (
    <div
      className={cn(
        "fixed left-0 top-16 h-[calc(100vh-4rem)] bg-background border-r transition-all duration-200 z-40",
        isOpen ? "w-[300px]" : "w-0"
      )}
    >
      <Button
        variant="ghost"
        size="icon"
        className="absolute -right-10 top-2"
        onClick={onToggle}
      >
        {isOpen ? <ChevronLeft /> : <ChevronRight />}
      </Button>

      {isOpen && (
        <div className="p-4 h-full flex flex-col">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Saved Meal Plans</h2>
            <Button variant="ghost" size="icon">
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          <ScrollArea className="flex-1">
            <div className="space-y-2">
              {savedMealPlans.map((plan) => (
                <div
                  key={plan.id}
                  className="p-3 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                >
                  <h3 className="font-medium">{plan.name}</h3>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(plan.date), "MMM d, yyyy")}
                  </p>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                      {plan.type}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {plan.meals} meals
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      )}
    </div>
  );
}