-- First make the column nullable and add it
ALTER TABLE "public"."users" 
ADD COLUMN "hashed_password" TEXT;

-- Update existing records with a temporary password hash
-- You'll need to have users reset their passwords
UPDATE "public"."users"
SET "hashed_password" = 'RESET_REQUIRED'
WHERE "hashed_password" IS NULL;

-- Then make the column required
ALTER TABLE "public"."users" 
ALTER COLUMN "hashed_password" SET NOT NULL;