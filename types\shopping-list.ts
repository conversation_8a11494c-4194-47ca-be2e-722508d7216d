export interface ShoppingItem {
  id: string;
  shopping_list_id: string;
  name: string;
  quantity: string;
  unit: string;
  category: string;
  checked: boolean;
  in_pantry: boolean;
  created_at: string;
  updated_at: string;
}

export interface ShoppingList {
  id: string;
  user_id: string;
  name: string;
  meal_plan_id?: string;
  status: string;
  created_at: string;
  updated_at: string;
  items?: ShoppingItem[];
}

export interface ShoppingCategory {
  name: string;
  items: ShoppingItem[];
}

export interface NewShoppingItem {
  name: string;
  quantity: string;
  unit: string;
  category: string;
  checked?: boolean;
  in_pantry?: boolean;
}

export interface NewShoppingList {
  name: string;
  meal_plan_id?: string;
  status?: string;
}
