// This script applies standardized RLS policies to all tables
// Run this script with: node scripts/apply_standard_policies.js

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Supabase connection details
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service role key not found in environment variables.');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { autoRefreshToken: false, persistSession: false }
});

// Table configuration - customize this based on your schema
const tableConfig = [
  { name: 'meal_plans', userIdColumn: 'user_id', enableRls: true },
  { name: 'meals', userIdColumn: 'meal_plan_id', enableRls: true },
  { name: 'recipes', userIdColumn: 'user_id', enableRls: true },
  { name: 'users', userIdColumn: 'id', enableRls: true },
  // Add more tables as needed
];

async function applyStandardPolicies() {
  try {
    console.log('Applying standardized RLS policies...\n');
    
    // First, apply the migration to create the standardize_rls_policies function
    const migrationPath = path.join(__dirname, '../supabase/migrations/20230803000000_standardize_rls_policies.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');
    
    const { error: migrationError } = await supabase.rpc('_', {}, {
      headers: {
        'Content-Type': 'application/json',
        'Prefer': 'params=single-object',
        'X-Raw-SQL': migrationSql
      }
    });
    
    if (migrationError) {
      console.error('Error applying migration:', migrationError);
      return;
    }
    
    console.log('Migration applied successfully.\n');
    
    // Apply standardized policies to each table
    for (const table of tableConfig) {
      console.log(`Standardizing policies for table: ${table.name}`);
      
      const { error } = await supabase.rpc('standardize_rls_policies', {
        p_table_name: table.name,
        p_user_id_column: table.userIdColumn,
        p_enable_rls: table.enableRls
      });
      
      if (error) {
        console.error(`Error standardizing policies for table ${table.name}:`, error);
      } else {
        console.log(`Successfully standardized policies for table: ${table.name}`);
      }
      
      console.log('-----------------------------------');
    }
    
    console.log('\nStandardization complete!');
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the script
applyStandardPolicies();
