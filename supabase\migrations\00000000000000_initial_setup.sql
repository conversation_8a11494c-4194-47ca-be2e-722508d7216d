-- Initial database setup
-- This migration creates the schema_migrations table to track migrations

-- Create schema_migrations table
CREATE TABLE IF NOT EXISTS public.schema_migrations (
    id SERIAL PRIMARY KEY,
    version VARCHAR(255) NOT NULL UNIQUE,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    description TEXT
);

-- Add comment to the table
COMMENT ON TABLE public.schema_migrations IS 'Tracks database migrations';

-- Insert this migration
INSERT INTO public.schema_migrations (version, description)
VALUES ('00000000000000', 'Initial setup - create schema_migrations table')
ON CONFLICT (version) DO NOTHING;
