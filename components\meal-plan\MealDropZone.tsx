"use client";

import { useDrop } from 'react-dnd';
import { useDragDrop } from './DragDropProvider';
import { MealDragItem } from '@/types/new-meal-plan';
import { Plus, MoveHorizontal } from 'lucide-react';
import { motion } from 'framer-motion';

interface MealDropZoneProps {
  day: string;
  mealType: string;
  onAddMeal: (day: string, mealType: string) => void;
  children?: React.ReactNode;
  isEmpty?: boolean;
  isValidTarget?: boolean;
}

export function MealDropZone({
  day,
  mealType,
  onAddMeal,
  children,
  isEmpty = false,
  isValidTarget = true
}: MealDropZoneProps) {
  const { onDrop } = useDragDrop();

  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: 'MEAL',
    canDrop: () => isValidTarget,
    drop: (item: MealDragItem) => {
      if (!isValidTarget) return undefined;
      onDrop(item, day, mealType);
      return { day, mealType };
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
      canDrop: !!monitor.canDrop() && isValidTarget,
    }),
  }), [day, mealType, onDrop, isValidTarget]);

  const isActive = isOver && canDrop;

  // Get the drag and drop context to check if we're currently dragging
  const { isDragging } = useDragDrop();

  return (
    <motion.div
      ref={drop}
      className={`
        rounded-md transition-all
        ${isActive ? 'bg-primary/10 ring-2 ring-primary/30' : ''}
        ${isEmpty ? 'min-h-[100px] flex items-center justify-center' : ''}
        ${canDrop ? 'hover:bg-primary/5' : ''}
        ${isDragging && !isValidTarget ? 'opacity-50' : ''}
        ${isDragging && isValidTarget && !isActive ? 'ring-1 ring-primary/20' : ''}
      `}
      animate={{
        scale: isActive ? 1.02 : 1,
        boxShadow: isActive ? '0 0 8px rgba(0, 0, 0, 0.1)' : 'none',
        borderColor: isDragging && isValidTarget && !isActive ? 'rgba(var(--primary), 0.3)' : 'transparent',
      }}
      transition={{ type: 'spring', stiffness: 300, damping: 20 }}
    >
      {isEmpty ? (
        <button
          onClick={() => onAddMeal(day, mealType)}
          className="w-full h-full flex flex-col items-center justify-center p-4 text-muted-foreground hover:text-primary hover:bg-primary/5 rounded-md transition-colors"
        >
          <Plus className="h-6 w-6 mb-1" />
          <span className="text-sm">Add {mealType}</span>
        </button>
      ) : (
        <div className="relative w-full h-full">
          {canDrop && (
            <div className="absolute inset-0 flex items-center justify-center bg-background/80 opacity-0 hover:opacity-100 transition-opacity z-10 rounded-md">
              <div className="flex items-center justify-center p-2 bg-primary/10 rounded-full">
                <MoveHorizontal className="h-5 w-5 text-primary" />
              </div>
            </div>
          )}
          {children}
        </div>
      )}
    </motion.div>
  );
}
