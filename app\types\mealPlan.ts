import { Database } from './supabase';

// Ensure alignment with Supabase types
type DBMealPlan = Database['public']['Tables']['meal_plans']['Row'];

export interface Meal {
  id: string;
  name: string;
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  nutritionInfo: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  prepTime: number;
  cost: number;
  imageUrl?: string;
  isFavorite?: boolean;
  customizations?: Record<string, any>;
}

export interface DayPlan {
  date: string;
  meals: Meal[];
  totalCalories?: number;
  totalCost?: number;
}

export interface WeeklyPlan {
  id: string;
  startDate: string;
  endDate: string;
  days: DayPlan[];
  totalCost: number;
  averageCalories: number;
  nutritionSummary: {
    totalProtein: number;
    totalCarbs: number;
    totalFat: number;
  };
}

