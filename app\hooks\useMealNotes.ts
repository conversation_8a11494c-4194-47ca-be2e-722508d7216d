import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { MealNote } from '@/types/database-extended';

export function useMealNotes(mealPlanId?: string) {
  const queryClient = useQueryClient();

  const {
    data: mealNotes,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['meal-notes', mealPlanId],
    queryFn: async () => {
      const url = mealPlanId 
        ? `/api/meal-notes?meal_plan_id=${mealPlanId}`
        : '/api/meal-notes';
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Failed to fetch meal notes');
      }
      const result = await response.json();
      return result.data as MealNote[];
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  const createOrUpdateNoteMutation = useMutation({
    mutationFn: async ({ mealPlanId, mealId, note }: {
      mealPlanId: string;
      mealId: string;
      note: string;
    }) => {
      const response = await fetch('/api/meal-notes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          meal_plan_id: mealPlanId,
          meal_id: mealId,
          note,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to save meal note');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['meal-notes'] });
    },
  });

  const saveNote = (mealPlanId: string, mealId: string, note: string) => {
    return createOrUpdateNoteMutation.mutateAsync({ mealPlanId, mealId, note });
  };

  const getNoteForMeal = (mealPlanId: string, mealId: string) => {
    return mealNotes?.find(
      note => note.meal_plan_id === mealPlanId && note.meal_id === mealId
    );
  };

  const hasNoteForMeal = (mealPlanId: string, mealId: string) => {
    return !!getNoteForMeal(mealPlanId, mealId);
  };

  return {
    mealNotes: mealNotes || [],
    isLoading,
    error,
    refetch,
    saveNote,
    getNoteForMeal,
    hasNoteForMeal,
    isSaving: createOrUpdateNoteMutation.isPending,
  };
}

export function useMealNote(mealPlanId: string, mealId: string) {
  const queryClient = useQueryClient();

  const {
    data: mealNote,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['meal-note', mealPlanId, mealId],
    queryFn: async () => {
      const response = await fetch(`/api/meal-notes?meal_plan_id=${mealPlanId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch meal note');
      }
      const result = await response.json();
      const notes = result.data as MealNote[];
      return notes.find(note => note.meal_id === mealId) || null;
    },
    enabled: !!mealPlanId && !!mealId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  const saveNoteMutation = useMutation({
    mutationFn: async (note: string) => {
      const response = await fetch('/api/meal-notes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          meal_plan_id: mealPlanId,
          meal_id: mealId,
          note,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to save meal note');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['meal-note', mealPlanId, mealId] });
      queryClient.invalidateQueries({ queryKey: ['meal-notes'] });
    },
  });

  const saveNote = (note: string) => {
    return saveNoteMutation.mutateAsync(note);
  };

  return {
    mealNote,
    isLoading,
    error,
    refetch,
    saveNote,
    isSaving: saveNoteMutation.isPending,
    hasNote: !!mealNote,
  };
}
