'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const router = useRouter();

  useEffect(() => {
    // Log the error to your error reporting service
    console.error('Application error:', error);
  }, [error]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="text-center">
        <h2 className="mb-4 text-2xl font-bold">Something went wrong!</h2>
        <p className="mb-4 text-gray-600">
          {error.message || 'An unexpected error occurred'}
        </p>
        <div className="space-x-4">
          <button
            onClick={() => reset()}
            className="rounded-md bg-primary px-4 py-2 text-white hover:bg-primary/90"
          >
            Try again
          </button>
          <button
            onClick={() => router.push('/')}
            className="rounded-md border border-gray-300 px-4 py-2 hover:bg-gray-50"
          >
            Go home
          </button>
        </div>
      </div>
    </div>
  );
}