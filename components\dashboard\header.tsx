'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import Link from "next/link";

interface DashboardHeaderProps {
  heading: string;
  text?: string;
  showCreateButton?: boolean;
}

export function DashboardHeader({
  heading,
  text,
  showCreateButton = true,
}: DashboardHeaderProps) {
  return (
    <div className="flex items-center justify-between px-2">
      <div className="grid gap-1">
        <h1 className="text-2xl font-bold tracking-wide">{heading}</h1>
        {text && <p className="text-neutral-500">{text}</p>}
      </div>
      {showCreateButton && (
        <Button asChild>
          <Link href="/meal-plan/generate">
            <Plus className="mr-2 h-4 w-4" />
            Create Plan
          </Link>
        </Button>
      )}
    </div>
  );
}