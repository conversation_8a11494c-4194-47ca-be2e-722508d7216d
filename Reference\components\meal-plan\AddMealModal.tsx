"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON>rkles, Loader2, ChevronRight, Plus, Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { generateMeal, addCustomMeal } from "@/lib/meal-plan";
import { useMealPlanStore } from "@/lib/stores/meal-plan-store";
import { Meal } from "@/types/meal-plan";
import { toast } from "sonner";

interface AddMealModalProps {
  isOpen: boolean;
  onClose: () => void;
  day: string;
  mealType: string;
}

export default function AddMealModal({ isOpen, onClose, day, mealType }: AddMealModalProps) {
  const [activeTab, setActiveTab] = useState("ai");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedMeal, setGeneratedMeal] = useState<Meal | null>(null);
  const [customMeal, setCustomMeal] = useState<Partial<Meal>>({
    name: "",
    ingredients: [],
    instructions: [],
    prepTime: 30,
    calories: 0,
    cost: 0,
    servings: 1,
    nutrition: { carbs: 0, protein: 0, fat: 0 }
  });
  const [newIngredient, setNewIngredient] = useState({ name: "", quantity: "", unit: "" });
  const [newInstruction, setNewInstruction] = useState("");
  
  const { addMeal } = useMealPlanStore();

  const handleGenerateMeal = async () => {
    setIsGenerating(true);
    try {
      const meal = await generateMeal({ mealType });
      setGeneratedMeal(meal);
    } catch (error) {
      console.error("Failed to generate meal:", error);
      toast.error("Failed to generate meal. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleConfirmGeneratedMeal = () => {
    if (generatedMeal) {
      addMeal(day, mealType, generatedMeal);
      toast.success(`Added ${generatedMeal.name} to your meal plan`);
      onClose();
    }
  };

  const handleAddIngredient = () => {
    if (newIngredient.name && newIngredient.quantity && newIngredient.unit) {
      setCustomMeal(prev => ({
        ...prev,
        ingredients: [...(prev.ingredients || []), {
          name: newIngredient.name,
          quantity: newIngredient.quantity,
          unit: newIngredient.unit
        }]
      }));
      setNewIngredient({ name: "", quantity: "", unit: "" });
    }
  };

  const handleRemoveIngredient = (index: number) => {
    setCustomMeal(prev => ({
      ...prev,
      ingredients: prev.ingredients?.filter((_, i) => i !== index)
    }));
  };

  const handleAddInstruction = () => {
    if (newInstruction) {
      setCustomMeal(prev => ({
        ...prev,
        instructions: [...(prev.instructions || []), newInstruction]
      }));
      setNewInstruction("");
    }
  };

  const handleRemoveInstruction = (index: number) => {
    setCustomMeal(prev => ({
      ...prev,
      instructions: prev.instructions?.filter((_, i) => i !== index)
    }));
  };

  const handleSaveCustomMeal = () => {
    if (!customMeal.name) {
      toast.error("Please provide a meal name");
      return;
    }

    if (!customMeal.ingredients?.length) {
      toast.error("Please add at least one ingredient");
      return;
    }

    const meal = addCustomMeal(customMeal as Meal);
    addMeal(day, mealType, meal);
    toast.success(`Added ${meal.name} to your meal plan`);
    onClose();
  };

  const dayFormatted = new Date(day).toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' });
  const mealTypeFormatted = mealType.charAt(0).toUpperCase() + mealType.slice(1);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Meal</DialogTitle>
          <DialogDescription>
            {dayFormatted} - {mealTypeFormatted}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="ai" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="ai">AI Suggestion</TabsTrigger>
            <TabsTrigger value="custom">Custom Meal</TabsTrigger>
          </TabsList>
          <TabsContent value="ai" className="space-y-4 py-4">
            {!generatedMeal ? (
              <div className="flex flex-col items-center justify-center p-6 space-y-4">
                <Sparkles className="h-12 w-12 text-yellow-500 mb-2" />
                <h3 className="text-lg font-medium text-center">
                  Generate a {mealTypeFormatted} Suggestion
                </h3>
                <p className="text-center text-muted-foreground">
                  Our AI will suggest a delicious and budget-friendly meal based on your preferences.
                </p>
                <Button onClick={handleGenerateMeal} disabled={isGenerating}>
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-4 w-4" />
                      Generate Meal
                    </>
                  )}
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex justify-between items-start">
                  <h3 className="text-lg font-medium">{generatedMeal.name}</h3>
                  <div className="flex items-center gap-2">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => setGeneratedMeal(null)}
                    >
                      Try Again
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-lg">
                    <div>
                      <p className="text-xs text-muted-foreground">Cost</p>
                      <p className="text-sm font-medium">${generatedMeal.cost.toFixed(2)}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-lg">
                    <div>
                      <p className="text-xs text-muted-foreground">Calories</p>
                      <p className="text-sm font-medium">{generatedMeal.calories} cal</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-1">Ingredients</h4>
                  <ul className="text-sm space-y-1">
                    {generatedMeal.ingredients.map((ingredient, i) => (
                      <li key={i} className="flex items-start gap-1">
                        <span className="inline-block h-2 w-2 rounded-full bg-primary mt-1.5"></span>
                        {ingredient.quantity} {ingredient.unit} {ingredient.name}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}
          </TabsContent>
          <TabsContent value="custom" className="space-y-4 py-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="meal-name">Meal Name</Label>
                <Input 
                  id="meal-name" 
                  value={customMeal.name} 
                  onChange={(e) => setCustomMeal({...customMeal, name: e.target.value})}
                />
              </div>

              <div className="grid grid-cols-3 gap-2">
                <div>
                  <Label htmlFor="calories">Calories</Label>
                  <Input 
                    id="calories" 
                    type="number"
                    value={customMeal.calories || ""}
                    onChange={(e) => setCustomMeal({...customMeal, calories: parseInt(e.target.value) || 0})}
                  />
                </div>
                <div>
                  <Label htmlFor="cost">Cost ($)</Label>
                  <Input 
                    id="cost" 
                    type="number" 
                    step="0.01"
                    value={customMeal.cost || ""}
                    onChange={(e) => setCustomMeal({...customMeal, cost: parseFloat(e.target.value) || 0})}
                  />
                </div>
                <div>
                  <Label htmlFor="prepTime">Prep Time (min)</Label>
                  <Input 
                    id="prepTime" 
                    type="number"
                    value={customMeal.prepTime || ""}
                    onChange={(e) => setCustomMeal({...customMeal, prepTime: parseInt(e.target.value) || 0})}
                  />
                </div>
              </div>

              <div>
                <Label className="mb-2 block">Ingredients</Label>
                <div className="space-y-2 mb-2">
                  {customMeal.ingredients?.map((ingredient, i) => (
                    <div key={i} className="flex items-center justify-between p-2 bg-muted/50 rounded-lg">
                      <span className="text-sm">
                        {ingredient.quantity} {ingredient.unit} {ingredient.name}
                      </span>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-6 w-6 text-destructive"
                        onClick={() => handleRemoveIngredient(i)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input 
                    placeholder="Quantity (e.g. 2)"
                    className="w-1/4"
                    value={newIngredient.quantity}
                    onChange={(e) => setNewIngredient({...newIngredient, quantity: e.target.value})}
                  />
                  <Input 
                    placeholder="Unit (e.g. tbsp)"
                    className="w-1/4"
                    value={newIngredient.unit}
                    onChange={(e) => setNewIngredient({...newIngredient, unit: e.target.value})}
                  />
                  <Input 
                    placeholder="Ingredient name"
                    className="flex-1"
                    value={newIngredient.name}
                    onChange={(e) => setNewIngredient({...newIngredient, name: e.target.value})}
                  />
                  <Button onClick={handleAddIngredient} size="icon">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div>
                <Label className="mb-2 block">Instructions</Label>
                <div className="space-y-2 mb-2">
                  {customMeal.instructions?.map((instruction, i) => (
                    <div key={i} className="flex items-center justify-between p-2 bg-muted/50 rounded-lg">
                      <span className="text-sm mr-2">
                        {i + 1}. {instruction}
                      </span>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-6 w-6 flex-shrink-0 text-destructive"
                        onClick={() => handleRemoveInstruction(i)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Textarea
                    placeholder="Add cooking instruction"
                    className="flex-1"
                    value={newInstruction}
                    onChange={(e) => setNewInstruction(e.target.value)}
                  />
                  <Button onClick={handleAddInstruction} className="h-full">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          {activeTab === "ai" && generatedMeal ? (
            <Button onClick={handleConfirmGeneratedMeal}>
              Add to Plan
            </Button>
          ) : activeTab === "custom" ? (
            <Button onClick={handleSaveCustomMeal}>
              Add to Plan
            </Button>
          ) : null}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}