'use client';

import { useState, useEffect } from 'react';
import { WeeklyPlan, Meal, DayPlan } from '@/types/mealPlan';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ChevronDown, ChevronUp, Star, Calendar, Clock } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { toast } from 'sonner';

interface WeeklyPlanViewProps {
  initialPlan: WeeklyPlan | null;
}

export function WeeklyPlanView({ initialPlan }: WeeklyPlanViewProps) {
  const [plan, setPlan] = useState<WeeklyPlan | null>(initialPlan);
  const [expandedDays, setExpandedDays] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setPlan(initialPlan);
  }, [initialPlan]);

  if (isLoading || !plan) {
    return <WeeklyPlanSkeleton />;
  }

  const toggleDayExpansion = (date: string) => {
    setExpandedDays(current =>
      current.includes(date)
        ? current.filter(d => d !== date)
        : [...current, date]
    );
  };

  const handleFavoriteToggle = (mealId: string) => {
    toast('Added to favorites!');
    // Implement actual favorite toggling logic here
  };

  return (
    <div className="space-y-6">
      <WeeklyPlanHeader 
        startDate={plan.startDate} 
        endDate={plan.endDate}
      />
      
      <div className="space-y-4">
        {plan.days.map((day) => (
          <DayPanel
            key={day.date}
            day={day}
            isExpanded={expandedDays.includes(day.date)}
            onToggle={() => toggleDayExpansion(day.date)}
            onFavoriteToggle={handleFavoriteToggle}
          />
        ))}
      </div>

      <WeeklyPlanSummary
        totalCost={plan.totalCost}
        averageCalories={plan.averageCalories}
        nutritionSummary={{
          protein: 0, // Default values
          carbs: 0,
          fat: 0
        }}
      />
    </div>
  );
}

function WeeklyPlanHeader({ startDate, endDate }: { startDate: string; endDate: string }) {
  const formattedStartDate = format(parseISO(startDate), 'MMMM d, yyyy');
  const formattedEndDate = format(parseISO(endDate), 'MMMM d, yyyy');
  
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h1 className="text-2xl font-bold">Weekly Meal Plan</h1>
        <p className="text-muted-foreground">
          {formattedStartDate} - {formattedEndDate}
        </p>
      </div>
      <div className="flex gap-2">
        <Button variant="outline" size="sm">
          <Calendar className="h-4 w-4 mr-2" />
          Add to Calendar
        </Button>
        <Button variant="outline" size="sm">
          <Star className="h-4 w-4 mr-2" />
          Save as Favorite
        </Button>
      </div>
    </div>
  );
}

interface DayPanelProps {
  day: DayPlan;
  isExpanded: boolean;
  onToggle: () => void;
  onFavoriteToggle: (mealId: string) => void;
}

function DayPanel({ day, isExpanded, onToggle, onFavoriteToggle }: DayPanelProps) {
  const formattedDate = format(parseISO(day.date), 'EEEE, MMMM d');
  
  return (
    <Card>
      <CardHeader className="py-3 cursor-pointer" onClick={onToggle}>
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">{formattedDate}</CardTitle>
          <Button variant="ghost" size="sm" onClick={(e) => { e.stopPropagation(); onToggle(); }}>
            {isExpanded ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </Button>
        </div>
      </CardHeader>
      {isExpanded && (
        <CardContent>
          <div className="space-y-4">
            {day.meals.map((meal) => (
              <MealCard 
                key={meal.id} 
                meal={meal} 
                onFavoriteToggle={() => onFavoriteToggle(meal.id)}
              />
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
}

interface MealCardProps {
  meal: Meal;
  onFavoriteToggle: () => void;
}

function MealCard({ meal, onFavoriteToggle }: MealCardProps) {
  return (
    <Card className="overflow-hidden">
      <div className="flex flex-col md:flex-row">
        {meal.image && (
          <div className="w-full md:w-1/4">
            <img 
              src={meal.image} 
              alt={meal.name} 
              className="w-full h-40 md:h-full object-cover"
            />
          </div>
        )}
        <div className="flex-1 p-4">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-semibold text-lg">{meal.name}</h3>
              <div className="flex items-center text-muted-foreground mt-1">
                <Clock className="h-4 w-4 mr-1" />
                <span className="text-sm">{meal.prepTime} min</span>
                <Badge variant="outline" className="ml-2">{meal.type}</Badge>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={onFavoriteToggle}>
              <Star className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="mt-4">
            <h4 className="font-medium text-sm">Nutrition:</h4>
            <p className="text-sm mt-1">
              {meal.nutrition.calories} calories • 
              {meal.nutrition.protein}g protein • 
              {meal.nutrition.carbs}g carbs • 
              {meal.nutrition.fats}g fat
            </p>
          </div>
          
          <div className="mt-4 flex flex-wrap gap-2">
            <Badge variant="secondary">${meal.cost.toFixed(2)}</Badge>
            <Badge variant="outline">{meal.servings} servings</Badge>
          </div>
        </div>
      </div>
    </Card>
  );
}

function WeeklyPlanSummary({ 
  totalCost, 
  averageCalories, 
  nutritionSummary 
}: { 
  totalCost: number; 
  averageCalories: number;
  nutritionSummary: {
    protein: number;
    carbs: number;
    fat: number;
  };
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Weekly Summary</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 bg-muted rounded-lg">
            <h3 className="font-medium text-sm text-muted-foreground">Total Cost</h3>
            <p className="text-2xl font-bold">${totalCost.toFixed(2)}</p>
          </div>
          <div className="p-4 bg-muted rounded-lg">
            <h3 className="font-medium text-sm text-muted-foreground">Avg. Calories</h3>
            <p className="text-2xl font-bold">{Math.round(averageCalories)}</p>
          </div>
          <div className="p-4 bg-muted rounded-lg">
            <h3 className="font-medium text-sm text-muted-foreground">Macros</h3>
            <div className="flex gap-2 mt-1">
              <Badge variant="outline">P: {nutritionSummary.protein}g</Badge>
              <Badge variant="outline">C: {nutritionSummary.carbs}g</Badge>
              <Badge variant="outline">F: {nutritionSummary.fat}g</Badge>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function WeeklyPlanSkeleton() {
  return (
    <div className="space-y-6" data-testid="weekly-plan-skeleton">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-32 mt-2" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-9 w-32" />
          <Skeleton className="h-9 w-32" />
        </div>
      </div>
      
      <div className="space-y-4">
        {[1, 2, 3, 4, 5, 6, 7].map((day) => (
          <Card key={day}>
            <CardHeader className="py-3">
              <div className="flex justify-between items-center">
                <Skeleton className="h-6 w-40" />
                <Skeleton className="h-8 w-8 rounded-full" />
              </div>
            </CardHeader>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
