'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Clock, Users, Scale } from 'lucide-react';
import type { Meal } from '@/app/types/mealPlanner';
import { toast } from 'sonner';

interface MealCustomizerProps {
  meal: Meal;
  isOpen: boolean;
  onClose: () => void;
  onSave: (meal: Meal) => void;
}

export function MealCustomizer({ meal, isOpen, onClose, onSave }: MealCustomizerProps) {
  const [customization, setCustomization] = useState({
    portionSize: meal.customization?.portionSize || 1,
    servingSize: meal.customization?.servingSize || meal.originalServings || 1,
    cookingTime: meal.customization?.cookingTime || meal.prepTime || 30,
  });

  // Reset customization when meal changes
  useEffect(() => {
    setCustomization({
      portionSize: meal.customization?.portionSize || 1,
      servingSize: meal.customization?.servingSize || meal.originalServings || 1,
      cookingTime: meal.customization?.cookingTime || meal.prepTime || 30,
    });
  }, [meal]);

  // Calculate calories based on portion size
  const calculatedCalories = Math.round(meal.calories * customization.portionSize);

  const handleSave = () => {
    const updatedMeal = {
      ...meal,
      customization: {
        ...customization,
        ingredients: meal.customization?.ingredients || [], 
      },
      calories: calculatedCalories,
      prepTime: customization.cookingTime,
    };
    onSave(updatedMeal);
    toast.success('Meal customization saved');
    onClose();
  };

  const handleServingSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value >= 1 && value <= 12) {
      setCustomization(prev => ({ ...prev, servingSize: value }));
    }
  };

  const handleCookingTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value >= 5 && value <= 240) {
      setCustomization(prev => ({ ...prev, cookingTime: value }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Customize {meal.name}</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* Portion Size Adjuster */}
          <div className="grid gap-2">
            <Label htmlFor="portion-size" className="flex items-center gap-2">
              <Scale className="h-4 w-4" />
              Portion Size Multiplier
            </Label>
            <div className="grid gap-2">
              <Slider
                id="portion-size"
                min={0.5}
                max={3}
                step={0.5}
                value={[customization.portionSize]}
                onValueChange={([value]) => 
                  setCustomization(prev => ({ ...prev, portionSize: value }))
                }
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>0.5x</span>
                <span>{customization.portionSize}x</span>
                <span>3x</span>
              </div>
              <div className="text-sm text-muted-foreground">
                Calories: {calculatedCalories}
              </div>
            </div>
          </div>

          {/* Serving Size Calculator */}
          <div className="grid gap-2">
            <Label htmlFor="serving-size" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Number of Servings
            </Label>
            <Input
              id="serving-size"
              type="number"
              min={1}
              max={12}
              value={customization.servingSize}
              onChange={handleServingSizeChange}
            />
          </div>

          {/* Cooking Time Modifier */}
          <div className="grid gap-2">
            <Label htmlFor="cooking-time" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Cooking Time (minutes)
            </Label>
            <Input
              id="cooking-time"
              type="number"
              min={5}
              max={240}
              value={customization.cookingTime}
              onChange={handleCookingTimeChange}
            />
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
