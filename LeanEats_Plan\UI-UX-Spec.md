### **UI/UX Specification: LeanEats MVP**

**Objective:** To translate the LeanEats PRD into a detailed design blueprint, defining intuitive, engaging, and feasible user experiences and interfaces for the Minimum Viable Product. This document will guide frontend development.

---

#### **1. User Onboarding Flow**

**Objective:** Guide new users through account creation and initial preference setup seamlessly, collecting essential information required for personalized meal plan generation.

**Overall Flow Type:** Multi-step Wizard with clear progress indication.

**Core User Journey:**
1.  Landing Page / Call to Action
2.  Sign-up / Login
3.  Welcome & Introduction
4.  Step 1: Basic Household & Budget
5.  Step 2: Dietary Needs & Exclusions
6.  Step 3: Cooking Habits & Cuisines
7.  Review & Confirm
8.  Onboarding Complete / Redirect to Dashboard

---

##### **Screen 1: Welcome / Sign-up / Login**

* **Purpose:** Initial entry point. Allow existing users to log in or new users to sign up and begin their journey.
* **Key UI Elements:**
    * **Header:** Prominent LeanEats logo/branding.
    * **Headline:** Engaging welcome message (e.g., "Your Personalized Meal Planning Journey Starts Here!").
    * **Sub-headline:** Briefly explain the app's value proposition (e.g., "Healthy, budget-friendly meals, tailored just for you.").
    * **Login Form:**
        * Email input field.
        * Password input field.
        * "Forgot Password?" link.
        * "Login" button (Primary action).
    * **"OR" Separator.**
    * **OAuth Buttons:** "Continue with Google," "Continue with Apple" (as per `Authentication.md`).
    * **"Don't have an account? Sign Up" link** (Secondary action, leads to Signup state).
* **Interactions:**
    * Clicking "Login": Validates fields (`Authentication.md`), shows loading spinner, attempts login. On success, transitions to Welcome screen or directly to preference wizard if new user/preferences not set. On failure, displays inline error messages (e.g., "Invalid email or password", "Too many attempts" - `Authentication.md`, `ErrorHandling.md`).
    * Clicking OAuth button: Initiates OAuth flow, redirects.
    * Clicking "Sign Up" link: Transitions to Signup form state (same screen, form changes).
* **Signup Form State (on Screen 1):**
    * **Fields:** Email, Password, Confirm Password.
    * **Button:** "Create Account" (Primary action).
    * **Interaction:** On "Create Account", validates fields (including password requirements), shows loading. Success sends verification email, redirects to email verification page (`Authentication.md`). Failure highlights errors.

---

##### **Screen 2: Welcome & Wizard Introduction**

* **Purpose:** A brief, friendly introduction to the personalization wizard after successful sign-up/login (if preferences are not yet complete). Sets user expectations.
* **Key UI Elements:**
    * **Header:** LeanEats logo, "Skip for now" link (small, top right).
    * **Headline:** "Let's Personalize Your Meals!"
    * **Illustration/Animation:** Engaging, relevant graphic (e.g., healthy food, family eating).
    * **Body Text:** Explain *why* preferences are important (e.g., "To give you the best meal plans, we need a little info about your household and tastes. This will only take a few minutes!").
    * **"Start Personalization" button** (Primary action).
* **Interactions:**
    * Clicking "Start Personalization": Transitions to "Step 1: Basic Household & Budget."
    * Clicking "Skip for now": Redirects to Dashboard with default settings or prompts for manual preference setup later.

---

##### **Wizard Structure (for subsequent steps)**

* **Persistent UI Elements (across all wizard steps):**
    * **Progress Indicator:** Visual representation (e.g., "Step 1 of 4", filled circles/bars) at the top, showing current step and total steps.
    * **"Back" Button:** (Enabled from Step 2 onwards) Allows navigating to the previous step.
    * **"Next" Button:** (Primary action) Advances to the next step.
    * **"Save & Exit" / "Skip" Button:** (Secondary action) Allows users to leave the wizard and save current progress, or skip to dashboard.

---

##### **Wizard Step 1: Basic Household & Budget**

* **Purpose:** Collect essential information about the user's household size and budget.
* **Key UI Elements:**
    * **Headline:** "Tell Us About Your Household & Budget"
    * **Input Field: Household Size:**
        * Type: Number input with +/- steppers.
        * Label: "How many people are you planning for?"
        * Default: 1
    * **Selection: Budget Tier:**
        * Type: Radio buttons or segmented control for clear choice.
        * Label: "What's your weekly grocery budget?"
        * Options:
            * "Budget Conscious" (e.g., "Under $75 / €70 / ₹5,500 per week")
            * "Moderate" (e.g., "$75 - $150 / €70 - €140 / ₹5,500 - ₹11,000 per week")
            * "Flexible" (e.g., "Above $150 / €140 / ₹11,000 per week")
            * **Note:** The currency symbol will dynamically update based on the user's selected region/currency preference (future feature or default based on IP, for MVP initial assumption).
    * **Selection: Preferred Currency:** (New addition)
        * Type: Dropdown select.
        * Label: "Preferred currency for cost estimates:"
        * Options: USD, EUR, GBP, JPY, INR (for MVP).
        * Help text: "Cost estimations for some regions may be approximate."
* **Interactions:**
    * Changing values dynamically updates labels or provides immediate feedback where applicable.
    * "Next" button validation: Ensures both fields are selected/entered. If not, displays inline error messages.

---

##### **Wizard Step 2: Dietary Needs & Exclusions**

* **Purpose:** Capture specific dietary restrictions, preferences, and ingredients to avoid.
* **Key UI Elements:**
    * **Headline:** "Any Dietary Needs or Ingredients to Avoid?"
    * **Multi-select Checkboxes/Toggles: Dietary Restrictions/Preferences:**
        * Label: "Select any dietary preferences or restrictions:"
        * Options: Vegan, Vegetarian, Gluten-Free, Keto, Paleo, Dairy-Free, Nut-Free, etc. (as per PRD & `UserProfile.md`).
    * **Text Input with Tags/Chips: Excluded Ingredients:**
        * Label: "List any specific ingredients you want to avoid (e.g., cilantro, mushrooms):"
        * Users type and press enter/comma to create "tags."
        * Ability to remove tags.
* **Interactions:**
    * Selecting options immediately reflects in the UI.
    * Typing excluded ingredients adds them as visible tags.
    * "Next" button: Advances without specific validation other than ensuring input formats are correct.

---

##### **Wizard Step 3: Cooking Habits & Cuisines**

* **Purpose:** Understand the user's cooking comfort level and preferred flavor profiles.
* **Key UI Elements:**
    * **Headline:** "Your Cooking Style & Taste"
    * **Selection: Cooking Skill Level:**
        * Type: Slider or discrete buttons (Beginner, Intermediate, Advanced) with short descriptions.
        * Label: "How would you describe your cooking skill?"
        * Descriptions (on hover/select):
            * **Beginner:** "Comfortable with basic recipes, prefer simple steps."
            * **Intermediate:** "Can follow most recipes, enjoy trying new techniques."
            * **Advanced:** "Confident with complex dishes, love culinary challenges."
    * **Input Field: Max Cooking Time:**
        * Type: Number input with +/- steppers, or slider.
        * Label: "Maximum total time (prep + cook) per meal (in minutes):"
        * Default: 30 minutes. Range: 15-120+.
    * **Multi-select Checkboxes/Tags: Cuisine Preferences:**
        * Label: "Which cuisines do you enjoy most?"
        * Options: Italian, Mexican, Indian, Chinese, Japanese, Mediterranean, American, Thai, etc. (Offer a diverse, but manageable list for MVP).
* **Interactions:**
    * Selections update visually.
    * "Next" button: Validates numerical input for cooking time.

---

##### **Screen (Wizard Step 4): Review & Confirm**

* **Purpose:** Allow users to review all their entered preferences before finalizing, offering a chance to go back and edit.
* **Key UI Elements:**
    * **Headline:** "Almost Done! Review Your Preferences"
    * **Summary Cards:** Display each preference category (Household, Budget, Dietary, Cooking) with the user's selections clearly listed.
        * Each card should have an "Edit" button/icon that takes the user back to that specific wizard step.
    * **"Confirm & Generate First Plan!" button** (Primary action).
    * **"Go Back" button** (Secondary action).
* **Interactions:**
    * Clicking "Edit" on a summary card navigates back to the corresponding wizard step.
    * Clicking "Confirm & Generate First Plan!": Triggers the saving of preferences to `user_profiles` table (`UserProfile.md`) and then initiates the first meal plan generation. Shows a prominent loading state. On success, redirects to the Dashboard with the newly generated meal plan.

---

#### **2. Dashboard Layout & Interactions**

**Objective:** Provide users with a comprehensive, at-a-glance overview of their current meal plan, quick access to key actions, and easy navigation to other features.

**Overall Layout:** A responsive, multi-panel layout designed for desktop-first, gracefully adapting to tablet and mobile. Consists of a persistent Header, a collapsible Navigation Sidebar (on desktop, bottom navigation or hamburger menu on mobile), and a main Content Area.

---

##### **2.1. Global UI Elements**

* **Header (Persistent across all main views):**
    * **Left:** LeanEats Logo (Clickable, leads to Dashboard).
    * **Center/Title:** Current page title (e.g., "Dashboard," "Meal Plan," "Shopping List").
    * **Right:**
        * **Notifications Icon:** (Bell icon) Displays unread count badge (`Notifications.md`). Clicking opens a notification dropdown/panel.
        * **Profile Icon/Avatar:** (User's profile picture or default icon) Clicking opens a dropdown menu with options: "Profile Settings," "Account Settings," "Help," "Logout" (`Dashboard.md`).
* **Navigation Sidebar (Desktop) / Mobile Navigation (Mobile):**
    * **Desktop Sidebar:** Collapsible. Contains primary navigation links:
        * Dashboard (Home icon)
        * Meal Plans (Calendar icon)
        * Shopping List (Shopping cart icon)
        * Recipes (Cookbook icon)
        * Settings (Gear icon)
    * **Mobile Navigation:** Bottom navigation bar for primary actions or a hamburger menu for full navigation, depending on screen width.

---

##### **2.2. Dashboard Main Content Area**

* **Structure:** Divided into logical sections to provide quick access and overview.
* **Sections:**

    **2.2.1. Weekly Meal Plan Overview (Primary Focus Area)**
    * **Purpose:** Display the current 7-day meal plan at a glance.
    * **Layout:** A horizontally scrollable or card-based layout showing each day of the week (Mon-Sun).
    * **Each Day Card/Section:**
        * **Day & Date:** Clearly displayed (e.g., "Monday, July 1st").
        * **Meal Slots:** Sections for "Breakfast," "Lunch," "Dinner," and "Snacks (Optional)".
        * **Recipe Cards (within slots):**
            * Small thumbnail image of the recipe (`Integration.md`).
            * Recipe Name (Truncated if long).
            * Quick info icons: Est. Prep Time, Est. Cost, Basic Calories (or a summary of macro nutrients).
    * **Interactions:**
        * **"Generate New Plan" Button:** Prominently placed (e.g., top right of this section). Clicking opens the Meal Plan Configuration Modal (`MealPlanner.md`).
        * **"Edit Plan" Button:** Allows drag-and-drop reordering of meals within and between days (`Dashboard.md`).
        * **Clicking a Recipe Card:** Expands to a detailed Recipe View (either as a modal or a new page - to be specified later).
        * **Meal Swapping:** On hover/click of a recipe card, a "Swap Meal" option appears. Clicking reveals 3-5 alternative suggestions (`MealPlanner.md`). Users can select an alternative, and the plan updates.

    **2.2.2. Quick Actions Section**
    * **Purpose:** Provide immediate access to frequent tasks.
    * **Layout:** A row or grid of prominent action buttons/cards.
    * **Actions:**
        * **"Generate New Plan"** (Duplicate from above for easy access).
        * **"View Shopping List"**: Navigates to the dedicated Shopping List page.
        * **"Add Custom Recipe"**: Opens a form/modal for adding a user-defined recipe (future phase).
        * **"Adjust Preferences"**: Navigates to the User Profile Settings page (`UserProfile.md`).
    * **Interactions:** Each button directly performs its action, often with a loading state before navigating or opening a modal.

    **2.2.3. Statistics & Insights (Optional/Folded by default for MVP)**
    * **Purpose:** Display key metrics related to meal planning and budget.
    * **Layout:** Small cards or widgets.
    * **Metrics (MVP):**
        * "Weekly Estimated Grocery Cost" (Based on current plan).
        * "Meals Remaining This Week."
        * "Average Cost Per Meal."
    * **Interactions:** Clicking a stat card could eventually lead to a more detailed analytics page (`Analytics.md`). A "Refresh" icon can be available to update data.

---

##### **2.3. Interactions & Feedback (General)**

* **Loading States:** For all data fetches (e.g., plan generation, data refresh), display clear skeleton screens or loading spinners to indicate activity (`ErrorHandling.md`).
* **Error Messages:** Utilize clear, user-friendly inline or toast notifications for validation errors, API failures, or network issues (`ErrorHandling.md`).
* **Success Notifications:** Brief, non-intrusive toast messages for successful actions (e.g., "Plan saved!", "Item added to list").
* **Responsiveness:**
    * **Desktop:** Full sidebar, multi-column main content.
    * **Tablet:** Sidebar may collapse or become an overlay. Content layout adjusts to fewer columns.
    * **Mobile:** Sidebar replaced by hamburger menu/bottom nav. Main content becomes single-column, optimized for vertical scrolling. Cards may stack.

---

#### **3. Shopping List Management**

**Objective:** Enable users to easily view, manage, customize, and export their grocery shopping lists generated from their meal plans.

---

##### **3.1. Shopping List View Screen**

* **Purpose:** Present a clear, organized, and actionable list of all ingredients needed for the selected meal plan.
* **Layout:**
    * **Header:** "Shopping List" title. "Generate New List" button (prominent, top right).
    * **Sub-header:** Displaying the total estimated cost of the list.
    * **Filtering/Sorting Options:** (Optional for MVP, but good to design for) E.g., filter by category, sort alphabetically.
    * **Search Bar:** To quickly find specific ingredients within the list.
    * **List Sections:** Ingredients grouped by logical categories (e.g., Produce, Dairy, Pantry, Meat, etc.). Each category can be collapsible.
* **Key UI Elements:**
    * **Shopping List Items (within categories):**
        * Checkbox for "purchased" status.
        * Ingredient Name (e.g., "Chicken Breast").
        * Quantity (e.g., "1.5 lbs" or "680g").
        * Unit of measurement (e.g., "lbs," "g," "cups," "units").
        * Estimated Cost (per item, if possible from Spoonacular API, or grouped cost for similar items).
        * Source Meal (optional, small text below item, e.g., "From Chicken Stir-fry").
        * "More Options" (ellipsis icon): For editing/removing an item.
* **Interactions:**
    * **"Generate New List" Button:** Triggers re-generation of the list based on the *current* active meal plan (`MealPlanner.md`). Displays a loading state. On success, refreshes the list.
    * **Checkbox (Item):** Toggles item as "purchased" (e.g., strikes through text, moves to bottom of list). Persists state.
    * **Clicking "More Options" (ellipsis):** Opens a small contextual menu with:
        * "Edit Quantity" (opens inline editor or small modal).
        * "Remove Item."
        * "Mark as Purchased/Unpurchased."
    * **Searching:** Filters the list in real-time as the user types.
    * **Collapsing/Expanding Categories:** Toggles visibility of items within a category.

---

##### **3.2. Item Details & Editing (Modal/Inline)**

* **Purpose:** Allow users to modify individual items on the shopping list.
* **Key UI Elements (Modal/Inline Edit):**
    * Ingredient Name (non-editable if from plan, editable if custom).
    * Quantity input field (number input with +/- steppers).
    * Unit dropdown (e.g., lbs, kg, cups, units, bundles).
    * "Notes" text field (optional, e.g., "organic," "from farmers market").
    * "Save" and "Cancel" buttons.
* **Interactions:**
    * **"Edit Quantity":** User enters new quantity; "Save" updates the item in the list (`DatabaseSchema.md`).
    * **"Remove Item":** Prompts for confirmation ("Are you sure you want to remove X?"). On confirmation, removes item from list.

---

##### **3.3. Customization: Add Custom Item**

* **Purpose:** Allow users to add items not generated from their meal plan.
* **Key UI Elements:**
    * **"Add Custom Item" Button:** Located prominently (e.g., floating action button, or fixed button at bottom of list).
    * **Add Item Modal/Form:**
        * Text Input: "Item Name" (e.g., "Paper Towels").
        * Number Input: "Quantity" (with +/-).
        * Dropdown: "Unit" (e.g., "units," "packs," "rolls").
        * Dropdown: "Category" (e.g., Household, Personal Care, Other - for grouping).
        * "Add to List" and "Cancel" buttons.
* **Interactions:**
    * Clicking "Add Custom Item": Opens the modal.
    * Filling out form and clicking "Add to List": Adds the item to the relevant category in the shopping list (`DatabaseSchema.md`).

---

##### **3.4. Export & Shop Options**

* **Purpose:** Provide convenient ways to take the list outside the app or initiate a purchase.
* **Key UI Elements:**
    * **"Export List" Button:** (Top right or bottom of list)
    * **"Shop Now" Button:** (Prominent, bottom of list, or sticky button).
* **Interactions:**
    * **"Export List" Button:**
        * Opens a small modal/dropdown with options:
            * "Download as PDF."
            * "Copy to Clipboard."
            * "Email List."
            * "Print."
    * **"Shop Now" Button:**
        * Navigates to an external page or opens a new tab.
        * For **US users:** Direct links to major online grocery retailers (e.g., Walmart, Amazon Fresh, Instacart) pre-populating a cart if possible (highly complex, likely future phase, for MVP just direct link to retailer homepage or search result for key items).
        * For **EU/Asian users:** Links to regional online grocery retailers if integrated, or a generic Google Shopping search for key items, with a clear disclaimer about limited integration/accuracy.

---

##### **3.5. Integration with Meal Plan**

* **Behavior:** The shopping list is dynamically generated from the *active* meal plan.
    * If a meal is swapped or removed from the meal plan, the shopping list automatically updates (`MealPlanner.md`).
    * If a custom item is added to the shopping list, it does *not* affect the meal plan.
    * If the meal plan preferences are changed and a new plan is generated, the shopping list will need to be regenerated or automatically update to reflect the new plan's ingredients.

---

#### **4. Recipe Viewing & Interaction**

**Objective:** Provide users with clear, detailed, and actionable recipe information, enabling them to explore, manage, utilize, and schedule individual recipes efficiently.

---

##### **4.1. Recipe Detail View Screen**

* **Purpose:** Display comprehensive information for a single recipe, whether accessed from a meal plan, search results, or favorites.
* **Layout:** A clean, scrollable layout, prioritizing readability and visual appeal.
* **Key UI Elements:**
    * **Recipe Hero Section:**
        * **Recipe Name:** Prominent title.
        * **Hero Image:** High-quality, appealing image of the finished dish (`Integration.md` for sourcing).
        * **Quick Stats Bar:** (Below image/name) Icons and text for:
            * Estimated Prep Time (e.g., "15 mins")
            * Estimated Cook Time (e.g., "25 mins")
            * Total Estimated Cost (e.g., "$8.50 per serving")
            * Number of Servings (e.g., "4 servings") - with interactive scaling.
            * Dietary Tags (e.g., "Vegan," "Gluten-Free").
        * **Action Buttons:**
            * "Add to Favorites" (Heart icon, toggles state - see `UserProfile.md` for favorites concept).
            * "Share Recipe" (Share icon, opens share options).
            * "Print Recipe" (Printer icon).
            * **"Add to Calendar" (Calendar+ icon):** This will be a prominent action.
            * "Add to Meal Plan" (Calendar+ icon - if not already part of current plan).
    * **Ingredients Section:**
        * **Headline:** "Ingredients"
        * **List:** Bulleted list of ingredients with clear quantities and units.
        * **Checkbox:** Optional checkbox next to each ingredient for "checked off as used" during cooking (local persistence only, not sync).
    * **Instructions Section:**
        * **Headline:** "Instructions"
        * **Numbered Steps:** Clear, concise cooking steps.
        * **Tips/Notes:** (Optional) If provided by API or AI.
    * **Nutritional Information Section:**
        * **Headline:** "Nutrition Facts (per serving)"
        * **Display:** Clear, digestible format (e.g., table or infographic) showing:
            * Calories, Protein, Carbohydrates, Fat (Total, Saturated, Unsaturated), Fiber, Sugar, Sodium.
        * **Source:** "Data provided by [API Name]" (e.g., Edamam, Spoonacular).
    * **"Generate Shopping List" Button:** (Sticky at bottom, or prominent action if not already on Shopping List page).

* **Interactions:**
    * **Servings Scaler:** Clicking +/- on the "Number of Servings" dynamically adjusts ingredient quantities in real-time. A tooltip/modal might explain the adjustment.
    * **"Add to Favorites":** Toggles recipe's favorite status. Provides visual feedback (e.g., heart fills/unfills, toast message "Added to Favorites").
    * **"Share Recipe":** Opens native share sheet (on mobile) or a modal with options (copy link, email, social media links).
    * **"Print Recipe":** Opens browser print dialogue, optimized for printing (e.g., removes navigation, ads).
    * **"Add to Meal Plan":** Opens a modal allowing the user to select a specific day/meal slot to add the recipe to.
    * **"Add to Calendar":**
        * Clicking this button opens a **"Schedule Meal" modal/dialogue.**
        * **Modal Content:**
            * **Headline:** "Schedule [Recipe Name]"
            * **Date Picker:** Input field with calendar icon to select the desired date.
            * **Time Picker:** Input field to select the desired time (e.g., "7:00 PM").
            * **Meal Type (Optional):** Dropdown (Breakfast, Lunch, Dinner, Snack) to help categorize.
            * **"Add to Google Calendar" button:** (Primary action)
            * **"Cancel" button:** (Secondary action)
        * **Modal Interactions:**
            * User selects date and time.
            * Clicking "Add to Google Calendar" triggers the `Calendar.create` API call.
                * `title`: Recipe Name
                * `start_datetime`: User selected date and time (yyyymmddTHHMM format).
                * `description`: Could include a summary of ingredients and a link back to the recipe in LeanEats.
                * `provider`: `google_calendar`
            * Displays a loading state.
            * On success: Displays a toast notification "Recipe added to your Google Calendar!" and closes the modal.
            * On failure: Displays an error message within the modal (e.g., "Failed to add to calendar. Please try again." - `ErrorHandling.md`).
    * **"Generate Shopping List":** If on this screen, generates a shopping list for *only this recipe* or prompts to update the full weekly list.

---

##### **4.2. Recipe Search & Browse (High-Level)**

* **Purpose:** Allow users to discover recipes beyond their generated meal plans.
* **Key UI Elements:**
    * **Search Bar:** Prominent, persistent search bar (e.g., on a dedicated "Recipes" page, or accessible from header).
    * **Filter Options:**
        * Dietary Restrictions (checkboxes).
        * Cuisine Type (multi-select).
        * Max Prep/Cook Time (slider/input).
        * Skill Level (dropdown).
        * Ingredient Inclusion/Exclusion (text input for tags).
    * **Recipe Cards (Search Results/Browse):** Similar to Dashboard cards, but perhaps with more focus on image and name.
* **Interactions:**
    * Typing in search bar filters results in real-time or on submit.
    * Applying filters refines the displayed recipe cards.
    * Clicking a recipe card leads to the Recipe Detail View.

---

##### **4.3. Integration Points**

* **Meal Plan:** Recipes form the core content of the meal plan. Changes to recipe quantities here could *eventually* propagate back to meal plan details if chosen to scale the entire plan.
* **Shopping List:** Ingredients from viewed recipes can be added to the shopping list. If a recipe is added to a meal plan, its ingredients are automatically added to the main shopping list.
* **User Preferences:** Recipe filtering and suggestions in search/browse should adhere to the user's saved preferences (dietary, skill, time).
* **Calendar Integration:** Direct scheduling of recipes to the user's external calendar (Google Calendar for MVP).

---

#### **5. Profile & Settings Management**

**Objective:** Provide users with a centralized and intuitive interface to view and modify their personal information, meal planning preferences, account settings, and privacy controls.

---

##### **5.1. Profile & Settings Dashboard (Overview)**

* **Purpose:** A landing page for settings, offering quick access to different categories.
* **Layout:** A sidebar navigation (or tabbed interface on mobile/tablet) on the left, with the main content area displaying the selected section on the right.
* **Key UI Elements:**
    * **Header:** "Settings" title.
    * **Navigation List (Sidebar/Tabs):**
        * "My Profile" (Default active section)
        * "Meal Preferences"
        * "Account Security"
        * "Notifications"
        * "Privacy"
        * "Data & Export"
        * "Help & Support" (Link out or simple FAQ)
    * **Main Content Area:** Dynamically loads the content for the selected navigation item.

---

##### **5.2. My Profile Section**

* **Purpose:** Allow users to view and edit their basic profile information.
* **Key UI Elements:**
    * **Profile Picture:** Circular display. "Change Picture" button/overlay on hover (`UserProfile.md` - Profile Picture Update).
    * **Editable Fields:**
        * "Name": Text input.
        * "Email": Text input (non-editable here, direct to Account Security for change).
        * "Location" (Optional for MVP): Text input (e.g., City, Country, to potentially refine future local recommendations).
    * **"Save Changes" Button** (Primary action, enabled when changes are made).
    * **"Cancel" Button** (Secondary action).
* **Interactions:**
    * **"Change Picture":** Opens a modal for file upload, preview, and cropping (`UserProfile.md` - Profile Picture Update).
    * **Editing fields:** Triggers "Save Changes" button to become active.
    * **"Save Changes":** Validates inputs. Displays loading state. On success, shows a success toast and updates the displayed profile (`UserProfile.md` - User Edits Profile). On failure, shows inline error messages (`ErrorHandling.md`).

---

##### **5.3. Meal Preferences Section**

* **Purpose:** Allow users to modify their core meal planning preferences (same as onboarding, but editable).
* **Key UI Elements:**
    * **Input Field: Household Size:** Number input with +/- steppers.
    * **Selection: Budget Tier:** Radio buttons/segmented control with dynamic currency display (USD/EUR/etc., based on user's selection).
    * **Selection: Preferred Currency:** Dropdown select.
    * **Multi-select Checkboxes/Toggles: Dietary Restrictions/Preferences.**
    * **Text Input with Tags/Chips: Excluded Ingredients.**
    * **Selection: Cooking Skill Level:** Slider or discrete buttons.
    * **Input Field: Max Cooking Time:** Number input with +/- steppers.
    * **Multi-select Checkboxes/Tags: Cuisine Preferences.**
    * **"Update Preferences" Button** (Primary action).
* **Interactions:**
    * Similar to the onboarding wizard, but changes are saved immediately upon clicking "Update Preferences."
    * Updating preferences may trigger a prompt: "Your meal plan preferences have changed. Would you like to generate a new meal plan reflecting these changes?" with "Yes, Generate Now" and "Later" options. This would link to `MealPlanner.md` for plan generation.

---

##### **5.4. Account Security Section**

* **Purpose:** Enable users to manage their account credentials.
* **Key UI Elements:**
    * **Password Change:**
        * Current Password input field.
        * New Password input field.
        * Confirm New Password input field.
        * "Change Password" button (`UserProfile.md` - Password Change, `Authentication.md`).
    * **Email Update:**
        * Current Email (display only).
        * New Email input field.
        * "Update Email" button (`UserProfile.md` - Email Update, `Authentication.md`).
    * **Two-Factor Authentication (2FA) Status:** (Optional for MVP, but design for it) Display status (Enabled/Disabled) with a "Manage 2FA" button.
* **Interactions:**
    * **Password Change:** Validates inputs (current password, new password complexity, match). Shows loading. On success, provides confirmation toast and may log out other sessions (`Authentication.md`). On failure, shows inline errors.
    * **Email Update:** Requires re-authentication, sends verification email to new address (`Authentication.md`). Displays instructions to verify new email.

---

##### **5.5. Notifications Section**

* **Purpose:** Allow users to control what notifications they receive and how.
* **Key UI Elements:**
    * **Toggle: Push Notifications** (Overall on/off).
    * **Checkboxes/Toggles: Notification Categories:**
        * Meal Plan Updates (e.g., new plan generated, plan modified).
        * Shopping Reminders (e.g., "Time to shop!").
        * Budget Alerts (e.g., "You're approaching your weekly budget limit").
        * Promotional/Announcements.
    * **Dropdown: Notification Frequency** (e.g., Daily, Weekly, Instant).
    * **Input: Quiet Hours** (Start Time, End Time for push notifications).
    * **"Save Notification Settings" Button.**
* **Interactions:**
    * Toggles and selections update dynamically.
    * "Save Notification Settings" persists changes (`Notifications.md`).

---

##### **5.6. Privacy Section**

* **Purpose:** Inform users about data handling and allow them to manage data sharing.
* **Key UI Elements:**
    * **Toggle: Data Sharing for Product Improvement:** (e.g., "Allow LeanEats to use anonymized data to improve meal plan recommendations.")
    * **Link: Privacy Policy.**
    * **Link: Terms of Service.**
    * **"Update Privacy Settings" Button.**
* **Interactions:**
    * Toggles update data sharing preferences (`UserProfile.md`).

---

##### **5.7. Data & Export Section**

* **Purpose:** Provide tools for users to manage and export their data.
* **Key UI Elements:**
    * **"Export My Data" Button:**
        * Opens a modal.
        * Checkbox options for data to export (e.g., "Meal Plans," "Saved Recipes," "Shopping Lists," "Profile Data").
        * Dropdown for format (e.g., "JSON," "CSV," "PDF").
        * "Generate & Download" button (`UserProfile.md` - Export Data).
    * **"Delete My Account" Button:** (Prominent, with warning styling).
        * Opens a confirmation modal.
        * Requires password re-entry for security.
        * Strong warning about data loss.
        * "Confirm Deletion" button (`UserProfile.md` - Account Deletion).
* **Interactions:**
    * **"Export My Data":** Triggers data aggregation and download. Shows loading and success/failure messages.
    * **"Delete My Account":** Initiates account deletion process after confirmation and password verification.

---