"use client";

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Button } from '@/components/ui/button';

export function SupabaseTest() {
  const [result, setResult] = useState<any>(null);
  const [user, setUser] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkAuth = async () => {
      const supabase = createClientComponentClient();
      const { data } = await supabase.auth.getUser();
      setUser(data.user);
    };

    checkAuth();
  }, []);

  const testSupabaseConnection = async () => {
    try {
      setError(null);
      const supabase = createClientComponentClient();

      // Test a simple query - just get all records and count them client-side
      const { data, error } = await supabase
        .from('meal_plans')
        .select('id');

      if (error) {
        setError(error.message);
        return;
      }

      setResult({
        connection: 'Success',
        count: data?.length || 0,
        message: `Successfully connected to Supabase and found ${data?.length || 0} meal plans`
      });
    } catch (err: any) {
      setError(err.message);
    }
  };

  const testSaveMealPlan = async () => {
    try {
      setError(null);
      const supabase = createClientComponentClient();

      // Get the current user
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        setError("Not authenticated");
        return;
      }

      // Create a simple test meal plan
      const testMealPlan = {
        test_day: {
          breakfast: {
            id: "test-meal-" + Date.now(),
            name: "Test Meal",
            description: "Test meal created for debugging",
            ingredients: [],
            instructions: [],
            nutrition: { protein: 10, carbs: 20, fat: 5 },
            calories: 200,
            cost: 5.99,
            servings: 1
          }
        }
      };

      // Save to database
      const { data, error } = await supabase
        .from('meal_plans')
        .insert({
          user_id: userData.user.id,
          start_date: new Date().toISOString(),
          end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          total_cost: 5.99,
          meal_data: testMealPlan,
          status: 'active'
        })
        .select();

      if (error) {
        setError(error.message);
        return;
      }

      setResult(data);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const testGetMealPlans = async () => {
    try {
      setError(null);
      const supabase = createClientComponentClient();

      // Get the current user
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        setError("Not authenticated");
        return;
      }

      // Get meal plans
      const { data, error } = await supabase
        .from('meal_plans')
        .select('*')
        .eq('user_id', userData.user.id)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (error) {
        setError(error.message);
        return;
      }

      setResult(data);
    } catch (err: any) {
      setError(err.message);
    }
  };

  return (
    <div className="p-4 border rounded-md">
      <h2 className="text-lg font-bold mb-2">Supabase Connection Test</h2>

      <div className="mb-4">
        <h3 className="font-semibold">Authentication Status:</h3>
        {user ? (
          <div className="text-green-600">
            Authenticated as: {user.email} (ID: {user.id})
          </div>
        ) : (
          <div className="text-red-600">Not authenticated</div>
        )}
      </div>

      <div className="flex flex-col gap-2 mb-4">
        <Button onClick={testSupabaseConnection}>
          Test Supabase Connection
        </Button>

        <Button onClick={testSaveMealPlan}>
          Test Save Meal Plan
        </Button>

        <Button onClick={testGetMealPlans}>
          Test Get Meal Plans
        </Button>
      </div>

      {error && (
        <div className="text-red-600 mt-2">
          Error: {error}
        </div>
      )}

      {result && (
        <div className="mt-2">
          <h3 className="font-semibold">Result:</h3>
          <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-96">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
