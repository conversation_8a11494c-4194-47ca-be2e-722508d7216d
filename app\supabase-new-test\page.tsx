'use client';

import React, { useEffect, useState } from 'react';
import { useSupabase } from '@/components/supabase-provider';

export default function NewTestPage() {
  const { supabase, isLoading } = useSupabase();
  const [user, setUser] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [testComplete, setTestComplete] = useState(false);

  useEffect(() => {
    // Only run this effect if Supabase is loaded
    if (isLoading || !supabase) return;

    console.log('NewTestPage: Supabase client is ready');

    // Get the current user
    const getUser = async () => {
      try {
        console.log('NewTestPage: Getting user');
        const { data, error } = await supabase.auth.getUser();

        if (error) {
          throw error;
        }

        console.log('NewTestPage: User data', data);
        setUser(data.user);
      } catch (err: any) {
        console.error('NewTestPage: Error getting user', err);
        setError(err.message || 'Failed to get user');
      } finally {
        setTestComplete(true);
      }
    };

    getUser();
  }, [supabase, isLoading]);

  return (
    <div className="container max-w-4xl py-8">
      <h1 className="text-2xl font-bold mb-6">New Supabase Provider Test</h1>

      <div className="p-4 bg-gray-50 border border-gray-200 rounded-md mb-6">
        <h2 className="text-lg font-semibold mb-2">Provider Status</h2>
        <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
        <p><strong>Supabase Client:</strong> {supabase ? 'Available' : 'Not Available'}</p>
      </div>

      {isLoading ? (
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-md text-blue-700">
          Loading Supabase client...
        </div>
      ) : !supabase ? (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md text-red-700">
          Error: Supabase client could not be created
        </div>
      ) : !testComplete ? (
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-md text-blue-700">
          Running test...
        </div>
      ) : error ? (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md text-red-700">
          Error: {error}
        </div>
      ) : (
        <div className="p-4 bg-green-50 border border-green-200 rounded-md text-green-700">
          <p><strong>Test Result:</strong> Success!</p>
          <p><strong>User:</strong> {user ? user.email || user.id : 'Not logged in'}</p>
        </div>
      )}
    </div>
  );
}
