import { NextRequest, NextResponse } from 'next/server';
import { generateRecipeWithAI } from '@/lib/api/openai';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { mealType, calories, dietaryPreferences, excludeIngredients, cookingTime } = body;
    
    if (!mealType || !calories) {
      return NextResponse.json(
        { error: 'Missing required parameters: mealType and calories are required' },
        { status: 400 }
      );
    }
    
    const recipe = await generateRecipeWithAI({
      mealType,
      calories,
      dietaryPreferences: dietaryPreferences || [],
      excludeIngredients: excludeIngredients || [],
      cookingTime: cookingTime || 'medium'
    });
    
    return NextResponse.json(recipe);
  } catch (error: any) {
    console.error('Error in OpenAI generate recipe API route:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while generating the recipe' },
      { status: 500 }
    );
  }
}
