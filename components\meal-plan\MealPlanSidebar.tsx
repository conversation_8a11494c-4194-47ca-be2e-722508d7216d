"use client";

import { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Calendar, Clock, DollarSign, Plus, ShoppingCart, Trash2 } from 'lucide-react';
import { MealPlanSidebarFilter, SortOption, FilterOption } from './MealPlanSidebarFilter';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { MealPlan } from '@/types/meal-plan';

interface MealPlanSidebarProps {
  mealPlans: MealPlan[];
  activePlanId: string | null;
  onSelectPlan: (plan: MealPlan) => void;
  onDeletePlan: (planId: string) => void;
  onAddToCalendar: (plan: MealPlan) => void;
  onGenerateShoppingList: (plan: MealPlan) => void;
  onCreatePlan: () => void;
}

export function MealPlanSidebar({
  mealPlans,
  activePlanId,
  onSelectPlan,
  onDeletePlan,
  onAddToCalendar,
  onGenerateShoppingList,
  onCreatePlan
}: MealPlanSidebarProps) {
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortOption, setSortOption] = useState<SortOption>('newest');
  const [filterOption, setFilterOption] = useState<FilterOption>('all');

  const handleDeleteClick = (planId: string, e: React.MouseEvent) => {
    e.stopPropagation();

    if (confirmDelete === planId) {
      onDeletePlan(planId);
      setConfirmDelete(null);
    } else {
      setConfirmDelete(planId);

      // Auto-reset after 3 seconds
      setTimeout(() => {
        setConfirmDelete(null);
      }, 3000);
    }
  };

  const handleAddToCalendar = (plan: MealPlan, e: React.MouseEvent) => {
    e.stopPropagation();
    onAddToCalendar(plan);
  };

  const handleGenerateShoppingList = (plan: MealPlan, e: React.MouseEvent) => {
    e.stopPropagation();
    onGenerateShoppingList(plan);

    // Show a toast message
    toast.success(`Generating shopping list for ${plan.name || 'Meal Plan'}`);
  };

  // Filter and sort meal plans
  const filteredMealPlans = useMemo(() => {
    // First apply filters
    let result = [...mealPlans];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(plan => {
        const name = plan.name || 'Meal Plan';
        return name.toLowerCase().includes(query);
      });
    }

    // Apply status filter
    if (filterOption !== 'all') {
      result = result.filter(plan => plan.status === filterOption);
    }

    // Apply sorting
    result.sort((a, b) => {
      switch (sortOption) {
        case 'newest':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'oldest':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case 'name-asc':
          return (a.name || 'Meal Plan').localeCompare(b.name || 'Meal Plan');
        case 'name-desc':
          return (b.name || 'Meal Plan').localeCompare(a.name || 'Meal Plan');
        case 'cost-asc':
          const costA = a.total_cost || (a.meal_data?.mealPlan?.summary?.totalCost || 0);
          const costB = b.total_cost || (b.meal_data?.mealPlan?.summary?.totalCost || 0);
          return costA - costB;
        case 'cost-desc':
          const costADesc = a.total_cost || (a.meal_data?.mealPlan?.summary?.totalCost || 0);
          const costBDesc = b.total_cost || (b.meal_data?.mealPlan?.summary?.totalCost || 0);
          return costBDesc - costADesc;
        default:
          return 0;
      }
    });

    return result;
  }, [mealPlans, searchQuery, sortOption, filterOption]);

  return (
    <div className="w-full h-full flex flex-col">
      <div className="p-4">
        <h2 className="text-lg font-semibold mb-2">My Meal Plans</h2>
        <Button
          onClick={onCreatePlan}
          className="w-full"
          variant="outline"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create New Plan
        </Button>
      </div>

      <Separator />

      <MealPlanSidebarFilter
        onSearch={setSearchQuery}
        onSort={setSortOption}
        onFilter={setFilterOption}
        currentSort={sortOption}
        currentFilter={filterOption}
      />

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {filteredMealPlans.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {mealPlans.length === 0 ? (
                <>
                  <p>No meal plans found</p>
                  <p className="text-sm mt-2">Create your first meal plan to get started</p>
                </>
              ) : searchQuery ? (
                <>
                  <p>No results found</p>
                  <p className="text-sm mt-2">Try a different search term</p>
                </>
              ) : filterOption !== 'all' ? (
                <>
                  <p>No {filterOption} plans found</p>
                  <p className="text-sm mt-2">Try a different filter</p>
                </>
              ) : (
                <p>No meal plans found</p>
              )}
            </div>
          ) : (
            filteredMealPlans.map((plan) => {
              const isActive = plan.id === activePlanId;
              const isConfirmingDelete = plan.id === confirmDelete;

              // Extract plan name or use default
              const planName = plan.name || 'Meal Plan';

              // Format dates
              const startDate = plan.start_date ? format(new Date(plan.start_date), 'MMM d, yyyy') : 'N/A';
              const endDate = plan.end_date ? format(new Date(plan.end_date), 'MMM d, yyyy') : 'N/A';

              // Extract total cost
              const totalCost = plan.total_cost ||
                (plan.meal_data?.mealPlan?.summary?.totalCost || 0);

              return (
                <Card
                  key={plan.id}
                  className={`cursor-pointer transition-all ${isActive ? 'border-primary bg-primary/5 shadow-md' : 'hover:border-muted-foreground hover:bg-muted/30'}`}
                  onClick={() => onSelectPlan(plan)}
                >
                  <CardHeader className="p-4 pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-base">{planName}</CardTitle>
                      {isActive && (
                        <div className="h-2 w-2 rounded-full bg-primary mt-1"></div>
                      )}
                    </div>
                    <CardDescription className="text-xs">
                      {startDate} - {endDate}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="p-4 pt-0 pb-2">
                    <div className="flex items-center text-sm text-muted-foreground">
                      <DollarSign className="h-3 w-3 mr-1" />
                      <span>${typeof totalCost === 'number' ? totalCost.toFixed(2) : '0.00'}</span>

                      <span className="mx-2">•</span>

                      <Clock className="h-3 w-3 mr-1" />
                      <span>7 days</span>
                    </div>
                  </CardContent>

                  <CardFooter className="p-2 flex justify-between">
                    <Button
                      variant={isActive ? "secondary" : "ghost"}
                      size="sm"
                      className="h-8 px-2"
                      onClick={(e) => handleAddToCalendar(plan, e)}
                    >
                      <Calendar className="h-3.5 w-3.5 mr-1" />
                      <span className="text-xs">Calendar</span>
                    </Button>

                    <Button
                      variant={isActive ? "secondary" : "ghost"}
                      size="sm"
                      className="h-8 px-2"
                      onClick={(e) => handleGenerateShoppingList(plan, e)}
                    >
                      <ShoppingCart className="h-3.5 w-3.5 mr-1" />
                      <span className="text-xs">Shopping</span>
                    </Button>

                    <Button
                      variant={isConfirmingDelete ? "destructive" : isActive ? "secondary" : "ghost"}
                      size="sm"
                      className="h-8 px-2"
                      onClick={(e) => handleDeleteClick(plan.id, e)}
                    >
                      <Trash2 className="h-3.5 w-3.5 mr-1" />
                      <span className="text-xs">{isConfirmingDelete ? "Confirm" : "Delete"}</span>
                    </Button>
                  </CardFooter>
                </Card>
              );
            })
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
