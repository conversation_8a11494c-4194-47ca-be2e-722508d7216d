import axios from 'axios';

/**
 * Search for recipes using the Edamam API (server-side version)
 */
export async function searchRecipesServer(params: {
  query: string;
  diet?: string[];
  health?: string[];
  calories?: string;
  mealType?: string[];
  cuisineType?: string[];
  excluded?: string[];
  random?: boolean;
}): Promise<any> {
  try {
    console.log('Searching recipes with Edamam API (server-side)...', params);
    
    // Get API keys from environment variables
    const EDAMAM_RECIPE_APP_ID = process.env.NEXT_PUBLIC_EDAMAM_APP_ID_RECIPIES;
    const EDAMAM_RECIPE_APP_KEY = process.env.NEXT_PUBLIC_EDAMAM_APP_KEY_RECIPIES;
    
    console.log('Edamam API Keys:', {
      APP_ID: EDAMAM_RECIPE_APP_ID,
      APP_KEY: EDAMAM_RECIPE_APP_KEY ? 'Present (not shown)' : 'Missing'
    });

    // Validate API keys
    if (!EDAMAM_RECIPE_APP_ID || !EDAMAM_RECIPE_APP_KEY) {
      throw new Error('Missing Edamam API keys');
    }

    // Prepare the query parameters
    const queryParams = new URLSearchParams({
      type: 'public',
      app_id: EDAMAM_RECIPE_APP_ID,
      app_key: EDAMAM_RECIPE_APP_KEY,
      q: params.query || 'meal' // Use 'meal' as a default query if none provided
    });

    // Add optional parameters
    if (params.diet && params.diet.length > 0) {
      params.diet.forEach(diet => queryParams.append('diet', diet));
    }

    if (params.health && params.health.length > 0) {
      params.health.forEach(health => queryParams.append('health', health));
    }

    if (params.calories) {
      queryParams.append('calories', params.calories);
    }

    if (params.mealType && params.mealType.length > 0) {
      params.mealType.forEach(mealType => queryParams.append('mealType', mealType));
    }

    if (params.cuisineType && params.cuisineType.length > 0) {
      params.cuisineType.forEach(cuisineType => queryParams.append('cuisineType', cuisineType));
    }

    if (params.excluded && params.excluded.length > 0) {
      params.excluded.forEach(excluded => queryParams.append('excluded', excluded));
    }

    if (params.random) {
      queryParams.append('random', 'true');
    }
    
    // Log the full URL for debugging
    const EDAMAM_RECIPE_BASE_URL = 'https://api.edamam.com/api/recipes/v2';
    const requestUrl = `${EDAMAM_RECIPE_BASE_URL}?${queryParams.toString()}`;
    console.log('Edamam API request URL:', requestUrl);

    // Make the API request
    try {
      const response = await axios.get(requestUrl, {
        headers: {
          'Edamam-Account-User': 'user123' // Add a default user ID
        }
      });
      
      return response.data;
    } catch (apiError: any) {
      console.error('Edamam API request failed:', apiError.message);
      
      if (apiError.response) {
        console.error('Response status:', apiError.response.status);
        console.error('Response data:', apiError.response.data);
      }
      
      throw apiError;
    }
  } catch (error) {
    console.error('Error searching recipes with Edamam API (server-side):', error);
    throw error;
  }
}
