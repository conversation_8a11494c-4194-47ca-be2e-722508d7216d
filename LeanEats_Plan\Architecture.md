# **LeanEats Application Architecture Plan**

**Version:** 1.0
**Date:** June 28, 2025
**Authored By:** The Architecture Team

-----

## **1. Introduction**

### **1.1 Purpose**

This document outlines the holistic architectural blueprint for the LeanEats application, covering both the current web platform and strategic considerations for a future mobile application. Its purpose is to provide a comprehensive guide for the development team, ensuring alignment, scalability, maintainability, and security across all components.

### **1.2 Scope**

This architecture plan encompasses:

  * The web-based LeanEats application (MVP).
  * The backend services and data layers supporting both web and mobile clients.
  * Detailed strategies for integrating external APIs and handling cross-cutting concerns like security, error handling, and analytics.
  * Design principles and technology recommendations to facilitate a seamless transition to a native mobile application in the future.

### **1.3 Key Architectural Principles**

  * **API-First Design:** All client-server interactions will be exclusively through well-defined, versioned APIs.
  * **Decoupled Architecture:** Clear separation between frontend clients and backend services for independent development, deployment, and scaling.
  * **Scalability:** Design components to handle increasing user loads and data volumes.
  * **Modularity:** Break down the system into independent, reusable services.
  * **Reliability & Resilience:** Implement robust error handling, monitoring, and fallback mechanisms.
  * **Security:** Integrate security best practices across all layers, from authentication to data storage.
  * **Data Consistency:** Ensure data integrity and synchronization across all platforms and services.
  * **Performance:** Optimize for fast response times and efficient resource utilization.
  * **Maintainability:** Prioritize clean code, comprehensive documentation, and ease of debugging.
  * **Extensibility:** Design for future feature additions and integrations.

-----

## **2. High-Level Architecture Overview**

LeanEats employs a **decoupled, API-first architecture** with distinct client applications communicating with a set of modular backend services. This approach allows for maximum flexibility, scalability, and the efficient addition of new client types, such as a mobile app.

### **2.1 Conceptual Diagram**

```mermaid
graph TD
    subgraph Clients
        A[LeanEats Web Application]
        B[LeanEats Mobile Application (Future)]
    end

    subgraph API_Layer
        C[API Gateway / Backend Service Layer]
    end

    subgraph Core_Services
        D[Authentication Service]
        E[User & Profile Service]
        F[Meal Plan Service]
        G[Recipe Service]
        H[Shopping List Service]
        I[Notification Service]
        J[Analytics Service]
    end

    subgraph External_Integrations
        K[Edamam API]
        L[Spoonacular API]
        M[OpenAI API]
        N[Google Calendar API]
        O[Grocery Retailer APIs / Links]
        P[Email/Push Notification Providers]
    end

    subgraph Data_Storage
        Q[Supabase PostgreSQL Database]
        R[Cloud Storage (Media/Assets)]
        S[Caching Layer]
    end

    A --> C
    B --> C

    C --> D
    C --> E
    C --> F
    C --> G
    C --> H
    C --> I
    C --> J

    D --> Q
    E --> Q
    F --> Q
    G --> Q
    H --> Q
    I --> Q
    J --> Q
    F --> R
    G --> R
    E --> R

    F --> K
    F --> L
    F --> M
    G --> K
    G --> L
    H --> L
    H --> O
    I --> P
    E --> N (Future Integration)

    Q --> S (Reads from DB, Writes from Services)
    C --> S (Reads for faster responses)
```

### **2.2 Client-Server Interaction Flow**

All client requests (from web or mobile) will first hit the **API Gateway/Backend Service Layer**. This layer acts as a unified entry point, handling routing, initial authentication checks, and potentially rate limiting. Requests are then routed to the appropriate **Core Service**, which contains the business logic. These services interact with the **Data Storage** (Supabase, Cloud Storage) and communicate with **External Integrations** as needed to fulfill the request. Responses are then sent back through the API Gateway to the client.

-----

## **3. Client-Side Architecture (Web & Mobile)**

The client-side architecture is designed for responsiveness, performance, and a consistent user experience across different platforms, while maximizing code reusability.

### **3.1 Web Application**

The LeanEats web application serves as the primary MVP client, built with a modern and efficient stack.

#### **3.1.1 Technology Stack**

  * **Framework:** **Next.js (React)**
      * **Rationale:** Provides server-side rendering (SSR) for initial load performance, file-system based routing, API routes for backend-for-frontend (BFF) patterns, and a robust development experience.
  * **Language:** **TypeScript**
      * **Rationale:** Enhances code quality, reduces runtime errors, and improves developer productivity with type checking and intelligent auto-completion.
  * **State Management:** **Zustand** (for global state) and **React Context API** (for local/component-tree specific states).
      * **Rationale:** Zustand is lightweight, flexible, and performant. React Context is ideal for simpler, localized state.
  * **Styling:** **Tailwind CSS** (for utility-first styling) with **CSS Modules** (for complex, encapsulated components).
  * **Data Fetching & Caching:** **React Query**
      * **Rationale:** Manages server state, handles caching, revalidation, and background fetching, greatly simplifying data synchronization with the API.

#### **3.1.2 Project Structure**

  * `/pages`: Next.js pages for routing.
  * `/components`: Reusable UI components (atomic to complex).
  * `/hooks`: Custom React hooks for reusable logic.
  * `/lib`: Utility functions, helpers, constants.
  * `/api`: Next.js API routes (BFF layer for specific client-side needs, e.g., session management).
  * `/services`: API client wrappers for interacting with the main backend services.
  * `/styles`: Global styles and Tailwind configurations.
  * `/types`: TypeScript type definitions.

#### **3.1.3 API Integration Strategy**

  * Dedicated `services` layer for all API calls.
  * Use `axios` or `fetch` for HTTP requests wrapped by React Query.
  * Centralized error handling for API responses (e.g., interceptors to handle 401s, 500s).

#### **3.1.4 Performance & Optimization**

  * **Image Optimization:** Next.js Image component for lazy loading, responsive images.
  * **Code Splitting:** Automatic by Next.js for faster initial loads.
  * **Data Caching:** React Query's robust caching mechanisms.
  * **Selective SSR/SSG:** Leverage Next.js's rendering strategies where appropriate (e.g., static generation for marketing pages, SSR for initial user data load, client-side fetching for dynamic content).

#### **3.1.5 Accessibility (A11y)**

  * Prioritize semantic HTML.
  * Utilize ARIA attributes for custom components.
  * Ensure keyboard navigation support.
  * Adhere to WCAG guidelines for color contrast and visual design.

#### **3.1.6 Testing Strategy**

  * **Unit Tests:** Jest and React Testing Library for components, hooks, utilities.
  * **Integration Tests:** React Testing Library for interactions between components or with mocked APIs.
  * **End-to-End Tests (Future):** Playwright or Cypress for full user flow testing.

#### **3.1.7 Deployment**

  * **Platform:** Vercel is recommended for seamless integration with Next.js, automatic scaling, and CI/CD capabilities.
  * **Environment Variables:** Secure management of sensitive configurations.

### **3.2 Mobile Application (Future)**

Strategic planning for a mobile application from the outset ensures efficient development and a consistent user experience.

#### **3.2.1 Rationale for React Native**

Given the existing frontend expertise in React and the web application's foundation, **React Native** is the ideal choice for future mobile development.

  * **Code Reusability:** Maximize sharing of JavaScript/TypeScript code, including:
      * API client wrappers and data models.
      * Core business logic (e.g., meal plan processing, shopping list aggregation that might run client-side).
      * Utility functions, type definitions, validation logic.
      * Potentially, generic UI components or design system elements.
  * **Developer Skillset:** Leverages existing React knowledge, reducing learning curve and accelerating development.
  * **Single Codebase:** Target both iOS and Android from one codebase.
  * **Mature Ecosystem:** Robust community and extensive libraries.

#### **3.2.2 Shared Code Strategy**

  * Implement a monorepo setup (e.g., Nx, Turborepo) to host both web and React Native projects, facilitating shared packages.
  * Extract common code into shared packages (e.g., `@lean-eats/api-client`, `@lean-eats/types`, `@lean-eats/utils`).
  * Adopt a unified design system that translates well to both web and native components.

#### **3.2.3 Specific Mobile Considerations**

  * **Push Notifications:** Integrate a push notification service (e.g., Firebase Cloud Messaging) with the Notification Service backend.
  * **Offline Support:** Implement robust offline capabilities with local data caching and synchronization mechanisms, especially critical for meal plans and shopping lists. This will involve queueing updates and retrying on network recovery.
  * **Device-Specific Features:** Account for features like camera access (for profile pictures, future recipe scanning), location services (for grocery store integration), and device storage.
  * **Performance:** Optimize bundle size, native module usage, and rendering performance for mobile devices.

#### **3.2.4 Deployment**

  * Deployment to Apple App Store (iOS) and Google Play Store (Android).
  * Implement continuous integration and continuous delivery (CI/CD) pipelines for mobile builds.

-----

## **4. Backend Services Architecture**

The backend is designed as a collection of cohesive services, exposing functionality through a unified API layer.

### **4.1 Core Principles**

#### **4.1.1 Service-Oriented Approach**

While not a full microservices architecture initially, the design is modular, with logically distinct services. This allows for easier transition to independent microservices if required by scale or complexity in the future.

#### **4.1.2 API-First Design**

  * All backend functionalities are exposed via a well-defined RESTful API.
  * API documentation (Swagger/OpenAPI) will be a core artifact.
  * Standardized request/response formats, status codes, and error messages.
  * Versioned APIs to support graceful evolution.

#### **4.1.3 Scalability & Reliability**

  * Stateless services (where possible) for horizontal scaling.
  * Use of managed services (Supabase) for database and authentication.
  * Implement caching strategies for frequently accessed data.
  * Asynchronous processing for long-running tasks (e.g., complex meal plan generation).

#### **4.1.4 Security Considerations**

  * Robust authentication and authorization mechanisms (JWTs).
  * Input validation on all API endpoints to prevent injection attacks.
  * Rate limiting to protect against brute-force attacks and abuse.
  * Secure handling of sensitive data (encryption at rest and in transit).
  * Row-Level Security (RLS) within Supabase for data access control.
  * Comprehensive error handling to prevent sensitive information disclosure.

### **4.2 Service Breakdown**

#### **4.2.1 Authentication Service**

  * **Purpose:** Manages user authentication and authorization.
  * **Key Responsibilities:**
      * User registration (email/password, OAuth providers like Google, Apple).
      * User login and session management.
      * Password reset and account recovery flows.
      * Token generation and validation (JWTs).
      * User verification (email verification).
  * **Technology:** Primarily leveraging **Supabase Auth** capabilities.
  * **Security:** Implements rate limiting for login attempts, session invalidation on password change, and secure credential handling.

#### **4.2.2 User & Profile Service**

  * **Purpose:** Manages user profiles, preferences, and account settings.
  * **Key Responsibilities:**
      * Creating and updating user profiles (basic info, profile pictures).
      * Managing dietary preferences (restrictions, allergies, goals).
      * Handling budget settings.
      * Account settings: email update, password change (triggers auth service), privacy settings (data sharing).
      * Data management: export user data, account deletion.
  * **Integrations:** Cloud Storage for profile pictures. Google Calendar API (future) for meal plan scheduling.

#### **4.2.3 Meal Plan Service**

  * **Purpose:** Generates, customizes, and manages personalized meal plans.

  * **Key Responsibilities:**

      * **Plan Generation:** Implements three approaches:
          * **EdaSpoon Approach:** Utilizes Edamam for recipe data and nutrition, Spoonacular for ingredient cost.
          * **AI Approach:** Uses OpenAI API to generate complete meal plans, including recipes, nutrition, and cost estimates.
          * **Hybrid Approach:** Combines AI for initial structure with Edamam/Spoonacular for real recipe data and accurate costs. This will be the primary robust approach.
      * **Plan Modification:** Swapping meals, adjusting servings, reordering, manual recipe additions.
      * **Budget & Dietary Compliance:** Ensures generated plans adhere to user-defined budget and dietary restrictions.
      * **Nutritional Calculation:** Aggregates nutritional information for meals and the entire plan.
      * **Plan Sharing:** Generates shareable links with permission controls.
      * **Data Synchronization:** Manages real-time updates and backups.

  * **External API Integrations:** Edamam, Spoonacular, OpenAI.

  * **Meal Plan Generation Flow (Hybrid Approach):**

    ```mermaid
    graph LR
        A[User Preferences & Goals] --> B{Meal Plan Service};
        B --> C[Send Prompt to OpenAI API];
        C --> D[OpenAI Generates Initial Plan Structure (Recipes, etc.)];
        D --> E{For Each AI-Generated Recipe};
        E --> F[Query Edamam API (Recipe Search)];
        F --> G{Found Edamam Recipe?};
        G -- Yes --> H[Fetch Recipe Details & Nutrition];
        G -- No --> I[Fallback: Use AI-Generated Recipe Directly];
        H --> J[Query Spoonacular API (Ingredient Costs)];
        I --> J;
        J --> K[Combine Data: AI + Edamam + Spoonacular];
        K --> L[Assemble Complete Meal Plan];
        L --> M[Validate & Save to DB];
        M --> N[Return Meal Plan to Client];
    ```

#### **4.2.4 Recipe Service**

  * **Purpose:** Manages recipe data, discovery, and user interactions with recipes.
  * **Key Responsibilities:**
      * Recipe discovery and search (integrating with Edamam, Spoonacular).
      * Providing detailed recipe views (ingredients, instructions, nutrition, cost).
      * Recipe substitution recommendations.
      * Managing user favorite recipes.
      * Handling user-submitted recipes (future enhancement).
  * **Integrations:** Edamam API, Spoonacular API, Cloud Storage (for recipe images).

#### **4.2.5 Shopping List Service**

  * **Purpose:** Generates and manages shopping lists from meal plans.
  * **Key Responsibilities:**
      * Aggregating ingredients from a meal plan.
      * Combining similar items and calculating quantities.
      * Categorizing items by grocery type/aisle.
      * Allowing customization (add, remove, adjust quantities, mark purchased).
      * Providing export options (PDF, email).
      * Estimating total shopping list cost (via Spoonacular).
  * **Integrations:** Spoonacular API. Future integration with Grocery Retailer APIs/links.

#### **4.2.6 Notification Service**

  * **Purpose:** Manages and delivers in-app, push, and email notifications.
  * **Key Responsibilities:**
      * Sending meal plan generation notifications.
      * Shopping reminders, prep reminders, budget alerts.
      * System alerts (updates, security).
      * Managing user notification preferences.
      * Real-time updates via WebSockets (for in-app notifications).
  * **Integrations:** Third-party push notification providers, email service providers.

#### **4.2.7 Analytics Service**

  * **Purpose:** Collects, processes, and reports on user behavior and application performance data.
  * **Key Responsibilities:**
      * User behavior tracking (page views, feature usage, click patterns, session analysis).
      * Performance metrics (load times, response times, error rates).
      * Business intelligence (user engagement, retention, acquisition, revenue tracking).
      * Recipe and shopping analytics.
      * Error tracking and technical monitoring.
      * Automated reporting and data visualization.
  * **Data Collection:** Event-driven tracking for user actions.
  * **Privacy:** Implement consent management, data minimization, anonymization, and access control.

-----

## **5. Data Layer**

The data layer is centered around a robust, scalable, and secure PostgreSQL database, supplemented by cloud storage for assets.

### **5.1 Database (Supabase PostgreSQL)**

  * **Platform:** **Supabase**
      * **Rationale:** Provides a managed PostgreSQL database, built-in authentication, real-time capabilities, and an API layer, accelerating backend development.
  * **Schema Details:**
      * **Core Tables:**
          * `users` (managed by Supabase Auth)
          * `user_profiles` (stores user-specific data like preferences, settings)
          * `meal_plans`
          * `recipes`
          * `shopping_lists`
          * `ingredients`
          * `user_favorites` (junction table for user-recipe relationships)
          * `meal_plan_recipes` (junction table)
          * `recipe_ingredients` (junction table)
      * **Indexing Strategy:** Utilize primary keys, foreign keys, and specific search/performance indexes for efficient querying.
      * **Data Types:** Appropriate use of UUIDs for IDs, JSONB for flexible schema data (e.g., complex meal plan configurations), array fields, and timestamps for tracking.
  * **Security:**
      * **Row-Level Security (RLS):** Policies will be defined to ensure users can only access and modify their own data, significantly enhancing data privacy and security.
      * Regular backups and recovery plans will be in place.
  * **Migrations:** Database schema changes will be managed via version control and migration scripts to ensure smooth updates and rollbacks.

### **5.2 Cloud Storage**

  * **Purpose:** Store unstructured data like user profile pictures, recipe images, and potentially exported files.
  * **Technology:** Supabase Storage (integrated with Supabase ecosystem) or a dedicated cloud storage solution (e.g., AWS S3, Google Cloud Storage).
  * **Security:** Access controls and public/private bucket configurations to protect sensitive assets.

-----

## **6. Cross-Cutting Concerns**

These are functionalities that impact multiple services and layers of the application, requiring a consistent approach.

### **6.1 API Gateway / Backend for Frontend (BFF)**

  * **Role:** While individual services expose APIs, a conceptual (or actual, depending on deployment strategy) API Gateway layer orchestrates requests. For Next.js, this can be partially handled by its API routes.
  * **Responsibilities:**
      * Request routing to appropriate backend services.
      * Authentication and authorization checks before forwarding requests.
      * Rate limiting.
      * Request/response transformation if necessary for specific clients.

### **6.2 Error Handling & Monitoring**

  * **Unified Strategy:** Implement consistent error handling across client-side, API, database, and external service integrations.

  * **Client-Side Errors:**

      * React Error Boundaries for graceful UI degradation.
      * Form validation with real-time feedback.
      * Clear, user-friendly error messages.

  * **API Errors:**

      * Standardized HTTP status codes and error response formats.
      * Retry mechanisms with exponential backoff for transient failures.
      * Circuit breakers to prevent cascading failures from external services.

  * **Database Errors:**

      * Transaction rollbacks on query failures.
      * Ensuring data integrity and recovery attempts.

  * **External Service Errors:**

      * Health checks for integrated services.
      * Timeouts and fallback options (e.g., degraded mode, cached responses).

  * **Logging System:**

      * Structured logging of errors, warnings, and information across all services.
      * Capture error details, stack traces, user context, and environment data.

  * **Monitoring & Alerting:**

      * Real-time performance monitoring (API latency, database queries, resource usage).
      * Automated alerts for critical errors, service downtime, and performance degradation.

  * **Recovery Procedures:**

      * Automatic recovery (self-healing, state reset) where feasible.
      * Manual intervention tools for support teams.
      * Comprehensive runbooks for incident response.

  * **Error Flow Example (API Request):**

    ```mermaid
    graph LR
        A[Client Request] --> B[API Gateway];
        B --> C{Backend Service};
        C --> D{External API / DB Call};
        D -- Error --> E[Backend Error Handler];
        E -- Log Error & Context --> F[Logging/Monitoring System];
        E -- Generate Standard Error Response --> G[Return Error to Client];
        C -- Success --> H[Return Success Response to Client];
        G --> I[Client Error Handling (UI Display, Retry)];
    ```

### **6.3 Integration System**

  * **External Service Integration:**
      * Standardized approach for connecting with third-party APIs (Edamam, Spoonacular, OpenAI, future payment gateways, calendar services).
      * Handling API keys, rate limits, and versioning.
  * **Data Synchronization:**
      * Real-time sync for critical data (e.g., meal plan changes reflected instantly).
      * Batch processing for less time-sensitive data updates.
      * Conflict resolution strategies for concurrent modifications.
  * **Notification Integration:** Integration with push notification services and email platforms.
  * **Analytics Integration:** Seamless flow of user and system data to the Analytics Service.

### **6.4 Analytics & Business Intelligence**

  * **Comprehensive Tracking:** Collect data on user behavior, engagement, and feature adoption.
  * **Performance Measurement:** Monitor application load times, API response times, and system health.
  * **Reporting:** Generate automated daily/weekly/monthly reports.
  * **Data Visualization:** Provide interactive dashboards for stakeholders to monitor key metrics.
  * **Privacy Compliance:** Ensure data collection and usage adheres to privacy regulations (e.g., GDPR, CCPA) with consent management and data anonymization.

### **6.5 Security**

  * **Authentication & Authorization:** As detailed in the Authentication Service, using JWTs, OAuth, and granular access controls.
  * **Data Privacy:** Strict policies for data collection, storage, and processing, including anonymization where appropriate.
  * **Input Validation:** Comprehensive validation on all user inputs and API requests to prevent malicious data.
  * **Rate Limiting:** Implement limits on various endpoints to prevent abuse and denial-of-service attacks.
  * **Secure Coding Practices:** Follow OWASP Top 10 guidelines and secure development lifecycle (SDL) principles.
  * **Vulnerability Scanning:** Regular security audits and vulnerability assessments.

### **6.6 Deployment & Operations**

  * **CI/CD Pipeline:** Automated build, test, and deployment processes for both web and mobile applications.
  * **Environment Management:** Segregated environments (development, staging, production) with appropriate access controls.
  * **Scalability Strategy:** Utilize cloud-native services (serverless functions, managed databases) for automatic scaling. Implement load balancing for client requests.
  * **Monitoring & Alerting:** Integrate with APM (Application Performance Monitoring) tools, log aggregation, and alerting systems to proactively identify and resolve issues.
  * **Rollback Procedures:** Ability to quickly revert to previous stable versions in case of critical deployment issues.

-----

## **7. Implementation Plan Overview**

The implementation will follow a phased approach, building incrementally from core infrastructure to advanced features and production readiness.

  * **Phase 1: Setup and Infrastructure:** API key setup, service wrappers, common types.
  * **Phase 2: EdaSpoon Implementation:** Core meal plan generation using Edamam and Spoonacular.
  * **Phase 3: AI Implementation:** Meal plan generation using OpenAI.
  * **Phase 4: Hybrid Approach Implementation:** Combining AI with external APIs for robust meal plan generation.
  * **Phase 5: Testing & Optimization:** Comprehensive testing (unit, integration, E2E), performance tuning, UX refinement, and comparison of approaches.
  * **Phase 6: Production Readiness:** Comprehensive error handling, monitoring, logging, documentation, and final deployment preparations.

-----

## **8. Conclusion**

This detailed architecture plan provides a robust, scalable, and secure foundation for LeanEats. By adhering to the principles of decoupled services, API-first design, and strategic mobile readiness, we are well-positioned to deliver a high-quality product that meets current user needs and is adaptable for future growth and expansion. The chosen technologies and methodologies aim to maximize development efficiency, ensure system reliability, and provide a seamless experience for users across web and future mobile platforms.

-----