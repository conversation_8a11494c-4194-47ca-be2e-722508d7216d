"use client";

import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical } from 'lucide-react';
import { MealCard } from './MealCard';
import { Meal } from '@/types/new-meal-plan';

interface DraggableMealCardProps {
  id: string;
  meal: Meal;
  day: string;
  mealType: string;
  onViewDetails: (meal: Meal) => void;
  onRemove: (day: string, mealType: string) => void;
  onStatusChange: (day: string, mealType: string, status: 'cooked' | 'skipped' | null) => void;
  onRegenerate: (day: string, mealType: string) => void;
  onSwap?: (day: string, mealType: string, meal: Meal) => void;
  onAdjustServings?: (day: string, mealType: string, meal: Meal) => void;
  onToggleFavorite?: (day: string, mealType: string) => void;
  onAddNotes?: (day: string, mealType: string, meal: Meal) => void;
}

export function DraggableMealCard({
  id,
  meal,
  day,
  mealType,
  onViewDetails,
  onRemove,
  onStatusChange,
  onRegenerate,
  onSwap,
  onAdjustServings,
  onToggleFavorite,
  onAddNotes,
}: DraggableMealCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1 : 0,
  };

  return (
    <div ref={setNodeRef} style={style} className="relative group h-full w-full">
      <div
        className="absolute left-0 top-0 bottom-0 w-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-grab z-10"
        {...attributes}
        {...listeners}
      >
        <GripVertical className="h-4 w-4 text-muted-foreground" />
      </div>
      <div className="pl-6 h-full w-full">
        <MealCard
          meal={meal}
          day={day}
          mealType={mealType}
          onViewDetails={onViewDetails}
          onRemove={onRemove}
          onStatusChange={onStatusChange}
          onRegenerate={onRegenerate}
          onSwap={onSwap}
          onAdjustServings={onAdjustServings}
          onToggleFavorite={onToggleFavorite}
          onAddNotes={onAddNotes}
        />
      </div>
    </div>
  );
}
