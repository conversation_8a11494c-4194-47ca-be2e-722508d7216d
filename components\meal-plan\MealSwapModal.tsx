"use client";

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { FilteredSearch, SearchFilters } from './FilteredSearch';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { useMealPlanStore } from '@/lib/stores/meal-plan-store-supabase';
import { DEFAULT_RECIPES } from '@/lib/mock-data/default-recipes';
import { FavoriteMeals } from './FavoriteMeals';
import { NutritionComparison } from './NutritionComparison';

interface MealSwapModalProps {
  isOpen: boolean;
  onClose: () => void;
  date: string;
  mealType: string;
  currentMeal: any;
}

export function MealSwapModal({ isOpen, onClose, date, mealType, currentMeal }: MealSwapModalProps) {
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    query: '',
    dietaryPreferences: [],
    cookingTime: [0, 120],
    calories: [0, 1500],
    costRange: [0, 50],
  });
  const [activeTab, setActiveTab] = useState<'suggested' | 'recent' | 'favorites'>('suggested');
  const [suggestedMeals, setSuggestedMeals] = useState<any[]>([]);
  const [recentMeals, setRecentMeals] = useState<any[]>([]);
  const [favoriteMeals, setFavoriteMeals] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedMeal, setSelectedMeal] = useState<any | null>(null);

  const { mealPlan, addMeal } = useMealPlanStore();

  // Load meals when the modal opens
  useEffect(() => {
    if (isOpen) {
      loadMeals();
    }
  }, [isOpen, mealType]);

  // Load meals from different sources
  const loadMeals = async () => {
    setIsLoading(true);

    try {
      // Get suggested meals (from default recipes)
      const defaultMealsForType = DEFAULT_RECIPES[mealType as keyof typeof DEFAULT_RECIPES] || [];
      setSuggestedMeals(defaultMealsForType);

      // Get recent meals from the meal plan
      const recentMealsArray: any[] = [];

      // Collect all meals of the specified type from the meal plan
      Object.entries(mealPlan || {}).forEach(([dateStr, dayMeals]: [string, any]) => {
        if (dayMeals[mealType] && dateStr !== date) {
          recentMealsArray.push({
            ...dayMeals[mealType],
            date: dateStr
          });
        }
      });

      // Sort by date (most recent first) and take the last 10
      recentMealsArray.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      setRecentMeals(recentMealsArray.slice(0, 10));

      // We'll load favorites from the FavoriteMeals component directly
    } catch (error) {
      console.error('Error loading meals:', error);
      toast.error('Failed to load meals');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle meal selection
  const handleSelectMeal = (meal: any) => {
    setSelectedMeal(meal);
  };

  // Handle confirm swap
  const handleConfirmSwap = () => {
    if (!selectedMeal) return;

    try {
      // Add the selected meal to the meal plan
      addMeal(date, mealType, selectedMeal);

      toast.success(`Swapped ${mealType} for ${format(new Date(date), 'MMM d, yyyy')}`);
      onClose();
    } catch (error) {
      console.error('Error swapping meal:', error);
      toast.error('Failed to swap meal');
    }
  };

  // Filter meals based on search filters
  const filterMeals = (meals: any[]) => {
    return meals.filter(meal => {
      // Text search
      const matchesQuery = !searchFilters.query ||
        meal.name.toLowerCase().includes(searchFilters.query.toLowerCase()) ||
        meal.ingredients?.some((ingredient: any) =>
          ingredient.name.toLowerCase().includes(searchFilters.query.toLowerCase())
        );

      // Dietary preferences
      const matchesDietary = searchFilters.dietaryPreferences.length === 0 ||
        searchFilters.dietaryPreferences.some(pref => {
          // This is a simplified check - in a real app, you'd have proper dietary tags on meals
          const prefLower = pref.toLowerCase();
          return meal.name.toLowerCase().includes(prefLower) ||
                 meal.ingredients?.some((i: any) => i.name.toLowerCase().includes(prefLower));
        });

      // Cooking time
      const cookTime = meal.prepTime || 0;
      const matchesCookTime = cookTime >= searchFilters.cookingTime[0] &&
                             cookTime <= searchFilters.cookingTime[1];

      // Calories
      const matchesCalories = meal.calories >= searchFilters.calories[0] &&
                             meal.calories <= searchFilters.calories[1];

      // Cost
      const matchesCost = meal.cost >= searchFilters.costRange[0] &&
                         meal.cost <= searchFilters.costRange[1];

      return matchesQuery && matchesDietary && matchesCookTime && matchesCalories && matchesCost;
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">
            Swap {mealType.charAt(0).toUpperCase() + mealType.slice(1)} for {format(new Date(date), 'MMMM d, yyyy')}
          </DialogTitle>
        </DialogHeader>

        <div className="mb-4">
          <div className="text-sm text-muted-foreground mb-2">Current meal:</div>
          <div className="p-3 border rounded-md bg-gray-50">
            <div className="font-medium">{currentMeal.name}</div>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                {currentMeal.calories} cal
              </Badge>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                ${currentMeal.cost?.toFixed(2) || '0.00'}
              </Badge>
            </div>
          </div>
        </div>

        <div className="mb-4">
          <FilteredSearch
            onSearch={setSearchFilters}
            initialFilters={{ query: searchFilters.query }}
            showFilterButton={true}
          />
        </div>

        <Tabs defaultValue="suggested" onValueChange={(value) => setActiveTab(value as any)}>
          <TabsList className="grid w-full grid-cols-3 mb-4">
            <TabsTrigger value="suggested">Suggested</TabsTrigger>
            <TabsTrigger value="recent">Recent</TabsTrigger>
            <TabsTrigger value="favorites">Favorites</TabsTrigger>
          </TabsList>

          <TabsContent value="suggested" className="space-y-4">
            {isLoading ? (
              <div className="text-center py-8">Loading suggested meals...</div>
            ) : filterMeals(suggestedMeals).length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">No suggested meals found</div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {filterMeals(suggestedMeals).map((meal, index) => (
                  <div
                    key={`suggested-${index}`}
                    className={`border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer ${selectedMeal?.id === meal.id ? 'border-primary ring-1 ring-primary' : ''}`}
                    onClick={() => handleSelectMeal(meal)}
                  >
                    <div className="flex items-start gap-3">
                      {meal.image && (
                        <img src={meal.image} alt={meal.name} className="w-16 h-16 object-cover rounded-md" />
                      )}
                      <div className="flex-1">
                        <h3 className="font-medium">{meal.name}</h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                            {meal.calories} cal
                          </Badge>
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                            ${meal.cost?.toFixed(2) || '0.00'}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground mt-1 line-clamp-2">
                          {meal.ingredients?.slice(0, 3).map((ingredient: any) => ingredient.name).join(', ')}
                          {meal.ingredients?.length > 3 && `, +${meal.ingredients.length - 3} more`}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="recent" className="space-y-4">
            {isLoading ? (
              <div className="text-center py-8">Loading recent meals...</div>
            ) : filterMeals(recentMeals).length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                {searchQuery ? 'No matching recent meals found' : 'No recent meals found'}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {filterMeals(recentMeals).map((meal, index) => (
                  <div
                    key={`recent-${index}`}
                    className={`border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer ${selectedMeal?.id === meal.id ? 'border-primary ring-1 ring-primary' : ''}`}
                    onClick={() => handleSelectMeal(meal)}
                  >
                    <div className="flex items-start gap-3">
                      {meal.image && (
                        <img src={meal.image} alt={meal.name} className="w-16 h-16 object-cover rounded-md" />
                      )}
                      <div className="flex-1">
                        <h3 className="font-medium">{meal.name}</h3>
                        <div className="text-xs text-muted-foreground">
                          Used on {format(new Date(meal.date), 'MMM d, yyyy')}
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                            {meal.calories} cal
                          </Badge>
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                            ${meal.cost?.toFixed(2) || '0.00'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="favorites" className="space-y-4">
            <FavoriteMeals onSelectMeal={handleSelectMeal} showAddButton={true} />
          </TabsContent>
        </Tabs>

        {selectedMeal && (
          <div className="mt-6 border-t pt-4">
            <NutritionComparison currentMeal={currentMeal} newMeal={selectedMeal} />
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          {selectedMeal && (
            <Button onClick={handleConfirmSwap}>
              Confirm Swap
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
