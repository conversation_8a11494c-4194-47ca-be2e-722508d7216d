import { NextResponse } from "next/server";
import { ClientType, getSupabaseClient } from '@/app/services/database';

export async function POST(request: Request) {
  try {
    // Get the admin client to bypass RLS
    const supabase = getSupabaseClient(ClientType.ADMIN);

    // Get the user ID from the request body
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }

    // Check if the user already has recipes
    const { data: existingRecipes, error: checkError } = await supabase
      .from('recipes')
      .select('id')
      .eq('user_id', userId)
      .limit(1);

    if (checkError) {
      console.error('Error checking existing recipes:', checkError);
      return NextResponse.json(
        { error: "Failed to check existing recipes" },
        { status: 500 }
      );
    }

    // If recipes already exist, return them
    if (existingRecipes && existingRecipes.length > 0) {
      return NextResponse.json({
        message: "User already has recipes",
        seeded: false
      });
    }

    // Generate sample recipes
    const sampleRecipes = getSampleRecipes(userId);

    // Insert sample recipes
    const { data, error } = await supabase
      .from('recipes')
      .insert(sampleRecipes)
      .select();

    if (error) {
      console.error('Error seeding recipes:', error);
      return NextResponse.json(
        { error: "Failed to seed recipes" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: "Sample recipes seeded successfully",
      count: sampleRecipes.length,
      seeded: true
    });
  } catch (error) {
    console.error("Unexpected error in seed-recipes API:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: process.env.NODE_ENV === 'development'
          ? (error instanceof Error ? error.message : 'Unknown error occurred')
          : undefined
      },
      { status: 500 }
    );
  }
}

// Function to generate sample recipes
function getSampleRecipes(userId: string) {
  return [
    {
      user_id: userId,
      name: 'Greek Yogurt Parfait',
      description: 'A healthy breakfast parfait with Greek yogurt, fresh berries, and granola.',
      ingredients: [
        { name: 'Greek yogurt', amount: '1', unit: 'cup' },
        { name: 'Mixed berries', amount: '1/2', unit: 'cup' },
        { name: 'Granola', amount: '1/4', unit: 'cup' },
        { name: 'Honey', amount: '1', unit: 'tbsp' }
      ],
      instructions: [
        'Layer Greek yogurt in a glass or bowl.',
        'Add mixed berries on top.',
        'Sprinkle granola over the berries.',
        'Drizzle with honey and serve immediately.'
      ],
      nutrition: {
        calories: 320,
        protein: 20,
        carbs: 45,
        fat: 8,
        fiber: 5
      },
      prep_time: 10,
      cook_time: 0,
      servings: 1,
      cost_per_serving: 3.50,
      cuisine: 'Mediterranean',
      difficulty: 'Easy',
      dietary_restrictions: ['vegetarian'],
      image_url: 'https://images.unsplash.com/photo-1484723091739-30a097e8f929',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      user_id: userId,
      name: 'Caprese Salad',
      description: 'A simple Italian salad made with fresh tomatoes, mozzarella, and basil.',
      ingredients: [
        { name: 'Tomatoes', amount: '2', unit: 'medium' },
        { name: 'Fresh mozzarella', amount: '4', unit: 'oz' },
        { name: 'Fresh basil leaves', amount: '10', unit: '' },
        { name: 'Olive oil', amount: '2', unit: 'tbsp' },
        { name: 'Balsamic glaze', amount: '1', unit: 'tbsp' },
        { name: 'Salt', amount: '1/4', unit: 'tsp' },
        { name: 'Black pepper', amount: '1/4', unit: 'tsp' }
      ],
      instructions: [
        'Slice tomatoes and mozzarella into 1/4-inch thick slices.',
        'Arrange tomato and mozzarella slices on a plate, alternating them.',
        'Tuck basil leaves between the tomato and cheese slices.',
        'Drizzle with olive oil and balsamic glaze.',
        'Season with salt and pepper to taste.'
      ],
      nutrition: {
        calories: 250,
        protein: 12,
        carbs: 10,
        fat: 18,
        fiber: 2
      },
      prep_time: 15,
      cook_time: 0,
      servings: 2,
      cost_per_serving: 3.25,
      cuisine: 'Italian',
      difficulty: 'Easy',
      dietary_restrictions: ['vegetarian', 'gluten-free'],
      image_url: 'https://images.unsplash.com/photo-1592417817098-8fd3d9eb14a5',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      user_id: userId,
      name: 'Vegetable Stir-Fry',
      description: 'A quick and healthy vegetable stir-fry with a savory sauce.',
      ingredients: [
        { name: 'Broccoli', amount: '1', unit: 'cup' },
        { name: 'Bell peppers', amount: '1', unit: 'medium' },
        { name: 'Carrots', amount: '2', unit: 'medium' },
        { name: 'Snow peas', amount: '1/2', unit: 'cup' },
        { name: 'Garlic', amount: '2', unit: 'cloves' },
        { name: 'Ginger', amount: '1', unit: 'tbsp' },
        { name: 'Soy sauce', amount: '2', unit: 'tbsp' },
        { name: 'Sesame oil', amount: '1', unit: 'tbsp' },
        { name: 'Vegetable oil', amount: '1', unit: 'tbsp' },
        { name: 'Cornstarch', amount: '1', unit: 'tsp' },
        { name: 'Water', amount: '1/4', unit: 'cup' }
      ],
      instructions: [
        'Chop all vegetables into bite-sized pieces.',
        'Mix soy sauce, sesame oil, cornstarch, and water to make the sauce.',
        'Heat vegetable oil in a wok or large pan over high heat.',
        'Add garlic and ginger, stir for 30 seconds.',
        'Add vegetables, starting with the ones that take longest to cook.',
        'Stir-fry for 5-7 minutes until vegetables are crisp-tender.',
        'Pour sauce over vegetables and stir until thickened.',
        'Serve hot over rice or noodles.'
      ],
      nutrition: {
        calories: 380,
        protein: 15,
        carbs: 55,
        fat: 12,
        fiber: 8
      },
      prep_time: 20,
      cook_time: 10,
      servings: 2,
      cost_per_serving: 4.50,
      cuisine: 'Asian',
      difficulty: 'Intermediate',
      dietary_restrictions: ['vegetarian', 'vegan'],
      image_url: 'https://images.unsplash.com/photo-1512058564366-18510be2db19',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      user_id: userId,
      name: 'Pesto Pasta',
      description: 'A simple pasta dish with homemade basil pesto sauce.',
      ingredients: [
        { name: 'Pasta', amount: '8', unit: 'oz' },
        { name: 'Fresh basil leaves', amount: '2', unit: 'cups' },
        { name: 'Pine nuts', amount: '1/4', unit: 'cup' },
        { name: 'Parmesan cheese', amount: '1/2', unit: 'cup' },
        { name: 'Garlic', amount: '2', unit: 'cloves' },
        { name: 'Olive oil', amount: '1/3', unit: 'cup' },
        { name: 'Salt', amount: '1/2', unit: 'tsp' },
        { name: 'Black pepper', amount: '1/4', unit: 'tsp' }
      ],
      instructions: [
        'Cook pasta according to package instructions.',
        'In a food processor, combine basil, pine nuts, Parmesan, and garlic.',
        'Pulse until coarsely chopped.',
        'With the processor running, slowly add olive oil until smooth.',
        'Season with salt and pepper.',
        'Drain pasta and return to pot.',
        'Toss pasta with pesto sauce.',
        'Serve with additional Parmesan cheese if desired.'
      ],
      nutrition: {
        calories: 420,
        protein: 12,
        carbs: 60,
        fat: 16,
        fiber: 4
      },
      prep_time: 15,
      cook_time: 10,
      servings: 2,
      cost_per_serving: 3.75,
      cuisine: 'Italian',
      difficulty: 'Easy',
      dietary_restrictions: ['vegetarian'],
      image_url: 'https://images.unsplash.com/photo-1551892374-ecf8754cf8b0',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      user_id: userId,
      name: 'Avocado Toast',
      description: 'Simple and nutritious avocado toast with optional toppings.',
      ingredients: [
        { name: 'Whole grain bread', amount: '2', unit: 'slices' },
        { name: 'Ripe avocado', amount: '1', unit: 'medium' },
        { name: 'Lemon juice', amount: '1', unit: 'tsp' },
        { name: 'Red pepper flakes', amount: '1/4', unit: 'tsp' },
        { name: 'Salt', amount: '1/4', unit: 'tsp' },
        { name: 'Black pepper', amount: '1/4', unit: 'tsp' },
        { name: 'Olive oil', amount: '1', unit: 'tsp' }
      ],
      instructions: [
        'Toast the bread until golden and firm.',
        'Cut the avocado in half, remove the pit, and scoop the flesh into a bowl.',
        'Add lemon juice, salt, and pepper to the avocado and mash with a fork.',
        'Spread the mashed avocado onto the toast.',
        'Drizzle with olive oil and sprinkle with red pepper flakes.',
        'Add additional toppings as desired.'
      ],
      nutrition: {
        calories: 280,
        protein: 8,
        carbs: 25,
        fat: 18,
        fiber: 7
      },
      prep_time: 10,
      cook_time: 5,
      servings: 2,
      cost_per_serving: 2.50,
      cuisine: 'American',
      difficulty: 'Easy',
      dietary_restrictions: ['vegetarian', 'vegan'],
      image_url: 'https://images.unsplash.com/photo-1525351484163-7529414344d8',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ];
}
