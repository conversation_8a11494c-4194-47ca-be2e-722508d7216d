import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { z } from 'zod';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';

const customizationSchema = z.object({
  mealPlanId: z.string().uuid(),
  mealId: z.string().uuid(),
  modifications: z.object({
    servings: z.number().optional(),
    prepTime: z.number().optional(),
    excludedIngredients: z.array(z.string()).optional(),
    additionalNotes: z.string().optional(),
  }),
});

export async function PATCH(request: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = customizationSchema.parse(body);

    // Update meal plan in database
    const updatedMeal = await prisma.meals.update({
      where: {
        id: validatedData.mealId,
        meal_plan: {
          user_id: session.user.id,
        },
      },
      data: {
        customization: validatedData.modifications,
        updated_at: new Date(),
      },
    });

    logger.info('Meal customization updated', {
      mealId: validatedData.mealId,
      userId: session.user.id,
    });

    return NextResponse.json({ data: updatedMeal });
  } catch (error) {
    logger.error('Meal customization error:', error);
    return NextResponse.json(
      { error: 'Failed to customize meal' },
      { status: 500 }
    );
  }
}