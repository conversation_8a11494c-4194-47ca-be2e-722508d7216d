"use client";

import React, { useState } from 'react';
import { DndContext, DragEndEvent, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { MealSwapModal } from './MealSwapModal';
import { MealServingsModal } from './MealServingsModal';
import { MealNotesModal } from './MealNotesModal';
import { DraggableMealCard } from './DraggableMealCard';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, isSameDay, isSameMonth, startOfMonth, endOfMonth } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { toast } from 'sonner';
import { MealPlan } from '@/types/new-meal-plan';
import { useMealPlanStore } from '@/lib/stores/meal-plan-store-supabase';

interface MealPlanCalendarViewProps {
  currentDate: Date;
  viewMode: 'week' | 'month';
  layout: 'grid' | 'list';
  mealPlan: MealPlan | null;
}

export function MealPlanCalendarView({
  currentDate,
  viewMode,
  layout,
  mealPlan
}: MealPlanCalendarViewProps) {
  // State for meal swap modal
  const [isSwapModalOpen, setIsSwapModalOpen] = useState(false);
  const [swapInfo, setSwapInfo] = useState<{ date: string; mealType: string; meal: any } | null>(null);

  // State for meal servings modal
  const [isServingsModalOpen, setIsServingsModalOpen] = useState(false);
  const [servingsInfo, setServingsInfo] = useState<{ date: string; mealType: string; meal: any } | null>(null);

  // State for meal notes modal
  const [isNotesModalOpen, setIsNotesModalOpen] = useState(false);
  const [notesInfo, setNotesInfo] = useState<{ date: string; mealType: string; meal: any } | null>(null);
  const startDate = startOfWeek(currentDate, { weekStartsOn: 1 });
  const endDate = endOfWeek(currentDate, { weekStartsOn: 1 });
  const weekDays = eachDayOfInterval({ start: startDate, end: endDate });

  const mealTypes = ['breakfast', 'lunch', 'dinner'];

  // Function to handle adding a meal
  const handleAddMeal = (date: Date, mealType: string) => {
    console.log(`Add meal for ${format(date, 'yyyy-MM-dd')} - ${mealType}`);
    // This would open a meal selection modal in a real implementation
  };

  // Function to handle viewing meal details
  const handleViewMealDetails = (meal: any) => {
    console.log('View meal details:', meal);
    // This would open a meal details modal in a real implementation
  };

  // Function to handle removing a meal
  const handleRemoveMeal = (date: string, mealType: string) => {
    console.log(`Remove meal for ${date} - ${mealType}`);
    // This would remove the meal in a real implementation
  };

  // Function to handle changing meal status
  const handleStatusChange = (date: string, mealType: string, status: 'cooked' | 'skipped' | null) => {
    console.log(`Change status for ${date} - ${mealType} to ${status}`);
    // This would update the meal status in a real implementation
  };

  // Function to handle regenerating a meal
  const handleRegenerateMeal = (date: string, mealType: string) => {
    console.log(`Regenerate meal for ${date} - ${mealType}`);
    // This would regenerate the meal in a real implementation
  };

  // Function to handle swapping a meal
  const handleSwapMeal = (date: string, mealType: string, meal: any) => {
    setSwapInfo({ date, mealType, meal });
    setIsSwapModalOpen(true);
  };

  // Function to handle adjusting meal servings
  const handleAdjustServings = (date: string, mealType: string, meal: any) => {
    setServingsInfo({ date, mealType, meal });
    setIsServingsModalOpen(true);
  };

  // Function to handle adding or editing meal notes
  const handleAddNotes = (date: string, mealType: string, meal: any) => {
    setNotesInfo({ date, mealType, meal });
    setIsNotesModalOpen(true);
  };

  // Get the necessary functions from the meal plan store
  const { toggleFavorite, moveMeal: moveMealInStore } = useMealPlanStore();

  // Handle drag end event
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) return;

    // Extract source and target information from the IDs
    // Format: day-mealType
    const sourceId = active.id.toString();
    const targetId = over.id.toString();

    const [sourceDay, sourceMealType] = sourceId.split('-');
    const [targetDay, targetMealType] = targetId.split('-');

    // Move the meal
    moveMealInStore(
      { day: sourceDay, mealType: sourceMealType },
      { day: targetDay, mealType: targetMealType }
    );

    toast.success(`Moved ${sourceMealType} from ${format(new Date(sourceDay), 'MMM d')} to ${format(new Date(targetDay), 'MMM d')}`);
  };

  // Function to handle toggling favorite status
  const handleToggleFavorite = (date: string, mealType: string) => {
    toggleFavorite(date, mealType);
  };

  // Check if a date is today
  const isToday = (date: Date) => isSameDay(date, new Date());

  // Generate a month calendar grid (6 weeks x 7 days)
  const generateMonthCalendar = (date: Date) => {
    const monthStart = startOfMonth(date);
    const monthEnd = endOfMonth(date);
    const startDate = startOfWeek(monthStart, { weekStartsOn: 0 }); // Start from Sunday
    const endDate = endOfWeek(monthEnd, { weekStartsOn: 0 }); // End on Saturday

    const days = eachDayOfInterval({ start: startDate, end: endDate });

    // Split into weeks
    const weeks: (Date | null)[][] = [];
    let week: (Date | null)[] = [];

    days.forEach((day) => {
      if (week.length === 7) {
        weeks.push(week);
        week = [];
      }
      week.push(day);
    });

    if (week.length > 0) {
      weeks.push(week);
    }

    return weeks;
  };

  return (
    <div className="p-4">
      {viewMode === 'week' && layout === 'grid' && (
        <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
          <div className="overflow-x-auto pb-2">
            <div className={`grid grid-cols-7 gap-4 min-w-[800px]`}>
              {/* Day headers */}
              {weekDays.map((day) => (
                <div
                  key={format(day, 'yyyy-MM-dd')}
                  className={`text-center p-2 border rounded-md ${isToday(day) ? 'bg-primary/10 border-primary' : 'bg-muted/20'}`}
                >
                  <div className="font-medium">{format(day, 'EEE')}</div>
                  <div className={`text-sm ${isToday(day) ? 'bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center mx-auto' : 'text-muted-foreground'}`}>
                    {format(day, 'd')}
                  </div>
                </div>
              ))}

              {/* Meal grid - render by meal type */}
              {mealTypes.map(mealType => {
                // Collect all meal IDs for this meal type
                const mealIds = weekDays.map(day => {
                  const dateStr = format(day, 'yyyy-MM-dd');
                  return `${dateStr}-${mealType}`;
                }).filter(id => {
                  const [date, type] = id.split('-');
                  return !!mealPlan?.[date]?.[type];
                });

                return (
                  <SortableContext key={`meal-type-${mealType}`} items={mealIds} strategy={verticalListSortingStrategy}>
                    {weekDays.map((day) => {
                      const dateStr = format(day, 'yyyy-MM-dd');
                      const meal = mealPlan?.[dateStr]?.[mealType];
                      const mealPlanned = !!meal;
                      const mealId = `${dateStr}-${mealType}`;

                      return (
                        <div
                          key={mealId}
                          className={`border rounded-md p-2 h-[160px] flex flex-col ${mealType === 'breakfast' ? `bg-red-50 ${!mealPlanned ? 'border-red-300' : 'border-red-100'}` : mealType === 'lunch' ? `bg-blue-50 ${!mealPlanned ? 'border-blue-300' : 'border-blue-100'}` : `bg-green-50 ${!mealPlanned ? 'border-green-300' : 'border-green-100'}`}`}
                        >
                          <div className="text-xs font-medium capitalize mb-2 flex-shrink-0">{mealType}</div>

                          {meal ? (
                            <DraggableMealCard
                              id={mealId}
                              meal={meal}
                              day={dateStr}
                              mealType={mealType}
                              onViewDetails={handleViewMealDetails}
                              onRemove={handleRemoveMeal}
                              onStatusChange={handleStatusChange}
                              onRegenerate={handleRegenerateMeal}
                              onSwap={handleSwapMeal}
                              onAdjustServings={handleAdjustServings}
                              onToggleFavorite={handleToggleFavorite}
                            />
                          ) : (
                            <div className="flex-1 flex items-center justify-center">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="w-full h-full border border-dashed border-gray-300 flex items-center justify-center"
                                onClick={() => handleAddMeal(day, mealType)}
                              >
                                <Plus className="h-5 w-5 text-muted-foreground" />
                              </Button>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </SortableContext>
                );
              })}
            </div>
          </div>
        </DndContext>
      )}

      {viewMode === 'week' && layout === 'list' && (
        <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
          <div className="space-y-6">
            {/* Sort days to show today first, then the rest of the week */}
            {[...weekDays].sort((a, b) => {
              // If a is today, it comes first
              if (isToday(a)) return -1;
              // If b is today, it comes first
              if (isToday(b)) return 1;
              // Otherwise, maintain the original order
              return weekDays.indexOf(a) - weekDays.indexOf(b);
            }).map((day) => {
              const dateStr = format(day, 'yyyy-MM-dd');
              const hasBreakfast = mealPlan?.[dateStr]?.breakfast;
              const hasLunch = mealPlan?.[dateStr]?.lunch;
              const hasDinner = mealPlan?.[dateStr]?.dinner;
              const allMealsPlanned = hasBreakfast && hasLunch && hasDinner;
              const noMealsPlanned = !hasBreakfast && !hasLunch && !hasDinner;

              // Create sortable IDs for each meal slot
              const breakfastId = `${dateStr}-breakfast`;
              const lunchId = `${dateStr}-lunch`;
              const dinnerId = `${dateStr}-dinner`;

              // Collect all meal IDs for the SortableContext
              const mealIds = [breakfastId, lunchId, dinnerId].filter(id => {
                const [date, type] = id.split('-');
                return !!mealPlan?.[date]?.[type];
              });

              return (
                <div
                  key={dateStr}
                  className={`border rounded-lg p-4 ${isToday(day) ? 'bg-primary/5 border-primary shadow-md relative' : noMealsPlanned ? 'border-red-300' : allMealsPlanned ? 'border-green-300' : 'border-gray-200'}`}
                >
                  {isToday(day) && (
                    <div className="absolute top-0 right-0 w-0 h-0 border-t-[40px] border-t-primary/20 border-l-[40px] border-l-transparent transform rotate-90"></div>
                  )}
                  <div className={`flex items-center justify-between mb-4 ${isToday(day) ? 'sticky top-0 z-10 bg-primary/5 p-3 -mx-4 -mt-4 rounded-t-lg border-b border-primary/20' : ''}`}>
                    <div className="flex items-center gap-2">
                      <div className={`text-lg font-medium ${isToday(day) ? 'text-primary' : ''}`}>
                        {format(day, 'EEEE')}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {format(day, 'MMMM d, yyyy')}
                      </div>
                    </div>
                    {isToday(day) && (
                      <div className="px-2 py-1 text-xs bg-primary text-primary-foreground rounded-full">
                        Today
                      </div>
                    )}
                  </div>

                  <SortableContext items={mealIds} strategy={verticalListSortingStrategy}>
                    <div className="space-y-3">
                      <div className={`flex items-start gap-3 p-2 rounded-md ${!hasBreakfast ? 'bg-red-50 border border-red-200' : 'bg-red-50'}`}>
                        <div className="font-medium text-sm w-24">Breakfast</div>
                        {hasBreakfast ? (
                          <div className="flex-1">
                            <DraggableMealCard
                              id={breakfastId}
                              meal={mealPlan[dateStr].breakfast}
                              day={dateStr}
                              mealType="breakfast"
                              onViewDetails={handleViewMealDetails}
                              onRemove={handleRemoveMeal}
                              onStatusChange={handleStatusChange}
                              onRegenerate={handleRegenerateMeal}
                              onSwap={handleSwapMeal}
                              onAdjustServings={handleAdjustServings}
                              onToggleFavorite={handleToggleFavorite}
                              onAddNotes={handleAddNotes}
                            />
                          </div>
                        ) : (
                          <div className="flex-1 flex items-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-xs"
                              onClick={() => handleAddMeal(day, 'breakfast')}
                            >
                              <Plus className="h-3 w-3 mr-1" />
                              Add Breakfast
                            </Button>
                          </div>
                        )}
                      </div>

                      <div className={`flex items-start gap-3 p-2 rounded-md ${!hasLunch ? 'bg-blue-50 border border-blue-200' : 'bg-blue-50'}`}>
                        <div className="font-medium text-sm w-24">Lunch</div>
                        {hasLunch ? (
                          <div className="flex-1">
                            <DraggableMealCard
                              id={lunchId}
                              meal={mealPlan[dateStr].lunch}
                              day={dateStr}
                              mealType="lunch"
                              onViewDetails={handleViewMealDetails}
                              onRemove={handleRemoveMeal}
                              onStatusChange={handleStatusChange}
                              onRegenerate={handleRegenerateMeal}
                              onSwap={handleSwapMeal}
                              onAdjustServings={handleAdjustServings}
                              onToggleFavorite={handleToggleFavorite}
                              onAddNotes={handleAddNotes}
                            />
                          </div>
                        ) : (
                          <div className="flex-1 flex items-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-xs"
                              onClick={() => handleAddMeal(day, 'lunch')}
                            >
                              <Plus className="h-3 w-3 mr-1" />
                              Add Lunch
                            </Button>
                          </div>
                        )}
                      </div>

                      <div className={`flex items-start gap-3 p-2 rounded-md ${!hasDinner ? 'bg-green-50 border border-green-200' : 'bg-green-50'}`}>
                        <div className="font-medium text-sm w-24">Dinner</div>
                        {hasDinner ? (
                          <div className="flex-1">
                            <DraggableMealCard
                              id={dinnerId}
                              meal={mealPlan[dateStr].dinner}
                              day={dateStr}
                              mealType="dinner"
                              onViewDetails={handleViewMealDetails}
                              onRemove={handleRemoveMeal}
                              onStatusChange={handleStatusChange}
                              onRegenerate={handleRegenerateMeal}
                              onSwap={handleSwapMeal}
                              onAdjustServings={handleAdjustServings}
                              onToggleFavorite={handleToggleFavorite}
                              onAddNotes={handleAddNotes}
                            />
                          </div>
                        ) : (
                          <div className="flex-1 flex items-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-xs"
                              onClick={() => handleAddMeal(day, 'dinner')}
                            >
                              <Plus className="h-3 w-3 mr-1" />
                              Add Dinner
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </SortableContext>
                </div>
              );
            })}
          </div>
        </DndContext>
      )}

      {viewMode === 'month' && (
        <div className="overflow-x-auto pb-2">
          <div className="grid grid-cols-7 gap-2 min-w-[800px]">
            {/* Month view headers (Sun-Sat) */}
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
              <div key={day} className="text-center p-2 font-medium text-sm bg-gray-50 border-b">
                {day}
              </div>
            ))}

            {/* Generate a month calendar grid */}
            {generateMonthCalendar(currentDate).map((week, weekIndex) => (
              <React.Fragment key={`week-${weekIndex}`}>
                {week.map((day, dayIndex) => {
                  const dateStr = day ? format(day, 'yyyy-MM-dd') : '';
                  const hasBreakfast = day && mealPlan?.[dateStr]?.breakfast;
                  const hasLunch = day && mealPlan?.[dateStr]?.lunch;
                  const hasDinner = day && mealPlan?.[dateStr]?.dinner;
                  const isCurrentMonth = day && isSameMonth(day, currentDate);
                  const isToday = day && isSameDay(day, new Date());
                  const allMealsPlanned = hasBreakfast && hasLunch && hasDinner;
                  const noMealsPlanned = !hasBreakfast && !hasLunch && !hasDinner && isCurrentMonth;

                  return (
                    <div
                      key={`day-${weekIndex}-${dayIndex}`}
                      className={`border rounded-md p-2 min-h-[120px] hover:shadow-sm transition-shadow ${!isCurrentMonth ? 'bg-gray-50 opacity-50' : isToday ? 'bg-primary/5 border-primary shadow-sm' : noMealsPlanned ? 'bg-white border-red-300' : allMealsPlanned ? 'bg-white border-green-300' : 'bg-white'}`}
                    >
                      {day && (
                        <>
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-xs text-muted-foreground">{format(day, 'EEE')}</span>
                            <span className={`text-sm font-medium ${isToday ? 'bg-primary text-primary-foreground rounded-full w-6 h-6 inline-flex items-center justify-center' : ''}`}>
                              {format(day, 'd')}
                            </span>
                          </div>
                          <div className="space-y-1">
                            {/* Breakfast */}
                            {isCurrentMonth && (
                              <div className={`text-xs px-1 py-0.5 rounded flex items-center ${hasBreakfast ? 'bg-red-50 border border-red-100' : 'bg-red-50 border border-red-300'}`}>
                                <div className="w-2 h-2 rounded-full bg-red-400 mr-1 flex-shrink-0"></div>
                                <span className="truncate max-w-[90%]" title={hasBreakfast ? mealPlan[dateStr].breakfast.name : 'Breakfast'}>{hasBreakfast ? mealPlan[dateStr].breakfast.name : 'Breakfast'}</span>
                              </div>
                            )}

                            {/* Lunch */}
                            {isCurrentMonth && (
                              <div className={`text-xs px-1 py-0.5 rounded flex items-center ${hasLunch ? 'bg-blue-50 border border-blue-100' : 'bg-blue-50 border border-blue-300'}`}>
                                <div className="w-2 h-2 rounded-full bg-blue-400 mr-1 flex-shrink-0"></div>
                                <span className="truncate max-w-[90%]" title={hasLunch ? mealPlan[dateStr].lunch.name : 'Lunch'}>{hasLunch ? mealPlan[dateStr].lunch.name : 'Lunch'}</span>
                              </div>
                            )}

                            {/* Dinner */}
                            {isCurrentMonth && (
                              <div className={`text-xs px-1 py-0.5 rounded flex items-center ${hasDinner ? 'bg-green-50 border border-green-100' : 'bg-green-50 border border-green-300'}`}>
                                <div className="w-2 h-2 rounded-full bg-green-400 mr-1 flex-shrink-0"></div>
                                <span className="truncate max-w-[90%]" title={hasDinner ? mealPlan[dateStr].dinner.name : 'Dinner'}>{hasDinner ? mealPlan[dateStr].dinner.name : 'Dinner'}</span>
                              </div>
                            )}

                          </div>
                        </>
                      )}
                    </div>
                  );
                })}
              </React.Fragment>
            ))}
          </div>
        </div>
      )}
      {/* Meal Swap Modal */}
      {swapInfo && (
        <MealSwapModal
          isOpen={isSwapModalOpen}
          onClose={() => setIsSwapModalOpen(false)}
          date={swapInfo.date}
          mealType={swapInfo.mealType}
          currentMeal={swapInfo.meal}
        />
      )}

      {/* Meal Servings Modal */}
      {servingsInfo && (
        <MealServingsModal
          isOpen={isServingsModalOpen}
          onClose={() => setIsServingsModalOpen(false)}
          date={servingsInfo.date}
          mealType={servingsInfo.mealType}
          currentMeal={servingsInfo.meal}
        />
      )}

      {/* Meal Notes Modal */}
      {notesInfo && (
        <MealNotesModal
          isOpen={isNotesModalOpen}
          onClose={() => setIsNotesModalOpen(false)}
          date={notesInfo.date}
          mealType={notesInfo.mealType}
          currentMeal={notesInfo.meal}
        />
      )}
    </div>
  );
}
