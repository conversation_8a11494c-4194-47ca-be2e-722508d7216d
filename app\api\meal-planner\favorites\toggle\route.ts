import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function POST(request: Request) {
  try {
    const { mealId } = await request.json();

    if (!mealId) {
      return NextResponse.json(
        { error: 'Missing meal ID' },
        { status: 400 }
      );
    }

    // Toggle favorite status in database
    const favorite = await prisma.mealFavorite.upsert({
      where: {
        userId_mealId: {
          userId: request.headers.get('user-id')!, // Assuming user ID is set in middleware
          mealId
        }
      },
      update: {
        isFavorite: {
          not: true
        }
      },
      create: {
        userId: request.headers.get('user-id')!,
        mealId,
        isFavorite: true
      }
    });

    return NextResponse.json({ 
      data: { 
        success: true,
        isFavorite: favorite.isFavorite
      } 
    });
  } catch (error) {
    console.error('Error toggling favorite:', error);
    return NextResponse.json(
      { error: 'Failed to toggle favorite status' },
      { status: 500 }
    );
  }
}