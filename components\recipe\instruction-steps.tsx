'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { ChefHat, Timer } from 'lucide-react';

interface InstructionStepsProps {
  instructions: string[];
  onStartCookingMode: () => void;
}

export function InstructionSteps({
  instructions,
  onStartCookingMode
}: InstructionStepsProps) {
  const [completedSteps, setCompletedSteps] = useState<Record<number, boolean>>({});
  
  // Toggle step completion
  const toggleStep = (index: number) => {
    setCompletedSteps(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };
  
  // Extract timer values from instruction text
  const extractTimerValue = (instruction: string): number | null => {
    // Look for patterns like "cook for 5 minutes" or "bake for 10-15 minutes"
    const timePattern = /(\d+)(?:-\d+)?\s*(?:minute|min)/i;
    const match = instruction.match(timePattern);
    return match ? parseInt(match[1], 10) : null;
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Cooking Instructions</CardTitle>
        <CardDescription>Step-by-step guide</CardDescription>
      </CardHeader>
      <CardContent>
        <ol className="space-y-6">
          {instructions.map((instruction, index) => {
            const timerValue = extractTimerValue(instruction);
            
            return (
              <li key={index} className="flex">
                <Checkbox
                  id={`step-${index}`}
                  checked={completedSteps[index] || false}
                  onCheckedChange={() => toggleStep(index)}
                  className="mt-1 mr-3"
                />
                <div className="flex-1">
                  <label
                    htmlFor={`step-${index}`}
                    className={`flex-1 cursor-pointer ${completedSteps[index] ? 'line-through text-muted-foreground' : ''}`}
                  >
                    <div className="flex items-center mb-1">
                      <span className="flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center mr-2">
                        {index + 1}
                      </span>
                      {timerValue && (
                        <span className="text-sm bg-primary/10 text-primary px-2 py-0.5 rounded-full flex items-center ml-auto">
                          <Timer className="h-3 w-3 mr-1" />
                          {timerValue} min
                        </span>
                      )}
                    </div>
                    <p>{instruction}</p>
                  </label>
                </div>
              </li>
            );
          })}
        </ol>
        
        <Button
          className="w-full mt-8"
          onClick={onStartCookingMode}
          variant="default"
        >
          <ChefHat className="h-4 w-4 mr-2" />
          Start Cooking Mode
        </Button>
      </CardContent>
    </Card>
  );
}
