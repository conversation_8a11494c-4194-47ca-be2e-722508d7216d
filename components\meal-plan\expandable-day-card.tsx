'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronDown, ChevronUp, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { DraggableMealCard } from './draggable-meal-card';
import { Progress } from '@/components/ui/progress';

interface ExpandableDayCardProps {
  date: string;
  meals: any[];
  onAddMeal?: (date: string) => void;
  onSwapMeal?: (fromDay: string, fromMealId: string, toDay: string, toMealId: string) => void;
  onEditMeal?: (mealId: string) => void;
  onDeleteMeal?: (mealId: string) => void;
  onAddToCalendar?: (mealId: string) => void;
  className?: string;
  nutritionGoals?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
}

export function ExpandableDayCard({
  date,
  meals,
  onAddMeal,
  onSwapMeal,
  onEditMeal,
  onDeleteMeal,
  onAddToCalendar,
  className,
  nutritionGoals
}: ExpandableDayCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Format the date
  const formattedDate = new Date(date).toLocaleDateString('en-US', {
    weekday: 'long',
    month: 'short',
    day: 'numeric'
  });
  
  // Calculate nutrition totals
  const nutritionTotals = meals.reduce((acc, meal) => {
    if (meal.nutrition) {
      acc.calories += meal.nutrition.calories || 0;
      acc.protein += meal.nutrition.protein || 0;
      acc.carbs += meal.nutrition.carbs || 0;
      acc.fat += meal.nutrition.fat || 0;
    }
    return acc;
  }, { calories: 0, protein: 0, carbs: 0, fat: 0 });
  
  // Calculate progress percentages
  const calorieProgress = nutritionGoals ? 
    Math.min(100, (nutritionTotals.calories / nutritionGoals.calories) * 100) : 0;
  
  const proteinProgress = nutritionGoals ? 
    Math.min(100, (nutritionTotals.protein / nutritionGoals.protein) * 100) : 0;
  
  const carbsProgress = nutritionGoals ? 
    Math.min(100, (nutritionTotals.carbs / nutritionGoals.carbs) * 100) : 0;
  
  const fatProgress = nutritionGoals ? 
    Math.min(100, (nutritionTotals.fat / nutritionGoals.fat) * 100) : 0;
  
  // Group meals by type
  const mealsByType = meals.reduce((acc: Record<string, any[]>, meal) => {
    const type = meal.type || 'Other';
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(meal);
    return acc;
  }, {});
  
  // Sort meal types in a logical order
  const mealTypeOrder = ['Breakfast', 'Lunch', 'Dinner', 'Snack', 'Other'];
  const sortedMealTypes = Object.keys(mealsByType).sort((a, b) => {
    const indexA = mealTypeOrder.indexOf(a);
    const indexB = mealTypeOrder.indexOf(b);
    return (indexA === -1 ? 999 : indexA) - (indexB === -1 ? 999 : indexB);
  });
  
  return (
    <Card className={cn('transition-all duration-300', className)}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{formattedDate}</CardTitle>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-8 w-8 p-0" 
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
        
        <div className="flex items-center justify-between mt-1">
          <div className="flex items-center">
            <Badge variant="outline" className="mr-2">
              {meals.length} meals
            </Badge>
            <span className="text-sm text-muted-foreground">
              {nutritionTotals.calories} cal
            </span>
          </div>
          
          <Button 
            variant="outline" 
            size="sm" 
            className="h-7 px-2"
            onClick={() => onAddMeal?.(date)}
          >
            <Plus className="h-3.5 w-3.5 mr-1" />
            Add
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className={cn(
        'grid gap-4 transition-all duration-300 overflow-hidden',
        isExpanded ? 'grid-rows-[1fr]' : 'grid-rows-[0fr]'
      )}>
        <div className={cn(
          'min-h-0 transition-all duration-300',
          isExpanded ? 'opacity-100' : 'opacity-0'
        )}>
          {/* Nutrition Progress */}
          {nutritionGoals && (
            <div className="mb-4 space-y-2">
              <div className="flex justify-between text-xs mb-1">
                <span>Calories</span>
                <span>{nutritionTotals.calories} / {nutritionGoals.calories}</span>
              </div>
              <Progress value={calorieProgress} className="h-2" />
              
              <div className="grid grid-cols-3 gap-2 mt-2">
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <span>Protein</span>
                    <span>{nutritionTotals.protein}g</span>
                  </div>
                  <Progress value={proteinProgress} className="h-1.5" />
                </div>
                
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <span>Carbs</span>
                    <span>{nutritionTotals.carbs}g</span>
                  </div>
                  <Progress value={carbsProgress} className="h-1.5" />
                </div>
                
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <span>Fat</span>
                    <span>{nutritionTotals.fat}g</span>
                  </div>
                  <Progress value={fatProgress} className="h-1.5" />
                </div>
              </div>
            </div>
          )}
          
          {/* Meals by Type */}
          <div className="space-y-4">
            {sortedMealTypes.map(type => (
              <div key={type}>
                <h4 className="text-sm font-medium mb-2">{type}</h4>
                <div className="grid gap-3">
                  {mealsByType[type].map(meal => (
                    <DraggableMealCard
                      key={meal.id}
                      meal={meal}
                      day={date}
                      onSwap={onSwapMeal}
                      onEdit={onEditMeal}
                      onDelete={onDeleteMeal}
                      onAddToCalendar={onAddToCalendar}
                    />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
