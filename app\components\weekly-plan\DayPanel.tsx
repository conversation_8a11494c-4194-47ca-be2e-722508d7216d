'use client';

// No React hooks needed
import { ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { MealCard } from './MealCard';
import { Meal } from '@/types/meal-plan';

interface DayPanelProps {
  day: string;
  meals: Meal[];
  isExpanded: boolean;
  onToggle: () => void;
}

export function DayPanel({ day, meals, isExpanded, onToggle }: DayPanelProps) {
  const totalCalories = meals.reduce((sum, meal) => sum + meal.nutrition.calories, 0);
  const totalCost = meals.reduce((sum, meal) => sum + meal.cost, 0);

  return (
    <div className="border rounded-lg overflow-hidden">
      <div
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50"
        onClick={onToggle}
      >
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold">{day}</h3>
          <div className="text-sm text-gray-500">
            {totalCalories} calories • ${totalCost.toFixed(2)}
          </div>
        </div>
        <Button variant="ghost" size="sm">
          {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </Button>
      </div>

      {isExpanded && (
        <div className="p-4 space-y-4">
          {meals.map((meal, index) => (
            <MealCard
              key={`${day}-${meal.type}-${index}-${meal.recipeId || 'no-recipe'}`}
              meal={meal}
            />
          ))}
        </div>
      )}
    </div>
  );
}
