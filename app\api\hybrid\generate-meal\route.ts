import { NextRequest, NextResponse } from 'next/server';
import { generateMealWithHybrid } from '@/lib/meal-plan-generators/hybrid-generator';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { mealType, calories, dietaryPreferences, excludeIngredients, cookingTime } = body;
    
    if (!mealType || !calories) {
      return NextResponse.json(
        { error: 'Missing required parameters: mealType and calories are required' },
        { status: 400 }
      );
    }
    
    const meal = await generateMealWithHybrid({
      mealType,
      calories,
      dietaryPreferences: dietaryPreferences || [],
      excludeIngredients: excludeIngredients || [],
      cookingTime: cookingTime || 'medium'
    });
    
    return NextResponse.json(meal);
  } catch (error: any) {
    console.error('Error in hybrid generate meal API route:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while generating the meal' },
      { status: 500 }
    );
  }
}
