import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { WeeklyPlanResponse } from '@/app/types/api';

export async function GET(): Promise<NextResponse<WeeklyPlanResponse>> {
  try {
    // TODO: Replace with actual database query
    const mockWeeklyPlan = [
      {
        date: "Monday",
        meals: [
          {
            id: "m1",
            type: "Breakfast",
            name: "Overnight Oats",
            image: "https://images.unsplash.com/photo-1517673132405-a56a62b18caf",
            calories: 300,
            prepTime: 10
          },
          {
            id: "m2",
            type: "Lunch",
            name: "Mediterranean Salad",
            image: "https://images.unsplash.com/photo-1540420773420-3366772f4999",
            calories: 400,
            prepTime: 15
          },
          {
            id: "m3",
            type: "Dinner",
            name: "Chicken Stir-Fry",
            image: "https://images.unsplash.com/photo-1512058564366-18510be2db19",
            calories: 550,
            prepTime: 30
          }
        ]
      },
      // Add more days...
    ];

    return NextResponse.json({ data: { weeklyPlan: mockWeeklyPlan } });
  } catch (error) {
    console.error('Error fetching weekly plan:', error);
    return NextResponse.json(
      { error: 'Failed to fetch weekly plan' },
      { status: 500 }
    );
  }
}



