-- Add back foreign keys
ALTER TABLE "public"."ingredients" 
ADD CONSTRAINT "ingredients_recipe_id_fkey" 
FOREIGN KEY ("recipe_id") REFERENCES "public"."recipes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."meal_plans" 
ADD CONSTRAINT "meal_plans_user_id_fkey" 
FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."meals" 
ADD CONSTRAINT "meals_meal_plan_id_fkey" 
FOREIGN KEY ("meal_plan_id") REFERENCES "public"."meal_plans"("id") ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT "meals_recipe_id_fkey" 
FOREIGN KEY ("recipe_id") REFERENCES "public"."recipes"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "public"."recipes" 
ADD CONSTRAINT "recipes_user_id_fkey" 
FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."shopping_items" 
ADD CONSTRAINT "shopping_items_shopping_list_id_fkey" 
FOREIGN KEY ("shopping_list_id") REFERENCES "public"."shopping_lists"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."shopping_lists" 
ADD CONSTRAINT "shopping_lists_user_id_fkey" 
FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_preferences" 
ADD CONSTRAINT "user_preferences_user_id_fkey" 
FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Update timestamp columns if needed
ALTER TABLE "public"."users" 
ALTER COLUMN "created_at" TYPE TIMESTAMPTZ USING created_at AT TIME ZONE 'UTC',
ALTER COLUMN "updated_at" TYPE TIMESTAMPTZ USING updated_at AT TIME ZONE 'UTC';