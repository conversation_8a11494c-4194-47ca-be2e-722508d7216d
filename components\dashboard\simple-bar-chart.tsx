"use client";

import { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Mock data for the chart
const DATA = {
  calories: [
    { day: 'Mon', value: 2100 },
    { day: 'Tue', value: 1950 },
    { day: 'Wed', value: 2300 },
    { day: 'Thu', value: 2000 },
    { day: 'Fri', value: 2400 },
    { day: 'Sat', value: 1800 },
    { day: 'Sun', value: 2250 },
  ],
  macros: [
    { day: 'Mon', protein: 120, carbs: 220, fats: 60 },
    { day: 'Tue', protein: 100, carbs: 180, fats: 70 },
    { day: 'Wed', protein: 140, carbs: 250, fats: 55 },
    { day: 'Thu', protein: 110, carbs: 200, fats: 65 },
    { day: 'Fri', protein: 130, carbs: 270, fats: 75 },
    { day: 'Sat', protein: 90, carbs: 160, fats: 50 },
    { day: 'Sun', protein: 125, carbs: 230, fats: 70 },
  ]
};

export function SimpleBarChart() {
  const [view, setView] = useState<'calories' | 'macros'>('calories');

  // Calculate the maximum value for scaling
  const maxCalories = Math.max(...DATA.calories.map(item => item.value));
  const maxProtein = Math.max(...DATA.macros.map(item => item.protein));
  const maxCarbs = Math.max(...DATA.macros.map(item => item.carbs));
  const maxFats = Math.max(...DATA.macros.map(item => item.fats));
  
  const maxValue = view === 'calories' ? maxCalories : Math.max(maxProtein, maxCarbs, maxFats);
  
  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Weekly Overview</CardTitle>
            <CardDescription>Track your nutrition progress</CardDescription>
          </div>
          <Select
            value={view}
            onValueChange={(value: 'calories' | 'macros') => setView(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select view" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="calories">Calories</SelectItem>
              <SelectItem value="macros">Macronutrients</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[400px] w-full">
          {view === 'calories' ? (
            <div className="flex h-full items-end space-x-2">
              {DATA.calories.map((item) => (
                <div key={item.day} className="flex flex-col items-center flex-1">
                  <div 
                    className="w-full bg-orange-500 rounded-t-sm hover:bg-orange-600 transition-colors relative group"
                    style={{ height: `${(item.value / maxValue) * 300}px` }}
                  >
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 opacity-0 group-hover:opacity-100 transition-opacity bg-background border rounded p-1 text-xs">
                      {item.value} kcal
                    </div>
                  </div>
                  <div className="text-xs mt-2">{item.day}</div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex h-full items-end space-x-2">
              {DATA.macros.map((item) => (
                <div key={item.day} className="flex flex-col items-center flex-1">
                  <div className="w-full flex space-x-1">
                    <div 
                      className="flex-1 bg-orange-500 rounded-t-sm hover:bg-orange-600 transition-colors relative group"
                      style={{ height: `${(item.protein / maxValue) * 300}px` }}
                    >
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 opacity-0 group-hover:opacity-100 transition-opacity bg-background border rounded p-1 text-xs">
                        {item.protein}g
                      </div>
                    </div>
                    <div 
                      className="flex-1 bg-cyan-600 rounded-t-sm hover:bg-cyan-700 transition-colors relative group"
                      style={{ height: `${(item.carbs / maxValue) * 300}px` }}
                    >
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 opacity-0 group-hover:opacity-100 transition-opacity bg-background border rounded p-1 text-xs">
                        {item.carbs}g
                      </div>
                    </div>
                    <div 
                      className="flex-1 bg-cyan-800 rounded-t-sm hover:bg-cyan-900 transition-colors relative group"
                      style={{ height: `${(item.fats / maxValue) * 300}px` }}
                    >
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 opacity-0 group-hover:opacity-100 transition-opacity bg-background border rounded p-1 text-xs">
                        {item.fats}g
                      </div>
                    </div>
                  </div>
                  <div className="text-xs mt-2">{item.day}</div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Legend */}
        <div className="flex justify-center mt-4 space-x-4">
          {view === 'calories' ? (
            <div className="flex items-center">
              <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
              <span className="text-sm">Calories</span>
            </div>
          ) : (
            <>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                <span className="text-sm">Protein</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-cyan-600 rounded-full mr-2"></div>
                <span className="text-sm">Carbs</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-cyan-800 rounded-full mr-2"></div>
                <span className="text-sm">Fats</span>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
